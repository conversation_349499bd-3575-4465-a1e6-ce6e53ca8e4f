#!/usr/bin/env node

/**
 * Test MCP Integration - Standalone test
 */

const { spawn } = require('child_process');
const path = require('path');

async function testMCPServers() {
    console.log('🧪 Testing MCP Server Integration...\n');
    
    // Test 1: Check if MCP SDK is installed
    console.log('📦 Checking MCP SDK installation...');
    try {
        require('@modelcontextprotocol/sdk/client/index.js');
        console.log('✅ MCP SDK is installed');
    } catch (error) {
        console.log('❌ MCP SDK not found:', error.message);
        return;
    }
    
    // Test 2: Test Exa server availability
    console.log('\n🔍 Testing Exa server...');
    await testServer('npx', ['-y', '@modelcontextprotocol/server-exa', '--help'], 'Exa');
    
    // Test 3: Test Firecrawl server availability  
    console.log('\n🕷️ Testing Firecrawl server...');
    await testServer('npx', ['-y', '@mendable/firecrawl-mcp', '--help'], 'Firecrawl');
    
    // Test 4: Check compiled extension files
    console.log('\n📁 Checking compiled extension files...');
    const fs = require('fs');
    const files = [
        'out/extension.js',
        'out/AizenMCPProvider.js',
        'out/providers/AizenSettingsProvider.js'
    ];
    
    for (const file of files) {
        if (fs.existsSync(file)) {
            console.log(`✅ ${file} exists`);
        } else {
            console.log(`❌ ${file} missing`);
        }
    }
    
    console.log('\n🎉 MCP Integration test completed!');
}

function testServer(command, args, name) {
    return new Promise((resolve) => {
        const process = spawn(command, args, { 
            stdio: 'pipe',
            timeout: 5000 
        });
        
        let output = '';
        process.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        process.stderr.on('data', (data) => {
            output += data.toString();
        });
        
        process.on('close', (code) => {
            if (code === 0 || output.includes('Usage:') || output.includes('help')) {
                console.log(`✅ ${name} server is available`);
            } else {
                console.log(`❌ ${name} server test failed (code: ${code})`);
            }
            resolve();
        });
        
        process.on('error', (error) => {
            console.log(`❌ ${name} server error:`, error.message);
            resolve();
        });
        
        // Kill after timeout
        setTimeout(() => {
            process.kill();
            console.log(`⏰ ${name} server test timed out`);
            resolve();
        }, 5000);
    });
}

// Run the test
testMCPServers().catch(console.error);
