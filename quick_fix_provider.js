/**
 * Quick Fix for Provider Registration
 * This script will help identify and fix the provider registration issue
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Quick Fix for Provider Registration Issue');
console.log('============================================\n');

// 1. Check if the compiled extension has the provider registration
console.log('1. Checking compiled extension.js...');
try {
    const extensionJs = fs.readFileSync('./out/extension.js', 'utf8');
    
    if (extensionJs.includes('registerWebviewViewProvider')) {
        console.log('✅ registerWebviewViewProvider found in compiled code');
    } else {
        console.log('❌ registerWebviewViewProvider NOT found in compiled code');
    }
    
    if (extensionJs.includes('aizen.chatView')) {
        console.log('✅ aizen.chatView view ID found in compiled code');
    } else {
        console.log('❌ aizen.chatView view ID NOT found in compiled code');
    }
    
    if (extensionJs.includes('AizenChatViewProvider')) {
        console.log('✅ AizenChatViewProvider class found in compiled code');
    } else {
        console.log('❌ AizenChatViewProvider class NOT found in compiled code');
    }
    
} catch (error) {
    console.log(`❌ Error reading extension.js: ${error.message}`);
}

// 2. Check if the provider file exists and is compiled
console.log('\n2. Checking provider files...');
const providerFiles = [
    './out/providers/AizenChatViewProvider.js',
    './out/services/AizenIntegrationService.js',
    './out/services/AizenExtensionManager.js'
];

providerFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file} exists`);
        
        // Check if it has the resolveWebviewView method
        try {
            const content = fs.readFileSync(file, 'utf8');
            if (content.includes('resolveWebviewView')) {
                console.log(`   ✅ resolveWebviewView method found`);
            }
        } catch (error) {
            console.log(`   ❌ Error reading file: ${error.message}`);
        }
    } else {
        console.log(`❌ ${file} missing`);
    }
});

// 3. Check UI files
console.log('\n3. Checking UI files...');
const uiFiles = [
    './out/ui/index.html',
    './out/ui/main.js',
    './out/ui/styles.css'
];

uiFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file} exists`);
    } else {
        console.log(`❌ ${file} missing`);
    }
});

console.log('\n🔧 Recommended Actions:');
console.log('1. Open VS Code Developer Console (Help → Toggle Developer Tools)');
console.log('2. Look for any red error messages when the extension loads');
console.log('3. Try running: Ctrl+Shift+P → "Aizen AI: Basic Test"');
console.log('4. If basic test fails, the extension isn\'t loading properly');
console.log('5. If basic test works, the issue is specifically with provider registration');

console.log('\n📋 Debug Commands to Try:');
console.log('• Ctrl+Shift+P → "Aizen AI: Basic Test"');
console.log('• Ctrl+Shift+P → "Aizen AI: Test Provider Registration"');
console.log('• Check Developer Console for error messages');
console.log('• Try: Ctrl+Shift+P → "Developer: Reload Window"');
