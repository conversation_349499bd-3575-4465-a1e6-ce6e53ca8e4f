/**
 * Basic Extension Test
 * Tests if the Aizen AI extension is properly loaded and functional
 */

const vscode = require('vscode');

async function testExtensionBasics() {
    console.log('🧪 Testing Aizen AI Extension Basics...');
    
    try {
        // Test 1: Check if extension is loaded
        const extension = vscode.extensions.getExtension('aizen-ai.aizen-revolutionary-ai');
        if (!extension) {
            console.error('❌ Extension not found in VS Code');
            return false;
        }
        
        console.log('✅ Extension found:', extension.id);
        console.log('📦 Extension version:', extension.packageJSON.version);
        console.log('🔄 Extension active:', extension.isActive);
        
        // Test 2: Activate extension if not active
        if (!extension.isActive) {
            console.log('🚀 Activating extension...');
            await extension.activate();
            console.log('✅ Extension activated');
        }
        
        // Test 3: Check if commands are registered
        const commands = await vscode.commands.getCommands();
        const aizenCommands = commands.filter(cmd => cmd.startsWith('aizen.'));
        
        console.log('🎯 Aizen commands found:', aizenCommands.length);
        aizenCommands.forEach(cmd => console.log(`  • ${cmd}`));
        
        // Test 4: Test basic command
        try {
            await vscode.commands.executeCommand('aizen.test.basic');
            console.log('✅ Basic test command executed successfully');
        } catch (error) {
            console.log('⚠️ Basic test command failed:', error.message);
        }
        
        // Test 5: Check configuration
        const config = vscode.workspace.getConfiguration('aizen');
        console.log('⚙️ Configuration loaded:');
        console.log('  • Model provider:', config.get('model.provider'));
        console.log('  • Model name:', config.get('model.name'));
        console.log('  • UI theme:', config.get('ui.theme'));
        
        // Test 6: Check MCP status
        try {
            await vscode.commands.executeCommand('aizen.mcp.status');
            console.log('✅ MCP status command executed successfully');
        } catch (error) {
            console.log('⚠️ MCP status command failed:', error.message);
        }
        
        console.log('✅ Basic extension tests completed successfully');
        return true;
        
    } catch (error) {
        console.error('❌ Extension test failed:', error);
        return false;
    }
}

// Run the test
testExtensionBasics().then(success => {
    if (success) {
        console.log('🎉 All basic tests passed!');
    } else {
        console.log('💥 Some tests failed. Check the logs above.');
    }
});
