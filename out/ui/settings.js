/**
 * Aizen AI Settings Page Controller
 * Handles all settings interactions and communication with the main extension
 */

class AizenSettingsController {
    constructor() {
        this.vscode = acquireVsCodeApi();
        this.settings = {};
        this.mcpServers = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadSettings();
        this.initializeUI();
        this.updateMcpStatus();
    }

    initializeElements() {
        // Header elements
        this.elements = {
            backButton: document.getElementById('backButton'),
            saveButton: document.getElementById('saveButton'),
            resetButton: document.getElementById('resetButton'),
            
            // AI Model settings
            modelProvider: document.getElementById('modelProvider'),
            modelName: document.getElementById('modelName'),
            temperature: document.getElementById('temperature'),
            temperatureValue: document.getElementById('temperatureValue'),
            maxTokens: document.getElementById('maxTokens'),
            
            // MCP settings
            addMcpServer: document.getElementById('addMcpServer'),
            mcpServersList: document.getElementById('mcpServersList'),
            
            // Theme settings
            themeRadios: document.querySelectorAll('input[name="theme"]'),
            accentColor: document.getElementById('accentColor'),
            colorPresets: document.querySelectorAll('.color-preset'),
            glassIntensity: document.getElementById('glassIntensity'),
            glassIntensityValue: document.getElementById('glassIntensityValue'),
            
            // Advanced settings
            debugMode: document.getElementById('debugMode'),
            autoSave: document.getElementById('autoSave'),
            streamingMode: document.getElementById('streamingMode'),
            
            // Extension preferences
            defaultMode: document.getElementById('defaultMode'),
            shortcutKey: document.getElementById('shortcutKey'),
            changeShortcut: document.getElementById('changeShortcut'),
            startupOpen: document.getElementById('startupOpen')
        };
    }

    setupEventListeners() {
        // Header buttons
        this.elements.backButton?.addEventListener('click', () => this.goBack());
        this.elements.saveButton?.addEventListener('click', () => this.saveSettings());
        this.elements.resetButton?.addEventListener('click', () => this.resetSettings());

        // AI Model settings
        this.elements.temperature?.addEventListener('input', (e) => {
            this.elements.temperatureValue.textContent = e.target.value;
        });

        // MCP settings
        this.elements.addMcpServer?.addEventListener('click', () => this.addMcpServer());

        // Theme settings
        this.elements.themeRadios?.forEach(radio => {
            radio.addEventListener('change', () => this.updateTheme());
        });

        this.elements.accentColor?.addEventListener('change', (e) => {
            this.updateAccentColor(e.target.value);
        });

        this.elements.colorPresets?.forEach(preset => {
            preset.addEventListener('click', (e) => {
                const color = e.target.dataset.color;
                this.elements.accentColor.value = color;
                this.updateAccentColor(color);
            });
        });

        this.elements.glassIntensity?.addEventListener('input', (e) => {
            this.elements.glassIntensityValue.textContent = e.target.value + '%';
            this.updateGlassIntensity(e.target.value);
        });

        // Extension preferences
        this.elements.changeShortcut?.addEventListener('click', () => this.changeShortcut());

        // Listen for messages from the main extension
        window.addEventListener('message', (event) => {
            this.handleMessage(event.data);
        });
    }

    initializeUI() {
        console.log('🚀 Aizen AI Settings initialized');
        
        // Add staggered animation to sections
        const sections = document.querySelectorAll('.settings-section');
        sections.forEach((section, index) => {
            section.style.animationDelay = `${index * 0.1}s`;
        });
    }

    loadSettings() {
        // Request settings from the main extension
        this.vscode.postMessage({
            type: 'getSettings'
        });
    }

    saveSettings() {
        // Collect all settings
        const settings = {
            aiModel: {
                provider: this.elements.modelProvider?.value,
                name: this.elements.modelName?.value,
                temperature: parseFloat(this.elements.temperature?.value || 0.7),
                maxTokens: parseInt(this.elements.maxTokens?.value || 4096)
            },
            mcpServers: this.mcpServers,
            theme: {
                mode: document.querySelector('input[name="theme"]:checked')?.value || 'auto',
                accentColor: this.elements.accentColor?.value || '#3b82f6',
                glassIntensity: parseInt(this.elements.glassIntensity?.value || 80)
            },
            advanced: {
                debugMode: this.elements.debugMode?.checked || false,
                autoSave: this.elements.autoSave?.checked || true,
                streamingMode: this.elements.streamingMode?.checked || true
            },
            extension: {
                defaultMode: this.elements.defaultMode?.value || 'auto',
                shortcutKey: this.elements.shortcutKey?.value || 'Ctrl+Shift+A',
                startupOpen: this.elements.startupOpen?.checked || false
            }
        };

        // Send settings to main extension
        this.vscode.postMessage({
            type: 'saveSettings',
            settings: settings
        });

        // Show save feedback
        this.showSaveSuccess();
    }

    resetSettings() {
        if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
            this.vscode.postMessage({
                type: 'resetSettings'
            });
        }
    }

    goBack() {
        this.vscode.postMessage({
            type: 'closeSettings'
        });
    }

    addMcpServer() {
        const name = prompt('Enter MCP server name:');
        if (!name) return;

        const url = prompt('Enter MCP server URL:');
        if (!url) return;

        const server = {
            id: Date.now().toString(),
            name: name,
            url: url,
            enabled: true
        };

        this.mcpServers.push(server);
        this.renderMcpServers();
    }

    removeMcpServer(id) {
        this.mcpServers = this.mcpServers.filter(server => server.id !== id);
        this.renderMcpServers();
    }

    toggleMcpServer(id) {
        const server = this.mcpServers.find(s => s.id === id);
        if (server) {
            server.enabled = !server.enabled;
            this.renderMcpServers();
        }
    }

    renderMcpServers() {
        const container = this.elements.mcpServersList;
        if (!container) return;

        // Don't clear the container - it has built-in servers in HTML
        // Only clear custom servers section
        const customServersContainer = container.querySelector('.custom-servers') || container;

        // Remove only custom servers, keep built-in ones
        const customServers = container.querySelectorAll('.mcp-server-card:not([data-server="exa"]):not([data-server="firecrawl"])');
        customServers.forEach(server => server.remove());

        if (this.mcpServers.length === 0) {
            // Built-in servers are already in HTML, so don't show empty state
            return;
        }

        this.mcpServers.forEach(server => {
            const serverElement = document.createElement('div');
            serverElement.className = 'mcp-server-item';
            serverElement.innerHTML = `
                <div class="mcp-server-info">
                    <div class="mcp-server-name">${server.name}</div>
                    <div class="mcp-server-url">${server.url}</div>
                </div>
                <div class="mcp-server-actions">
                    <button class="mcp-action-btn toggle" title="${server.enabled ? 'Disable' : 'Enable'}" data-id="${server.id}">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            ${server.enabled ? 
                                '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/><circle cx="12" cy="12" r="3"/>' :
                                '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/><line x1="1" y1="1" x2="23" y2="23"/>'
                            }
                        </svg>
                    </button>
                    <button class="mcp-action-btn delete" title="Remove" data-id="${server.id}">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3,6 5,6 21,6"/>
                            <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                            <line x1="10" y1="11" x2="10" y2="17"/>
                            <line x1="14" y1="11" x2="14" y2="17"/>
                        </svg>
                    </button>
                </div>
            `;

            // Add event listeners
            const toggleBtn = serverElement.querySelector('.toggle');
            const deleteBtn = serverElement.querySelector('.delete');

            toggleBtn?.addEventListener('click', () => this.toggleMcpServer(server.id));
            deleteBtn?.addEventListener('click', () => this.removeMcpServer(server.id));

            container.appendChild(serverElement);
        });
    }

    // New MCP management functions
    configureServer(serverType) {
        console.log('🔧 Configuring server:', serverType);
        console.log('📤 Sending command message to extension');

        // Send command to extension to configure API key
        this.vscode.postMessage({
            type: 'command',
            command: `aizen.mcp.configure${serverType.charAt(0).toUpperCase() + serverType.slice(1)}`
        });

        console.log('✅ Command message sent');
    }

    testServer(serverType) {
        console.log('🧪 Testing server:', serverType);

        // Update UI to show testing state
        const statusElement = document.getElementById(`${serverType}Status`);
        if (statusElement) {
            statusElement.textContent = 'Testing...';
            statusElement.className = 'status-badge status-testing';
        }

        // Send test command to extension
        this.vscode.postMessage({
            type: 'command',
            command: `aizen.mcp.test${serverType.charAt(0).toUpperCase() + serverType.slice(1)}`
        });

        // Update status after test
        setTimeout(() => this.updateMcpStatus(), 2000);
    }

    updateMcpStatus() {
        // Request MCP status from the extension
        this.vscode.postMessage({
            type: 'getMcpStatus'
        });
    }

    refreshMcpStatus() {
        this.updateMcpStatus();
    }

    removeServer(serverType) {
        if (confirm(`Are you sure you want to remove the ${serverType} server configuration?`)) {
            // Send command to extension to remove server
            this.vscode.postMessage({
                type: 'command',
                command: `aizen.mcp.remove${serverType.charAt(0).toUpperCase() + serverType.slice(1)}`
            });

            // Update UI
            setTimeout(() => this.updateMcpStatus(), 1000);
        }
    }

    updateTheme() {
        const selectedTheme = document.querySelector('input[name="theme"]:checked')?.value;
        if (selectedTheme) {
            document.body.setAttribute('data-theme', selectedTheme);
        }
    }

    updateAccentColor(color) {
        document.documentElement.style.setProperty('--accent-primary', color);
        
        // Update color presets selection
        this.elements.colorPresets?.forEach(preset => {
            preset.style.borderColor = preset.dataset.color === color ? color : 'var(--border-color)';
        });
    }

    updateGlassIntensity(intensity) {
        const opacity = intensity / 100 * 0.1;
        document.documentElement.style.setProperty('--glass-opacity', opacity);
    }

    changeShortcut() {
        // This would typically open a shortcut recording dialog
        alert('Shortcut change functionality would be implemented here');
    }

    handleMessage(message) {
        switch (message.type) {
            case 'settingsData':
                this.populateSettings(message.settings);
                break;
            case 'settingsSaved':
                this.showSaveSuccess();
                break;
            case 'settingsReset':
                this.loadSettings();
                break;
            case 'mcpStatus':
                this.updateMcpStatusDisplay(message.status);
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }

    updateMcpStatusDisplay(status) {
        console.log('📊 Updating MCP status display:', status);

        // Update Exa status
        const exaStatus = document.getElementById('exaStatus');
        if (exaStatus && status.exa) {
            exaStatus.textContent = status.exa.configured ? 'Configured' : 'Not Configured';
            exaStatus.className = `status-badge ${status.exa.configured ? 'status-configured' : 'status-unconfigured'}`;

            // Enable/disable test button
            const exaTestBtn = document.querySelector('[onclick="testServer(\'exa\')"]');
            if (exaTestBtn) {
                exaTestBtn.disabled = !status.exa.configured;
            }
        }

        // Update Firecrawl status
        const firecrawlStatus = document.getElementById('firecrawlStatus');
        if (firecrawlStatus && status.firecrawl) {
            firecrawlStatus.textContent = status.firecrawl.configured ? 'Configured' : 'Not Configured';
            firecrawlStatus.className = `status-badge ${status.firecrawl.configured ? 'status-configured' : 'status-unconfigured'}`;

            // Enable/disable test button
            const firecrawlTestBtn = document.querySelector('[onclick="testServer(\'firecrawl\')"]');
            if (firecrawlTestBtn) {
                firecrawlTestBtn.disabled = !status.firecrawl.configured;
            }
        }
    }

    populateSettings(settings) {
        if (!settings) return;

        // AI Model settings
        if (settings.aiModel) {
            if (this.elements.modelProvider) this.elements.modelProvider.value = settings.aiModel.provider || 'openai';
            if (this.elements.modelName) this.elements.modelName.value = settings.aiModel.name || 'gpt-4';
            if (this.elements.temperature) {
                this.elements.temperature.value = settings.aiModel.temperature || 0.7;
                this.elements.temperatureValue.textContent = settings.aiModel.temperature || 0.7;
            }
            if (this.elements.maxTokens) this.elements.maxTokens.value = settings.aiModel.maxTokens || 4096;
        }

        // MCP servers
        if (settings.mcpServers) {
            this.mcpServers = settings.mcpServers;
            this.renderMcpServers();
        }

        // Theme settings
        if (settings.theme) {
            const themeRadio = document.querySelector(`input[name="theme"][value="${settings.theme.mode}"]`);
            if (themeRadio) themeRadio.checked = true;
            
            if (this.elements.accentColor) {
                this.elements.accentColor.value = settings.theme.accentColor || '#3b82f6';
                this.updateAccentColor(settings.theme.accentColor || '#3b82f6');
            }
            
            if (this.elements.glassIntensity) {
                this.elements.glassIntensity.value = settings.theme.glassIntensity || 80;
                this.elements.glassIntensityValue.textContent = (settings.theme.glassIntensity || 80) + '%';
                this.updateGlassIntensity(settings.theme.glassIntensity || 80);
            }
        }

        // Advanced settings
        if (settings.advanced) {
            if (this.elements.debugMode) this.elements.debugMode.checked = settings.advanced.debugMode || false;
            if (this.elements.autoSave) this.elements.autoSave.checked = settings.advanced.autoSave !== false;
            if (this.elements.streamingMode) this.elements.streamingMode.checked = settings.advanced.streamingMode !== false;
        }

        // Extension preferences
        if (settings.extension) {
            if (this.elements.defaultMode) this.elements.defaultMode.value = settings.extension.defaultMode || 'auto';
            if (this.elements.shortcutKey) this.elements.shortcutKey.value = settings.extension.shortcutKey || 'Ctrl+Shift+A';
            if (this.elements.startupOpen) this.elements.startupOpen.checked = settings.extension.startupOpen || false;
        }

        this.updateTheme();
    }

    showSaveSuccess() {
        const saveBtn = this.elements.saveButton;
        if (!saveBtn) return;

        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = `
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="20,6 9,17 4,12"/>
            </svg>
            <span>Saved!</span>
        `;
        saveBtn.style.background = 'var(--accent-success)';

        setTimeout(() => {
            saveBtn.innerHTML = originalText;
            saveBtn.style.background = 'var(--accent-primary)';
        }, 2000);
    }
}

// Initialize the settings controller when DOM is loaded
let aizenSettingsInstance;
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        aizenSettingsInstance = new AizenSettingsController();
        // Make functions globally available for onclick handlers
        window.configureServer = (serverType) => {
            console.log('🌍 Global configureServer called with:', serverType);
            return aizenSettingsInstance.configureServer(serverType);
        };
        window.testServer = (serverType) => {
            console.log('🌍 Global testServer called with:', serverType);
            return aizenSettingsInstance.testServer(serverType);
        };
        window.removeServer = (serverType) => {
            console.log('🌍 Global removeServer called with:', serverType);
            return aizenSettingsInstance.removeServer(serverType);
        };
    });
} else {
    aizenSettingsInstance = new AizenSettingsController();
    // Make functions globally available for onclick handlers
    window.configureServer = (serverType) => {
        console.log('🌍 Global configureServer called with:', serverType);
        return aizenSettingsInstance.configureServer(serverType);
    };
    window.testServer = (serverType) => {
        console.log('🌍 Global testServer called with:', serverType);
        return aizenSettingsInstance.testServer(serverType);
    };
    window.removeServer = (serverType) => {
        console.log('🌍 Global removeServer called with:', serverType);
        return aizenSettingsInstance.removeServer(serverType);
    };
}
