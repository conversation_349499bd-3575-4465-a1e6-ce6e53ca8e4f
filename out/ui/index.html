<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src {{CSP_SOURCE}} 'unsafe-inline'; script-src {{CSP_SOURCE}} 'nonce-{{NONCE}}'; font-src {{CSP_SOURCE}} https:; img-src {{CSP_SOURCE}} data: https:;">
    
    <title>Aizen AI Chat</title>
    
    <!-- Use system fonts for better performance and CSP compliance -->
    <link rel="stylesheet" href="{{CSS_URI}}">
    <link rel="stylesheet" href="{{CHAT_CSS_URI}}">

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="color-scheme" content="dark light">
</head>
<body class="chat-body">
    <div class="chat-container">
        <!-- Chat Header -->
        <header class="chat-header glass" role="banner">
            <div class="header-content">
                <div class="header-left">
                    <div class="aizen-logo">
                        <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                            <line x1="9" y1="9" x2="9.01" y2="9"/>
                            <line x1="15" y1="9" x2="15.01" y2="9"/>
                        </svg>
                        <span>Aizen AI</span>
                    </div>
                </div>
                <div class="header-right">
                    <button class="settings-btn glass" id="settingsButton" aria-label="Open settings" title="Open settings">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Chat Messages -->
        <main class="chat-main" role="main">
            <div class="messages-container" id="messagesContainer">
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                            <line x1="9" y1="9" x2="9.01" y2="9"/>
                            <line x1="15" y1="9" x2="15.01" y2="9"/>
                        </svg>
                    </div>
                    <h2>Welcome to Aizen AI</h2>
                    <p>Your revolutionary AI coding assistant. Ask me anything about your code!</p>
                </div>
            </div>
        </main>

        <!-- Chat Input -->
        <footer class="chat-footer" role="contentinfo">
            <div class="input-container glass">
                <div class="input-wrapper">
                    <textarea 
                        id="messageInput" 
                        class="message-input" 
                        placeholder="Ask Aizen AI anything..."
                        rows="1"
                        aria-label="Message input"
                    ></textarea>
                    <button 
                        id="sendButton" 
                        class="send-btn" 
                        aria-label="Send message" 
                        title="Send message"
                        disabled
                    >
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="22" y1="2" x2="11" y2="13"/>
                            <polygon points="22,2 15,22 11,13 2,9"/>
                        </svg>
                    </button>
                </div>
            </div>
        </footer>
    </div>

    <script nonce="{{NONCE}}">
        const vscode = acquireVsCodeApi();
        
        // DOM elements
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const messagesContainer = document.getElementById('messagesContainer');
        const settingsButton = document.getElementById('settingsButton');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            
            // Enable/disable send button
            sendButton.disabled = !this.value.trim();
        });

        // Send message on Enter (but not Shift+Enter)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Send button click
        sendButton.addEventListener('click', sendMessage);

        // Settings button click
        settingsButton.addEventListener('click', function() {
            vscode.postMessage({
                type: 'openSettings'
            });
        });

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message to chat
            addMessage(message, 'user');
            
            // Clear input
            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendButton.disabled = true;

            // Send to extension
            vscode.postMessage({
                type: 'sendMessage',
                message: message
            });
        }

        function addMessage(content, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = content;
            
            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Listen for messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.type) {
                case 'aiResponse':
                    addMessage(message.content, 'ai');
                    break;
                case 'error':
                    addMessage(`Error: ${message.content}`, 'error');
                    break;
            }
        });

        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html>
