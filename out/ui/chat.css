/* Chat-specific styles */
.chat-body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    height: 100vh;
    overflow: hidden;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.chat-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--vscode-panel-border);
    background: var(--vscode-sideBar-background);
    flex-shrink: 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.aizen-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--vscode-foreground);
}

.logo-icon {
    width: 24px;
    height: 24px;
    color: var(--vscode-textLink-foreground);
}

.settings-btn {
    background: transparent;
    border: 1px solid var(--vscode-button-border);
    color: var(--vscode-button-foreground);
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.settings-btn:hover {
    background: var(--vscode-button-hoverBackground);
}

.settings-btn .icon {
    width: 16px;
    height: 16px;
}

/* Main chat area */
.chat-main {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.messages-container {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Welcome message */
.welcome-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--vscode-descriptionForeground);
}

.welcome-icon {
    margin-bottom: 16px;
}

.welcome-icon svg {
    width: 48px;
    height: 48px;
    color: var(--vscode-textLink-foreground);
}

.welcome-message h2 {
    margin: 0 0 8px 0;
    color: var(--vscode-foreground);
    font-size: 24px;
    font-weight: 600;
}

.welcome-message p {
    margin: 0;
    font-size: 16px;
    opacity: 0.8;
}

/* Messages */
.message {
    display: flex;
    margin-bottom: 16px;
}

.user-message {
    justify-content: flex-end;
}

.ai-message {
    justify-content: flex-start;
}

.error-message {
    justify-content: center;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 12px;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.user-message .message-content {
    background: var(--vscode-textLink-foreground);
    color: white;
    border-bottom-right-radius: 4px;
}

.ai-message .message-content {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    color: var(--vscode-input-foreground);
    border-bottom-left-radius: 4px;
}

.error-message .message-content {
    background: var(--vscode-errorBackground);
    color: var(--vscode-errorForeground);
    border: 1px solid var(--vscode-errorBorder);
    text-align: center;
    max-width: 90%;
}

/* Footer input */
.chat-footer {
    padding: 16px;
    border-top: 1px solid var(--vscode-panel-border);
    background: var(--vscode-sideBar-background);
    flex-shrink: 0;
}

.input-container {
    max-width: 800px;
    margin: 0 auto;
}

.input-wrapper {
    display: flex;
    gap: 8px;
    align-items: flex-end;
}

.message-input {
    flex: 1;
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    color: var(--vscode-input-foreground);
    padding: 12px 16px;
    border-radius: 8px;
    font-family: inherit;
    font-size: 14px;
    resize: none;
    min-height: 20px;
    max-height: 120px;
    line-height: 1.4;
    transition: border-color 0.2s ease;
}

.message-input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
}

.message-input::placeholder {
    color: var(--vscode-input-placeholderForeground);
}

.send-btn {
    background: var(--vscode-button-background);
    border: none;
    color: var(--vscode-button-foreground);
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.send-btn:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-btn .icon {
    width: 18px;
    height: 18px;
}

/* Glass effect */
.glass {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Responsive design */
@media (max-width: 768px) {
    .chat-header {
        padding: 8px 12px;
    }
    
    .chat-main {
        padding: 12px;
    }
    
    .chat-footer {
        padding: 12px;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .welcome-message {
        padding: 20px 12px;
    }
    
    .welcome-message h2 {
        font-size: 20px;
    }
    
    .welcome-message p {
        font-size: 14px;
    }
}
