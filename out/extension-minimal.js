"use strict";

const vscode = require("vscode");

async function activate(context) {
    console.log('🚀 MINIMAL TEST: Aizen AI Extension activating...');
    
    try {
        // Register basic test command
        const testCommand = vscode.commands.registerCommand('aizen.test', () => {
            vscode.window.showInformationMessage('🎉 Basic test works! Extension is loading.');
            console.log('✅ Basic test command executed');
        });
        
        // Register MCP config command
        const mcpConfigCommand = vscode.commands.registerCommand('aizen.mcp.configEditor', () => {
            vscode.window.showInformationMessage('🚀 MCP Config Editor works! (Minimal version)');
            console.log('✅ MCP Config command executed');
        });
        
        // Register other MCP commands
        const mcpStatusCommand = vscode.commands.registerCommand('aizen.mcp.status', () => {
            vscode.window.showInformationMessage('📊 MCP Status: All systems operational (minimal)');
        });
        
        const mcpSettingsCommand = vscode.commands.registerCommand('aizen.mcp.openSettings', () => {
            vscode.window.showInformationMessage('⚙️ MCP Settings opened (minimal)');
        });
        
        const mcpAddServerCommand = vscode.commands.registerCommand('aizen.mcp.addExternalServer', () => {
            vscode.window.showInformationMessage('➕ Add External Server (minimal)');
        });
        
        const mcpTestExaCommand = vscode.commands.registerCommand('aizen.mcp.testExaSearch', () => {
            vscode.window.showInformationMessage('🔍 Exa Search Test (minimal)');
        });
        
        const mcpTestFirecrawlCommand = vscode.commands.registerCommand('aizen.mcp.testFirecrawlScrape', () => {
            vscode.window.showInformationMessage('🕷️ Firecrawl Test (minimal)');
        });
        
        const mcpListToolsCommand = vscode.commands.registerCommand('aizen.mcp.listTools', () => {
            vscode.window.showInformationMessage('🛠️ MCP Tools List (minimal)');
        });
        
        const mcpRunTestsCommand = vscode.commands.registerCommand('aizen.mcp.runTests', () => {
            vscode.window.showInformationMessage('🧪 MCP Tests Running (minimal)');
        });
        
        // Add all commands to subscriptions
        context.subscriptions.push(
            testCommand,
            mcpConfigCommand,
            mcpStatusCommand,
            mcpSettingsCommand,
            mcpAddServerCommand,
            mcpTestExaCommand,
            mcpTestFirecrawlCommand,
            mcpListToolsCommand,
            mcpRunTestsCommand
        );
        
        console.log('✅ MINIMAL TEST: All commands registered successfully');
        vscode.window.showInformationMessage('🚀 Aizen AI Extension loaded! (Minimal Test Version)');
        
    } catch (error) {
        console.error('❌ MINIMAL TEST: Error during activation:', error);
        vscode.window.showErrorMessage(`Extension activation failed: ${error.message}`);
    }
}

function deactivate() {
    console.log('🛑 MINIMAL TEST: Extension deactivating...');
}

module.exports = { activate, deactivate };
