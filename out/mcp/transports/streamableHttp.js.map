{"version": 3, "file": "streamableHttp.js", "sourceRoot": "", "sources": ["../../../src/mcp/transports/streamableHttp.ts"], "names": [], "mappings": ";;;AAAA,qEAAqE;AACrE,mCAAsC;AAatC,MAAa,uBAAwB,SAAQ,qBAAY;IAOrD,YAAoB,MAAqC;QACrD,KAAK,EAAE,CAAC;QADQ,WAAM,GAAN,MAAM,CAA+B;QANjD,cAAS,GAAkB,IAAI,CAAC;QAChC,gBAAW,GAAG,KAAK,CAAC;QACpB,gBAAW,GAAuB,IAAI,CAAC;QAM3C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG;YACX,cAAc,EAAE,kBAAkB;YAClC,GAAG,MAAM,CAAC,OAAO;SACpB,CAAC;IACN,CAAC;IAED,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QAED,0EAA0E;QAC1E,iDAAiD;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAuB;QAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACpC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;gBACvC,MAAM,EAAE,MAAM;gBACd,OAAO;gBACP,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC7B,2EAA2E;aAC9E,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,sDAAsD;YACtD,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YACjE,IAAI,iBAAiB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACvC,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC;gBACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5B,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAqB,CAAC;YACxD,OAAO,MAAM,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,KAAK,CAAC,KAAK;QACP,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC5B,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,2CAA2C;YAC3C,IAAI,CAAC;gBACD,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;oBACtB,MAAM,EAAE,QAAQ;oBAChB,OAAO,EAAE;wBACL,gBAAgB,EAAE,IAAI,CAAC,SAAS;qBACnC;iBACJ,CAAC,CAAC;YACP,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAED,SAAS,CAAC,OAA2C;QACjD,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,CAAC,OAA+B;QACnC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED,OAAO,CAAC,OAAmB;QACvB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC;IAEO,gBAAgB;QACpB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,OAAO;QACX,CAAC;QAED,yDAAyD;QACzD,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAE7D,IAAI,CAAC;YACD,0FAA0F;YAC1F,mEAAmE;YACnE,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE;gBAC1D,OAAO,EAAE;oBACL,gBAAgB,EAAE,IAAI,CAAC,SAAS;iBACnC;aACG,CAAC,CAAC;YAEV,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE;gBACnC,IAAI,CAAC;oBACD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACvC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAClC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;oBACrD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC3E,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;gBACjC,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;gBAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC;YAClE,CAAC,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAc,CAAC,CAAC;QACvC,CAAC;IACL,CAAC;CACJ;AA3ID,0DA2IC;AAED,gFAAgF;AAChF,MAAa,8BAA+B,SAAQ,qBAAY;IAO5D,YAAoB,MAAqC;QACrD,KAAK,EAAE,CAAC;QADQ,WAAM,GAAN,MAAM,CAA+B;QANjD,cAAS,GAAkB,IAAI,CAAC;QAChC,gBAAW,GAAG,KAAK,CAAC;QACpB,oBAAe,GAA0B,IAAI,CAAC;QAMlD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG;YACX,cAAc,EAAE,kBAAkB;YAClC,GAAG,MAAM,CAAC,OAAO;SACpB,CAAC;IACN,CAAC;IAED,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAuB;QAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACpC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;gBACvC,MAAM,EAAE,MAAM;gBACd,OAAO;gBACP,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC7B,2EAA2E;aAC9E,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YACjE,IAAI,iBAAiB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACvC,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC;gBACnC,IAAI,CAAC,YAAY,EAAE,CAAC;YACxB,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAqB,CAAC;YACxD,OAAO,MAAM,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,KAAK,CAAC,KAAK;QACP,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAChC,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC;gBACD,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;oBACtB,MAAM,EAAE,QAAQ;oBAChB,OAAO,EAAE;wBACL,gBAAgB,EAAE,IAAI,CAAC,SAAS;qBACnC;iBACJ,CAAC,CAAC;YACP,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAED,SAAS,CAAC,OAA2C;QACjD,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,CAAC,OAA+B;QACnC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED,OAAO,CAAC,OAAmB;QACvB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC;IAEO,YAAY;QAChB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1C,OAAO;QACX,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC1C,IAAI,CAAC;gBACD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;oBACvC,MAAM,EAAE,KAAK;oBACb,OAAO,EAAE;wBACL,gBAAgB,EAAE,IAAI,CAAC,SAAU;qBACpC;iBACJ,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;oBACd,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACvC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC1B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;oBAC/D,CAAC;gBACL,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,oBAAoB;IAClC,CAAC;CACJ;AAzHD,wEAyHC"}