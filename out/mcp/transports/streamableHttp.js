"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamableHttpPollingTransport = exports.StreamableHttpTransport = void 0;
// MCP Streamable HTTP Transport Implementation - Protocol 2025-06-18
const events_1 = require("events");
class StreamableHttpTransport extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.config = config;
        this.sessionId = null;
        this.isConnected = false;
        this.eventSource = null;
        this.baseUrl = config.url;
        this.headers = {
            'Content-Type': 'application/json',
            ...config.headers
        };
    }
    async connect() {
        if (this.isConnected) {
            throw new Error('Already connected');
        }
        // For streamable HTTP, we don't need to establish a persistent connection
        // The connection is established on first request
        this.isConnected = true;
    }
    async send(message) {
        if (!this.isConnected) {
            throw new Error('Not connected');
        }
        const headers = { ...this.headers };
        if (this.sessionId) {
            headers['mcp-session-id'] = this.sessionId;
        }
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers,
                body: JSON.stringify(message),
                // timeout: this.config.timeout || 30000 // Not supported in standard fetch
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            // Extract session ID from response headers if present
            const responseSessionId = response.headers.get('mcp-session-id');
            if (responseSessionId && !this.sessionId) {
                this.sessionId = responseSessionId;
                this.startEventStream();
            }
            const result = await response.json();
            return result;
        }
        catch (error) {
            this.emit('error', error);
            throw error;
        }
    }
    async close() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        if (this.sessionId) {
            // Send DELETE request to terminate session
            try {
                await fetch(this.baseUrl, {
                    method: 'DELETE',
                    headers: {
                        'mcp-session-id': this.sessionId
                    }
                });
            }
            catch (error) {
                console.warn('Failed to terminate MCP session:', error);
            }
            this.sessionId = null;
        }
        this.isConnected = false;
        this.emit('close');
    }
    onMessage(handler) {
        this.on('message', handler);
    }
    onError(handler) {
        this.on('error', handler);
    }
    onClose(handler) {
        this.on('close', handler);
    }
    startEventStream() {
        if (!this.sessionId || this.eventSource) {
            return;
        }
        // Use EventSource for server-sent events (notifications)
        const eventSourceUrl = new URL(this.baseUrl);
        eventSourceUrl.searchParams.set('sessionId', this.sessionId);
        try {
            // Note: In a real VS Code extension, you might need to use a different SSE implementation
            // as EventSource might not be available in the Node.js environment
            this.eventSource = new EventSource(eventSourceUrl.toString(), {
                headers: {
                    'mcp-session-id': this.sessionId
                }
            });
            this.eventSource.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.emit('message', message);
                }
                catch (error) {
                    console.error('Failed to parse SSE message:', error);
                    this.emit('error', new Error(`Failed to parse SSE message: ${error}`));
                }
            };
            this.eventSource.onerror = (error) => {
                console.error('EventSource error:', error);
                this.emit('error', new Error('EventSource connection error'));
            };
        }
        catch (error) {
            console.error('Failed to create EventSource:', error);
            this.emit('error', error);
        }
    }
}
exports.StreamableHttpTransport = StreamableHttpTransport;
// Alternative implementation using polling for environments without EventSource
class StreamableHttpPollingTransport extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.config = config;
        this.sessionId = null;
        this.isConnected = false;
        this.pollingInterval = null;
        this.baseUrl = config.url;
        this.headers = {
            'Content-Type': 'application/json',
            ...config.headers
        };
    }
    async connect() {
        if (this.isConnected) {
            throw new Error('Already connected');
        }
        this.isConnected = true;
    }
    async send(message) {
        if (!this.isConnected) {
            throw new Error('Not connected');
        }
        const headers = { ...this.headers };
        if (this.sessionId) {
            headers['mcp-session-id'] = this.sessionId;
        }
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers,
                body: JSON.stringify(message),
                // timeout: this.config.timeout || 30000 // Not supported in standard fetch
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const responseSessionId = response.headers.get('mcp-session-id');
            if (responseSessionId && !this.sessionId) {
                this.sessionId = responseSessionId;
                this.startPolling();
            }
            const result = await response.json();
            return result;
        }
        catch (error) {
            this.emit('error', error);
            throw error;
        }
    }
    async close() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
        if (this.sessionId) {
            try {
                await fetch(this.baseUrl, {
                    method: 'DELETE',
                    headers: {
                        'mcp-session-id': this.sessionId
                    }
                });
            }
            catch (error) {
                console.warn('Failed to terminate MCP session:', error);
            }
            this.sessionId = null;
        }
        this.isConnected = false;
        this.emit('close');
    }
    onMessage(handler) {
        this.on('message', handler);
    }
    onError(handler) {
        this.on('error', handler);
    }
    onClose(handler) {
        this.on('close', handler);
    }
    startPolling() {
        if (!this.sessionId || this.pollingInterval) {
            return;
        }
        this.pollingInterval = setInterval(async () => {
            try {
                const response = await fetch(this.baseUrl, {
                    method: 'GET',
                    headers: {
                        'mcp-session-id': this.sessionId
                    }
                });
                if (response.ok) {
                    const messages = await response.json();
                    if (Array.isArray(messages)) {
                        messages.forEach(message => this.emit('message', message));
                    }
                }
            }
            catch (error) {
                console.error('Polling error:', error);
            }
        }, 1000); // Poll every second
    }
}
exports.StreamableHttpPollingTransport = StreamableHttpPollingTransport;
//# sourceMappingURL=streamableHttp.js.map