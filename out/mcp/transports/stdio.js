"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StdioTransport = void 0;
// MCP STDIO Transport Implementation
const child_process_1 = require("child_process");
const events_1 = require("events");
class StdioTransport extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.config = config;
        this.process = null;
        this.messageBuffer = '';
        this.isConnected = false;
    }
    async connect() {
        if (this.process) {
            throw new Error('Already connected');
        }
        return new Promise((resolve, reject) => {
            const env = { ...process.env, ...this.config.env };
            this.process = (0, child_process_1.spawn)(this.config.command, this.config.args || [], {
                env,
                cwd: this.config.cwd,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            this.process.on('error', (error) => {
                this.emit('error', error);
                reject(error);
            });
            this.process.on('exit', (code, signal) => {
                this.isConnected = false;
                this.emit('close');
            });
            if (this.process.stdout) {
                this.process.stdout.on('data', (data) => {
                    this.handleData(data.toString());
                });
            }
            if (this.process.stderr) {
                this.process.stderr.on('data', (data) => {
                    console.error('MCP Server stderr:', data.toString());
                });
            }
            // Give the process a moment to start
            setTimeout(() => {
                if (this.process && !this.process.killed) {
                    this.isConnected = true;
                    resolve();
                }
                else {
                    reject(new Error('Process failed to start'));
                }
            }, 100);
        });
    }
    async send(message) {
        if (!this.process || !this.process.stdin || !this.isConnected) {
            throw new Error('Not connected');
        }
        const messageStr = JSON.stringify(message) + '\n';
        return new Promise((resolve, reject) => {
            this.process.stdin.write(messageStr, (error) => {
                if (error) {
                    reject(error);
                }
                else {
                    // For stdio transport, we don't wait for response here
                    // The response will come through the data handler
                    resolve({});
                }
            });
        });
    }
    async close() {
        if (this.process) {
            this.isConnected = false;
            this.process.kill();
            this.process = null;
        }
    }
    onMessage(handler) {
        this.on('message', handler);
    }
    onError(handler) {
        this.on('error', handler);
    }
    onClose(handler) {
        this.on('close', handler);
    }
    handleData(data) {
        this.messageBuffer += data;
        // Process complete messages (separated by newlines)
        const lines = this.messageBuffer.split('\n');
        this.messageBuffer = lines.pop() || ''; // Keep the incomplete line in buffer
        for (const line of lines) {
            if (line.trim()) {
                try {
                    const message = JSON.parse(line);
                    this.emit('message', message);
                }
                catch (error) {
                    console.error('Failed to parse MCP message:', error, 'Line:', line);
                    this.emit('error', new Error(`Failed to parse message: ${error}`));
                }
            }
        }
    }
}
exports.StdioTransport = StdioTransport;
//# sourceMappingURL=stdio.js.map