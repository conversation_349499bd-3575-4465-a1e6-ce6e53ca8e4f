"use strict";
// MCP Module Exports - Comprehensive Model Context Protocol Implementation
// Based on Protocol 2025-06-18 with security best practices
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCP_IMPLEMENTATION_NAME = exports.MCP_PROTOCOL_VERSION = exports.MCP_IMPLEMENTATION_VERSION = exports.runMCPTests = exports.FirecrawlMCPHelper = exports.FirecrawlMCPTools = exports.FirecrawlMCPServerConfig = exports.ExaMCPHelper = exports.ExaMCPTools = exports.ExaMCPServerConfig = exports.StreamableHttpPollingTransport = exports.StreamableHttpTransport = exports.StdioTransport = exports.MCPSecurityManager = exports.MCPManager = exports.MCPHub = exports.MCPClient = void 0;
// Core MCP Components
var client_1 = require("./client");
Object.defineProperty(exports, "MCPClient", { enumerable: true, get: function () { return client_1.MCPClient; } });
var hub_1 = require("./hub");
Object.defineProperty(exports, "MCPHub", { enumerable: true, get: function () { return hub_1.MCPHub; } });
var manager_1 = require("./manager");
Object.defineProperty(exports, "MCPManager", { enumerable: true, get: function () { return manager_1.MCPManager; } });
var security_1 = require("./security");
Object.defineProperty(exports, "MCPSecurityManager", { enumerable: true, get: function () { return security_1.MCPSecurityManager; } });
// Transport Implementations
var stdio_1 = require("./transports/stdio");
Object.defineProperty(exports, "StdioTransport", { enumerable: true, get: function () { return stdio_1.StdioTransport; } });
var streamableHttp_1 = require("./transports/streamableHttp");
Object.defineProperty(exports, "StreamableHttpTransport", { enumerable: true, get: function () { return streamableHttp_1.StreamableHttpTransport; } });
Object.defineProperty(exports, "StreamableHttpPollingTransport", { enumerable: true, get: function () { return streamableHttp_1.StreamableHttpPollingTransport; } });
// External Server Configurations
var exa_1 = require("./servers/exa");
Object.defineProperty(exports, "ExaMCPServerConfig", { enumerable: true, get: function () { return exa_1.ExaMCPServerConfig; } });
Object.defineProperty(exports, "ExaMCPTools", { enumerable: true, get: function () { return exa_1.ExaMCPTools; } });
Object.defineProperty(exports, "ExaMCPHelper", { enumerable: true, get: function () { return exa_1.ExaMCPHelper; } });
var firecrawl_1 = require("./servers/firecrawl");
Object.defineProperty(exports, "FirecrawlMCPServerConfig", { enumerable: true, get: function () { return firecrawl_1.FirecrawlMCPServerConfig; } });
Object.defineProperty(exports, "FirecrawlMCPTools", { enumerable: true, get: function () { return firecrawl_1.FirecrawlMCPTools; } });
Object.defineProperty(exports, "FirecrawlMCPHelper", { enumerable: true, get: function () { return firecrawl_1.FirecrawlMCPHelper; } });
// UI Components - TODO: Implement React components
// export { MCPServerManager } from '../ui/components/mcp/MCPServerManager';
// Testing
var test_1 = require("./test");
Object.defineProperty(exports, "runMCPTests", { enumerable: true, get: function () { return test_1.runMCPTests; } });
// Types and Interfaces
__exportStar(require("./types"), exports);
// Version and Protocol Information
exports.MCP_IMPLEMENTATION_VERSION = '2.0.0';
exports.MCP_PROTOCOL_VERSION = '2025-06-18';
exports.MCP_IMPLEMENTATION_NAME = 'Aizen AI MCP';
/**
 * Aizen AI MCP Implementation
 *
 * This is a comprehensive implementation of the Model Context Protocol (MCP)
 * designed to integrate external AI services and tools into VS Code extensions.
 *
 * Features:
 * - Protocol 2025-06-18 compliance
 * - Security-first design with user consent flows
 * - Support for multiple transport types (STDIO, HTTP, Streamable HTTP)
 * - Integration with external services (Exa AI, Firecrawl)
 * - Comprehensive testing suite
 * - TypeScript implementation with full type safety
 * - React UI components for server management
 *
 * Security Features:
 * - Explicit user consent for all operations
 * - Risk assessment for tools and resources
 * - Permission management and expiration
 * - Activity logging and monitoring
 * - Domain-based access controls
 *
 * Supported External Services:
 * - Exa AI: Web search, research papers, company research
 * - Firecrawl: Web scraping, crawling, content extraction
 *
 * Usage:
 * ```typescript
 * import { MCPHub, MCPSecurityManager } from './mcp';
 *
 * const mcpHub = new MCPHub(context);
 * const mcpSecurity = new MCPSecurityManager(context);
 *
 * await mcpHub.initialize();
 *
 * // Add external servers
 * const exaServerId = await mcpHub.addServer(ExaMCPServerConfig);
 *
 * // Execute tools with security
 * const result = await mcpHub.executeTool(exaServerId, 'web_search_exa', {
 *   query: 'latest AI developments',
 *   numResults: 5
 * });
 * ```
 */
//# sourceMappingURL=index.js.map