{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../AIAppArchitect/src/mcp/security.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gEAAgE;AAChE,+CAAiC;AACjC,mCAAsC;AA0CtC,MAAa,kBAAmB,SAAQ,qBAAY;IAahD,YAAoB,OAAgC;QAChD,KAAK,EAAE,CAAC;QADQ,YAAO,GAAP,OAAO,CAAyB;QAV5C,oBAAe,GAAG,IAAI,GAAG,EAA0B,CAAC;QACpD,gBAAW,GAOd,EAAE,CAAC;QAIJ,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAED,wBAAwB;IACxB,KAAK,CAAC,iBAAiB,CACnB,QAAgB,EAChB,UAAkB,EAClB,IAA4B,EAC5B,MAAc,EACd,WAAmB,EACnB,QAA8B;QAE9B,kDAAkD;QAClD,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/E,IAAI,kBAAkB,IAAI,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACnE,OAAO,kBAAkB,CAAC,OAAO,CAAC;QACtC,CAAC;QAED,oBAAoB;QACpB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAE/D,yBAAyB;QACzB,MAAM,cAAc,GAAmB;YACnC,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACtE,QAAQ;YACR,UAAU;YACV,IAAI;YACJ,MAAM;YACN,WAAW;YACX,SAAS;YACT,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,YAAY;YAC7D,QAAQ;SACX,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;QAE5D,IAAI,CAAC;YACD,8BAA8B;YAC9B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAE9D,mBAAmB;YACnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAE5E,eAAe;YACf,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,GAAG,IAAI,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAE7E,OAAO,OAAO,CAAC;QAEnB,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAuB;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAEhD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACjD,OAAO,EACP,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,GAAG,OAAO,CACb,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,YAAY,CAAC;QAC9D,MAAM,SAAS,GAAG,MAAM,KAAK,OAAO,CAAC,CAAC;YAClC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,0BAA0B;YACvE,SAAS,CAAC,CAAC,gCAAgC;QAE/C,OAAO;YACH,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,OAAO;YACP,SAAS;YACT,WAAW,EAAE,IAAI,IAAI,EAAE;SAC1B,CAAC;IACN,CAAC;IAEO,oBAAoB,CAAC,OAAuB;QAChD,MAAM,SAAS,GAAG;YACd,GAAG,EAAE,IAAI;YACT,MAAM,EAAE,IAAI;YACZ,IAAI,EAAE,IAAI;SACb,CAAC;QAEF,MAAM,gBAAgB,GAAG;YACrB,IAAI,EAAE,gBAAgB;YACtB,QAAQ,EAAE,mBAAmB;YAC7B,MAAM,EAAE,cAAc;YACtB,WAAW,EAAE,kBAAkB;YAC/B,YAAY,EAAE,qBAAqB;SACtC,CAAC;QAEF,MAAM,WAAW,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,gCAAgC;YAC/E,WAAW,OAAO,CAAC,UAAU,cAAc,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK;YAC9E,IAAI,OAAO,CAAC,MAAM,OAAO;YACzB,GAAG,OAAO,CAAC,WAAW,MAAM;YAC5B,eAAe,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;QAErD,qDAAqD;QACrD,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,IAAI,OAAO,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;YACjE,OAAO,WAAW,GAAG,6EAA6E,CAAC;QACvG,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,cAAc,IAAI,OAAO,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;YAClE,OAAO,WAAW,GAAG,sEAAsE,CAAC;QAChG,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;YAC1D,OAAO,WAAW,GAAG,gEAAgE,CAAC;QAC1F,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,iBAAiB,CAAC,OAAuB;QAC7C,MAAM,WAAW,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAE3C,2DAA2D;QAC3D,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAC9B,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAChC,OAAuB,EACvB,QAAyB;QAEzB,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACnB,2BAA2B;YAC3B,MAAM,UAAU,GAAkB;gBAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,QAAQ,EAAE,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBAC9D,WAAW,EAAE,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBACrE,UAAU,EAAE,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBAClE,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,QAAQ,CAAC,WAAW;gBAC/B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,KAAK,EAAE,OAAO,CAAC,IAAI,KAAK,aAAa,IAAI,OAAO,CAAC,IAAI,KAAK,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI;aACrG,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEjC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,kBAAkB;IACV,eAAe,CACnB,IAA4B,EAC5B,MAAc,EACd,QAA8B;QAE9B,uBAAuB;QACvB,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;YACpD,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;YAClB,MAAM,aAAa,GAAG;gBAClB,iBAAiB;gBACjB,YAAY;gBACZ,aAAa;gBACb,aAAa;gBACb,iBAAiB;aACpB,CAAC;YAEF,MAAM,eAAe,GAAG;gBACpB,WAAW;gBACX,gBAAgB;gBAChB,YAAY;gBACZ,UAAU;aACb,CAAC;YAEF,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBAClE,OAAO,MAAM,CAAC;YAClB,CAAC;YAED,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACpE,OAAO,QAAQ,CAAC;YACpB,CAAC;QACL,CAAC;QAED,oCAAoC;QACpC,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YACtB,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBAChE,OAAO,QAAQ,CAAC;YACpB,CAAC;YAED,IAAI,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChE,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,sBAAsB;QACtB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,wBAAwB;IAChB,sBAAsB,CAC1B,QAAgB,EAChB,IAA4B,EAC5B,MAAc;QAEd,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAC7C,CAAC,CAAC,QAAQ,KAAK,QAAQ;YACvB,CAAC,CAAC,OAAO;YACT,CACI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC;gBAC1C,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,CAAC;gBACjD,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,UAAU,KAAK,MAAM,CAAC;gBAC9C,CAAC,IAAI,KAAK,aAAa,IAAI,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC;gBAChD,CAAC,IAAI,KAAK,cAAc,IAAI,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,CACpD,CACJ,CAAC;IACN,CAAC;IAEO,iBAAiB,CAAC,UAAyB;QAC/C,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YAC5D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,8BAA8B;IAC9B,wBAAwB,CAAC,MAAyB;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;QAE3D,wBAAwB;QACxB,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,uCAAuC;QACvC,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,qBAAqB,CAAC,QAAgB,EAAE,IAAa;QACjD,wCAAwC;QACxC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC;YAC7C,MAAM,qBAAqB,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC3E,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvE,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,mBAAmB;IACX,WAAW,CACf,QAAgB,EAChB,MAAc,EACd,MAAc,EACd,OAAgB,EAChB,SAAiB;QAEjB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;YACxC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ;YACR,MAAM;YACN,MAAM;YACN,OAAO;YACP,SAAS;SACZ,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/E,CAAC;IAED,wBAAwB;IACxB,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,KAAa,EAAE,MAAe;QACnE,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CACzD,CAAC,CAAC,QAAQ,KAAK,QAAQ;YACvB,CAAC,CAAC,KAAK,KAAK,KAAK;YACjB,CACI,CAAC,MAAM;gBACP,CAAC,CAAC,QAAQ,KAAK,MAAM;gBACrB,CAAC,CAAC,WAAW,KAAK,MAAM;gBACxB,CAAC,CAAC,UAAU,KAAK,MAAM,CAC1B,CACJ,CAAC;QAEF,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAClD,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,QAAgB;QACvC,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC;QAC9D,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CACtE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAC/B,CAAC;QAEF,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,GAAG,aAAa,EAAE,CAAC;YAC1D,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAED,UAAU;IACV,cAAc,CAAC,QAAiB;QAC5B,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IACjD,CAAC;IAED,cAAc,CAAC,QAAgB,GAAG;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,iBAAiB;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAgC;QACvD,IAAI,CAAC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;QAC7D,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAChF,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC5D,CAAC;IAED,yBAAyB;IACjB,aAAa,CAAC,GAAW;QAC7B,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC;YACxE,OAAO,MAAM,CAAC,QAAQ,CAAC;QAC3B,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAEO,mBAAmB;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAqB,oBAAoB,CAAC,CAAC;QACtF,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI;YAC7B,MAAM,EAAE,SAAS;YACjB,WAAW,EAAE,EAAE;YACf,sBAAsB,EAAE,IAAI;YAC5B,cAAc,EAAE,EAAE;YAClB,cAAc,EAAE,EAAE;SACrB,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACtF,CAAC;IAEO,kBAAkB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAiB,mBAAmB,CAAC,CAAC;QACjF,IAAI,CAAC,cAAc,GAAG,MAAM,IAAI;YAC5B,sBAAsB,EAAE,IAAI;YAC5B,cAAc,EAAE,EAAE;YAClB,cAAc,EAAE,EAAE;YAClB,oBAAoB,EAAE,KAAK;YAC3B,eAAe,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;YAC1C,qBAAqB,EAAE,IAAI;YAC3B,gBAAgB,EAAE,IAAI;YACtB,gBAAgB,EAAE,IAAI;SACzB,CAAC;IACN,CAAC;IAED,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IAChC,CAAC;CACJ;AAvZD,gDAuZC"}