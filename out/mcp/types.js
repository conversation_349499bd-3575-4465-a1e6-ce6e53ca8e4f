"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPConnectionStatus = exports.MCPErrorCode = exports.MCP_PROTOCOL_VERSION = void 0;
// MCP Protocol Version - Latest
exports.MCP_PROTOCOL_VERSION = "2025-06-18";
// MCP Error Codes
var MCPErrorCode;
(function (MCPErrorCode) {
    // Standard JSON-RPC errors
    MCPErrorCode[MCPErrorCode["ParseError"] = -32700] = "ParseError";
    MCPErrorCode[MCPErrorCode["InvalidRequest"] = -32600] = "InvalidRequest";
    MCPErrorCode[MCPErrorCode["MethodNotFound"] = -32601] = "MethodNotFound";
    MCPErrorCode[MCPErrorCode["InvalidParams"] = -32602] = "InvalidParams";
    MCPErrorCode[MCPErrorCode["InternalError"] = -32603] = "InternalError";
    // MCP-specific errors
    MCPErrorCode[MCPErrorCode["InvalidCapabilities"] = -32000] = "InvalidCapabilities";
    MCPErrorCode[MCPErrorCode["InvalidTool"] = -32001] = "InvalidTool";
    MCPErrorCode[MCPErrorCode["InvalidResource"] = -32002] = "InvalidResource";
    MCPErrorCode[MCPErrorCode["InvalidPrompt"] = -32003] = "InvalidPrompt";
    MCPErrorCode[MCPErrorCode["ResourceNotFound"] = -32004] = "ResourceNotFound";
    MCPErrorCode[MCPErrorCode["ToolExecutionError"] = -32005] = "ToolExecutionError";
    MCPErrorCode[MCPErrorCode["PromptNotFound"] = -32006] = "PromptNotFound";
    MCPErrorCode[MCPErrorCode["Unauthorized"] = -32007] = "Unauthorized";
    MCPErrorCode[MCPErrorCode["RateLimited"] = -32008] = "RateLimited";
    MCPErrorCode[MCPErrorCode["ServerError"] = -32009] = "ServerError";
})(MCPErrorCode || (exports.MCPErrorCode = MCPErrorCode = {}));
// MCP Connection Status
var MCPConnectionStatus;
(function (MCPConnectionStatus) {
    MCPConnectionStatus["Disconnected"] = "disconnected";
    MCPConnectionStatus["Connecting"] = "connecting";
    MCPConnectionStatus["Connected"] = "connected";
    MCPConnectionStatus["Error"] = "error";
    MCPConnectionStatus["Reconnecting"] = "reconnecting";
})(MCPConnectionStatus || (exports.MCPConnectionStatus = MCPConnectionStatus = {}));
//# sourceMappingURL=types.js.map