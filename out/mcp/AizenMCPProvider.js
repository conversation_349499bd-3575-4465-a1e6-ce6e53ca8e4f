"use strict";
/**
 * Aizen MCP Hub - Manages MCP server connections
 * Based on Kilo's implementation approach
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AizenMCPHub = void 0;
const index_js_1 = require("@modelcontextprotocol/sdk/client/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/client/stdio.js");
const types_js_1 = require("@modelcontextprotocol/sdk/types.js");
const vscode = __importStar(require("vscode"));
class AizenMCPHub {
    constructor(context) {
        this.connections = [];
        this.isDisposed = false;
        this._onDidChangeMcpServerDefinitions = new vscode.EventEmitter();
        this.onDidChangeMcpServerDefinitions = this._onDidChangeMcpServerDefinitions.event;
        this.context = context;
    }
    async initialize() {
        console.log('🚀 Initializing Aizen MCP Hub...');
        // Initialize built-in servers
        await this.initializeBuiltinServers();
        console.log('✅ Aizen MCP Hub initialized');
    }
    async initializeBuiltinServers() {
        console.log('🔧 Initializing built-in MCP servers...');
        // Always initialize server definitions, even without API keys
        // This allows the UI to show them and configure them
        const servers = {
            'exa': {
                type: 'stdio',
                command: 'npx',
                args: ['-y', '@modelcontextprotocol/server-exa'],
                env: {},
                disabled: true // Start disabled until API key is configured
            },
            'firecrawl': {
                type: 'stdio',
                command: 'npx',
                args: ['-y', '@mendable/firecrawl-mcp'],
                env: {},
                disabled: true // Start disabled until API key is configured
            }
        };
        // Check if API keys are configured and enable servers
        const exaApiKey = await this.getApiKey('EXA_API_KEY');
        if (exaApiKey) {
            servers['exa'].env = { EXA_API_KEY: exaApiKey };
            servers['exa'].disabled = false;
        }
        const firecrawlApiKey = await this.getApiKey('FIRECRAWL_API_KEY');
        if (firecrawlApiKey) {
            servers['firecrawl'].env = { FIRECRAWL_API_KEY: firecrawlApiKey };
            servers['firecrawl'].disabled = false;
        }
        // Try to connect to enabled servers
        for (const [name, config] of Object.entries(servers)) {
            if (!config.disabled) {
                try {
                    await this.connectToServer(name, config);
                    console.log(`✅ Connected to ${name} MCP server`);
                }
                catch (error) {
                    console.error(`❌ Failed to connect to ${name}:`, error);
                }
            }
            else {
                console.log(`⏸️ ${name} server disabled (no API key)`);
            }
        }
        console.log('✅ Built-in MCP servers initialized');
    }
    async connectToServer(name, config) {
        console.log(`🔌 Connecting to MCP server: ${name}`);
        try {
            const transport = new stdio_js_1.StdioClientTransport({
                command: config.command,
                args: config.args || [],
                env: { ...process.env, ...config.env }
            });
            const client = new index_js_1.Client({
                name: `aizen-${name}`,
                version: '1.0.0'
            }, {
                capabilities: {}
            });
            // Add timeout to prevent hanging
            const connectPromise = client.connect(transport);
            const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Connection timeout')), 10000));
            await Promise.race([connectPromise, timeoutPromise]);
            const connection = {
                server: {
                    name,
                    config,
                    status: 'connected'
                },
                client,
                transport
            };
            this.connections.push(connection);
            console.log(`✅ Connected to MCP server: ${name}`);
        }
        catch (error) {
            console.error(`❌ Failed to connect to MCP server ${name}:`, error);
            // Don't throw - just log and continue
            const connection = {
                server: {
                    name,
                    config,
                    status: 'error'
                },
                client: null,
                transport: null
            };
            this.connections.push(connection);
        }
    }
    async callTool(serverName, toolName, args) {
        const connection = this.connections.find(conn => conn.server.name === serverName);
        if (!connection) {
            throw new Error(`No connection found for server: ${serverName}`);
        }
        if (connection.server.status !== 'connected') {
            throw new Error(`Server ${serverName} is not connected`);
        }
        try {
            const result = await connection.client.request({
                method: 'tools/call',
                params: {
                    name: toolName,
                    arguments: args || {}
                }
            }, types_js_1.CallToolResultSchema);
            console.log(`✅ Tool ${toolName} called successfully on server ${serverName}`);
            return result;
        }
        catch (error) {
            console.error(`❌ Error calling tool ${toolName} on server ${serverName}:`, error);
            throw error;
        }
    }
    getServers() {
        return this.connections
            .filter(conn => !conn.server.config.disabled)
            .map(conn => ({
            name: conn.server.name,
            status: conn.server.status,
            tools: [] // Tools will be populated when we list them
        }));
    }
    async getServerTools(serverName) {
        const connection = this.connections.find(conn => conn.server.name === serverName);
        if (!connection) {
            throw new Error(`No connection found for server: ${serverName}`);
        }
        try {
            const result = await connection.client.request({ method: 'tools/list' }, types_js_1.ListToolsResultSchema);
            return result.tools.map(tool => tool.name);
        }
        catch (error) {
            console.error(`Error listing tools for server ${serverName}:`, error);
            return [];
        }
    }
    async getApiKey(envVar) {
        // First check environment variables
        const envValue = process.env[envVar];
        if (envValue) {
            return envValue;
        }
        // Then check VS Code settings
        const config = vscode.workspace.getConfiguration('aizen.mcp');
        const settingsKey = envVar.toLowerCase().replace('_', '.');
        const settingsValue = config.get(settingsKey);
        if (settingsValue) {
            return settingsValue;
        }
        // Check if stored in extension context (secure storage)
        const storedValue = await this.context.secrets.get(envVar);
        if (storedValue) {
            return storedValue;
        }
        return undefined;
    }
    async configureApiKey(service) {
        const envVar = service === 'exa' ? 'EXA_API_KEY' : 'FIRECRAWL_API_KEY';
        const displayName = service === 'exa' ? 'Exa API Key' : 'Firecrawl API Key';
        const placeholder = service === 'exa' ? 'Enter your Exa API key...' : 'Enter your Firecrawl API key...';
        const apiKey = await vscode.window.showInputBox({
            prompt: `Enter your ${displayName}`,
            placeHolder: placeholder,
            password: true,
            ignoreFocusOut: true
        });
        if (apiKey) {
            // Store in secure storage
            await this.context.secrets.store(envVar, apiKey);
            // Try to connect to the server now that API key is configured
            try {
                const config = {
                    type: 'stdio',
                    command: 'npx',
                    args: service === 'exa' ? ['-y', '@modelcontextprotocol/server-exa'] : ['-y', '@mendable/firecrawl-mcp'],
                    env: { [envVar]: apiKey }
                };
                await this.connectToServer(service, config);
                vscode.window.showInformationMessage(`${displayName} configured and connected successfully!`);
            }
            catch (error) {
                console.error(`Failed to connect to ${service} after configuration:`, error);
                vscode.window.showWarningMessage(`${displayName} configured but connection failed. Check console for details.`);
            }
            // Notify that server definitions have changed
            this._onDidChangeMcpServerDefinitions.fire();
        }
    }
    async removeApiKey(service) {
        const envVar = service === 'exa' ? 'EXA_API_KEY' : 'FIRECRAWL_API_KEY';
        const displayName = service === 'exa' ? 'Exa API Key' : 'Firecrawl API Key';
        await this.context.secrets.delete(envVar);
        this._onDidChangeMcpServerDefinitions.fire();
        vscode.window.showInformationMessage(`${displayName} removed successfully!`);
    }
    async checkServerStatus() {
        const exaApiKey = await this.getApiKey('EXA_API_KEY');
        const firecrawlApiKey = await this.getApiKey('FIRECRAWL_API_KEY');
        // Check if servers are actually connected
        const exaConnected = this.connections.some(conn => conn.server.name === 'exa' && conn.server.status === 'connected');
        const firecrawlConnected = this.connections.some(conn => conn.server.name === 'firecrawl' && conn.server.status === 'connected');
        return {
            exa: !!exaApiKey && exaConnected,
            firecrawl: !!firecrawlApiKey && firecrawlConnected
        };
    }
    async testServer(service) {
        try {
            console.log(`🧪 Testing ${service} server connection...`);
            // Check if API key is configured
            const envVar = service === 'exa' ? 'EXA_API_KEY' : 'FIRECRAWL_API_KEY';
            const apiKey = await this.getApiKey(envVar);
            if (!apiKey) {
                console.log(`❌ ${service} API key not configured`);
                return false;
            }
            // Check if server connection exists
            const connection = this.connections.find(conn => conn.server.name === service);
            if (!connection) {
                console.log(`❌ ${service} server not connected`);
                return false;
            }
            // Try to get server tools as a basic connectivity test
            try {
                const tools = await this.getServerTools(service);
                console.log(`✅ ${service} server test successful. Available tools: ${tools.length}`);
                return true;
            }
            catch (error) {
                console.log(`❌ ${service} server test failed:`, error);
                return false;
            }
        }
        catch (error) {
            console.error(`❌ Error testing ${service} server:`, error);
            return false;
        }
    }
    async dispose() {
        if (this.isDisposed) {
            return;
        }
        console.log('🔌 Disposing MCP Hub...');
        this.isDisposed = true;
        // Close all connections
        for (const connection of this.connections) {
            try {
                await connection.transport.close();
                console.log(`✅ Closed connection to ${connection.server.name}`);
            }
            catch (error) {
                console.error(`❌ Error closing connection to ${connection.server.name}:`, error);
            }
        }
        this.connections = [];
        this._onDidChangeMcpServerDefinitions.dispose();
        console.log('✅ MCP Hub disposed');
    }
}
exports.AizenMCPHub = AizenMCPHub;
//# sourceMappingURL=AizenMCPProvider.js.map