"use strict";

const vscode = require('vscode');

/**
 * MINIMAL TEST EXTENSION - To verify VS Code extension loading works
 */

function activate(context) {
    console.log('🚀 MINIMAL TEST: Extension activating...');
    
    try {
        // Register a simple test command
        const testCommand = vscode.commands.registerCommand('aizen.test.simple', () => {
            vscode.window.showInformationMessage('🎉 MINIMAL TEST WORKS! Extension is loading correctly.');
            console.log('🎉 MINIMAL TEST COMMAND EXECUTED');
        });
        
        context.subscriptions.push(testCommand);
        
        // Register MCP config command
        const mcpCommand = vscode.commands.registerCommand('aizen.mcp.configEditor', () => {
            vscode.window.showInformationMessage('🚀 MCP Config command works!');
            console.log('🚀 MCP CONFIG COMMAND EXECUTED');
        });
        
        context.subscriptions.push(mcpCommand);
        
        // Register settings command
        const settingsCommand = vscode.commands.registerCommand('aizen.showSettings', () => {
            vscode.window.showInformationMessage('⚙️ Settings command works!');
            console.log('⚙️ SETTINGS COMMAND EXECUTED');
        });
        
        context.subscriptions.push(settingsCommand);
        
        console.log('✅ MINIMAL TEST: Commands registered successfully');
        vscode.window.showInformationMessage('🚀 Aizen AI Minimal Test Extension loaded! Try commands in Command Palette.');
        
    } catch (error) {
        console.error('❌ MINIMAL TEST: Error during activation:', error);
        vscode.window.showErrorMessage(`Minimal test failed: ${error.message}`);
    }
}

function deactivate() {
    console.log('🛑 MINIMAL TEST: Extension deactivating...');
}

module.exports = { activate, deactivate };
