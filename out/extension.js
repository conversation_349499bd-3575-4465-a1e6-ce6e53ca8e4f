"use strict";

console.log('🔥 EXTENSION DEBUG: File loaded!');

const vscode = require("vscode");

console.log('🔥 EXTENSION DEBUG: VS Code imported!');

function activate(context) {
    console.log('🔥 EXTENSION DEBUG: activate() called!');
    console.log('🔥 EXTENSION DEBUG: Context:', context);
    
    try {
        // Show immediate message
        vscode.window.showInformationMessage('🔥 EXTENSION DEBUG: Extension is ALIVE!');
        console.log('🔥 EXTENSION DEBUG: Message shown!');
        
        // Register one simple command
        const disposable = vscode.commands.registerCommand('aizen.debug', () => {
            vscode.window.showInformationMessage('🔥 DEBUG COMMAND WORKS!');
            console.log('🔥 EXTENSION DEBUG: Command executed!');
        });
        
        context.subscriptions.push(disposable);
        console.log('🔥 EXTENSION DEBUG: Command registered!');
        
        return Promise.resolve();
        
    } catch (error) {
        console.error('🔥 EXTENSION DEBUG: ERROR in activate:', error);
        vscode.window.showErrorMessage(`DEBUG ERROR: ${error.message}`);
        throw error;
    }
}

function deactivate() {
    console.log('🔥 EXTENSION DEBUG: deactivate() called!');
}

console.log('🔥 EXTENSION DEBUG: Exporting functions...');

module.exports = {
    activate,
    deactivate
};

console.log('🔥 EXTENSION DEBUG: Module exported!');
