"use strict";
/**
 * Aizen AI Extension Manager
 * Central coordinator for all extension functionality
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AizenExtensionManager = void 0;
const vscode = __importStar(require("vscode"));
const AizenChatViewProvider_1 = require("../providers/AizenChatViewProvider");
const AizenSettingsProvider_1 = require("../providers/AizenSettingsProvider");
const AizenSettingsService_1 = require("./AizenSettingsService");
const AizenIntegrationService_1 = require("./AizenIntegrationService");
class AizenExtensionManager {
    constructor(context) {
        this._disposables = [];
        this._isActivated = false;
        this._context = context;
        this._settingsService = AizenSettingsService_1.AizenSettingsService.getInstance();
        this._integrationService = new AizenIntegrationService_1.AizenIntegrationService();
    }
    static getInstance(context) {
        if (!AizenExtensionManager._instance && context) {
            AizenExtensionManager._instance = new AizenExtensionManager(context);
        }
        return AizenExtensionManager._instance;
    }
    setMCPHub(mcpHub) {
        this._mcpHub = mcpHub;
    }
    /**
     * Activate the extension with all components
     */
    async activate() {
        if (this._isActivated) {
            console.log('🔄 Aizen AI Extension already activated');
            return;
        }
        try {
            console.log('🚀 Activating Aizen AI Extension...');
            // Initialize services
            await this._initializeServices();
            // Register providers
            this._registerProviders();
            // Register commands
            this._registerCommands();
            // Setup event listeners
            this._setupEventListeners();
            // Initialize status bar
            this._initializeStatusBar();
            this._isActivated = true;
            console.log('✅ Aizen AI Extension activated successfully');
            // Show welcome message if first time
            await this._showWelcomeMessage();
        }
        catch (error) {
            console.error('❌ Failed to activate Aizen AI Extension:', error);
            vscode.window.showErrorMessage(`Failed to activate Aizen AI: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    /**
     * Deactivate the extension and cleanup resources
     */
    async deactivate() {
        console.log('🔄 Deactivating Aizen AI Extension...');
        try {
            // Dispose all disposables
            this._disposables.forEach(disposable => {
                try {
                    disposable.dispose();
                }
                catch (error) {
                    console.error('Error disposing resource:', error);
                }
            });
            this._disposables = [];
            // Cleanup services
            if (this._integrationService) {
                await this._integrationService.dispose();
            }
            this._isActivated = false;
            console.log('✅ Aizen AI Extension deactivated successfully');
        }
        catch (error) {
            console.error('❌ Error during deactivation:', error);
        }
    }
    /**
     * Get the chat provider instance
     */
    getChatProvider() {
        return this._chatProvider;
    }
    /**
     * Get the settings service instance
     */
    getSettingsService() {
        return this._settingsService;
    }
    /**
     * Get the integration service instance
     */
    getIntegrationService() {
        return this._integrationService;
    }
    async _initializeServices() {
        console.log('⚙️ Initializing services...');
        // Validate settings
        const validation = this._settingsService.validateSettings();
        if (!validation.isValid) {
            console.warn('⚠️ Settings validation warnings:', validation.warnings);
            // Show warnings to user
            for (const warning of validation.warnings) {
                vscode.window.showWarningMessage(`Aizen AI: ${warning}`);
            }
        }
        // Initialize integration service
        await this._integrationService.initialize();
        console.log('✅ Services initialized');
    }
    _registerProviders() {
        console.log('📝 Registering providers...');
        // Register chat view provider
        this._chatProvider = new AizenChatViewProvider_1.AizenChatViewProvider(this._context.extensionUri, this._integrationService);
        const chatProviderDisposable = vscode.window.registerWebviewViewProvider('aizen.chatView', this._chatProvider, {
            webviewOptions: {
                retainContextWhenHidden: true
            }
        });
        this._disposables.push(chatProviderDisposable);
        console.log('✅ Providers registered');
    }
    _registerCommands() {
        console.log('🎯 Registering commands...');
        const commands = [
            // Settings commands
            {
                command: 'aizen.showSettings',
                handler: () => this._showSettings()
            },
            {
                command: 'aizen.resetSettings',
                handler: () => this._resetSettings()
            },
            // Chat commands
            {
                command: 'aizen.newChat',
                handler: () => this._newChat()
            },
            {
                command: 'aizen.clearChat',
                handler: () => this._clearChat()
            },
            // Integration commands
            {
                command: 'aizen.testConnection',
                handler: () => this._testConnection()
            },
            {
                command: 'aizen.refreshAgents',
                handler: () => this._refreshAgents()
            },
            // Development commands
            {
                command: 'aizen.showLogs',
                handler: () => this._showLogs()
            },
            {
                command: 'aizen.exportSettings',
                handler: () => this._exportSettings()
            },
            {
                command: 'aizen.importSettings',
                handler: () => this._importSettings()
            }
        ];
        commands.forEach(({ command, handler }) => {
            const disposable = vscode.commands.registerCommand(command, handler);
            this._disposables.push(disposable);
        });
        console.log(`✅ ${commands.length} commands registered`);
    }
    _setupEventListeners() {
        console.log('👂 Setting up event listeners...');
        // Listen for settings changes
        const settingsListener = this._settingsService.onDidChangeSettings(settings => {
            console.log('⚙️ Settings changed:', settings);
            this._onSettingsChanged(settings);
        });
        this._disposables.push(settingsListener);
        // Listen for workspace changes
        const workspaceListener = vscode.workspace.onDidChangeWorkspaceFolders(event => {
            console.log('📁 Workspace folders changed:', event);
            this._onWorkspaceChanged(event);
        });
        this._disposables.push(workspaceListener);
        // Listen for configuration changes
        const configListener = vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration('aizen')) {
                console.log('🔧 Aizen configuration changed');
                this._onConfigurationChanged(event);
            }
        });
        this._disposables.push(configListener);
        console.log('✅ Event listeners setup');
    }
    _initializeStatusBar() {
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.text = '$(robot) Aizen AI';
        statusBarItem.tooltip = 'Aizen AI - Click to open chat';
        statusBarItem.command = 'aizen.chatView.focus';
        statusBarItem.show();
        this._disposables.push(statusBarItem);
        console.log('✅ Status bar initialized');
    }
    async _showWelcomeMessage() {
        const settings = this._settingsService.getSettings();
        if (settings.features.notifications) {
            const isFirstTime = !this._context.globalState.get('aizen.hasShownWelcome', false);
            if (isFirstTime) {
                const action = await vscode.window.showInformationMessage('Welcome to Aizen AI! 🚀 Your revolutionary AI assistant is ready.', 'Open Settings', 'Start Chat', 'Don\'t show again');
                switch (action) {
                    case 'Open Settings':
                        await vscode.commands.executeCommand('aizen.showSettings');
                        break;
                    case 'Start Chat':
                        await vscode.commands.executeCommand('aizen.chatView.focus');
                        break;
                }
                await this._context.globalState.update('aizen.hasShownWelcome', true);
            }
        }
    }
    // Command handlers
    async _showSettings() {
        AizenSettingsProvider_1.AizenSettingsProvider.createOrShow(this._context.extensionUri, this._context, this._mcpHub);
    }
    async _resetSettings() {
        const confirm = await vscode.window.showWarningMessage('Are you sure you want to reset all Aizen AI settings to defaults?', { modal: true }, 'Reset Settings');
        if (confirm === 'Reset Settings') {
            await this._settingsService.resetSettings();
        }
    }
    async _newChat() {
        if (this._chatProvider) {
            // TODO: Implement newChat method
            // await this._chatProvider.newChat();
        }
        await vscode.commands.executeCommand('aizen.chatView.focus');
    }
    async _clearChat() {
        if (this._chatProvider) {
            // TODO: Implement clearChat method
            // await this._chatProvider.clearChat();
        }
    }
    async _testConnection() {
        try {
            // TODO: Implement testConnection method
            // const result = await this._integrationService.testConnection();
            const result = { success: true, error: '' };
            if (result.success) {
                vscode.window.showInformationMessage('✅ Connection test successful!');
            }
            else {
                vscode.window.showErrorMessage(`❌ Connection test failed: ${result.error}`);
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`❌ Connection test error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async _refreshAgents() {
        // TODO: Implement refreshAgents method
        // await this._integrationService.refreshAgents();
        vscode.window.showInformationMessage('🔄 Agents refreshed!');
        vscode.window.showInformationMessage('🔄 Agents refreshed');
    }
    async _showLogs() {
        // Implementation for showing logs
        vscode.window.showInformationMessage('📋 Logs feature coming soon!');
    }
    async _exportSettings() {
        // Implementation for exporting settings
        vscode.window.showInformationMessage('📤 Export settings feature coming soon!');
    }
    async _importSettings() {
        // Implementation for importing settings
        vscode.window.showInformationMessage('📥 Import settings feature coming soon!');
    }
    // Event handlers
    _onSettingsChanged(settings) {
        // Handle settings changes
        console.log('Settings changed, updating components...');
    }
    _onWorkspaceChanged(event) {
        // Handle workspace changes
        console.log('Workspace changed, updating context...');
    }
    _onConfigurationChanged(event) {
        // Handle configuration changes
        console.log('Configuration changed, reloading...');
    }
}
exports.AizenExtensionManager = AizenExtensionManager;
//# sourceMappingURL=AizenExtensionManager.js.map