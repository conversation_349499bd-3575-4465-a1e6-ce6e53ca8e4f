{"version": 3, "file": "AizenExtensionManager.js", "sourceRoot": "", "sources": ["../../src/services/AizenExtensionManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,8EAA2E;AAC3E,8EAA2E;AAC3E,iEAA8D;AAC9D,uEAAoE;AAGpE,MAAa,qBAAqB;IAW9B,YAAoB,OAAgC;QAH5C,iBAAY,GAAwB,EAAE,CAAC;QACvC,iBAAY,GAAG,KAAK,CAAC;QAGzB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,2CAAoB,CAAC,WAAW,EAAE,CAAC;QAC3D,IAAI,CAAC,mBAAmB,GAAG,IAAI,iDAAuB,EAAE,CAAC;IAC7D,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,OAAiC;QACvD,IAAI,CAAC,qBAAqB,CAAC,SAAS,IAAI,OAAO,EAAE,CAAC;YAC9C,qBAAqB,CAAC,SAAS,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,qBAAqB,CAAC,SAAS,CAAC;IAC3C,CAAC;IAEM,SAAS,CAAC,MAAc;QAC3B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YAEnD,sBAAsB;YACtB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEjC,qBAAqB;YACrB,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,oBAAoB;YACpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,wBAAwB;YACxB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,wBAAwB;YACxB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAE3D,qCAAqC;YACrC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAErC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACzH,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACnB,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAErD,IAAI,CAAC;YACD,0BAA0B;YAC1B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBACnC,IAAI,CAAC;oBACD,UAAU,CAAC,OAAO,EAAE,CAAC;gBACzB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBACtD,CAAC;YACL,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YAEvB,mBAAmB;YACnB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;YAC7C,CAAC;YAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAEjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,qBAAqB;QACxB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAE3C,oBAAoB;QACpB,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;QAC5D,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACtB,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;YAEtE,wBAAwB;YACxB,KAAK,MAAM,OAAO,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,aAAa,OAAO,EAAE,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;QAED,iCAAiC;QACjC,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC;QAE5C,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IAC1C,CAAC;IAEO,kBAAkB;QACtB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAE3C,8BAA8B;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,6CAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACrG,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CACpE,gBAAgB,EAChB,IAAI,CAAC,aAAa,EAClB;YACI,cAAc,EAAE;gBACZ,uBAAuB,EAAE,IAAI;aAChC;SACJ,CACJ,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAE/C,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IAC1C,CAAC;IAEO,iBAAiB;QACrB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAE1C,MAAM,QAAQ,GAAG;YACb,oBAAoB;YACpB;gBACI,OAAO,EAAE,oBAAoB;gBAC7B,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE;aACtC;YACD;gBACI,OAAO,EAAE,qBAAqB;gBAC9B,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE;aACvC;YAED,gBAAgB;YAChB;gBACI,OAAO,EAAE,eAAe;gBACxB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE;aACjC;YACD;gBACI,OAAO,EAAE,iBAAiB;gBAC1B,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE;aACnC;YAED,uBAAuB;YACvB;gBACI,OAAO,EAAE,sBAAsB;gBAC/B,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE;aACxC;YACD;gBACI,OAAO,EAAE,qBAAqB;gBAC9B,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE;aACvC;YAED,uBAAuB;YACvB;gBACI,OAAO,EAAE,gBAAgB;gBACzB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE;aAClC;YACD;gBACI,OAAO,EAAE,sBAAsB;gBAC/B,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE;aACxC;YACD;gBACI,OAAO,EAAE,sBAAsB;gBAC/B,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE;aACxC;SACJ,CAAC;QAEF,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;YACtC,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACrE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,MAAM,sBAAsB,CAAC,CAAC;IAC5D,CAAC;IAEO,oBAAoB;QACxB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAEhD,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE;YAC1E,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEzC,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,KAAK,CAAC,EAAE;YAC3E,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACpD,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAE1C,mCAAmC;QACnC,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE;YACrE,IAAI,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;gBAC9C,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAC3C,CAAC;IAEO,oBAAoB;QACxB,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC9F,aAAa,CAAC,IAAI,GAAG,mBAAmB,CAAC;QACzC,aAAa,CAAC,OAAO,GAAG,+BAA+B,CAAC;QACxD,aAAa,CAAC,OAAO,GAAG,sBAAsB,CAAC;QAC/C,aAAa,CAAC,IAAI,EAAE,CAAC;QAErB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC5C,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;QACrD,IAAI,QAAQ,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YAClC,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAEnF,IAAI,WAAW,EAAE,CAAC;gBACd,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACrD,mEAAmE,EACnE,eAAe,EACf,YAAY,EACZ,mBAAmB,CACtB,CAAC;gBAEF,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,eAAe;wBAChB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;wBAC3D,MAAM;oBACV,KAAK,YAAY;wBACb,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;wBAC7D,MAAM;gBACd,CAAC;gBAED,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;YAC1E,CAAC;QACL,CAAC;IACL,CAAC;IAED,mBAAmB;IACX,KAAK,CAAC,aAAa;QACvB,6CAAqB,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAChG,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAClD,mEAAmE,EACnE,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,gBAAgB,CACnB,CAAC;QAEF,IAAI,OAAO,KAAK,gBAAgB,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAChD,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,QAAQ;QAClB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,iCAAiC;YACjC,sCAAsC;QAC1C,CAAC;QACD,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;IACjE,CAAC;IAEO,KAAK,CAAC,UAAU;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,mCAAmC;YACnC,wCAAwC;QAC5C,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,eAAe;QACzB,IAAI,CAAC;YACD,wCAAwC;YACxC,kEAAkE;YAClE,MAAM,MAAM,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YAC5C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;YAC1E,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YAChF,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACzH,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,uCAAuC;QACvC,kDAAkD;QAClD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;QAC7D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;IAChE,CAAC;IAEO,KAAK,CAAC,SAAS;QACnB,kCAAkC;QAClC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,8BAA8B,CAAC,CAAC;IACzE,CAAC;IAEO,KAAK,CAAC,eAAe;QACzB,wCAAwC;QACxC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yCAAyC,CAAC,CAAC;IACpF,CAAC;IAEO,KAAK,CAAC,eAAe;QACzB,wCAAwC;QACxC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yCAAyC,CAAC,CAAC;IACpF,CAAC;IAED,iBAAiB;IACT,kBAAkB,CAAC,QAAa;QACpC,0BAA0B;QAC1B,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAC5D,CAAC;IAEO,mBAAmB,CAAC,KAAyC;QACjE,2BAA2B;QAC3B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAC1D,CAAC;IAEO,uBAAuB,CAAC,KAAsC;QAClE,+BAA+B;QAC/B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACvD,CAAC;CACJ;AA9WD,sDA8WC"}