"use strict";
/**
 * Aizen Integration Service
 * Bridges Rust backend, Python AI backend, and React frontend
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AizenIntegrationService = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const child_process = __importStar(require("child_process"));
class AizenIntegrationService {
    // private websocket: WebSocket | null = null; // Temporarily disabled
    constructor() {
        this.pythonProcess = null;
        this.rustProcess = null;
        this.isConnected = false;
        this.pythonBaseUrl = 'http://localhost:8000';
        this.rustBaseUrl = 'http://localhost:8001';
        const extensionPath = vscode.extensions.getExtension('aizen-ai.aizen-revolutionary-ai')?.extensionPath ||
            path.join(__dirname, '..', '..');
        this.pythonBackendPath = path.join(extensionPath, 'python_backend_ai');
        this.rustBackendPath = path.join(extensionPath, 'target', 'release');
    }
    async initialize() {
        try {
            console.log('🚀 Initializing Aizen Integration Service...');
            // For now, initialize in mock mode for testing
            // Backend integration will be added later
            console.log('⚠️ Running in mock mode - backend integration pending');
            this.isConnected = true;
            console.log('✅ Aizen Integration Service initialized successfully (mock mode)');
        }
        catch (error) {
            console.error('❌ Failed to initialize Aizen Integration Service:', error);
            // Don't throw error in mock mode
            this.isConnected = true;
            console.log('✅ Aizen Integration Service initialized in fallback mode');
        }
    }
    async startPythonBackend() {
        try {
            console.log('🐍 Starting Python AI Backend...');
            // Check if already running
            const isRunning = await this.checkPythonBackendHealth();
            if (isRunning) {
                console.log('✅ Python backend already running');
                return;
            }
            this.pythonProcess = child_process.spawn('python', [
                path.join(this.pythonBackendPath, 'start_server.py'),
                '--host', '127.0.0.1',
                '--port', '8000',
                '--log-level', 'INFO'
            ], {
                cwd: this.pythonBackendPath,
                stdio: 'pipe'
            });
            this.pythonProcess.stdout?.on('data', (data) => {
                console.log(`Python Backend: ${data}`);
            });
            this.pythonProcess.stderr?.on('data', (data) => {
                console.error(`Python Backend Error: ${data}`);
            });
            this.pythonProcess.on('error', (error) => {
                console.error('Python Backend Process Error:', error);
            });
            // Wait for server to start
            await this.waitForPythonServerStart();
            console.log('✅ Python AI Backend started successfully');
        }
        catch (error) {
            throw new Error(`Failed to start Python backend: ${error}`);
        }
    }
    async startRustBackend() {
        try {
            console.log('🦀 Starting Rust Backend...');
            // Check if Rust binary exists
            const rustBinaryPath = path.join(this.rustBackendPath, 'aizen_ai_extension.exe');
            // For now, we'll skip Rust backend if not built
            console.log('⚠️ Rust backend not yet integrated - using Python backend only');
        }
        catch (error) {
            console.log('⚠️ Rust backend unavailable, continuing with Python backend only');
        }
    }
    async connectWebSocket() {
        try {
            // For now, we'll use HTTP polling instead of WebSocket
            // WebSocket integration can be added later
            console.log('📡 Using HTTP polling for real-time updates');
        }
        catch (error) {
            console.log('⚠️ WebSocket connection failed, using HTTP polling');
        }
    }
    async waitForPythonServerStart() {
        // Mock implementation for UI testing
        console.log('⚠️ Mock mode: Skipping Python server health check');
        return Promise.resolve();
    }
    async checkPythonBackendHealth() {
        // Mock implementation for UI testing
        console.log('⚠️ Mock mode: Python backend health check always returns true');
        return true;
    }
    // Agent Management Methods
    async createAgent(agentType, config = {}) {
        // Return mock agent creation for testing
        console.log(`Creating ${agentType} agent with config:`, config);
        return {
            id: `agent-${Date.now()}`,
            name: config.name || `${agentType}-agent-${Date.now()}`,
            type: agentType,
            status: 'idle',
            created: new Date().toISOString()
        };
    }
    async getAllAgents() {
        // Return mock data for testing
        return [
            {
                id: 'agent-1',
                name: 'Code Assistant',
                type: 'code-generation',
                status: 'idle',
                currentTask: undefined,
                capabilities: ['code-generation', 'debugging', 'optimization']
            },
            {
                id: 'agent-2',
                name: 'Chat Assistant',
                type: 'conversational',
                status: 'busy',
                currentTask: 'Processing user query',
                capabilities: ['chat', 'explanation', 'help']
            }
        ];
    }
    async getMetrics() {
        // Return mock metrics for testing
        return {
            totalAgents: 2,
            activeAgents: 1,
            completedTasks: 15,
            failedTasks: 1,
            averageExecutionTime: 2.5,
            swarmEfficiency: 0.85,
            evolutionScore: 0.92
        };
    }
    async executeTask(description, taskType = 'code-generation', context = {}) {
        // Return mock task execution for testing
        console.log(`Executing ${taskType} task: ${description}`, context);
        return {
            taskId: `task-${Date.now()}`,
            description,
            taskType,
            status: 'started',
            created: new Date().toISOString()
        };
    }
    // Framework Integration Methods
    async enableSwarmIntelligence() {
        console.log('🐝 Swarm Intelligence enabled (mock mode)');
    }
    async enableEvolution() {
        console.log('🧠 Evolution enabled (mock mode)');
    }
    async executeWithTaskWeaver(userQuery, context = {}) {
        console.log('🔧 TaskWeaver execution (mock mode):', userQuery, context);
        return {
            result: `TaskWeaver processed: ${userQuery}`,
            status: 'completed',
            timestamp: new Date().toISOString()
        };
    }
    dispose() {
        if (this.pythonProcess) {
            this.pythonProcess.kill();
            this.pythonProcess = null;
        }
        if (this.rustProcess) {
            this.rustProcess.kill();
            this.rustProcess = null;
        }
        // WebSocket cleanup temporarily disabled
        // if (this.websocket) {
        //     this.websocket.close();
        //     this.websocket = null;
        // }
        this.isConnected = false;
        console.log('🔄 Aizen Integration Service disposed');
    }
}
exports.AizenIntegrationService = AizenIntegrationService;
//# sourceMappingURL=AizenIntegrationService.js.map