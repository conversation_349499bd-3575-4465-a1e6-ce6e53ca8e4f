/**
 * Aizen AI Settings Provider
 * Handles settings webview panel creation and management
 */

import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { AizenSettingsService } from '../services/AizenSettingsService';
import { AizenMCPClient } from '../mcp/OfficialMCPProvider';

export class AizenSettingsProvider {
    public static readonly viewType = 'aizenSettings';
    
    private _panel: vscode.WebviewPanel | undefined;
    private readonly _extensionUri: vscode.Uri;
    private readonly _context: vscode.ExtensionContext;
    private readonly _settingsService: AizenSettingsService;
    private _mcpClient: AizenMCPClient | undefined;
    private _disposables: vscode.Disposable[] = [];

    constructor(extensionUri: vscode.Uri, context: vscode.ExtensionContext, mcpClient?: AizenMCPClient) {
        this._extensionUri = extensionUri;
        this._context = context;
        this._settingsService = AizenSettingsService.getInstance();
        this._mcpClient = mcpClient;
    }

    public static createOrShow(extensionUri: vscode.Uri, context: vscode.ExtensionContext, mcpClient?: AizenMCPClient) {
        // If we already have a panel, show it
        if (AizenSettingsProvider._currentPanel) {
            AizenSettingsProvider._currentPanel._panel?.reveal(vscode.ViewColumn.Two);
            return;
        }

        // Get current VS Code theme for icon selection
        const currentTheme = vscode.window.activeColorTheme.kind;
        const logoFileName = currentTheme === vscode.ColorThemeKind.Dark ? 'aizen-logo-white.svg' : 'aizen-logo.svg';
        const iconPath = vscode.Uri.file(path.join(extensionUri.fsPath, 'media', 'icons', logoFileName));

        // Otherwise, create a new panel in a separate tab
        const panel = vscode.window.createWebviewPanel(
            AizenSettingsProvider.viewType,
            'Aizen AI Settings',
            vscode.ViewColumn.Two, // Force settings to open in a separate tab
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.file(path.join(extensionUri.fsPath, 'out')),
                    vscode.Uri.file(path.join(extensionUri.fsPath, 'src')),
                    vscode.Uri.file(path.join(extensionUri.fsPath, 'media'))
                ]
            }
        );

        // Set the icon for the webview panel tab
        panel.iconPath = iconPath;

        AizenSettingsProvider._currentPanel = new AizenSettingsProvider(extensionUri, context, mcpClient);
        AizenSettingsProvider._currentPanel._panel = panel;
        AizenSettingsProvider._currentPanel._update();
        AizenSettingsProvider._currentPanel._setupEventListeners();
    }

    private static _currentPanel: AizenSettingsProvider | undefined;

    public dispose() {
        AizenSettingsProvider._currentPanel = undefined;

        // Clean up our resources
        this._panel?.dispose();

        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }

    private _setupEventListeners() {
        if (!this._panel) return;

        // Handle panel disposal
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        // Handle messages from the webview
        this._panel.webview.onDidReceiveMessage(
            async (message) => {
                await this._handleMessage(message);
            },
            null,
            this._disposables
        );
    }

    private async _handleMessage(message: any) {
        switch (message.type) {
            case 'getSettings':
                // Send current VS Code settings to webview
                const settings = this._settingsService.getSettings();
                this._panel?.webview.postMessage({
                    type: 'settingsData',
                    settings: settings
                });
                break;
            
            case 'saveSettings':
                // Legacy support - convert old format to new
                await this._saveSettingsLegacy(message.settings);
                break;

            case 'updateSettings':
                // Update multiple settings using VS Code configuration API
                await this._settingsService.updateSettings(message.updates);

                // Validate settings and send feedback
                const validation = this._settingsService.validateSettings();
                this._panel?.webview.postMessage({
                    type: 'settingsValidation',
                    validation: validation
                });
                break;
            
            case 'resetSettings':
                // Reset all settings to defaults
                await this._settingsService.resetSettings();
                const resetSettings = this._settingsService.getSettings();
                this._panel?.webview.postMessage({
                    type: 'settingsData',
                    settings: resetSettings
                });
                break;
            
            case 'closeSettings':
                this._panel?.dispose();
                break;

            case 'openVSCodeSettings':
                // Open VS Code settings to specific Aizen setting
                await this._settingsService.openVSCodeSettings(message.settingId);
                break;

            case 'getMcpStatus':
                // Get MCP server status
                if (this._mcpClient) {
                    const servers = this._mcpClient.getConnectedServers();
                    const tools = this._mcpClient.getAvailableTools();
                    const exaConnected = servers.some(s => s.id === 'exa-ai');
                    const firecrawlConnected = servers.some(s => s.id === 'firecrawl');

                    const status = {
                        exa: {
                            configured: true, // Always configured with built-in API key
                            status: exaConnected ? 'connected' : 'connecting',
                            tools: tools.filter(t => t.serverId === 'exa-ai').length,
                            builtin: true // Mark as built-in server
                        },
                        firecrawl: {
                            configured: true, // Always configured with built-in API key
                            status: firecrawlConnected ? 'connected' : 'connecting',
                            tools: tools.filter(t => t.serverId === 'firecrawl').length,
                            builtin: true // Mark as built-in server
                        }
                    };

                    console.log('📊 Sending MCP status to UI:', status);
                    this._panel?.webview.postMessage({
                        type: 'mcpStatus',
                        data: status
                    });
                } else {
                    // MCP Client not available
                    console.log('❌ MCP Client not available');
                    this._panel?.webview.postMessage({
                        type: 'mcpStatus',
                        data: {
                            exa: { configured: false, status: 'unavailable', tools: 0, builtin: true },
                            firecrawl: { configured: false, status: 'unavailable', tools: 0, builtin: true }
                        }
                    });
                }
                break;

            case 'configureBuiltinServer':
                // Handle attempts to configure built-in servers
                const serverType = message.serverType;
                if (serverType === 'exa' || serverType === 'firecrawl') {
                    // Show message that server is already configured
                    vscode.window.showInformationMessage(
                        `✅ ${serverType === 'exa' ? 'Exa AI Search' : 'Firecrawl'} is already configured with Aizen AI's API key. No additional setup needed!`
                    );

                    // Refresh status to show current state
                    if (this._mcpClient) {
                        const servers = this._mcpClient.getConnectedServers();
                        const tools = this._mcpClient.getAvailableTools();
                        const exaConnected = servers.some(s => s.id === 'exa-ai');
                        const firecrawlConnected = servers.some(s => s.id === 'firecrawl');

                        const status = {
                            exa: {
                                configured: true,
                                status: exaConnected ? 'connected' : 'connecting',
                                tools: tools.filter(t => t.serverId === 'exa-ai').length,
                                builtin: true
                            },
                            firecrawl: {
                                configured: true,
                                status: firecrawlConnected ? 'connected' : 'connecting',
                                tools: tools.filter(t => t.serverId === 'firecrawl').length,
                                builtin: true
                            }
                        };

                        this._panel?.webview.postMessage({
                            type: 'mcpStatus',
                            data: status
                        });
                    }
                }
                break;

            case 'addExternalMcpServer':
                // Handle adding external MCP servers
                if (this._mcpClient) {
                    await this._mcpClient.addExternalMCPServer();
                    // Refresh status after adding
                    setTimeout(() => {
                        this._panel?.webview.postMessage({
                            type: 'refreshMcpStatus'
                        });
                    }, 1000);
                }
                break;

            case 'command':
                // Execute VS Code command
                console.log('🎯 Settings webview executing command:', message.command);
                if (message.command) {
                    try {
                        await vscode.commands.executeCommand(message.command);
                        console.log('✅ Command executed successfully:', message.command);
                    } catch (error) {
                        console.error('❌ Command execution failed:', message.command, error);
                        vscode.window.showErrorMessage(`Command failed: ${message.command}`);
                    }
                }
                break;

            default:
                console.log('Unknown settings message:', message.type);
        }
    }

    private async _saveSettingsLegacy(settings: any): Promise<void> {
        // Convert legacy settings format to VS Code configuration format
        const settingsUpdates: Record<string, any> = {};

        if (settings.aiModel) {
            settingsUpdates['model.provider'] = settings.aiModel.provider;
            settingsUpdates['model.name'] = settings.aiModel.name;
            settingsUpdates['model.temperature'] = settings.aiModel.temperature;
            settingsUpdates['model.maxTokens'] = settings.aiModel.maxTokens;
        }

        if (settings.theme) {
            settingsUpdates['ui.theme'] = settings.theme.mode;
        }

        if (settings.advanced) {
            settingsUpdates['features.autoSave'] = settings.advanced.autoSave;
            settingsUpdates['features.notifications'] = settings.advanced.debugMode;
        }

        // Update settings using the service
        await this._settingsService.updateSettings(settingsUpdates);

        // Send validation feedback
        const validation = this._settingsService.validateSettings();
        this._panel?.webview.postMessage({
            type: 'settingsValidation',
            validation: validation
        });
    }

    private async _sendSettings() {
        if (!this._panel) return;

        // Get current settings from VS Code configuration
        const config = vscode.workspace.getConfiguration('aizenAI');
        const globalState = this._context.globalState;

        const settings = {
            aiModel: {
                provider: config.get('aiModel.provider', 'openai'),
                name: config.get('aiModel.name', 'gpt-4'),
                temperature: config.get('aiModel.temperature', 0.7),
                maxTokens: config.get('aiModel.maxTokens', 4096)
            },
            mcpServers: globalState.get('mcpServers', []),
            theme: {
                mode: config.get('theme.mode', 'auto'),
                accentColor: config.get('theme.accentColor', '#3b82f6'),
                glassIntensity: config.get('theme.glassIntensity', 80)
            },
            advanced: {
                debugMode: config.get('advanced.debugMode', false),
                autoSave: config.get('advanced.autoSave', true),
                streamingMode: config.get('advanced.streamingMode', true)
            },
            extension: {
                defaultMode: config.get('extension.defaultMode', 'auto'),
                shortcutKey: config.get('extension.shortcutKey', 'Ctrl+Shift+A'),
                startupOpen: config.get('extension.startupOpen', false)
            }
        };

        await this._panel.webview.postMessage({
            type: 'settingsData',
            settings: settings
        });
    }

    private async _saveSettings(settings: any) {
        if (!this._panel) return;

        try {
            const config = vscode.workspace.getConfiguration('aizenAI');
            const globalState = this._context.globalState;

            // Save AI model settings
            if (settings.aiModel) {
                await config.update('aiModel.provider', settings.aiModel.provider, vscode.ConfigurationTarget.Global);
                await config.update('aiModel.name', settings.aiModel.name, vscode.ConfigurationTarget.Global);
                await config.update('aiModel.temperature', settings.aiModel.temperature, vscode.ConfigurationTarget.Global);
                await config.update('aiModel.maxTokens', settings.aiModel.maxTokens, vscode.ConfigurationTarget.Global);
            }

            // Save MCP servers to global state
            if (settings.mcpServers) {
                await globalState.update('mcpServers', settings.mcpServers);
            }

            // Save theme settings
            if (settings.theme) {
                await config.update('theme.mode', settings.theme.mode, vscode.ConfigurationTarget.Global);
                await config.update('theme.accentColor', settings.theme.accentColor, vscode.ConfigurationTarget.Global);
                await config.update('theme.glassIntensity', settings.theme.glassIntensity, vscode.ConfigurationTarget.Global);
            }

            // Save advanced settings
            if (settings.advanced) {
                await config.update('advanced.debugMode', settings.advanced.debugMode, vscode.ConfigurationTarget.Global);
                await config.update('advanced.autoSave', settings.advanced.autoSave, vscode.ConfigurationTarget.Global);
                await config.update('advanced.streamingMode', settings.advanced.streamingMode, vscode.ConfigurationTarget.Global);
            }

            // Save extension preferences
            if (settings.extension) {
                await config.update('extension.defaultMode', settings.extension.defaultMode, vscode.ConfigurationTarget.Global);
                await config.update('extension.shortcutKey', settings.extension.shortcutKey, vscode.ConfigurationTarget.Global);
                await config.update('extension.startupOpen', settings.extension.startupOpen, vscode.ConfigurationTarget.Global);
            }

            // Notify webview that settings were saved
            await this._panel.webview.postMessage({
                type: 'settingsSaved'
            });

            vscode.window.showInformationMessage('Aizen AI settings saved successfully!');

        } catch (error) {
            console.error('Failed to save settings:', error);
            vscode.window.showErrorMessage(`Failed to save settings: ${error}`);
        }
    }

    private async _resetSettings() {
        if (!this._panel) return;

        try {
            const config = vscode.workspace.getConfiguration('aizenAI');
            const globalState = this._context.globalState;

            // Reset all settings to defaults
            await config.update('aiModel.provider', undefined, vscode.ConfigurationTarget.Global);
            await config.update('aiModel.name', undefined, vscode.ConfigurationTarget.Global);
            await config.update('aiModel.temperature', undefined, vscode.ConfigurationTarget.Global);
            await config.update('aiModel.maxTokens', undefined, vscode.ConfigurationTarget.Global);

            await globalState.update('mcpServers', []);

            await config.update('theme.mode', undefined, vscode.ConfigurationTarget.Global);
            await config.update('theme.accentColor', undefined, vscode.ConfigurationTarget.Global);
            await config.update('theme.glassIntensity', undefined, vscode.ConfigurationTarget.Global);

            await config.update('advanced.debugMode', undefined, vscode.ConfigurationTarget.Global);
            await config.update('advanced.autoSave', undefined, vscode.ConfigurationTarget.Global);
            await config.update('advanced.streamingMode', undefined, vscode.ConfigurationTarget.Global);

            await config.update('extension.defaultMode', undefined, vscode.ConfigurationTarget.Global);
            await config.update('extension.shortcutKey', undefined, vscode.ConfigurationTarget.Global);
            await config.update('extension.startupOpen', undefined, vscode.ConfigurationTarget.Global);

            // Notify webview that settings were reset
            await this._panel.webview.postMessage({
                type: 'settingsReset'
            });

            vscode.window.showInformationMessage('Aizen AI settings reset to defaults!');

        } catch (error) {
            console.error('Failed to reset settings:', error);
            vscode.window.showErrorMessage(`Failed to reset settings: ${error}`);
        }
    }

    private _update() {
        if (!this._panel) return;

        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        // Get file URIs with cache busting
        const timestamp = Date.now();
        const settingsScriptPathOnDisk = vscode.Uri.file(path.join(this._extensionUri.fsPath, 'out', 'ui', 'settings.js'));
        const settingsScriptUri = webview.asWebviewUri(settingsScriptPathOnDisk);

        const stylesPathOnDisk = vscode.Uri.file(path.join(this._extensionUri.fsPath, 'out', 'ui', 'styles.css'));
        const stylesUri = webview.asWebviewUri(stylesPathOnDisk);

        const settingsStylesPathOnDisk = vscode.Uri.file(path.join(this._extensionUri.fsPath, 'out', 'ui', 'settings.css'));
        const settingsStylesUri = webview.asWebviewUri(settingsStylesPathOnDisk);

        const settingsHtmlPathOnDisk = vscode.Uri.file(path.join(this._extensionUri.fsPath, 'out', 'ui', 'settings.html'));

        const nonce = this._getNonce();

        // Get current VS Code theme
        const currentTheme = vscode.window.activeColorTheme.kind;
        const themeClass = currentTheme === vscode.ColorThemeKind.Dark ? 'dark-theme' : 'light-theme';

        // Read HTML template
        try {
            console.log('🔍 Reading Settings HTML template from:', settingsHtmlPathOnDisk.fsPath);
            console.log('🔍 File exists:', fs.existsSync(settingsHtmlPathOnDisk.fsPath));

            const htmlContent = fs.readFileSync(settingsHtmlPathOnDisk.fsPath, 'utf8');
            console.log('✅ Settings HTML template loaded successfully');

            // Replace placeholders with cache busting
            const processedHtml = htmlContent
                .replace(/{{CSS_URI}}/g, `${stylesUri.toString()}?t=${timestamp}`)
                .replace(/{{SETTINGS_CSS_URI}}/g, `${settingsStylesUri.toString()}?t=${timestamp}`)
                .replace(/{{SETTINGS_SCRIPT_URI}}/g, `${settingsScriptUri.toString()}?t=${timestamp}`)
                .replace(/{{THEME_CLASS}}/g, themeClass)
                .replace(/{{NONCE}}/g, nonce)
                .replace(/{{CSP_SOURCE}}/g, webview.cspSource);

            console.log('✅ Settings HTML placeholders replaced');
            return processedHtml;

        } catch (error) {
            console.error('❌ Failed to load settings HTML template:', error);
            return this._getFallbackSettingsHtml(webview, settingsScriptUri, stylesUri, settingsStylesUri, themeClass, nonce);
        }
    }

    private _getFallbackSettingsHtml(
        webview: vscode.Webview,
        settingsScriptUri: vscode.Uri,
        stylesUri: vscode.Uri,
        settingsStylesUri: vscode.Uri,
        themeClass: string,
        nonce: string
    ): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline' https://fonts.googleapis.com; script-src 'nonce-${nonce}'; font-src ${webview.cspSource} data: https://fonts.gstatic.com; img-src ${webview.cspSource} data: https:;">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Aizen AI Settings</title>
            <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
            <link rel="stylesheet" href="${stylesUri}">
            <link rel="stylesheet" href="${settingsStylesUri}">
        </head>
        <body class="settings-body ${themeClass}">
            <div class="settings-container">
                <header class="settings-header glass">
                    <h1>Aizen AI Settings</h1>
                    <p>Settings page is loading...</p>
                </header>
                <main class="settings-main">
                    <div class="loading-message">
                        <p>Loading settings interface...</p>
                    </div>
                </main>
            </div>
            <script nonce="${nonce}" src="${settingsScriptUri}"></script>
        </body>
        </html>`;
    }

    private _getNonce(): string {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
}
