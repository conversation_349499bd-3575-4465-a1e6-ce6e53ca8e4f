use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::codebase::CodebaseIndexer;
use crate::memory::{SharedM<PERSON>ory, AgentMemoryManager};
use crate::indexing::CodeIndexer;

// Advanced context engine modules based on 2025 research
pub mod advanced_context;
pub mod contextual_reasoning;
pub mod adaptive_context;
pub mod multi_modal_context;

use std::path::Path;
use fastembed::{EmbeddingModel, InitOptions, TextEmbedding};
use qdrant_client::prelude::*;
use qdrant_client::qdrant::{CreateCollectionBuilder, VectorParamsBuilder};
use tree_sitter::{Language, Parser, Query, QueryCursor};
use walkdir::WalkDir;
use ignore::Walk;
use git2::Repository;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeChunk {
    pub id: String,
    pub file_path: String,
    pub content: String,
    pub start_line: usize,
    pub end_line: usize,
    pub chunk_type: String,
    pub language: String,
    pub dependencies: Vec<String>,
    pub exports: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextResult {
    pub chunks: Vec<CodeChunk>,
    pub similarity_score: f32,
    pub file_path: String,
}

pub struct ContextEngine {
    embedding_model: TextEmbedding,
    qdrant_client: QdrantClient,
    parsers: HashMap<String, Parser>,
    chunk_cache: HashMap<String, Vec<CodeChunk>>,
}

impl ContextEngine {
    pub fn new() -> Self {
        let model = TextEmbedding::try_new(InitOptions {
            model_name: fastembed::EmbeddingModel::AllMiniLML6V2,
            show_download_progress: true,
            ..Default::default()
        }).expect("Failed to initialize embedding model");

        let client = QdrantClient::from_url("http://localhost:6333")
            .build()
            .expect("Failed to connect to Qdrant");

        let mut parsers = HashMap::new();

        // Initialize parsers for different languages
        let mut ts_parser = Parser::new();
        ts_parser.set_language(tree_sitter_typescript::language()).unwrap();
        parsers.insert("typescript".to_string(), ts_parser);

        let mut js_parser = Parser::new();
        js_parser.set_language(tree_sitter_javascript::language()).unwrap();
        parsers.insert("javascript".to_string(), js_parser);

        let mut rust_parser = Parser::new();
        rust_parser.set_language(tree_sitter_rust::language()).unwrap();
        parsers.insert("rust".to_string(), rust_parser);

        let mut py_parser = Parser::new();
        py_parser.set_language(tree_sitter_python::language()).unwrap();
        parsers.insert("python".to_string(), py_parser);

        Self {
            embedding_model: model,
            qdrant_client: client,
            parsers,
            chunk_cache: HashMap::new(),
        }
    }

    pub async fn index_codebase(&self, workspace_path: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Create collection for this workspace
        let collection_name = format!("workspace_{}", 
            workspace_path.replace("/", "_").replace("\\", "_"));

        self.qdrant_client
            .create_collection(
                CreateCollectionBuilder::new(&collection_name)
                    .vectors_config(VectorParamsBuilder::new(384, Distance::Cosine))
            )
            .await?;

        // Get all relevant files using gitignore rules
        let mut files = Vec::new();
        for entry in Walk::new(workspace_path) {
            let entry = entry?;
            if entry.file_type().map_or(false, |ft| ft.is_file()) {
                if let Some(path_str) = entry.path().to_str() {
                    if self.is_code_file(path_str) {
                        files.push(path_str.to_string());
                    }
                }
            }
        }

        // Process files in chunks for large repositories
        let chunk_size = 50;
        for file_chunk in files.chunks(chunk_size) {
            let mut points = Vec::new();

            for file_path in file_chunk {
                let chunks = self.extract_semantic_chunks(file_path)?;

                for chunk in chunks {
                    let embedding = self.embedding_model
                        .embed(vec![chunk.content.clone()], None)?
                        .into_iter()
                        .next()
                        .unwrap();

                    let point = PointStruct::new(
                        chunk.id.clone(),
                        embedding,
                        serde_json::to_value(&chunk)?,
                    );
                    points.push(point);
                }
            }

            if !points.is_empty() {
                self.qdrant_client
                    .upsert_points(&collection_name, None, points, None)
                    .await?;
            }
        }

        Ok(())
    }

    fn extract_semantic_chunks(&self, file_path: &str) -> Result<Vec<CodeChunk>, Box<dyn std::error::Error + Send + Sync>> {
        let content = std::fs::read_to_string(file_path)?;
        let language = self.detect_language(file_path);

        if let Some(parser) = self.parsers.get(&language) {
            self.extract_chunks_with_tree_sitter(file_path, &content, &language, parser)
        } else {
            // Fallback to simple text chunking
            Ok(self.extract_simple_chunks(file_path, &content, &language))
        }
    }

    fn extract_chunks_with_tree_sitter(
        &self,
        file_path: &str,
        content: &str,
        language: &str,
        parser: &Parser,
    ) -> Result<Vec<CodeChunk>, Box<dyn std::error::Error + Send + Sync>> {
        let tree = parser.parse(content, None).unwrap();
        let mut chunks = Vec::new();

        // Define queries for different semantic units
        let queries = match language.as_str() {
            "typescript" | "javascript" => vec![
                "(function_declaration) @function",
                "(class_declaration) @class",
                "(interface_declaration) @interface",
                "(type_alias_declaration) @type",
                "(export_statement) @export",
            ],
            "rust" => vec![
                "(function_item) @function",
                "(struct_item) @struct",
                "(enum_item) @enum",
                "(impl_item) @impl",
                "(mod_item) @module",
            ],
            "python" => vec![
                "(function_definition) @function",
                "(class_definition) @class",
                "(import_statement) @import",
                "(import_from_statement) @import",
            ],
            _ => vec!["(ERROR) @error"],
        };

        for query_str in queries {
            if let Ok(query) = Query::new(tree.language(), query_str) {
                let mut cursor = QueryCursor::new();
                let matches = cursor.matches(&query, tree.root_node(), content.as_bytes());

                for match_ in matches {
                    for capture in match_.captures {
                        let node = capture.node;
                        let start_line = node.start_position().row + 1;
                        let end_line = node.end_position().row + 1;
                        let node_content = &content[node.byte_range()];

                        let chunk = CodeChunk {
                            id: uuid::Uuid::new_v4().to_string(),
                            file_path: file_path.to_string(),
                            content: node_content.to_string(),
                            start_line,
                            end_line,
                            chunk_type: query_str.split('@').last().unwrap_or("unknown").to_string(),
                            language: language.to_string(),
                            dependencies: self.extract_dependencies(node_content, language),
                            exports: self.extract_exports(node_content, language),
                        };

                        chunks.push(chunk);
                    }
                }
            }
        }

        // Add file-level chunk for overall context
        chunks.push(CodeChunk {
            id: uuid::Uuid::new_v4().to_string(),
            file_path: file_path.to_string(),
            content: content.to_string(),
            start_line: 1,
            end_line: content.lines().count(),
            chunk_type: "file".to_string(),
            language: language.to_string(),
            dependencies: Vec::new(),
            exports: Vec::new(),
        });

        Ok(chunks)
    }

    fn extract_simple_chunks(&self, file_path: &str, content: &str, language: &str) -> Vec<CodeChunk> {
        let lines: Vec<&str> = content.lines().collect();
        let chunk_size = 100; // lines per chunk
        let mut chunks = Vec::new();

        for (i, chunk_lines) in lines.chunks(chunk_size).enumerate() {
            let chunk_content = chunk_lines.join("\n");
            let start_line = i * chunk_size + 1;
            let end_line = start_line + chunk_lines.len() - 1;

            chunks.push(CodeChunk {
                id: uuid::Uuid::new_v4().to_string(),
                file_path: file_path.to_string(),
                content: chunk_content,
                start_line,
                end_line,
                chunk_type: "text_chunk".to_string(),
                language: language.to_string(),
                dependencies: Vec::new(),
                exports: Vec::new(),
            });
        }

        chunks
    }

    pub async fn get_context_for_task(
        &self,
        task_description: &str,
        files: &[String],
        folders: &[String],
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let mut context_parts = Vec::new();

        // Get relevant code chunks based on task description
        let relevant_chunks = self.query_similar_code(task_description, None).await?;

        for result in relevant_chunks {
            for chunk in result.chunks {
                context_parts.push(format!(
                    "File: {}\nLines: {}-{}\nType: {}\n{}\n",
                    chunk.file_path,
                    chunk.start_line,
                    chunk.end_line,
                    chunk.chunk_type,
                    chunk.content
                ));
            }
        }

        // Add specific files if requested
        for file_path in files {
            if let Ok(content) = std::fs::read_to_string(file_path) {
                context_parts.push(format!("File: {}\n{}\n", file_path, content));
            }
        }

        // Add folder structure if requested
        for folder_path in folders {
            let structure = self.get_folder_structure(folder_path)?;
            context_parts.push(format!("Folder Structure: {}\n{}\n", folder_path, structure));
        }

        Ok(context_parts.join("\n---\n"))
    }

    pub async fn query_similar_code(
        &self,
        query: &str,
        file_filter: Option<Vec<String>>,
    ) -> Result<Vec<ContextResult>, Box<dyn std::error::Error + Send + Sync>> {
        let query_embedding = self.embedding_model
            .embed(vec![query.to_string()], None)?
            .into_iter()
            .next()
            .unwrap();

        let collection_name = "workspace_default"; // TODO: Make this configurable

        let search_result = self.qdrant_client
            .search_points(&SearchPointsBuilder::new(
                collection_name,
                query_embedding,
                10,
            ))
            .await?;

        let mut results = Vec::new();
        for scored_point in search_result.result {
            if let Ok(chunk) = serde_json::from_value::<CodeChunk>(scored_point.payload.unwrap()) {
                // Apply file filter if specified
                if let Some(ref filters) = file_filter {
                    if !filters.iter().any(|f| chunk.file_path.contains(f)) {
                        continue;
                    }
                }

                results.push(ContextResult {
                    chunks: vec![chunk.clone()],
                    similarity_score: scored_point.score,
                    file_path: chunk.file_path,
                });
            }
        }

        Ok(results)
    }

    fn detect_language(&self, file_path: &str) -> String {
        if let Some(extension) = Path::new(file_path).extension() {
            match extension.to_str().unwrap_or("") {
                "ts" | "tsx" => "typescript".to_string(),
                "js" | "jsx" => "javascript".to_string(),
                "rs" => "rust".to_string(),
                "py" => "python".to_string(),
                "go" => "go".to_string(),
                "java" => "java".to_string(),
                "cpp" | "cc" | "cxx" => "cpp".to_string(),
                "c" => "c".to_string(),
                "cs" => "csharp".to_string(),
                _ => "text".to_string(),
            }
        } else {
            "text".to_string()
        }
    }

    fn is_code_file(&self, file_path: &str) -> bool {
        let extensions = [
            "ts", "tsx", "js", "jsx", "rs", "py", "go", "java",
            "cpp", "cc", "cxx", "c", "cs", "php", "rb", "swift",
            "kt", "scala", "clj", "hs", "ml", "elm", "dart",
            "vue", "svelte", "html", "css", "scss", "sass",
            "json", "yaml", "yml", "toml", "xml", "md"
        ];

        if let Some(ext) = Path::new(file_path).extension() {
            extensions.contains(&ext.to_str().unwrap_or(""))
        } else {
            false
        }
    }

    fn extract_dependencies(&self, content: &str, language: &str) -> Vec<String> {
        let mut deps = Vec::new();

        match language {
            "typescript" | "javascript" => {
                for line in content.lines() {
                    if line.trim().starts_with("import") && line.contains("from") {
                        if let Some(module) = line.split("from").nth(1) {
                            deps.push(module.trim().trim_matches('"').trim_matches('\'').to_string());
                        }
                    }
                },
            "rust" => {
                for line in content.lines() {
                    if line.trim().starts_with("use ") {
                        let use_part = line.trim().strip_prefix("use ").unwrap_or("");
                        deps.push(use_part.split("::").next().unwrap_or(use_part).to_string());
                    }
                }
            },
            "python" => {
                for line in content.lines() {
                    if line.trim().starts_with("import ") || line.trim().starts_with("from ") {
                        deps.push(line.trim().to_string());
                    }
                }
            },
            _ => {}
        }

        deps
    }

    fn extract_exports(&self, content: &str, language: &str) -> Vec<String> {
        let mut exports = Vec::new();

        match language {
            "typescript" | "javascript" => {
                for line in content.lines() {
                    if line.trim().starts_with("export") {
                        exports.push(line.trim().to_string());
                    }
                }
            },
            "rust" => {
                for line in content.lines() {
                    if line.trim().starts_with("pub ") {
                        exports.push(line.trim().to_string());
                    }
                }
            },
            _ => {}
        }

        exports
    }

    fn get_folder_structure(&self, folder_path: &str) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let mut structure = Vec::new();

        for entry in WalkDir::new(folder_path).max_depth(3) {
            let entry = entry?;
            let depth = entry.depth();
            let indent = "  ".repeat(depth);
            let name = entry.file_name().to_string_lossy();

            if entry.file_type().is_dir() {
                structure.push(format!("{}📁 {}/", indent, name));
            } else if self.is_code_file(entry.path().to_str().unwrap_or("")) {
                structure.push(format!("{}📄 {}", indent, name));
            }
        }

        Ok(structure.join("\n"))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextStore {
    pub active_contexts: HashMap<String, Context>,
    pub context_history: VecDeque<Context>,
    pub global_context: GlobalContext,
    pub user_preferences: UserPreferences,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Context {
    pub id: String,
    pub session_id: String,
    pub context_type: ContextType,
    pub content: serde_json::Value,
    pub relevance_score: f64,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_accessed: chrono::DateTime<chrono::Utc>,
    pub access_count: u64,
    pub related_files: Vec<String>,
    pub related_agents: Vec<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContextType {
    UserRequest,
    CodebaseAnalysis,
    TaskExecution,
    AgentCommunication,
    ErrorContext,
    LearningContext,
    ProjectContext,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalContext {
    pub workspace_path: String,
    pub project_type: Option<String>,
    pub main_technologies: Vec<String>,
    pub coding_standards: HashMap<String, serde_json::Value>,
    pub active_features: Vec<String>,
    pub recent_changes: Vec<RecentChange>,
    pub performance_metrics: PerformanceMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentChange {
    pub file_path: String,
    pub change_type: ChangeType,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub description: String,
    pub agent_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChangeType {
    FileCreated,
    FileModified,
    FileDeleted,
    DependencyAdded,
    DependencyRemoved,
    ConfigurationChanged,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub total_requests: u64,
    pub average_response_time: f64,
    pub success_rate: f64,
    pub context_hit_rate: f64,
    pub memory_efficiency: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPreferences {
    pub preferred_languages: Vec<String>,
    pub coding_style: CodingStyle,
    pub verbosity_level: VerbosityLevel,
    pub auto_save: bool,
    pub parallel_execution: bool,
    pub max_context_depth: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CodingStyle {
    Minimal,
    Descriptive,
    Enterprise,
    Custom(HashMap<String, serde_json::Value>),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum VerbosityLevel {
    Silent,
    Minimal,
    Normal,
    Verbose,
    Debug,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionPlan {
    pub id: String,
    pub request: String,
    pub tasks: Vec<crate::agents::Task>,
    pub dependencies: HashMap<String, Vec<String>>,
    pub estimated_duration: u64,
    pub priority: crate::agents::TaskPriority,
    pub context: serde_json::Value,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

impl ContextEngine {
    pub async fn new() -> Result<Self, anyhow::Error> {
        let context_store = Arc::new(RwLock::new(ContextStore {
            active_contexts: HashMap::new(),
            context_history: VecDeque::new(),
            global_context: GlobalContext {
                workspace_path: String::new(),
                project_type: None,
                main_technologies: Vec::new(),
                coding_standards: HashMap::new(),
                active_features: Vec::new(),
                recent_changes: Vec::new(),
                performance_metrics: PerformanceMetrics {
                    total_requests: 0,
                    average_response_time: 0.0,
                    success_rate: 0.0,
                    context_hit_rate: 0.0,
                    memory_efficiency: 0.0,
                },
            },
            user_preferences: UserPreferences {
                preferred_languages: vec!["typescript".to_string(), "rust".to_string()],
                coding_style: CodingStyle::Descriptive,
                verbosity_level: VerbosityLevel::Normal,
                auto_save: true,
                parallel_execution: true,
                max_context_depth: 10,
            },
        }));

        let memory = Arc::new(SharedMemory::new().await?);

        Ok(Self {
            context_store,
            codebase_indexer: None,
            memory,
            context_window_size: 100,
            relevance_threshold: 0.7,
        })
    }

    pub async fn initialize(&mut self) -> Result<(), anyhow::Error> {
        // Load saved context if available
        self.load_persisted_context().await?;
        
        // Initialize context cleanup task
        self.start_context_cleanup_task().await;
        
        Ok(())
    }

    pub async fn set_codebase_indexer(&mut self, indexer: Arc<CodebaseIndexer>) {
        self.codebase_indexer = Some(indexer);
    }

    pub async fn analyze_request(&self, request: &str, context: &serde_json::Value) -> Result<ExecutionPlan, anyhow::Error> {
        let session_id = Uuid::new_v4().to_string();
        
        // Store request context
        let request_context = Context {
            id: Uuid::new_v4().to_string(),
            session_id: session_id.clone(),
            context_type: ContextType::UserRequest,
            content: serde_json::json!({
                "request": request,
                "additional_context": context
            }),
            relevance_score: 1.0,
            created_at: chrono::Utc::now(),
            last_accessed: chrono::Utc::now(),
            access_count: 1,
            related_files: Vec::new(),
            related_agents: Vec::new(),
            metadata: HashMap::new(),
        };

        self.store_context(request_context).await?;

        // Analyze request and extract requirements
        let analysis = self.extract_requirements(request, context).await?;
        
        // Generate execution plan
        let plan = self.create_execution_plan(session_id, request, analysis).await?;
        
        // Store plan context
        let plan_context = Context {
            id: Uuid::new_v4().to_string(),
            session_id: plan.id.clone(),
            context_type: ContextType::TaskExecution,
            content: serde_json::to_value(&plan)?,
            relevance_score: 0.9,
            created_at: chrono::Utc::now(),
            last_accessed: chrono::Utc::now(),
            access_count: 0,
            related_files: Vec::new(),
            related_agents: Vec::new(),
            metadata: HashMap::new(),
        };

        self.store_context(plan_context).await?;

        Ok(plan)
    }

    async fn extract_requirements(&self, request: &str, context: &serde_json::Value) -> Result<RequestAnalysis, anyhow::Error> {
        // Extract intent from request
        let intent = self.classify_intent(request).await?;
        
        // Extract technologies mentioned
        let technologies = self.extract_technologies(request).await?;
        
        // Extract file/folder references
        let file_references = self.extract_file_references(request).await?;
        
        // Get relevant codebase context
        let codebase_context = if let Some(indexer) = &self.codebase_indexer {
            if !file_references.is_empty() {
                Some(indexer.get_context_for_files(&file_references).await?)
            } else {
                None
            }
        } else {
            None
        };

        // Get historical context
        let historical_context = self.get_relevant_historical_context(request).await?;

        Ok(RequestAnalysis {
            intent,
            technologies,
            file_references,
            codebase_context,
            historical_context,
            complexity: self.estimate_complexity(request).await?,
            requirements: self.extract_specific_requirements(request).await?,
        })
    }

    async fn classify_intent(&self, request: &str) -> Result<Intent, anyhow::Error> {
        let request_lower = request.to_lowercase();
        
        if request_lower.contains("create") || request_lower.contains("build") || request_lower.contains("generate") {
            Intent::Create
        } else if request_lower.contains("fix") || request_lower.contains("debug") || request_lower.contains("error") {
            Intent::Fix
        } else if request_lower.contains("test") || request_lower.contains("spec") {
            Intent::Test
        } else if request_lower.contains("document") || request_lower.contains("readme") || request_lower.contains("docs") {
            Intent::Document
        } else if request_lower.contains("review") || request_lower.contains("audit") || request_lower.contains("analyze") {
            Intent::Review
        } else if request_lower.contains("refactor") || request_lower.contains("optimize") || request_lower.contains("improve") {
            Intent::Refactor
        } else {
            Intent::General
        }
    }

    async fn extract_technologies(&self, request: &str) -> Result<Vec<String>, anyhow::Error> {
        let technologies = vec![
            "react", "vue", "angular", "svelte", "next", "nuxt", "gatsby",
            "node", "express", "fastify", "koa", "nest",
            "python", "django", "flask", "fastapi",
            "rust", "actix", "warp", "axum",
            "typescript", "javascript", "html", "css", "sass", "tailwind",
            "mongodb", "mysql", "postgresql", "redis", "sqlite",
            "docker", "kubernetes", "aws", "azure", "gcp",
        ];

        let request_lower = request.to_lowercase();
        let found_technologies: Vec<String> = technologies
            .iter()
            .filter(|&tech| request_lower.contains(tech))
            .map(|&tech| tech.to_string())
            .collect();

        Ok(found_technologies)
    }

    async fn extract_file_references(&self, request: &str) -> Result<Vec<String>, anyhow::Error> {
        let mut file_references = Vec::new();
        
        // Simple regex-based extraction - could be enhanced with NLP
        let file_patterns = [
            r"src/[^\s]+",
            r"[^\s]+\.(ts|js|rs|py|json|md|yaml|yml|toml)",
            r"[^\s]+/[^\s]+",
        ];

        for pattern in &file_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                for capture in regex.find_iter(request) {
                    file_references.push(capture.as_str().to_string());
                }
            }
        }

        Ok(file_references)
    }

    async fn get_relevant_historical_context(&self, request: &str) -> Result<Vec<Context>, anyhow::Error> {
        let store = self.context_store.read().await;
        let mut relevant_contexts = Vec::new();

        // Simple keyword matching for now - could be enhanced with semantic similarity
        let request_words: Vec<&str> = request.split_whitespace().collect();

        for context in &store.context_history {
            if let Some(content_str) = context.content.as_str() {
                let content_words: Vec<&str> = content_str.split_whitespace().collect();
                let similarity = self.calculate_similarity(&request_words, &content_words);
                
                if similarity > self.relevance_threshold {
                    relevant_contexts.push(context.clone());
                }
            }
        }

        // Sort by relevance and recency
        relevant_contexts.sort_by(|a, b| {
            b.relevance_score.partial_cmp(&a.relevance_score).unwrap_or(std::cmp::Ordering::Equal)
                .then(b.last_accessed.cmp(&a.last_accessed))
        });

        // Limit to most relevant contexts
        relevant_contexts.truncate(5);

        Ok(relevant_contexts)
    }

    fn calculate_similarity(&self, words1: &[&str], words2: &[&str]) -> f64 {
        let set1: std::collections::HashSet<&str> = words1.iter().cloned().collect();
        let set2: std::collections::HashSet<&str> = words2.iter().cloned().collect();
        
        let intersection = set1.intersection(&set2).count();
        let union = set1.union(&set2).count();
        
        if union == 0 {
            0.0
        } else {
            intersection as f64 / union as f64
        }
    }

    async fn estimate_complexity(&self, request: &str) -> Result<ComplexityLevel, anyhow::Error> {
        let complexity_indicators = [
            ("simple", 1), ("create", 2), ("build", 3), ("implement", 3),
            ("complex", 4), ("advanced", 4), ("enterprise", 5), ("scale", 4),
            ("authentication", 3), ("database", 3), ("api", 2), ("frontend", 2),
            ("backend", 3), ("fullstack", 4), ("microservice", 4), ("deployment", 3),
        ];

        let request_lower = request.to_lowercase();
        let mut total_score = 0;
        let mut matches = 0;
        
        for (indicator, score) in &complexity_indicators {
            if request_lower.contains(indicator) {
                total_score += score;
                matches += 1;
            }
        }

        let average_score = if matches > 0 {
            total_score as f64 / matches as f64
        } else {
            2.0 // Default to medium complexity
        };

        Ok(match average_score {
            x if x <= 1.5 => ComplexityLevel::Low,
            x if x <= 2.5 => ComplexityLevel::Medium,
            x if x <= 3.5 => ComplexityLevel::High,
            _ => ComplexityLevel::VeryHigh,
        })
    }

    async fn extract_specific_requirements(&self, request: &str) -> Result<Vec<String>, anyhow::Error> {
        let mut requirements = Vec::new();
        
        // Extract specific requirements using patterns
        let requirement_patterns = [
            ("authentication", "User authentication system"),
            ("database", "Database integration and modeling"),
            ("api", "REST API development"),
            ("frontend", "User interface development"),
            ("backend", "Server-side logic implementation"),
            ("testing", "Comprehensive testing suite"),
            ("documentation", "Project documentation"),
            ("deployment", "Production deployment setup"),
        ];

        let request_lower = request.to_lowercase();
        for (pattern, requirement) in &requirement_patterns {
            if request_lower.contains(pattern) {
                requirements.push(requirement.to_string());
            }
        }

        if requirements.is_empty() {
            requirements.push("General development task".to_string());
        }

        Ok(requirements)
    }

    async fn create_execution_plan(&self, session_id: String, request: &str, analysis: RequestAnalysis) -> Result<ExecutionPlan, anyhow::Error> {
        let mut tasks = Vec::new();
        let plan_id = Uuid::new_v4().to_string();

        // Generate tasks based on intent and requirements
        match analysis.intent {
            Intent::Create => {
                tasks.extend(self.generate_creation_tasks(&analysis).await?);
            }
            Intent::Fix => {
                tasks.extend(self.generate_fix_tasks(&analysis).await?);
            }
            Intent::Test => {
                tasks.extend(self.generate_test_tasks(&analysis).await?);
            }
            Intent::Document => {
                tasks.extend(self.generate_documentation_tasks(&analysis).await?);
            }
            Intent::Review => {
                tasks.extend(self.generate_review_tasks(&analysis).await?);
            }
            Intent::Refactor => {
                tasks.extend(self.generate_refactor_tasks(&analysis).await?);
            }
            Intent::General => {
                tasks.extend(self.generate_general_tasks(&analysis).await?);
            }
        }

        // Calculate dependencies
        let dependencies = self.calculate_task_dependencies(&tasks).await?;

        // Estimate total duration
        let estimated_duration = tasks.iter()
            .map(|task| task.estimated_duration.unwrap_or(300000)) // 5 minutes default
            .sum();

        // Determine priority
        let priority = match analysis.complexity {
            ComplexityLevel::Low => crate::agents::TaskPriority::Low,
            ComplexityLevel::Medium => crate::agents::TaskPriority::Medium,
            ComplexityLevel::High => crate::agents::TaskPriority::High,
            ComplexityLevel::VeryHigh => crate::agents::TaskPriority::Critical,
        };

        Ok(ExecutionPlan {
            id: plan_id,
            request: request.to_string(),
            tasks,
            dependencies,
            estimated_duration,
            priority,
            context: serde_json::to_value(&analysis)?,
            created_at: chrono::Utc::now(),
        })
    }

    async fn generate_creation_tasks(&self, analysis: &RequestAnalysis) -> Result<Vec<crate::agents::Task>, anyhow::Error> {
        let mut tasks = Vec::new();

        // Project structure task
        tasks.push(crate::agents::Task {
            id: Uuid::new_v4().to_string(),
            description: "Create project structure and configuration".to_string(),
            agent_type: crate::agents::AgentType::CodeGeneration,
            priority: crate::agents::TaskPriority::High,
            context: serde_json::json!({
                "technologies": analysis.technologies,
                "requirements": analysis.requirements
            }),
            dependencies: Vec::new(),
            files: analysis.file_references.clone(),
            created_at: chrono::Utc::now(),
            estimated_duration: Some(600000), // 10 minutes
        });

        // Implementation tasks based on requirements
        for requirement in &analysis.requirements {
            tasks.push(crate::agents::Task {
                id: Uuid::new_v4().to_string(),
                description: format!("Implement {}", requirement),
                agent_type: crate::agents::AgentType::CodeGeneration,
                priority: crate::agents::TaskPriority::Medium,
                context: serde_json::json!({
                    "requirement": requirement,
                    "technologies": analysis.technologies
                }),
                dependencies: vec![tasks[0].id.clone()], // Depends on project structure
                files: Vec::new(),
                created_at: chrono::Utc::now(),
                estimated_duration: Some(900000), // 15 minutes
            });
        }

        Ok(tasks)
    }

    async fn generate_fix_tasks(&self, analysis: &RequestAnalysis) -> Result<Vec<crate::agents::Task>, anyhow::Error> {
        vec![crate::agents::Task {
            id: Uuid::new_v4().to_string(),
            description: "Analyze and fix identified issues".to_string(),
            agent_type: crate::agents::AgentType::CodeGeneration,
            priority: crate::agents::TaskPriority::High,
            context: serde_json::to_value(analysis)?,
            dependencies: Vec::new(),
            files: analysis.file_references.clone(),
            created_at: chrono::Utc::now(),
            estimated_duration: Some(600000),
        }]
    }

    async fn generate_test_tasks(&self, analysis: &RequestAnalysis) -> Result<Vec<crate::agents::Task>, anyhow::Error> {
        vec![crate::agents::Task {
            id: Uuid::new_v4().to_string(),
            description: "Generate comprehensive test suite".to_string(),
            agent_type: crate::agents::AgentType::Testing,
            priority: crate::agents::TaskPriority::Medium,
            context: serde_json::to_value(analysis)?,
            dependencies: Vec::new(),
            files: analysis.file_references.clone(),
            created_at: chrono::Utc::now(),
            estimated_duration: Some(900000),
        }]
    }

    async fn generate_documentation_tasks(&self, analysis: &RequestAnalysis) -> Result<Vec<crate::agents::Task>, anyhow::Error> {
        vec![crate::agents::Task {
            id: Uuid::new_v4().to_string(),
            description: "Generate project documentation".to_string(),
            agent_type: crate::agents::AgentType::Documentation,
            priority: crate::agents::TaskPriority::Low,
            context: serde_json::to_value(analysis)?,
            dependencies: Vec::new(),
            files: analysis.file_references.clone(),
            created_at: chrono::Utc::now(),
            estimated_duration: Some(600000),
        }]
    }

    async fn generate_review_tasks(&self, analysis: &RequestAnalysis) -> Result<Vec<crate::agents::Task>, anyhow::Error> {
        vec![crate::agents::Task {
            id: Uuid::new_v4().to_string(),
            description: "Perform comprehensive code review".to_string(),
            agent_type: crate::agents::AgentType::Review,
            priority: crate::agents::TaskPriority::Medium,
            context: serde_json::to_value(analysis)?,
            dependencies: Vec::new(),
            files: analysis.file_references.clone(),
            created_at: chrono::Utc::now(),
            estimated_duration: Some(1200000),
        }]
    }

    async fn generate_refactor_tasks(&self, analysis: &RequestAnalysis) -> Result<Vec<crate::agents::Task>, anyhow::Error> {
        vec![crate::agents::Task {
            id: Uuid::new_v4().to_string(),
            description: "Refactor and optimize code".to_string(),
            agent_type: crate::agents::AgentType::CodeGeneration,
            priority: crate::agents::TaskPriority::Medium,
            context: serde_json::to_value(analysis)?,
            dependencies: Vec::new(),
            files: analysis.file_references.clone(),
            created_at: chrono::Utc::now(),
            estimated_duration: Some(900000),
        }]
    }

    async fn generate_general_tasks(&self, analysis: &RequestAnalysis) -> Result<Vec<crate::agents::Task>, anyhow::Error> {
        vec![crate::agents::Task {
            id: Uuid::new_v4().to_string(),
            description: "Execute general development task".to_string(),
            agent_type: crate::agents::AgentType::CodeGeneration,
            priority: crate::agents::TaskPriority::Medium,
            context: serde_json::to_value(analysis)?,
            dependencies: Vec::new(),
            files: analysis.file_references.clone(),
            created_at: chrono::Utc::now(),
            estimated_duration: Some(600000),
        }]
    }

    async fn calculate_task_dependencies(&self, tasks: &[crate::agents::Task]) -> Result<HashMap<String, Vec<String>>, anyhow::Error> {
        let mut dependencies = HashMap::new();
        
        for task in tasks {
            dependencies.insert(task.id.clone(), task.dependencies.clone());
        }
        
        Ok(dependencies)
    }

    async fn store_context(&self, context: Context) -> Result<(), anyhow::Error> {
        let mut store = self.context_store.write().await;
        
        // Add to active contexts
        store.active_contexts.insert(context.id.clone(), context.clone());
        
        // Add to history
        store.context_history.push_back(context);
        
        // Maintain history size
        if store.context_history.len() > self.context_window_size {
            store.context_history.pop_front();
        }
        
        Ok(())
    }

    pub async fn update_context(&mut self, context_data: serde_json::Value) -> Result<(), anyhow::Error> {
        let mut store = self.context_store.write().await;
        
        // Update global context based on the data
        if let Some(workspace_path) = context_data.get("workspace_path").and_then(|v| v.as_str()) {
            store.global_context.workspace_path = workspace_path.to_string();
        }
        
        if let Some(technologies) = context_data.get("technologies").and_then(|v| v.as_array()) {
            store.global_context.main_technologies = technologies
                .iter()
                .filter_map(|v| v.as_str().map(|s| s.to_string()))
                .collect();
        }
        
        Ok(())
    }

    async fn load_persisted_context(&self) -> Result<(), anyhow::Error> {
        // Load context from persistent storage if available
        // This would integrate with the memory system
        Ok(())
    }

    async fn start_context_cleanup_task(&self) {
        let context_store = self.context_store.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(3600)); // 1 hour
            
            loop {
                interval.tick().await;
                
                // Clean up old contexts
                let mut store = context_store.write().await;
                let cutoff_time = chrono::Utc::now() - chrono::Duration::hours(24);
                
                store.active_contexts.retain(|_, context| {
                    context.last_accessed > cutoff_time || context.relevance_score > 0.8
                });
            }
        });
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RequestAnalysis {
    pub intent: Intent,
    pub technologies: Vec<String>,
    pub file_references: Vec<String>,
    pub codebase_context: Option<serde_json::Value>,
    pub historical_context: Vec<Context>,
    pub complexity: ComplexityLevel,
    pub requirements: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Intent {
    Create,
    Fix,
    Test,
    Document,
    Review,
    Refactor,
    General,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComplexityLevel {
    Low,
    Medium,
    High,
    VeryHigh,
}