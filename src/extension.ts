/**
 * Aizen AI Extension - VS Code Entry Point
 * Connects React UI to Python AI backend with real AI frameworks
 */

import * as vscode from 'vscode';
import { AizenExtensionManager } from './services/AizenExtensionManager';
import { AizenMCPClient } from './mcp/MCPClient';
import { MCPSecurityManager } from './mcp/MCPSecurityManager';
import { MCPSettingsProvider } from './mcp/MCPSettingsProvider';
import { MCPConfigEditorProvider } from './mcp/MCPConfigEditorProvider';
import { StandardMCPManager } from './mcp/StandardMCPManager';

// Extension manager instance
let extensionManager: AizenExtensionManager;
let mcpClient: AizenMCPClient;
let mcpSecurityManager: MCPSecurityManager;
let mcpConfigEditorProvider: MCPConfigEditorProvider;
let standardMCPManager: StandardMCPManager;
let mcpSettingsProvider: MCPSettingsProvider;


export async function activate(context: vscode.ExtensionContext) {
    console.log('🚀 Activating Aizen AI Extension - Revolutionary AI Assistant');

    try {
        // REGISTER SETTINGS COMMAND FIRST - BEFORE ANYTHING ELSE
        console.log('🎯 Registering aizen.showSettings command...');
        const settingsCommand = vscode.commands.registerCommand('aizen.showSettings', async () => {
            console.log('🎯 MAIN EXTENSION: aizen.showSettings called');
            try {
                const panel = vscode.window.createWebviewPanel(
                    'aizenSettings',
                    'Aizen AI Settings',
                    vscode.ViewColumn.Two,
                    {
                        enableScripts: true,
                        retainContextWhenHidden: true,
                        localResourceRoots: [
                            vscode.Uri.file(context.extensionPath + '/out/ui'),
                            vscode.Uri.file(context.extensionPath + '/media')
                        ]
                    }
                );

                // Set icon
                panel.iconPath = vscode.Uri.file(context.extensionPath + '/media/icons/aizen-logo-white.svg');

                // Load settings HTML
                const fs = require('fs');
                const path = require('path');
                const settingsHtmlPath = path.join(context.extensionPath, 'out', 'ui', 'settings.html');
                console.log('🎯 Loading settings HTML from:', settingsHtmlPath);

                if (fs.existsSync(settingsHtmlPath)) {
                    let htmlContent = fs.readFileSync(settingsHtmlPath, 'utf8');

                    // Replace placeholders with proper URIs
                    const stylesUri = panel.webview.asWebviewUri(vscode.Uri.file(path.join(context.extensionPath, 'out', 'ui', 'styles.css')));
                    const settingsStylesUri = panel.webview.asWebviewUri(vscode.Uri.file(path.join(context.extensionPath, 'out', 'ui', 'settings.css')));
                    const scriptUri = panel.webview.asWebviewUri(vscode.Uri.file(path.join(context.extensionPath, 'out', 'ui', 'settings.js')));
                    const logoUri = panel.webview.asWebviewUri(vscode.Uri.file(path.join(context.extensionPath, 'media', 'icons', 'aizen-logo-white.svg')));

                    htmlContent = htmlContent
                        .replace(/{{CSS_URI}}/g, stylesUri.toString())
                        .replace(/{{SETTINGS_CSS_URI}}/g, settingsStylesUri.toString())
                        .replace(/{{SETTINGS_SCRIPT_URI}}/g, scriptUri.toString())
                        .replace(/{{LOGO_URI}}/g, logoUri.toString())
                        .replace(/{{THEME_CLASS}}/g, 'dark-theme')
                        .replace(/{{NONCE}}/g, 'settings-nonce')
                        .replace(/{{CSP_SOURCE}}/g, panel.webview.cspSource);

                    panel.webview.html = htmlContent;
                    console.log('✅ Settings webview created successfully');
                    vscode.window.showInformationMessage('✅ Settings opened!');
                } else {
                    throw new Error(`Settings HTML not found: ${settingsHtmlPath}`);
                }
            } catch (error) {
                console.error('❌ Settings failed:', error);
                vscode.window.showErrorMessage(`Settings failed: ${error.message}`);
            }
        });

        context.subscriptions.push(settingsCommand);
        console.log('✅ aizen.showSettings command registered successfully');

        // Add a simple test command to verify command registration works
        const testCommand = vscode.commands.registerCommand('aizen.test', async () => {
            vscode.window.showInformationMessage('🎯 TEST COMMAND WORKS! Extension is loaded correctly.');
            console.log('🎯 TEST COMMAND EXECUTED');
        });
        context.subscriptions.push(testCommand);
        console.log('✅ aizen.test command registered');

        // Note: Chat View Provider will be registered by AizenExtensionManager

        // Initialize MCP Security Manager
        mcpSecurityManager = new MCPSecurityManager({
            context,
            enableTelemetry: true
        });

        // Initialize Aizen MCP Client
        mcpClient = new AizenMCPClient({
            context,
            securityPolicy: mcpSecurityManager.getSecurityPolicy(),
            debugMode: true
        });

        try {
            await mcpClient.initialize();
            console.log('✅ Aizen MCP Client initialized successfully');

            // Initialize Standard MCP Manager
            standardMCPManager = new StandardMCPManager(context, mcpClient, mcpSecurityManager);
            await standardMCPManager.initialize();
            console.log('✅ Standard MCP Manager initialized successfully');

            // Initialize MCP Configuration Editor
            mcpConfigEditorProvider = new MCPConfigEditorProvider(context);
            console.log('✅ MCP Configuration Editor initialized successfully');

            // Initialize MCP Settings Provider
            mcpSettingsProvider = new MCPSettingsProvider(context, mcpClient, mcpSecurityManager, mcpConfigEditorProvider);

            vscode.window.showInformationMessage('🚀 MCP tools ready! Built-in Exa and Firecrawl servers are pre-configured.');
        } catch (error) {
            console.error('❌ MCP Client initialization failed:', error);
            vscode.window.showErrorMessage(`Failed to initialize MCP client: ${error}`);
        }

        // Register MCP commands
        context.subscriptions.push(
            vscode.commands.registerCommand('aizen.test.basic', () => {
                console.log('🎯 Basic test command executed!');
                vscode.window.showInformationMessage('🎯 Basic test works! MCP commands should work too.');
            }),

            vscode.commands.registerCommand('aizen.mcp.status', async () => {
                try {
                    const health = mcpClient.getHealthStatus();
                    const servers = mcpClient.getConnectedServers();
                    const tools = mcpClient.getAvailableTools();

                    let message = `🔌 MCP Status:\n`;
                    message += `• Connected Servers: ${health.connectedServers}/${health.totalServers}\n`;
                    message += `• Available Tools: ${health.totalTools}\n`;
                    message += `• Available Resources: ${health.totalResources}\n`;
                    message += `• Available Prompts: ${health.totalPrompts}\n`;

                    if (health.errors.length > 0) {
                        message += `\n⚠️ Errors:\n${health.errors.join('\n')}`;
                    }

                    vscode.window.showInformationMessage(message);
                } catch (error) {
                    vscode.window.showErrorMessage(`MCP Status check failed: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.addExternalServer', async () => {
                try {
                    const serverName = await vscode.window.showInputBox({
                        prompt: 'Enter MCP Server Name',
                        placeHolder: 'My Custom Server'
                    });

                    if (!serverName) return;

                    const command = await vscode.window.showInputBox({
                        prompt: 'Enter MCP Server Command',
                        placeHolder: 'npx my-mcp-server'
                    });

                    if (!command) return;

                    const parts = command.split(' ');
                    const serverId = await mcpClient.addExternalServer({
                        name: serverName,
                        transport: 'stdio',
                        command: parts[0],
                        args: parts.slice(1),
                        enabled: true,
                        autoStart: true,
                        timeout: 30000,
                        retryAttempts: 3,
                        retryDelay: 1000
                    });

                    vscode.window.showInformationMessage(`✅ External MCP server "${serverName}" added successfully!`);
                } catch (error) {
                    vscode.window.showErrorMessage(`Failed to add external MCP server: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.openSettings', async () => {
                try {
                    await mcpSettingsProvider.openSettings();
                } catch (error) {
                    vscode.window.showErrorMessage(`Failed to open MCP settings: ${error}`);
                }
            }),
            vscode.commands.registerCommand('aizen.mcp.configEditor', async () => {
                try {
                    await mcpConfigEditorProvider.openConfigEditor();
                } catch (error) {
                    vscode.window.showErrorMessage(`Failed to open MCP configuration editor: ${error}`);
                }
            }),
            vscode.commands.registerCommand('aizen.mcp.testExaSearch', async () => {
                try {
                    console.log('🧪 Testing Exa search...');
                    const result = await mcpClient.callTool('web_search_exa_exa', {
                        query: 'latest AI developments 2025',
                        numResults: 3
                    });

                    const resultText = JSON.stringify(result, null, 2).substring(0, 500);
                    vscode.window.showInformationMessage(`✅ Exa search successful! Result: ${resultText}...`);
                } catch (error) {
                    console.error('❌ Exa test failed:', error);
                    vscode.window.showErrorMessage(`❌ Exa test failed: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.testFirecrawlScrape', async () => {
                try {
                    console.log('🧪 Testing Firecrawl scrape...');
                    const result = await mcpClient.callTool('firecrawl_scrape_firecrawl-mcp', {
                        url: 'https://example.com',
                        formats: ['markdown']
                    });

                    const resultText = JSON.stringify(result, null, 2).substring(0, 500);
                    vscode.window.showInformationMessage(`✅ Firecrawl scrape successful! Result: ${resultText}...`);
                } catch (error) {
                    console.error('❌ Firecrawl test failed:', error);
                    vscode.window.showErrorMessage(`❌ Firecrawl test failed: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.listTools', async () => {
                try {
                    const tools = mcpClient.getAvailableTools();
                    const toolsList = tools.map(t => `• ${t.name} (${t.serverId}): ${t.description}`).join('\n');
                    const message = `🔧 Available MCP Tools (${tools.length}):\n\n${toolsList || 'No tools available. Configure API keys first.'}`;
                    vscode.window.showInformationMessage(message);
                } catch (error) {
                    vscode.window.showErrorMessage(`Failed to list tools: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.runTests', async () => {
                try {
                    console.log('🧪 Running basic MCP tests...');
                    vscode.window.showInformationMessage('🧪 MCP servers are registered! Use Command Palette > "MCP: List Servers" to see them, or try asking in Chat: "What MCP tools are available?"');
                } catch (error) {
                    console.error('❌ MCP tests failed:', error);
                    vscode.window.showErrorMessage(`MCP tests failed: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.test.simple', () => {
                console.log('🎯 Simple test command executed!');
                vscode.window.showInformationMessage('🎯 Simple test command works!');
            })
        );

        // Initialize Extension Manager
        extensionManager = AizenExtensionManager.getInstance(context);
        // TODO: Update AizenExtensionManager to use new MCP client interface
        // extensionManager.setMCPClient(mcpClient);
        await extensionManager.activate();

        console.log('✅ Aizen AI Extension activated successfully');

    } catch (error) {
        console.error('❌ Failed to activate Aizen AI Extension:', error);
        vscode.window.showErrorMessage(`Failed to activate Aizen AI: ${error instanceof Error ? error.message : String(error)}`);
    }
}

export async function deactivate() {
    console.log('🔄 Deactivating Aizen AI Extension...');

    if (extensionManager) {
        await extensionManager.deactivate();
    }

    if (mcpClient) {
        await mcpClient.dispose();
    }

    console.log('✅ Aizen AI Extension deactivated');
}