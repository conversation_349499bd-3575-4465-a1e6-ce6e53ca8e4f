/**
 * MCP Server Manager Component
 * 
 * Provides a comprehensive UI for managing MCP servers including:
 * - Built-in server status and configuration
 * - External server addition and management
 * - Real-time connection status
 * - Tool and resource counts
 * - Security settings
 */

import React, { useState, useEffect } from 'react';
import { 
    ExtendedMCPServerConfig, 
    MCPServerStatus, 
    MCPConnectionStatus,
    ExtendedMCPTool,
    ExtendedMCPResource,
    ExtendedMCPPrompt
} from '../../mcp/types';

interface MCPServerManagerProps {
    vscode: any;
    onServerAdded?: (serverId: string) => void;
    onServerRemoved?: (serverId: string) => void;
    onServerToggled?: (serverId: string, enabled: boolean) => void;
}

interface ServerData {
    config: ExtendedMCPServerConfig;
    status: MCPServerStatus;
    tools: ExtendedMCPTool[];
    resources: ExtendedMCPResource[];
    prompts: ExtendedMCPPrompt[];
}

export const MCPServerManager: React.FC<MCPServerManagerProps> = ({
    vscode,
    onServerAdded,
    onServerRemoved,
    onServerToggled
}) => {
    const [servers, setServers] = useState<Map<string, ServerData>>(new Map());
    const [loading, setLoading] = useState(true);
    const [showAddDialog, setShowAddDialog] = useState(false);
    const [selectedServer, setSelectedServer] = useState<string | null>(null);

    useEffect(() => {
        loadServers();
        
        // Listen for server updates
        const handleMessage = (event: MessageEvent) => {
            const message = event.data;
            switch (message.type) {
                case 'serverStatusUpdate':
                    updateServerStatus(message.serverId, message.status);
                    break;
                case 'serverAdded':
                    loadServers(); // Reload all servers
                    break;
                case 'serverRemoved':
                    removeServer(message.serverId);
                    break;
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, []);

    const loadServers = async () => {
        try {
            setLoading(true);
            vscode.postMessage({ type: 'getMCPServers' });
        } catch (error) {
            console.error('Failed to load MCP servers:', error);
        } finally {
            setLoading(false);
        }
    };

    const updateServerStatus = (serverId: string, status: MCPServerStatus) => {
        setServers(prev => {
            const newServers = new Map(prev);
            const serverData = newServers.get(serverId);
            if (serverData) {
                newServers.set(serverId, {
                    ...serverData,
                    status
                });
            }
            return newServers;
        });
    };

    const removeServer = (serverId: string) => {
        setServers(prev => {
            const newServers = new Map(prev);
            newServers.delete(serverId);
            return newServers;
        });
    };

    const handleToggleServer = async (serverId: string, enabled: boolean) => {
        try {
            vscode.postMessage({
                type: 'toggleMCPServer',
                serverId,
                enabled
            });
            
            onServerToggled?.(serverId, enabled);
        } catch (error) {
            console.error('Failed to toggle server:', error);
        }
    };

    const handleTestConnection = async (serverId: string) => {
        try {
            vscode.postMessage({
                type: 'testMCPConnection',
                serverId
            });
        } catch (error) {
            console.error('Failed to test connection:', error);
        }
    };

    const handleRemoveServer = async (serverId: string) => {
        const serverData = servers.get(serverId);
        if (!serverData) return;

        const confirmed = await new Promise<boolean>((resolve) => {
            vscode.postMessage({
                type: 'confirmDialog',
                message: `Are you sure you want to remove the server "${serverData.config.name}"?`,
                callback: resolve
            });
        });

        if (confirmed) {
            try {
                vscode.postMessage({
                    type: 'removeMCPServer',
                    serverId
                });
                
                onServerRemoved?.(serverId);
            } catch (error) {
                console.error('Failed to remove server:', error);
            }
        }
    };

    const getStatusIcon = (status: MCPConnectionStatus): string => {
        switch (status) {
            case MCPConnectionStatus.Connected:
                return '✅';
            case MCPConnectionStatus.Connecting:
                return '🔄';
            case MCPConnectionStatus.Disconnected:
                return '⚫';
            case MCPConnectionStatus.Error:
                return '❌';
            case MCPConnectionStatus.Reconnecting:
                return '🔄';
            default:
                return '❓';
        }
    };

    const getStatusColor = (status: MCPConnectionStatus): string => {
        switch (status) {
            case MCPConnectionStatus.Connected:
                return '#10b981'; // green
            case MCPConnectionStatus.Connecting:
            case MCPConnectionStatus.Reconnecting:
                return '#f59e0b'; // yellow
            case MCPConnectionStatus.Disconnected:
                return '#6b7280'; // gray
            case MCPConnectionStatus.Error:
                return '#ef4444'; // red
            default:
                return '#6b7280';
        }
    };

    const renderServerCard = (serverData: ServerData) => {
        const { config, status, tools, resources, prompts } = serverData;
        
        return (
            <div key={config.id} className="mcp-server-card glass">
                <div className="server-header">
                    <div className="server-info">
                        <div className="server-title">
                            <span className="server-icon">{config.icon || '🔧'}</span>
                            <h4>{config.name}</h4>
                            {config.isBuiltIn && <span className="built-in-badge">Built-in</span>}
                        </div>
                        <p className="server-description">{config.description}</p>
                        <div className="server-stats">
                            <span className="stat">
                                <span className="stat-icon">🔧</span>
                                {status.toolCount} tools
                            </span>
                            <span className="stat">
                                <span className="stat-icon">📄</span>
                                {status.resourceCount} resources
                            </span>
                            <span className="stat">
                                <span className="stat-icon">💬</span>
                                {status.promptCount} prompts
                            </span>
                        </div>
                    </div>
                    <div className="server-status">
                        <span 
                            className="status-badge"
                            style={{ color: getStatusColor(status.status) }}
                        >
                            {getStatusIcon(status.status)} {status.status}
                        </span>
                        {status.lastError && (
                            <div className="error-message" title={status.lastError}>
                                ⚠️ {status.lastError.substring(0, 50)}...
                            </div>
                        )}
                    </div>
                </div>

                <div className="server-actions">
                    <label className="toggle-switch">
                        <input
                            type="checkbox"
                            checked={config.enabled}
                            onChange={(e) => handleToggleServer(config.id, e.target.checked)}
                        />
                        <span className="toggle-slider glass"></span>
                    </label>

                    <button
                        className="btn btn-small btn-secondary"
                        onClick={() => handleTestConnection(config.id)}
                        disabled={status.status === MCPConnectionStatus.Connecting}
                        title="Test connection"
                    >
                        🔍 Test
                    </button>

                    <button
                        className="btn btn-small"
                        onClick={() => setSelectedServer(config.id)}
                        title="View details"
                    >
                        📋 Details
                    </button>

                    {!config.isBuiltIn && (
                        <button
                            className="btn btn-small btn-danger"
                            onClick={() => handleRemoveServer(config.id)}
                            title="Remove server"
                        >
                            🗑️ Remove
                        </button>
                    )}
                </div>

                {selectedServer === config.id && (
                    <div className="server-details">
                        <div className="details-header">
                            <h5>Server Details</h5>
                            <button
                                className="close-btn"
                                onClick={() => setSelectedServer(null)}
                            >
                                ✕
                            </button>
                        </div>
                        
                        <div className="details-content">
                            <div className="detail-section">
                                <h6>Configuration</h6>
                                <div className="detail-grid">
                                    <div className="detail-item">
                                        <span className="detail-label">Transport:</span>
                                        <span className="detail-value">{config.transport}</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="detail-label">Version:</span>
                                        <span className="detail-value">{config.version || 'Unknown'}</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="detail-label">Timeout:</span>
                                        <span className="detail-value">{config.timeout}ms</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="detail-label">Auto-start:</span>
                                        <span className="detail-value">{config.autoStart ? 'Yes' : 'No'}</span>
                                    </div>
                                </div>
                            </div>

                            {tools.length > 0 && (
                                <div className="detail-section">
                                    <h6>Available Tools ({tools.length})</h6>
                                    <div className="tools-list">
                                        {tools.slice(0, 5).map(tool => (
                                            <div key={tool.name} className="tool-item">
                                                <span className="tool-name">{tool.name}</span>
                                                <span className="tool-description">{tool.description}</span>
                                                {tool.riskLevel && (
                                                    <span className={`risk-badge risk-${tool.riskLevel}`}>
                                                        {tool.riskLevel}
                                                    </span>
                                                )}
                                            </div>
                                        ))}
                                        {tools.length > 5 && (
                                            <div className="more-items">
                                                +{tools.length - 5} more tools
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {status.lastConnected && (
                                <div className="detail-section">
                                    <h6>Connection Info</h6>
                                    <div className="detail-item">
                                        <span className="detail-label">Last connected:</span>
                                        <span className="detail-value">
                                            {status.lastConnected.toLocaleString()}
                                        </span>
                                    </div>
                                    {status.uptime && (
                                        <div className="detail-item">
                                            <span className="detail-label">Uptime:</span>
                                            <span className="detail-value">
                                                {Math.round(status.uptime / 1000)}s
                                            </span>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        );
    };

    if (loading) {
        return (
            <div className="mcp-loading">
                <div className="loading-spinner"></div>
                <p>Loading MCP servers...</p>
            </div>
        );
    }

    const builtInServers = Array.from(servers.values()).filter(s => s.config.isBuiltIn);
    const externalServers = Array.from(servers.values()).filter(s => !s.config.isBuiltIn);

    return (
        <div className="mcp-server-manager">
            <div className="manager-header">
                <h3>MCP Server Configuration</h3>
                <button
                    className="btn btn-primary"
                    onClick={() => setShowAddDialog(true)}
                >
                    <span className="btn-icon">➕</span>
                    Add External Server
                </button>
            </div>

            {builtInServers.length > 0 && (
                <div className="server-section">
                    <h4>Built-in Servers</h4>
                    <p className="section-description">
                        These servers are pre-configured with Aizen AI and ready to use.
                    </p>
                    <div className="servers-grid">
                        {builtInServers.map(serverData => renderServerCard(serverData))}
                    </div>
                </div>
            )}

            {externalServers.length > 0 && (
                <div className="server-section">
                    <h4>External Servers</h4>
                    <p className="section-description">
                        Custom MCP servers you've added to extend functionality.
                    </p>
                    <div className="servers-grid">
                        {externalServers.map(serverData => renderServerCard(serverData))}
                    </div>
                </div>
            )}

            {externalServers.length === 0 && (
                <div className="empty-state">
                    <div className="empty-icon">🔧</div>
                    <h4>No External Servers</h4>
                    <p>Add external MCP servers to extend Aizen AI's capabilities.</p>
                    <button
                        className="btn btn-primary"
                        onClick={() => setShowAddDialog(true)}
                    >
                        Add Your First Server
                    </button>
                </div>
            )}

            {showAddDialog && (
                <AddServerDialog
                    vscode={vscode}
                    onClose={() => setShowAddDialog(false)}
                    onServerAdded={(serverId) => {
                        setShowAddDialog(false);
                        onServerAdded?.(serverId);
                        loadServers();
                    }}
                />
            )}
        </div>
    );
};

// Add Server Dialog Component (will be implemented separately)
const AddServerDialog: React.FC<{
    vscode: any;
    onClose: () => void;
    onServerAdded: (serverId: string) => void;
}> = ({ vscode, onClose, onServerAdded }) => {
    // This will be implemented in the next part
    return (
        <div className="dialog-overlay">
            <div className="dialog glass">
                <h4>Add External MCP Server</h4>
                <p>Dialog implementation coming next...</p>
                <button onClick={onClose}>Close</button>
            </div>
        </div>
    );
};
