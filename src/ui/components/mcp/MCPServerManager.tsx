// MCP Server Manager UI Component
import React, { useState, useEffect } from 'react';
import { 
    MCPServerInstance, 
    MCPConnectionStatus, 
    MCPServerConfig,
    MCPTool,
    MCPResource,
    MCPPrompt
} from '../../../mcp/types';

interface MCPServerManagerProps {
    onServerAdd: (config: MCPServerConfig) => Promise<string>;
    onServerRemove: (serverId: string) => Promise<void>;
    onServerConnect: (serverId: string) => Promise<void>;
    onServerDisconnect: (serverId: string) => Promise<void>;
    onToolExecute: (serverId: string, toolName: string, args: Record<string, any>) => Promise<any>;
    servers: MCPServerInstance[];
}

export const MCPServerManager: React.FC<MCPServerManagerProps> = ({
    onServerAdd,
    onServerRemove,
    onServerConnect,
    onServerDisconnect,
    onToolExecute,
    servers
}) => {
    const [selectedServer, setSelectedServer] = useState<string | null>(null);
    const [showAddDialog, setShowAddDialog] = useState(false);
    const [newServerConfig, setNewServerConfig] = useState<Partial<MCPServerConfig>>({
        name: '',
        transport: 'streamable-http',
        enabled: true,
        autoStart: true
    });

    const getStatusIcon = (status: MCPConnectionStatus) => {
        switch (status) {
            case MCPConnectionStatus.Connected:
                return '🟢';
            case MCPConnectionStatus.Connecting:
                return '🟡';
            case MCPConnectionStatus.Error:
                return '🔴';
            case MCPConnectionStatus.Reconnecting:
                return '🟠';
            default:
                return '⚪';
        }
    };

    const getStatusText = (status: MCPConnectionStatus) => {
        switch (status) {
            case MCPConnectionStatus.Connected:
                return 'Connected';
            case MCPConnectionStatus.Connecting:
                return 'Connecting...';
            case MCPConnectionStatus.Error:
                return 'Error';
            case MCPConnectionStatus.Reconnecting:
                return 'Reconnecting...';
            default:
                return 'Disconnected';
        }
    };

    const handleAddServer = async () => {
        if (!newServerConfig.name || (!newServerConfig.url && !newServerConfig.command)) {
            alert('Please provide server name and either URL or command');
            return;
        }

        try {
            await onServerAdd(newServerConfig as MCPServerConfig);
            setShowAddDialog(false);
            setNewServerConfig({
                name: '',
                transport: 'streamable-http',
                enabled: true,
                autoStart: true
            });
        } catch (error) {
            alert(`Failed to add server: ${error}`);
        }
    };

    const handleServerAction = async (serverId: string, action: 'connect' | 'disconnect' | 'remove') => {
        try {
            switch (action) {
                case 'connect':
                    await onServerConnect(serverId);
                    break;
                case 'disconnect':
                    await onServerDisconnect(serverId);
                    break;
                case 'remove':
                    if (confirm('Are you sure you want to remove this server?')) {
                        await onServerRemove(serverId);
                    }
                    break;
            }
        } catch (error) {
            alert(`Failed to ${action} server: ${error}`);
        }
    };

    const selectedServerData = selectedServer ? servers.find(s => s.id === selectedServer) : null;

    return (
        <>
        <div className="mcp-server-manager">
            <div className="mcp-header">
                <h2>🔌 MCP Server Manager</h2>
                <button 
                    className="btn btn-primary"
                    onClick={() => setShowAddDialog(true)}
                >
                    ➕ Add Server
                </button>
            </div>

            <div className="mcp-content">
                <div className="server-list">
                    <h3>Servers ({servers.length})</h3>
                    {servers.length === 0 ? (
                        <div className="empty-state">
                            <p>No MCP servers configured</p>
                            <p>Add external servers like Exa AI or Firecrawl to get started</p>
                        </div>
                    ) : (
                        <div className="server-grid">
                            {servers.map(server => (
                                <div 
                                    key={server.id}
                                    className={`server-card ${selectedServer === server.id ? 'selected' : ''}`}
                                    onClick={() => setSelectedServer(server.id)}
                                >
                                    <div className="server-header">
                                        <div className="server-name">
                                            <span className="status-icon">{getStatusIcon(server.status)}</span>
                                            <span className="name">{server.config.name}</span>
                                        </div>
                                        <div className="server-actions">
                                            {server.status === MCPConnectionStatus.Connected ? (
                                                <button 
                                                    className="btn btn-sm btn-secondary"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleServerAction(server.id, 'disconnect');
                                                    }}
                                                >
                                                    Disconnect
                                                </button>
                                            ) : (
                                                <button 
                                                    className="btn btn-sm btn-primary"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleServerAction(server.id, 'connect');
                                                    }}
                                                    disabled={server.status === MCPConnectionStatus.Connecting}
                                                >
                                                    Connect
                                                </button>
                                            )}
                                            <button 
                                                className="btn btn-sm btn-danger"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleServerAction(server.id, 'remove');
                                                }}
                                            >
                                                🗑️
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div className="server-info">
                                        <div className="status">
                                            Status: <span className={`status-text ${server.status}`}>
                                                {getStatusText(server.status)}
                                            </span>
                                        </div>
                                        <div className="transport">
                                            Transport: {server.config.transport}
                                        </div>
                                        {server.config.url && (
                                            <div className="url">URL: {server.config.url}</div>
                                        )}
                                        {server.config.command && (
                                            <div className="command">Command: {server.config.command}</div>
                                        )}
                                        {server.lastError && (
                                            <div className="error">Error: {server.lastError}</div>
                                        )}
                                    </div>

                                    {server.status === MCPConnectionStatus.Connected && (
                                        <div className="server-stats">
                                            <div className="stat">
                                                <span className="label">Tools:</span>
                                                <span className="value">{server.tools?.length || 0}</span>
                                            </div>
                                            <div className="stat">
                                                <span className="label">Resources:</span>
                                                <span className="value">{server.resources?.length || 0}</span>
                                            </div>
                                            <div className="stat">
                                                <span className="label">Prompts:</span>
                                                <span className="value">{server.prompts?.length || 0}</span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {selectedServerData && (
                    <div className="server-details">
                        <h3>Server Details: {selectedServerData.config.name}</h3>
                        
                        {selectedServerData.status === MCPConnectionStatus.Connected && (
                            <>
                                {selectedServerData.tools && selectedServerData.tools.length > 0 && (
                                    <div className="tools-section">
                                        <h4>🔧 Available Tools ({selectedServerData.tools.length})</h4>
                                        <div className="tools-grid">
                                            {selectedServerData.tools.map(tool => (
                                                <div key={tool.name} className="tool-card">
                                                    <div className="tool-name">{tool.name}</div>
                                                    {tool.description && (
                                                        <div className="tool-description">{tool.description}</div>
                                                    )}
                                                    <button 
                                                        className="btn btn-sm btn-primary"
                                                        onClick={() => {
                                                            // This would open a tool execution dialog
                                                            console.log('Execute tool:', tool.name);
                                                        }}
                                                    >
                                                        Execute
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {selectedServerData.resources && selectedServerData.resources.length > 0 && (
                                    <div className="resources-section">
                                        <h4>📄 Available Resources ({selectedServerData.resources.length})</h4>
                                        <div className="resources-list">
                                            {selectedServerData.resources.map(resource => (
                                                <div key={resource.uri} className="resource-item">
                                                    <div className="resource-name">{resource.name}</div>
                                                    <div className="resource-uri">{resource.uri}</div>
                                                    {resource.description && (
                                                        <div className="resource-description">{resource.description}</div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {selectedServerData.prompts && selectedServerData.prompts.length > 0 && (
                                    <div className="prompts-section">
                                        <h4>💬 Available Prompts ({selectedServerData.prompts.length})</h4>
                                        <div className="prompts-list">
                                            {selectedServerData.prompts.map(prompt => (
                                                <div key={prompt.name} className="prompt-item">
                                                    <div className="prompt-name">{prompt.name}</div>
                                                    {prompt.description && (
                                                        <div className="prompt-description">{prompt.description}</div>
                                                    )}
                                                    {prompt.arguments && prompt.arguments.length > 0 && (
                                                        <div className="prompt-args">
                                                            Arguments: {prompt.arguments.map(arg => arg.name).join(', ')}
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                )}
            </div>

            {showAddDialog && (
                <div className="modal-overlay">
                    <div className="modal">
                        <div className="modal-header">
                            <h3>Add MCP Server</h3>
                            <button 
                                className="btn btn-sm"
                                onClick={() => setShowAddDialog(false)}
                            >
                                ✕
                            </button>
                        </div>
                        
                        <div className="modal-body">
                            <div className="form-group">
                                <label>Server Name *</label>
                                <input 
                                    type="text"
                                    value={newServerConfig.name || ''}
                                    onChange={(e) => setNewServerConfig({
                                        ...newServerConfig,
                                        name: e.target.value
                                    })}
                                    placeholder="e.g., Exa AI, Firecrawl"
                                />
                            </div>

                            <div className="form-group">
                                <label>Transport</label>
                                <select 
                                    value={newServerConfig.transport}
                                    onChange={(e) => setNewServerConfig({
                                        ...newServerConfig,
                                        transport: e.target.value as any
                                    })}
                                >
                                    <option value="streamable-http">Streamable HTTP</option>
                                    <option value="stdio">STDIO</option>
                                    <option value="http">HTTP</option>
                                </select>
                            </div>

                            {newServerConfig.transport === 'streamable-http' || newServerConfig.transport === 'http' ? (
                                <div className="form-group">
                                    <label>Server URL *</label>
                                    <input 
                                        type="url"
                                        value={newServerConfig.url || ''}
                                        onChange={(e) => setNewServerConfig({
                                            ...newServerConfig,
                                            url: e.target.value
                                        })}
                                        placeholder="https://api.example.com/mcp"
                                    />
                                </div>
                            ) : (
                                <div className="form-group">
                                    <label>Command *</label>
                                    <input 
                                        type="text"
                                        value={newServerConfig.command || ''}
                                        onChange={(e) => setNewServerConfig({
                                            ...newServerConfig,
                                            command: e.target.value
                                        })}
                                        placeholder="node server.js"
                                    />
                                </div>
                            )}

                            <div className="form-group">
                                <label>
                                    <input 
                                        type="checkbox"
                                        checked={newServerConfig.enabled}
                                        onChange={(e) => setNewServerConfig({
                                            ...newServerConfig,
                                            enabled: e.target.checked
                                        })}
                                    />
                                    Enable server
                                </label>
                            </div>

                            <div className="form-group">
                                <label>
                                    <input 
                                        type="checkbox"
                                        checked={newServerConfig.autoStart}
                                        onChange={(e) => setNewServerConfig({
                                            ...newServerConfig,
                                            autoStart: e.target.checked
                                        })}
                                    />
                                    Auto-start on extension load
                                </label>
                            </div>
                        </div>

                        <div className="modal-footer">
                            <button 
                                className="btn btn-secondary"
                                onClick={() => setShowAddDialog(false)}
                            >
                                Cancel
                            </button>
                            <button 
                                className="btn btn-primary"
                                onClick={handleAddServer}
                            >
                                Add Server
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>

        <style>{`
            .mcp-server-manager {
                padding: 20px;
                max-width: 1200px;
                margin: 0 auto;
            }

            .mcp-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;
                padding-bottom: 16px;
                border-bottom: 1px solid var(--vscode-panel-border);
            }

            .mcp-header h2 {
                margin: 0;
                color: var(--vscode-foreground);
                font-size: 24px;
                font-weight: 600;
            }

            .mcp-content {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
            }

            .server-list h3 {
                margin: 0 0 16px 0;
                color: var(--vscode-foreground);
                font-size: 18px;
                font-weight: 500;
            }

            .empty-state {
                text-align: center;
                padding: 40px 20px;
                color: var(--vscode-descriptionForeground);
                background: var(--vscode-editor-background);
                border-radius: 8px;
                border: 1px dashed var(--vscode-panel-border);
            }

            .server-grid {
                display: flex;
                flex-direction: column;
                gap: 12px;
            }

            .server-card {
                background: var(--vscode-editor-background);
                border: 1px solid var(--vscode-panel-border);
                border-radius: 8px;
                padding: 16px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .server-card:hover {
                border-color: var(--vscode-focusBorder);
                background: var(--vscode-list-hoverBackground);
            }

            .server-card.selected {
                border-color: var(--vscode-focusBorder);
                background: var(--vscode-list-activeSelectionBackground);
            }

            .server-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
            }

            .server-name {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 500;
                color: var(--vscode-foreground);
            }

            .status-icon {
                font-size: 12px;
            }

            .server-actions {
                display: flex;
                gap: 8px;
            }

            .server-info {
                display: flex;
                flex-direction: column;
                gap: 4px;
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
            }

            .status-text.connected {
                color: var(--vscode-terminal-ansiGreen);
            }

            .status-text.error {
                color: var(--vscode-errorForeground);
            }

            .status-text.connecting {
                color: var(--vscode-terminal-ansiYellow);
            }

            .server-stats {
                display: flex;
                gap: 16px;
                margin-top: 12px;
                padding-top: 12px;
                border-top: 1px solid var(--vscode-panel-border);
            }

            .stat {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 2px;
            }

            .stat .label {
                font-size: 10px;
                color: var(--vscode-descriptionForeground);
                text-transform: uppercase;
            }

            .stat .value {
                font-size: 14px;
                font-weight: 600;
                color: var(--vscode-foreground);
            }

            .server-details {
                background: var(--vscode-editor-background);
                border: 1px solid var(--vscode-panel-border);
                border-radius: 8px;
                padding: 20px;
                max-height: 600px;
                overflow-y: auto;
            }

            .server-details h3 {
                margin: 0 0 20px 0;
                color: var(--vscode-foreground);
                font-size: 18px;
                font-weight: 500;
            }

            .server-details h4 {
                margin: 20px 0 12px 0;
                color: var(--vscode-foreground);
                font-size: 14px;
                font-weight: 500;
            }

            .tools-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 12px;
            }

            .tool-card {
                background: var(--vscode-input-background);
                border: 1px solid var(--vscode-input-border);
                border-radius: 6px;
                padding: 12px;
            }

            .tool-name {
                font-weight: 500;
                color: var(--vscode-foreground);
                margin-bottom: 4px;
            }

            .tool-description {
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
                margin-bottom: 8px;
                line-height: 1.4;
            }

            .resources-list, .prompts-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .resource-item, .prompt-item {
                background: var(--vscode-input-background);
                border: 1px solid var(--vscode-input-border);
                border-radius: 6px;
                padding: 12px;
            }

            .resource-name, .prompt-name {
                font-weight: 500;
                color: var(--vscode-foreground);
                margin-bottom: 4px;
            }

            .resource-uri {
                font-family: var(--vscode-editor-font-family);
                font-size: 11px;
                color: var(--vscode-textLink-foreground);
                margin-bottom: 4px;
            }

            .resource-description, .prompt-description {
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
                line-height: 1.4;
            }

            .prompt-args {
                font-size: 11px;
                color: var(--vscode-descriptionForeground);
                font-style: italic;
            }

            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }

            .modal {
                background: var(--vscode-editor-background);
                border: 1px solid var(--vscode-panel-border);
                border-radius: 8px;
                width: 500px;
                max-width: 90vw;
                max-height: 90vh;
                overflow-y: auto;
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid var(--vscode-panel-border);
            }

            .modal-header h3 {
                margin: 0;
                color: var(--vscode-foreground);
                font-size: 18px;
                font-weight: 500;
            }

            .modal-body {
                padding: 20px;
            }

            .modal-footer {
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                padding: 20px;
                border-top: 1px solid var(--vscode-panel-border);
            }

            .form-group {
                margin-bottom: 16px;
            }

            .form-group label {
                display: block;
                margin-bottom: 6px;
                color: var(--vscode-foreground);
                font-size: 13px;
                font-weight: 500;
            }

            .form-group input[type="text"],
            .form-group input[type="url"],
            .form-group select {
                width: 100%;
                padding: 8px 12px;
                background: var(--vscode-input-background);
                border: 1px solid var(--vscode-input-border);
                border-radius: 4px;
                color: var(--vscode-input-foreground);
                font-size: 13px;
            }

            .form-group input[type="text"]:focus,
            .form-group input[type="url"]:focus,
            .form-group select:focus {
                outline: none;
                border-color: var(--vscode-focusBorder);
            }

            .form-group input[type="checkbox"] {
                margin-right: 8px;
            }

            .btn {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 6px;
            }

            .btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .btn-primary {
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
            }

            .btn-primary:hover:not(:disabled) {
                background: var(--vscode-button-hoverBackground);
            }

            .btn-secondary {
                background: var(--vscode-button-secondaryBackground);
                color: var(--vscode-button-secondaryForeground);
            }

            .btn-secondary:hover:not(:disabled) {
                background: var(--vscode-button-secondaryHoverBackground);
            }

            .btn-danger {
                background: var(--vscode-errorForeground);
                color: var(--vscode-editor-background);
            }

            .btn-danger:hover:not(:disabled) {
                background: var(--vscode-errorForeground);
                opacity: 0.8;
            }

            .btn-sm {
                padding: 4px 8px;
                font-size: 11px;
            }

            @media (max-width: 768px) {
                .mcp-content {
                    grid-template-columns: 1fr;
                }
            }
        `}</style>
        </>
    );
};
