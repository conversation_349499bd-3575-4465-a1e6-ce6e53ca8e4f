/**
 * Aizen AI MCP Configuration Editor
 * Beautiful UI for configuring MCP servers with real-time validation
 */

class MCPConfigEditor {
    constructor() {
        this.vscode = acquireVsCodeApi();
        this.currentConfig = { mcpServers: {} };
        this.selectedServer = null;
        this.isDirty = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadConfiguration();
    }

    setupEventListeners() {
        // Header actions
        document.getElementById('addServerBtn').addEventListener('click', () => this.addNewServer());
        document.getElementById('importBtn').addEventListener('click', () => this.importConfiguration());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportConfiguration());
        document.getElementById('saveBtn').addEventListener('click', () => this.saveConfiguration());

        // Listen for messages from VS Code
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.type) {
                case 'configurationLoaded':
                    this.handleConfigurationLoaded(message.config);
                    break;
                case 'configurationSaved':
                    this.handleConfigurationSaved();
                    break;
                case 'validationResult':
                    this.handleValidationResult(message.result);
                    break;
            }
        });
    }

    loadConfiguration() {
        this.vscode.postMessage({
            type: 'loadConfiguration'
        });
    }

    handleConfigurationLoaded(config) {
        this.currentConfig = config || { mcpServers: {} };
        this.renderServerList();
        this.updateJsonPreview();
    }

    renderServerList() {
        const serverList = document.getElementById('serverList');
        serverList.innerHTML = '';

        const servers = Object.keys(this.currentConfig.mcpServers || {});
        
        if (servers.length === 0) {
            serverList.innerHTML = '<li style="text-align: center; color: var(--text-secondary); padding: 20px;">No servers configured</li>';
            return;
        }

        servers.forEach(serverId => {
            const server = this.currentConfig.mcpServers[serverId];
            const serverItem = document.createElement('li');
            serverItem.className = 'server-item';
            serverItem.dataset.serverId = serverId;
            
            const isDisabled = server.disabled === true;
            const icon = this.getServerIcon(server);
            
            serverItem.innerHTML = `
                <div class="server-info">
                    <span class="server-icon">${icon}</span>
                    <span class="server-name">${serverId}</span>
                </div>
                <div class="server-status ${isDisabled ? 'disabled' : ''}"></div>
            `;
            
            serverItem.addEventListener('click', () => this.selectServer(serverId));
            serverList.appendChild(serverItem);
        });
    }

    getServerIcon(server) {
        if (server.command) {
            const cmd = server.command.toLowerCase();
            if (cmd.includes('node') || cmd.includes('npm') || cmd.includes('npx')) return '🟢';
            if (cmd.includes('python')) return '🐍';
            if (cmd.includes('docker')) return '🐳';
            if (cmd.includes('bun')) return '🥖';
            return '⚡';
        }
        if (server.url || server.serverUrl) return '🌐';
        return '🔧';
    }

    selectServer(serverId) {
        // Update active state
        document.querySelectorAll('.server-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-server-id="${serverId}"]`).classList.add('active');
        
        this.selectedServer = serverId;
        this.renderServerEditor(serverId);
        this.showSaveButton();
    }

    renderServerEditor(serverId) {
        const server = this.currentConfig.mcpServers[serverId];
        const editorPanel = document.getElementById('serverEditor');
        const emptyState = document.getElementById('emptyState');
        
        emptyState.style.display = 'none';
        editorPanel.style.display = 'block';
        
        const isStdio = !!server.command;
        const isHttp = !!(server.url || server.serverUrl);
        
        editorPanel.innerHTML = `
            <h3>Configure ${serverId}</h3>
            
            <div class="form-group">
                <label class="form-label">Server ID</label>
                <input type="text" class="form-input" id="serverId" value="${serverId}" onchange="this.updateServerId(this.value)">
            </div>
            
            <div class="form-group">
                <label class="form-label">Transport Type</label>
                <select class="form-select" id="transportType" onchange="this.updateTransportType(this.value)">
                    <option value="stdio" ${isStdio ? 'selected' : ''}>STDIO (Command)</option>
                    <option value="http" ${isHttp ? 'selected' : ''}>HTTP/HTTPS</option>
                    <option value="sse" ${server.serverUrl ? 'selected' : ''}>Server-Sent Events</option>
                </select>
            </div>
            
            <div class="transport-config">
                ${this.renderTransportConfig(server)}
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">
                        <input type="checkbox" ${server.disabled ? 'checked' : ''} onchange="this.updateServerProperty('disabled', this.checked)"> 
                        Disabled
                    </label>
                </div>
                <div class="form-group">
                    <label class="form-label">Auto Approve Tools</label>
                    <input type="text" class="form-input" placeholder="tool1,tool2,tool3" 
                           value="${(server.autoApprove || []).join(',')}"
                           onchange="this.updateAutoApprove(this.value)">
                </div>
            </div>
            
            <div class="form-group">
                <button class="btn btn-secondary" onclick="this.duplicateServer()">📋 Duplicate</button>
                <button class="btn" style="background: var(--error-color); color: white;" onclick="this.deleteServer()">🗑️ Delete</button>
            </div>
        `;
        
        this.updateJsonPreview();
    }

    renderTransportConfig(server) {
        if (server.command) {
            return `
                <h4>STDIO Configuration</h4>
                <div class="form-group">
                    <label class="form-label">Command</label>
                    <input type="text" class="form-input" value="${server.command}" 
                           onchange="this.updateServerProperty('command', this.value)">
                </div>
                <div class="form-group">
                    <label class="form-label">Arguments</label>
                    <input type="text" class="form-input" placeholder="arg1,arg2,arg3" 
                           value="${(server.args || []).join(',')}"
                           onchange="this.updateArgs(this.value)">
                </div>
                <div class="form-group">
                    <label class="form-label">Working Directory</label>
                    <input type="text" class="form-input" value="${server.cwd || ''}" 
                           onchange="this.updateServerProperty('cwd', this.value)">
                </div>
                ${this.renderEnvVars(server.env || {})}
            `;
        } else if (server.url || server.serverUrl) {
            return `
                <h4>HTTP Configuration</h4>
                <div class="form-group">
                    <label class="form-label">URL</label>
                    <input type="url" class="form-input" value="${server.url || server.serverUrl || ''}" 
                           onchange="this.updateServerUrl(this.value)">
                </div>
                ${this.renderHeaders(server.headers || {})}
            `;
        }
        return '';
    }

    renderEnvVars(envVars) {
        const envEntries = Object.entries(envVars);
        return `
            <div class="env-vars">
                <label class="form-label">Environment Variables</label>
                <div id="envVarsList">
                    ${envEntries.map(([key, value], index) => `
                        <div class="env-var-item">
                            <input type="text" class="form-input" placeholder="Variable name" value="${key}" 
                                   onchange="this.updateEnvVar(${index}, 'key', this.value)">
                            <input type="text" class="form-input" placeholder="Variable value" value="${value}" 
                                   onchange="this.updateEnvVar(${index}, 'value', this.value)">
                            <button class="btn-remove" onclick="this.removeEnvVar(${index})">✕</button>
                        </div>
                    `).join('')}
                </div>
                <button class="btn-add" onclick="this.addEnvVar()">+ Add Variable</button>
            </div>
        `;
    }

    renderHeaders(headers) {
        const headerEntries = Object.entries(headers);
        return `
            <div class="env-vars">
                <label class="form-label">HTTP Headers</label>
                <div id="headersList">
                    ${headerEntries.map(([key, value], index) => `
                        <div class="env-var-item">
                            <input type="text" class="form-input" placeholder="Header name" value="${key}" 
                                   onchange="this.updateHeader(${index}, 'key', this.value)">
                            <input type="text" class="form-input" placeholder="Header value" value="${value}" 
                                   onchange="this.updateHeader(${index}, 'value', this.value)">
                            <button class="btn-remove" onclick="this.removeHeader(${index})">✕</button>
                        </div>
                    `).join('')}
                </div>
                <button class="btn-add" onclick="this.addHeader()">+ Add Header</button>
            </div>
        `;
    }

    updateServerId(newId) {
        if (newId && newId !== this.selectedServer) {
            const server = this.currentConfig.mcpServers[this.selectedServer];
            delete this.currentConfig.mcpServers[this.selectedServer];
            this.currentConfig.mcpServers[newId] = server;
            this.selectedServer = newId;
            this.markDirty();
            this.renderServerList();
            this.selectServer(newId);
        }
    }

    updateTransportType(type) {
        const server = this.currentConfig.mcpServers[this.selectedServer];
        
        // Clear existing transport config
        delete server.command;
        delete server.args;
        delete server.cwd;
        delete server.env;
        delete server.url;
        delete server.serverUrl;
        delete server.headers;
        
        // Set default for new transport type
        if (type === 'stdio') {
            server.command = 'npx';
            server.args = [];
        } else if (type === 'http') {
            server.url = 'https://';
        } else if (type === 'sse') {
            server.serverUrl = 'https://';
        }
        
        this.markDirty();
        this.renderServerEditor(this.selectedServer);
    }

    updateServerProperty(property, value) {
        if (value === '') {
            delete this.currentConfig.mcpServers[this.selectedServer][property];
        } else {
            this.currentConfig.mcpServers[this.selectedServer][property] = value;
        }
        this.markDirty();
        this.updateJsonPreview();
    }

    updateArgs(argsString) {
        const args = argsString.split(',').map(arg => arg.trim()).filter(arg => arg);
        this.currentConfig.mcpServers[this.selectedServer].args = args;
        this.markDirty();
        this.updateJsonPreview();
    }

    updateAutoApprove(toolsString) {
        const tools = toolsString.split(',').map(tool => tool.trim()).filter(tool => tool);
        if (tools.length > 0) {
            this.currentConfig.mcpServers[this.selectedServer].autoApprove = tools;
        } else {
            delete this.currentConfig.mcpServers[this.selectedServer].autoApprove;
        }
        this.markDirty();
        this.updateJsonPreview();
    }

    updateServerUrl(url) {
        const server = this.currentConfig.mcpServers[this.selectedServer];
        if (server.serverUrl !== undefined) {
            server.serverUrl = url;
        } else {
            server.url = url;
        }
        this.markDirty();
        this.updateJsonPreview();
    }

    addNewServer() {
        const serverId = prompt('Enter server ID (alphanumeric, underscore, hyphen only):');
        if (!serverId || !/^[a-zA-Z0-9_-]+$/.test(serverId)) {
            alert('Invalid server ID. Use only alphanumeric characters, underscores, and hyphens.');
            return;
        }
        
        if (this.currentConfig.mcpServers[serverId]) {
            alert('Server ID already exists. Choose a different ID.');
            return;
        }
        
        this.currentConfig.mcpServers[serverId] = {
            command: 'npx',
            args: []
        };
        
        this.markDirty();
        this.renderServerList();
        this.selectServer(serverId);
    }

    duplicateServer() {
        const newId = prompt('Enter new server ID:');
        if (!newId || !/^[a-zA-Z0-9_-]+$/.test(newId)) {
            alert('Invalid server ID.');
            return;
        }
        
        if (this.currentConfig.mcpServers[newId]) {
            alert('Server ID already exists.');
            return;
        }
        
        this.currentConfig.mcpServers[newId] = JSON.parse(JSON.stringify(this.currentConfig.mcpServers[this.selectedServer]));
        this.markDirty();
        this.renderServerList();
        this.selectServer(newId);
    }

    deleteServer() {
        if (confirm(`Are you sure you want to delete server "${this.selectedServer}"?`)) {
            delete this.currentConfig.mcpServers[this.selectedServer];
            this.selectedServer = null;
            this.markDirty();
            this.renderServerList();
            document.getElementById('emptyState').style.display = 'block';
            document.getElementById('serverEditor').style.display = 'none';
            this.updateJsonPreview();
        }
    }

    markDirty() {
        this.isDirty = true;
        this.showSaveButton();
        this.updateJsonPreview();
    }

    showSaveButton() {
        document.getElementById('saveBtn').style.display = 'block';
    }

    updateJsonPreview() {
        const preview = document.getElementById('jsonPreview');
        const formatted = JSON.stringify(this.currentConfig, null, 2);
        preview.textContent = formatted;
        
        // Validate configuration
        this.validateConfiguration();
    }

    validateConfiguration() {
        const status = document.getElementById('validationStatus');
        
        try {
            // Basic validation
            if (!this.currentConfig.mcpServers) {
                throw new Error('mcpServers property is required');
            }
            
            for (const [serverId, server] of Object.entries(this.currentConfig.mcpServers)) {
                if (!serverId.match(/^[a-zA-Z0-9_-]+$/)) {
                    throw new Error(`Invalid server ID: ${serverId}`);
                }
                
                if (server.command && !server.command.trim()) {
                    throw new Error(`Server ${serverId}: command cannot be empty`);
                }
                
                if ((server.url || server.serverUrl) && !(server.url || server.serverUrl).startsWith('http')) {
                    throw new Error(`Server ${serverId}: URL must start with http:// or https://`);
                }
            }
            
            status.className = 'validation-status valid';
            status.innerHTML = '✅ Configuration is valid';
            
        } catch (error) {
            status.className = 'validation-status invalid';
            status.innerHTML = `❌ ${error.message}`;
        }
    }

    saveConfiguration() {
        this.vscode.postMessage({
            type: 'saveConfiguration',
            config: this.currentConfig
        });
    }

    handleConfigurationSaved() {
        this.isDirty = false;
        document.getElementById('saveBtn').style.display = 'none';
        
        // Show success message
        const saveBtn = document.getElementById('saveBtn');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = '✅ Saved!';
        saveBtn.style.background = 'var(--success-color)';
        
        setTimeout(() => {
            saveBtn.textContent = originalText;
            saveBtn.style.background = '';
        }, 2000);
    }

    importConfiguration() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const config = JSON.parse(e.target.result);
                        this.currentConfig = config;
                        this.markDirty();
                        this.renderServerList();
                        this.updateJsonPreview();
                    } catch (error) {
                        alert('Invalid JSON file: ' + error.message);
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    exportConfiguration() {
        const dataStr = JSON.stringify(this.currentConfig, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'aizen_mcp_config.json';
        link.click();
        URL.revokeObjectURL(url);
    }
}

// Initialize the editor when the page loads
window.addEventListener('DOMContentLoaded', () => {
    window.mcpEditor = new MCPConfigEditor();
});

    // Environment variables management
    addEnvVar() {
        const server = this.currentConfig.mcpServers[this.selectedServer];
        if (!server.env) server.env = {};

        const key = prompt('Environment variable name:');
        if (key && key.match(/^[A-Z_][A-Z0-9_]*$/)) {
            server.env[key] = '';
            this.markDirty();
            this.renderServerEditor(this.selectedServer);
        } else if (key) {
            alert('Invalid environment variable name. Use uppercase letters, numbers, and underscores only.');
        }
    }

    removeEnvVar(index) {
        const server = this.currentConfig.mcpServers[this.selectedServer];
        const keys = Object.keys(server.env || {});
        if (keys[index]) {
            delete server.env[keys[index]];
            if (Object.keys(server.env).length === 0) {
                delete server.env;
            }
            this.markDirty();
            this.renderServerEditor(this.selectedServer);
        }
    }

    updateEnvVar(index, type, value) {
        const server = this.currentConfig.mcpServers[this.selectedServer];
        const keys = Object.keys(server.env || {});
        const oldKey = keys[index];

        if (type === 'key' && value !== oldKey) {
            if (value.match(/^[A-Z_][A-Z0-9_]*$/)) {
                const oldValue = server.env[oldKey];
                delete server.env[oldKey];
                server.env[value] = oldValue;
                this.markDirty();
                this.renderServerEditor(this.selectedServer);
            } else {
                alert('Invalid environment variable name.');
                return;
            }
        } else if (type === 'value') {
            server.env[oldKey] = value;
            this.markDirty();
            this.updateJsonPreview();
        }
    }

    // HTTP headers management
    addHeader() {
        const server = this.currentConfig.mcpServers[this.selectedServer];
        if (!server.headers) server.headers = {};

        const key = prompt('Header name:');
        if (key && key.match(/^[A-Za-z0-9-]+$/)) {
            server.headers[key] = '';
            this.markDirty();
            this.renderServerEditor(this.selectedServer);
        } else if (key) {
            alert('Invalid header name. Use letters, numbers, and hyphens only.');
        }
    }

    removeHeader(index) {
        const server = this.currentConfig.mcpServers[this.selectedServer];
        const keys = Object.keys(server.headers || {});
        if (keys[index]) {
            delete server.headers[keys[index]];
            if (Object.keys(server.headers).length === 0) {
                delete server.headers;
            }
            this.markDirty();
            this.renderServerEditor(this.selectedServer);
        }
    }

    updateHeader(index, type, value) {
        const server = this.currentConfig.mcpServers[this.selectedServer];
        const keys = Object.keys(server.headers || {});
        const oldKey = keys[index];

        if (type === 'key' && value !== oldKey) {
            if (value.match(/^[A-Za-z0-9-]+$/)) {
                const oldValue = server.headers[oldKey];
                delete server.headers[oldKey];
                server.headers[value] = oldValue;
                this.markDirty();
                this.renderServerEditor(this.selectedServer);
            } else {
                alert('Invalid header name.');
                return;
            }
        } else if (type === 'value') {
            server.headers[oldKey] = value;
            this.markDirty();
            this.updateJsonPreview();
        }
    }
}

// Make methods available globally for inline event handlers
window.updateServerId = function(value) { window.mcpEditor.updateServerId(value); };
window.updateTransportType = function(value) { window.mcpEditor.updateTransportType(value); };
window.updateServerProperty = function(prop, value) { window.mcpEditor.updateServerProperty(prop, value); };
window.updateArgs = function(value) { window.mcpEditor.updateArgs(value); };
window.updateAutoApprove = function(value) { window.mcpEditor.updateAutoApprove(value); };
window.updateServerUrl = function(value) { window.mcpEditor.updateServerUrl(value); };
window.duplicateServer = function() { window.mcpEditor.duplicateServer(); };
window.deleteServer = function() { window.mcpEditor.deleteServer(); };
window.addEnvVar = function() { window.mcpEditor.addEnvVar(); };
window.removeEnvVar = function(index) { window.mcpEditor.removeEnvVar(index); };
window.updateEnvVar = function(index, type, value) { window.mcpEditor.updateEnvVar(index, type, value); };
window.addHeader = function() { window.mcpEditor.addHeader(); };
window.removeHeader = function(index) { window.mcpEditor.removeHeader(index); };
window.updateHeader = function(index, type, value) { window.mcpEditor.updateHeader(index, type, value); };
