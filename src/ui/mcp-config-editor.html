<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aizen AI MCP Configuration Editor</title>
    <style>
        :root {
            --primary-color: #007acc;
            --secondary-color: #1e1e1e;
            --background-color: #0d1117;
            --surface-color: #161b22;
            --border-color: #30363d;
            --text-primary: #f0f6fc;
            --text-secondary: #8b949e;
            --success-color: #238636;
            --warning-color: #d29922;
            --error-color: #da3633;
            --glass-bg: rgba(22, 27, 34, 0.8);
            --glass-border: rgba(240, 246, 252, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 20px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 600;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-color), #00d4ff);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #005a9e;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--surface-color);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            min-height: 600px;
        }

        .sidebar {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            height: fit-content;
        }

        .sidebar h3 {
            margin-bottom: 16px;
            color: var(--text-primary);
            font-size: 16px;
        }

        .server-list {
            list-style: none;
        }

        .server-item {
            padding: 12px;
            margin-bottom: 8px;
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .server-item:hover {
            background: var(--border-color);
            transform: translateX(4px);
        }

        .server-item.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .server-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .server-icon {
            font-size: 16px;
        }

        .server-name {
            font-weight: 500;
            font-size: 14px;
        }

        .server-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
        }

        .server-status.disabled {
            background: var(--text-secondary);
        }

        .editor-panel {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 10px 12px;
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
        }

        .form-select {
            width: 100%;
            padding: 10px 12px;
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 14px;
        }

        .form-textarea {
            width: 100%;
            min-height: 80px;
            padding: 10px 12px;
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 14px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .transport-config {
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin-top: 12px;
        }

        .transport-config h4 {
            margin-bottom: 12px;
            color: var(--text-primary);
            font-size: 14px;
        }

        .env-vars {
            margin-top: 12px;
        }

        .env-var-item {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 8px;
            margin-bottom: 8px;
            align-items: center;
        }

        .btn-remove {
            background: var(--error-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 8px;
            cursor: pointer;
            font-size: 12px;
        }

        .btn-add {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 8px;
        }

        .preview-panel {
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
        }

        .preview-panel h4 {
            margin-bottom: 12px;
            color: var(--text-primary);
        }

        .json-preview {
            background: #0d1117;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            color: var(--text-primary);
            overflow-x: auto;
            white-space: pre;
        }

        .validation-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 12px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
        }

        .validation-status.valid {
            background: rgba(35, 134, 54, 0.2);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .validation-status.invalid {
            background: rgba(218, 54, 51, 0.2);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        .floating-save {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 24px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
            z-index: 1000;
        }

        .floating-save:hover {
            background: #2ea043;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state h3 {
            margin-bottom: 12px;
            color: var(--text-primary);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <div class="logo">A</div>
                MCP Configuration Editor
            </h1>
            <div class="actions">
                <button class="btn btn-secondary" id="importBtn">
                    📥 Import
                </button>
                <button class="btn btn-secondary" id="exportBtn">
                    📤 Export
                </button>
                <button class="btn btn-primary" id="addServerBtn">
                    ➕ Add Server
                </button>
            </div>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <h3>MCP Servers</h3>
                <ul class="server-list" id="serverList">
                    <!-- Server items will be populated here -->
                </ul>
            </div>

            <div class="editor-panel">
                <div id="emptyState" class="empty-state">
                    <h3>No Server Selected</h3>
                    <p>Select a server from the sidebar to edit its configuration, or add a new server to get started.</p>
                </div>

                <div id="serverEditor" style="display: none;">
                    <!-- Server editor form will be populated here -->
                </div>

                <div class="preview-panel">
                    <h4>JSON Preview</h4>
                    <div class="json-preview" id="jsonPreview">
                        // Select a server to see its JSON configuration
                    </div>
                    <div class="validation-status valid" id="validationStatus">
                        ✅ Configuration is valid
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="floating-save" id="saveBtn" style="display: none;">
        💾 Save Configuration
    </button>

    <script src="mcp-config-editor.js"></script>
</body>
</html>
