<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src {{CSP_SOURCE}} 'unsafe-inline'; script-src {{CSP_SOURCE}} 'nonce-{{NONCE}}'; font-src {{CSP_SOURCE}} https:; img-src {{CSP_SOURCE}} data: https:;">
    
    <title>Aizen AI Settings</title>
    
    <!-- Use system fonts for better performance and CSP compliance -->
    <link rel="stylesheet" href="{{CSS_URI}}">
    <link rel="stylesheet" href="{{SETTINGS_CSS_URI}}">

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="color-scheme" content="dark light">
</head>
<body class="settings-body">
    <div class="settings-container">
        <!-- Settings Header -->
        <header class="settings-header glass" role="banner">
            <div class="header-content">
                <div class="header-left">
                    <button class="back-btn glass" id="backButton" aria-label="Back to chat" title="Back to chat">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M19 12H5"/>
                            <path d="M12 19l-7-7 7-7"/>
                        </svg>
                    </button>
                    <div class="header-title">
                        <h1>Aizen AI Settings</h1>
                        <p>Configure your AI assistant</p>
                    </div>
                </div>
                <div class="header-right">
                    <button class="save-btn glass" id="saveButton" aria-label="Save settings" title="Save all settings">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                            <polyline points="17,21 17,13 7,13 7,21"/>
                            <polyline points="7,3 7,8 15,8"/>
                        </svg>
                        <span>Save</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Settings Content -->
        <main class="settings-main" role="main">
            <div class="settings-grid">
                <!-- AI Model Configuration -->
                <section class="settings-section glass" aria-labelledby="ai-model-title">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="11" width="18" height="10" rx="2" ry="2"/>
                                <circle cx="12" cy="5" r="2"/>
                                <path d="M12 7v4"/>
                                <line x1="8" y1="16" x2="8" y2="16"/>
                                <line x1="16" y1="16" x2="16" y2="16"/>
                            </svg>
                        </div>
                        <h2 id="ai-model-title">AI Model Configuration</h2>
                    </div>
                    <div class="section-content">
                        <div class="setting-group">
                            <label for="modelProvider" class="setting-label">Model Provider</label>
                            <select id="modelProvider" class="setting-select glass">
                                <option value="openai">OpenAI</option>
                                <option value="anthropic">Anthropic</option>
                                <option value="google">Google</option>
                                <option value="local">Local Model</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label for="modelName" class="setting-label">Model Name</label>
                            <select id="modelName" class="setting-select glass">
                                <option value="gpt-4">GPT-4</option>
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                <option value="claude-3-opus">Claude 3 Opus</option>
                                <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label for="temperature" class="setting-label">Temperature</label>
                            <div class="slider-container">
                                <input type="range" id="temperature" class="setting-slider" min="0" max="2" step="0.1" value="0.7">
                                <span class="slider-value" id="temperatureValue">0.7</span>
                            </div>
                        </div>
                        <div class="setting-group">
                            <label for="maxTokens" class="setting-label">Max Tokens</label>
                            <input type="number" id="maxTokens" class="setting-input glass" value="4096" min="1" max="32768">
                        </div>
                    </div>
                </section>

                <!-- MCP Server Settings -->
                <section class="settings-section glass" aria-labelledby="mcp-title">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                                <path d="M2 17l10 5 10-5"/>
                                <path d="M2 12l10 5 10-5"/>
                            </svg>
                        </div>
                        <h2 id="mcp-title">MCP Server Configuration</h2>
                    </div>
                    <div class="section-content">
                        <div class="setting-group">
                            <div class="setting-header">
                                <label class="setting-label">Enabled MCP Servers</label>
                                <button class="add-btn glass" id="addMcpServer" aria-label="Add MCP server">
                                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="12" y1="5" x2="12" y2="19"/>
                                        <line x1="5" y1="12" x2="19" y2="12"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="mcp-servers-list" id="mcpServersList">
                                <!-- Built-in MCP Servers -->
                                <div class="mcp-server-card" data-server="exa">
                                    <div class="server-header">
                                        <div class="server-info">
                                            <h4>🔍 Exa Search</h4>
                                            <p>Advanced web search and research capabilities</p>
                                        </div>
                                        <span class="status-badge status-unconfigured" id="exaStatus">Not Configured</span>
                                    </div>
                                    <div class="server-actions">
                                        <button class="btn btn-small" onclick="configureServer('exa')">Configure API Key</button>
                                        <button class="btn btn-small btn-secondary" onclick="testServer('exa')" disabled>Test Connection</button>
                                        <button class="btn btn-small btn-danger" onclick="removeServer('exa')" style="display: none;">Remove</button>
                                    </div>
                                </div>

                                <div class="mcp-server-card" data-server="firecrawl">
                                    <div class="server-header">
                                        <div class="server-info">
                                            <h4>🕷️ Firecrawl</h4>
                                            <p>Web scraping and content extraction</p>
                                        </div>
                                        <span class="status-badge status-unconfigured" id="firecrawlStatus">Not Configured</span>
                                    </div>
                                    <div class="server-actions">
                                        <button class="btn btn-small" onclick="configureServer('firecrawl')">Configure API Key</button>
                                        <button class="btn btn-small btn-secondary" onclick="testServer('firecrawl')" disabled>Test Connection</button>
                                        <button class="btn btn-small btn-danger" onclick="removeServer('firecrawl')" style="display: none;">Remove</button>
                                    </div>
                                </div>

                                <!-- Custom servers will be added here dynamically -->
                            </div>
                        </div>
                    </div>
                </section>

                <!-- UI Theme Preferences -->
                <section class="settings-section glass" aria-labelledby="theme-title">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="5"/>
                                <line x1="12" y1="1" x2="12" y2="3"/>
                                <line x1="12" y1="21" x2="12" y2="23"/>
                                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
                                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
                                <line x1="1" y1="12" x2="3" y2="12"/>
                                <line x1="21" y1="12" x2="23" y2="12"/>
                                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
                                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
                            </svg>
                        </div>
                        <h2 id="theme-title">UI Theme & Appearance</h2>
                    </div>
                    <div class="section-content">
                        <div class="setting-group">
                            <label for="themeMode" class="setting-label">Theme Mode</label>
                            <div class="radio-group">
                                <label class="radio-option">
                                    <input type="radio" name="theme" value="auto" checked>
                                    <span class="radio-custom"></span>
                                    <span>Auto</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="theme" value="light">
                                    <span class="radio-custom"></span>
                                    <span>Light</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="theme" value="dark">
                                    <span class="radio-custom"></span>
                                    <span>Dark</span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-group">
                            <label for="accentColor" class="setting-label">Accent Color</label>
                            <div class="color-picker-container">
                                <input type="color" id="accentColor" class="color-picker" value="#3b82f6">
                                <div class="color-presets">
                                    <button class="color-preset" data-color="#3b82f6" style="background: #3b82f6"></button>
                                    <button class="color-preset" data-color="#8b5cf6" style="background: #8b5cf6"></button>
                                    <button class="color-preset" data-color="#06b6d4" style="background: #06b6d4"></button>
                                    <button class="color-preset" data-color="#10b981" style="background: #10b981"></button>
                                    <button class="color-preset" data-color="#f59e0b" style="background: #f59e0b"></button>
                                    <button class="color-preset" data-color="#ef4444" style="background: #ef4444"></button>
                                </div>
                            </div>
                        </div>
                        <div class="setting-group">
                            <label for="glassIntensity" class="setting-label">Glass Effect Intensity</label>
                            <div class="slider-container">
                                <input type="range" id="glassIntensity" class="setting-slider" min="0" max="100" step="5" value="80">
                                <span class="slider-value" id="glassIntensityValue">80%</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Advanced Mode Settings -->
                <section class="settings-section glass" aria-labelledby="advanced-title">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                            </svg>
                        </div>
                        <h2 id="advanced-title">Advanced Settings</h2>
                    </div>
                    <div class="section-content">
                        <div class="setting-group">
                            <div class="toggle-setting">
                                <div class="toggle-info">
                                    <label for="debugMode" class="setting-label">Debug Mode</label>
                                    <p class="setting-description">Enable detailed logging and debug information</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="debugMode">
                                    <span class="toggle-slider glass"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-group">
                            <div class="toggle-setting">
                                <div class="toggle-info">
                                    <label for="autoSave" class="setting-label">Auto-save Conversations</label>
                                    <p class="setting-description">Automatically save chat history</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="autoSave" checked>
                                    <span class="toggle-slider glass"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-group">
                            <div class="toggle-setting">
                                <div class="toggle-info">
                                    <label for="streamingMode" class="setting-label">Streaming Responses</label>
                                    <p class="setting-description">Show responses as they are generated</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="streamingMode" checked>
                                    <span class="toggle-slider glass"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Extension Preferences -->
                <section class="settings-section glass" aria-labelledby="extension-title">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"/>
                                <line x1="16" y1="8" x2="2" y2="22"/>
                                <line x1="17.5" y1="15" x2="9" y2="15"/>
                            </svg>
                        </div>
                        <h2 id="extension-title">Extension Preferences</h2>
                    </div>
                    <div class="section-content">
                        <div class="setting-group">
                            <label for="defaultMode" class="setting-label">Default AI Mode</label>
                            <select id="defaultMode" class="setting-select glass">
                                <option value="auto">Auto Mode</option>
                                <option value="research">Research Mode</option>
                                <option value="debug">Debug Mode</option>
                                <option value="deep">Deep Mode</option>
                                <option value="mcp">MCP Mode</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label for="shortcutKey" class="setting-label">Keyboard Shortcut</label>
                            <div class="shortcut-input">
                                <input type="text" id="shortcutKey" class="setting-input glass" value="Ctrl+Shift+A" readonly>
                                <button class="change-shortcut-btn glass" id="changeShortcut">Change</button>
                            </div>
                        </div>
                        <div class="setting-group">
                            <div class="toggle-setting">
                                <div class="toggle-info">
                                    <label for="startupOpen" class="setting-label">Open on Startup</label>
                                    <p class="setting-description">Automatically open Aizen AI when VS Code starts</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="startupOpen">
                                    <span class="toggle-slider glass"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- Settings Footer -->
        <footer class="settings-footer glass" role="contentinfo">
            <div class="footer-content">
                <div class="footer-left">
                    <button class="reset-btn glass" id="resetButton" aria-label="Reset to defaults">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="1,4 1,10 7,10"/>
                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                        </svg>
                        Reset to Defaults
                    </button>
                </div>
                <div class="footer-right">
                    <span class="version-info">Aizen AI v2.0.0</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- Modern script loading -->
    <script nonce="{{NONCE}}" src="{{SETTINGS_SCRIPT_URI}}" defer></script>
    
    <!-- Skip link for accessibility -->
    <a href="#saveButton" class="sr-only focus-visible">Skip to save button</a>
</body>
</html>
