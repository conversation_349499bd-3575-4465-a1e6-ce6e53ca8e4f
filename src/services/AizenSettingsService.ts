/**
 * Aizen AI Settings Service
 * Manages extension configuration using VS Code's native settings system
 */

import * as vscode from 'vscode';

export interface AizenSettings {
    // Model Configuration
    model: {
        provider: 'Anthropic' | 'OpenAI' | 'Google' | 'Local';
        name: string;
        temperature: number;
        maxTokens: number;
    };
    
    // API Keys
    api: {
        anthropicKey: string;
        openaiKey: string;
        googleKey: string;
    };
    
    // MCP Configuration
    mcp: {
        exaEnabled: boolean;
        firecrawlEnabled: boolean;
        exaApiKey: string;
        firecrawlApiKey: string;
    };
    
    // UI Configuration
    ui: {
        theme: 'auto' | 'light' | 'dark';
        fontSize: 'small' | 'medium' | 'large';
    };
    
    // Features
    features: {
        autoSave: boolean;
        notifications: boolean;
    };
}

export class AizenSettingsService {
    private static _instance: AizenSettingsService;
    private _onDidChangeSettings = new vscode.EventEmitter<AizenSettings>();
    
    public readonly onDidChangeSettings = this._onDidChangeSettings.event;
    
    private constructor() {
        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration('aizen')) {
                this._onDidChangeSettings.fire(this.getSettings());
            }
        });
    }
    
    public static getInstance(): AizenSettingsService {
        if (!AizenSettingsService._instance) {
            AizenSettingsService._instance = new AizenSettingsService();
        }
        return AizenSettingsService._instance;
    }
    
    /**
     * Get all Aizen settings
     */
    public getSettings(): AizenSettings {
        const config = vscode.workspace.getConfiguration('aizen');
        
        return {
            model: {
                provider: config.get('model.provider', 'Anthropic'),
                name: config.get('model.name', 'claude-3-5-sonnet-20241022'),
                temperature: config.get('model.temperature', 0.7),
                maxTokens: config.get('model.maxTokens', 4096)
            },
            api: {
                anthropicKey: config.get('api.anthropicKey', ''),
                openaiKey: config.get('api.openaiKey', ''),
                googleKey: config.get('api.googleKey', '')
            },
            mcp: {
                exaEnabled: config.get('mcp.exaEnabled', true),
                firecrawlEnabled: config.get('mcp.firecrawlEnabled', true),
                exaApiKey: config.get('mcp.exaApiKey', ''),
                firecrawlApiKey: config.get('mcp.firecrawlApiKey', '')
            },
            ui: {
                theme: config.get('ui.theme', 'auto'),
                fontSize: config.get('ui.fontSize', 'medium')
            },
            features: {
                autoSave: config.get('features.autoSave', true),
                notifications: config.get('features.notifications', true)
            }
        };
    }
    
    /**
     * Update a specific setting
     */
    public async updateSetting(key: string, value: any, target?: vscode.ConfigurationTarget): Promise<void> {
        const config = vscode.workspace.getConfiguration('aizen');
        await config.update(key, value, target || vscode.ConfigurationTarget.Global);
        
        if (this.getSettings().features.notifications) {
            vscode.window.showInformationMessage(`Aizen AI: ${key} updated successfully`);
        }
    }
    
    /**
     * Update multiple settings at once
     */
    public async updateSettings(updates: Partial<Record<string, any>>, target?: vscode.ConfigurationTarget): Promise<void> {
        const config = vscode.workspace.getConfiguration('aizen');
        
        // Update all settings
        const promises = Object.entries(updates).map(([key, value]) => 
            config.update(key, value, target || vscode.ConfigurationTarget.Global)
        );
        
        await Promise.all(promises);
        
        if (this.getSettings().features.notifications) {
            vscode.window.showInformationMessage(`Aizen AI: Settings updated successfully`);
        }
    }
    
    /**
     * Reset all settings to defaults
     */
    public async resetSettings(): Promise<void> {
        const config = vscode.workspace.getConfiguration('aizen');
        const keys = [
            'model.provider', 'model.name', 'model.temperature', 'model.maxTokens',
            'api.anthropicKey', 'api.openaiKey', 'api.googleKey',
            'mcp.exaEnabled', 'mcp.firecrawlEnabled', 'mcp.exaApiKey', 'mcp.firecrawlApiKey',
            'ui.theme', 'ui.fontSize',
            'features.autoSave', 'features.notifications'
        ];
        
        const promises = keys.map(key => config.update(key, undefined, vscode.ConfigurationTarget.Global));
        await Promise.all(promises);
        
        vscode.window.showInformationMessage('Aizen AI: All settings reset to defaults');
    }
    
    /**
     * Get model-specific settings for AI integration
     */
    public getModelConfig() {
        const settings = this.getSettings();
        return {
            provider: settings.model.provider,
            model: settings.model.name,
            temperature: settings.model.temperature,
            maxTokens: settings.model.maxTokens,
            apiKey: this.getApiKeyForProvider(settings.model.provider, settings.api)
        };
    }
    
    /**
     * Get MCP server configuration
     */
    public getMcpConfig() {
        const settings = this.getSettings();
        return {
            exa: {
                enabled: settings.mcp.exaEnabled,
                apiKey: settings.mcp.exaApiKey
            },
            firecrawl: {
                enabled: settings.mcp.firecrawlEnabled,
                apiKey: settings.mcp.firecrawlApiKey
            }
        };
    }
    
    private getApiKeyForProvider(provider: string, apiKeys: AizenSettings['api']): string {
        switch (provider) {
            case 'Anthropic': return apiKeys.anthropicKey;
            case 'OpenAI': return apiKeys.openaiKey;
            case 'Google': return apiKeys.googleKey;
            default: return '';
        }
    }
    
    /**
     * Validate settings and show warnings for missing required values
     */
    public validateSettings(): { isValid: boolean; warnings: string[] } {
        const settings = this.getSettings();
        const warnings: string[] = [];
        
        // Check API key for selected provider
        const apiKey = this.getApiKeyForProvider(settings.model.provider, settings.api);
        if (!apiKey) {
            warnings.push(`Missing API key for ${settings.model.provider}`);
        }
        
        // MCP servers have built-in API keys, no validation needed
        // Built-in Exa and Firecrawl servers are pre-configured
        
        return {
            isValid: warnings.length === 0,
            warnings
        };
    }
    
    /**
     * Open VS Code settings to specific Aizen setting
     */
    public async openVSCodeSettings(settingId?: string): Promise<void> {
        const settingToOpen = settingId ? `aizen.${settingId}` : 'aizen';
        await vscode.commands.executeCommand('workbench.action.openSettings', settingToOpen);
    }
}
