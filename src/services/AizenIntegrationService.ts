/**
 * Aizen Integration Service
 * Bridges Rust backend, Python AI backend, React frontend, and MCP servers
 */

import * as vscode from 'vscode';
import * as path from 'path';
import * as child_process from 'child_process';
import axios from 'axios';
import WebSocket from 'ws';
import { AizenSettingsService } from './AizenSettingsService';

export interface AgentMetrics {
    totalAgents: number;
    activeAgents: number;
    completedTasks: number;
    failedTasks: number;
    averageExecutionTime: number;
    swarmEfficiency: number;
    evolutionScore: number;
}

export interface AgentInfo {
    id: string;
    name: string;
    type: string;
    status: 'idle' | 'busy' | 'error';
    currentTask?: string;
    capabilities: string[];
}

export interface AIResponse {
    content: string;
    model: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

export interface ConnectionTestResult {
    success: boolean;
    error?: string;
    latency?: number;
    model?: string;
}

export interface ChatMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    id: string;
}

export class AizenIntegrationService {
    private pythonProcess: child_process.ChildProcess | null = null;
    private rustProcess: child_process.ChildProcess | null = null;
    private isConnected: boolean = false;
    private pythonBaseUrl: string = 'http://localhost:8000';
    private rustBaseUrl: string = 'http://localhost:8001';
    private pythonBackendPath: string;
    private rustBackendPath: string;
    private websocket: WebSocket | null = null;
    private settingsService: AizenSettingsService;
    private mcpServers: Map<string, any> = new Map();
    private isInitialized = false;

    constructor() {
        const extensionPath = vscode.extensions.getExtension('aizen-ai.aizen-revolutionary-ai')?.extensionPath ||
                             path.join(__dirname, '..', '..');

        this.pythonBackendPath = path.join(extensionPath, 'python_backend_ai');
        this.rustBackendPath = path.join(extensionPath, 'target', 'release');
        this.settingsService = AizenSettingsService.getInstance();
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        try {
            console.log('🚀 Initializing Aizen Integration Service...');



            // Initialize MCP servers
            await this.initializeMcpServers();

            // For now, initialize in mock mode for testing
            // Backend integration will be added later
            console.log('⚠️ Running in mock mode - backend integration pending');

            // Validate configuration
            await this.validateConfiguration();

            this.isConnected = true;
            this.isInitialized = true;
            console.log('✅ Aizen Integration Service initialized successfully (mock mode)');

        } catch (error) {
            console.error('❌ Failed to initialize Aizen Integration Service:', error);
            // Don't throw error in mock mode
            this.isConnected = true;
            this.isInitialized = true;
            console.log('✅ Aizen Integration Service initialized in fallback mode');
        }
    }

    /**
     * Send a chat message and get AI response
     */
    async sendMessage(
        message: string,
        conversationHistory: ChatMessage[] = [],
        options?: {
            stream?: boolean;
            temperature?: number;
            maxTokens?: number;
        }
    ): Promise<AIResponse> {
        if (!this.isInitialized) {
            throw new Error('Integration service not initialized');
        }

        const modelConfig = this.settingsService.getModelConfig();

        if (!modelConfig.apiKey) {
            // Return mock response if no API key
            return this.getMockResponse(modelConfig.model);
        }

        try {
            console.log(`🤖 Sending message to ${modelConfig.provider}:${modelConfig.model}`);

            // Prepare the conversation context
            const messages = this.prepareMessages(message, conversationHistory);

            // Send to appropriate AI provider
            const response = await this.callAIProvider(messages, {
                ...modelConfig,
                temperature: options?.temperature ?? modelConfig.temperature,
                maxTokens: options?.maxTokens ?? modelConfig.maxTokens
            });

            console.log('✅ Received AI response');
            return response;

        } catch (error) {
            console.error('❌ Error sending message:', error);
            // Return mock response on error
            return this.getMockResponse(modelConfig.model, `Error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Test connection to the configured AI provider
     */
    async testConnection(): Promise<ConnectionTestResult> {
        try {
            const startTime = Date.now();
            const modelConfig = this.settingsService.getModelConfig();

            if (!modelConfig.apiKey) {
                return {
                    success: false,
                    error: `Missing API key for ${modelConfig.provider}`
                };
            }

            // Send a simple test message
            const testResponse = await this.callAIProvider([
                { role: 'user', content: 'Hello, please respond with "Connection test successful"' }
            ], modelConfig);

            const latency = Date.now() - startTime;

            return {
                success: true,
                latency,
                model: modelConfig.model
            };

        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * Refresh MCP agents/servers
     */
    async refreshAgents(): Promise<void> {
        console.log('🔄 Refreshing MCP agents...');
        await this.initializeMcpServers();
    }

    /**
     * Dispose of resources
     */
    async dispose(): Promise<void> {
        console.log('🧹 Disposing AI Integration Service...');

        // Cleanup MCP servers
        for (const [name, server] of this.mcpServers) {
            try {
                if (server.dispose) {
                    await server.dispose();
                }
            } catch (error) {
                console.error(`Error disposing MCP server ${name}:`, error);
            }
        }

        this.mcpServers.clear();
        this.isInitialized = false;
        this.isConnected = false;

        console.log('✅ AI Integration Service disposed');
    }



    private async initializeMcpServers(): Promise<void> {
        const mcpConfig = this.settingsService.getMcpConfig();

        console.log('🔌 Initializing MCP servers...');

        // Initialize Exa server if enabled
        if (mcpConfig.exa.enabled) {
            try {
                const exaServer = await this.initializeExaServer(mcpConfig.exa.apiKey);
                this.mcpServers.set('exa', exaServer);
                console.log('✅ Exa MCP server initialized');
            } catch (error) {
                console.error('❌ Failed to initialize Exa server:', error);
            }
        }

        // Initialize Firecrawl server if enabled
        if (mcpConfig.firecrawl.enabled) {
            try {
                const firecrawlServer = await this.initializeFirecrawlServer(mcpConfig.firecrawl.apiKey);
                this.mcpServers.set('firecrawl', firecrawlServer);
                console.log('✅ Firecrawl MCP server initialized');
            } catch (error) {
                console.error('❌ Failed to initialize Firecrawl server:', error);
            }
        }

        console.log(`✅ ${this.mcpServers.size} MCP servers initialized`);
    }



    private async initializeExaServer(apiKey: string): Promise<any> {
        // Simulate Exa server initialization
        return {
            name: 'exa',
            tools: [
                { name: 'search', description: 'Search the web using Exa' },
                { name: 'research', description: 'Research topics using Exa' }
            ],
            apiKey,
            dispose: async () => { /* cleanup */ }
        };
    }

    private async initializeFirecrawlServer(apiKey: string): Promise<any> {
        // Simulate Firecrawl server initialization
        return {
            name: 'firecrawl',
            tools: [
                { name: 'scrape', description: 'Scrape web pages using Firecrawl' },
                { name: 'crawl', description: 'Crawl websites using Firecrawl' }
            ],
            apiKey,
            dispose: async () => { /* cleanup */ }
        };
    }

    private async validateConfiguration(): Promise<void> {
        const validation = this.settingsService.validateSettings();

        if (!validation.isValid) {
            console.warn('⚠️ Configuration validation warnings:', validation.warnings);
        }
    }

    private prepareMessages(message: string, history: ChatMessage[]): any[] {
        const messages = [];

        // Add system message
        messages.push({
            role: 'system',
            content: 'You are Aizen AI, a revolutionary AI assistant integrated into VS Code. You help developers with coding, research, and productivity tasks.'
        });

        // Add conversation history
        history.forEach(msg => {
            messages.push({
                role: msg.role,
                content: msg.content
            });
        });

        // Add current message
        messages.push({
            role: 'user',
            content: message
        });

        return messages;
    }

    private async callAIProvider(messages: any[], config: any): Promise<AIResponse> {
        switch (config.provider) {
            case 'Anthropic':
                return await this.callAnthropic(messages, config);
            case 'OpenAI':
                return await this.callOpenAI(messages, config);
            case 'Google':
                return await this.callGoogle(messages, config);
            case 'Local':
                return await this.callLocal(messages, config);
            default:
                throw new Error(`Unsupported AI provider: ${config.provider}`);
        }
    }

    private async callAnthropic(messages: any[], config: any): Promise<AIResponse> {
        // Simulate Anthropic API call
        console.log('🤖 Calling Anthropic API...');

        await new Promise(resolve => setTimeout(resolve, 1000));

        return {
            content: `Hello! I'm Aizen AI powered by ${config.model}. I'm ready to help you with your development tasks. This is a simulated response - please configure your API keys in settings to enable real AI functionality.`,
            model: config.model,
            usage: {
                promptTokens: 50,
                completionTokens: 30,
                totalTokens: 80
            }
        };
    }

    private async callOpenAI(messages: any[], config: any): Promise<AIResponse> {
        // Simulate OpenAI API call
        console.log('🤖 Calling OpenAI API...');

        await new Promise(resolve => setTimeout(resolve, 800));

        return {
            content: `Greetings! I'm Aizen AI using ${config.model}. Ready to assist with your coding needs. Please configure your OpenAI API key to enable full functionality.`,
            model: config.model,
            usage: {
                promptTokens: 45,
                completionTokens: 25,
                totalTokens: 70
            }
        };
    }

    private async callGoogle(messages: any[], config: any): Promise<AIResponse> {
        // Simulate Google API call
        console.log('🤖 Calling Google API...');

        await new Promise(resolve => setTimeout(resolve, 900));

        return {
            content: `Hi there! I'm Aizen AI running on ${config.model}. I'm here to help with your development workflow. Please set up your Google API key in settings for full capabilities.`,
            model: config.model,
            usage: {
                promptTokens: 40,
                completionTokens: 28,
                totalTokens: 68
            }
        };
    }

    private async callLocal(messages: any[], config: any): Promise<AIResponse> {
        // Simulate local model call
        console.log('🤖 Calling local model...');

        await new Promise(resolve => setTimeout(resolve, 1200));

        return {
            content: `Hello! I'm Aizen AI running locally with ${config.model}. I'm ready to help with your coding tasks. Local models provide privacy and offline capabilities.`,
            model: config.model,
            usage: {
                promptTokens: 35,
                completionTokens: 32,
                totalTokens: 67
            }
        };
    }

    private getMockResponse(model: string, errorMessage?: string): AIResponse {
        const content = errorMessage
            ? `I encountered an issue: ${errorMessage}. Please check your settings and try again.`
            : `Hello! I'm Aizen AI (${model}). I'm currently running in demo mode. Please configure your API keys in settings to enable full AI functionality.`;

        return {
            content,
            model,
            usage: {
                promptTokens: 20,
                completionTokens: 15,
                totalTokens: 35
            }
        };
    }

    private async startPythonBackend(): Promise<void> {
        try {
            console.log('🐍 Starting Python AI Backend...');
            
            // Check if already running
            const isRunning = await this.checkPythonBackendHealth();
            if (isRunning) {
                console.log('✅ Python backend already running');
                return;
            }

            this.pythonProcess = child_process.spawn('python', [
                path.join(this.pythonBackendPath, 'start_server.py'),
                '--host', '127.0.0.1',
                '--port', '8000',
                '--log-level', 'INFO'
            ], {
                cwd: this.pythonBackendPath,
                stdio: 'pipe'
            });

            this.pythonProcess.stdout?.on('data', (data) => {
                console.log(`Python Backend: ${data}`);
            });

            this.pythonProcess.stderr?.on('data', (data) => {
                console.error(`Python Backend Error: ${data}`);
            });

            this.pythonProcess.on('error', (error) => {
                console.error('Python Backend Process Error:', error);
            });

            // Wait for server to start
            await this.waitForPythonServerStart();
            console.log('✅ Python AI Backend started successfully');
            
        } catch (error) {
            throw new Error(`Failed to start Python backend: ${error}`);
        }
    }

    private async startRustBackend(): Promise<void> {
        try {
            console.log('🦀 Starting Rust Backend...');
            
            // Check if Rust binary exists
            const rustBinaryPath = path.join(this.rustBackendPath, 'aizen_ai_extension.exe');
            
            // For now, we'll skip Rust backend if not built
            console.log('⚠️ Rust backend not yet integrated - using Python backend only');
            
        } catch (error) {
            console.log('⚠️ Rust backend unavailable, continuing with Python backend only');
        }
    }

    private async connectWebSocket(): Promise<void> {
        try {
            // For now, we'll use HTTP polling instead of WebSocket
            // WebSocket integration can be added later
            console.log('📡 Using HTTP polling for real-time updates');
        } catch (error) {
            console.log('⚠️ WebSocket connection failed, using HTTP polling');
        }
    }

    private async waitForPythonServerStart(): Promise<void> {
        const maxAttempts = 30;
        let attempts = 0;

        while (attempts < maxAttempts) {
            try {
                const response = await axios.get(`${this.pythonBaseUrl}/health`, { timeout: 1000 });
                if (response.status === 200) {
                    return;
                }
            } catch (error) {
                // Server not ready yet
            }

            await new Promise(resolve => setTimeout(resolve, 1000));
            attempts++;
        }

        throw new Error('Python backend server failed to start within timeout');
    }

    private async checkPythonBackendHealth(): Promise<boolean> {
        try {
            const response = await axios.get(`${this.pythonBaseUrl}/health`, { timeout: 2000 });
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    // Agent Management Methods
    async createAgent(agentType: string, config: any = {}): Promise<any> {
        // Return mock agent creation for testing
        console.log(`Creating ${agentType} agent with config:`, config);
        return {
            id: `agent-${Date.now()}`,
            name: config.name || `${agentType}-agent-${Date.now()}`,
            type: agentType,
            status: 'idle',
            created: new Date().toISOString()
        };
    }

    async getAllAgents(): Promise<AgentInfo[]> {
        // Return mock data for testing
        return [
            {
                id: 'agent-1',
                name: 'Code Assistant',
                type: 'code-generation',
                status: 'idle',
                currentTask: undefined,
                capabilities: ['code-generation', 'debugging', 'optimization']
            },
            {
                id: 'agent-2',
                name: 'Chat Assistant',
                type: 'conversational',
                status: 'busy',
                currentTask: 'Processing user query',
                capabilities: ['chat', 'explanation', 'help']
            }
        ];
    }

    async getMetrics(): Promise<AgentMetrics> {
        // Return mock metrics for testing
        return {
            totalAgents: 2,
            activeAgents: 1,
            completedTasks: 15,
            failedTasks: 1,
            averageExecutionTime: 2.5,
            swarmEfficiency: 0.85,
            evolutionScore: 0.92
        };
    }

    async executeTask(description: string, taskType: string = 'code-generation', context: any = {}): Promise<any> {
        // Return mock task execution for testing
        console.log(`Executing ${taskType} task: ${description}`, context);
        return {
            taskId: `task-${Date.now()}`,
            description,
            taskType,
            status: 'started',
            created: new Date().toISOString()
        };
    }

    // Framework Integration Methods
    async enableSwarmIntelligence(): Promise<void> {
        console.log('🐝 Swarm Intelligence enabled (mock mode)');
    }

    async enableEvolution(): Promise<void> {
        console.log('🧠 Evolution enabled (mock mode)');
    }

    async executeWithTaskWeaver(userQuery: string, context: any = {}): Promise<any> {
        console.log('🔧 TaskWeaver execution (mock mode):', userQuery, context);
        return {
            result: `TaskWeaver processed: ${userQuery}`,
            status: 'completed',
            timestamp: new Date().toISOString()
        };
    }

    // Legacy dispose method - now handled by the async dispose method above
    disposeSync(): void {
        if (this.pythonProcess) {
            this.pythonProcess.kill();
            this.pythonProcess = null;
        }

        if (this.rustProcess) {
            this.rustProcess.kill();
            this.rustProcess = null;
        }

        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }

        this.isConnected = false;
        console.log('🔄 Aizen Integration Service disposed (sync)');
    }
}
