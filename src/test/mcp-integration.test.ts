/**
 * MCP Integration Tests
 * 
 * Comprehensive tests for the MCP client implementation including:
 * - Built-in server initialization
 * - External server management
 * - Security policy enforcement
 * - Tool execution with user consent
 * - Settings persistence
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import { AizenMCPClient } from '../mcp/MCPClient';
import { MCPSecurityManager } from '../mcp/MCPSecurityManager';
import { MCPSettingsProvider } from '../mcp/MCPSettingsProvider';
import { ExtendedMCPServerConfig, MCPConnectionStatus } from '../mcp/types';

suite('MCP Integration Tests', () => {
    let context: vscode.ExtensionContext;
    let mcpClient: AizenMCPClient;
    let securityManager: MCPSecurityManager;
    let settingsProvider: MCPSettingsProvider;

    suiteSetup(async () => {
        // Get extension context
        const extension = vscode.extensions.getExtension('aizen.aizen-revolutionary-ai');
        if (!extension) {
            throw new Error('Extension not found');
        }
        
        if (!extension.isActive) {
            await extension.activate();
        }
        
        context = extension.exports?.context;
        if (!context) {
            throw new Error('Extension context not available');
        }
    });

    setup(async () => {
        // Initialize MCP components for each test
        securityManager = new MCPSecurityManager({
            context,
            enableTelemetry: false
        });

        mcpClient = new AizenMCPClient({
            context,
            securityPolicy: securityManager.getSecurityPolicy(),
            debugMode: true
        });

        settingsProvider = new MCPSettingsProvider(context, mcpClient, securityManager);
    });

    teardown(async () => {
        // Clean up after each test
        if (mcpClient) {
            await mcpClient.dispose();
        }
        if (securityManager) {
            await securityManager.dispose();
        }
        if (settingsProvider) {
            settingsProvider.dispose();
        }
    });

    test('MCP Client Initialization', async () => {
        await mcpClient.initialize();
        
        const health = mcpClient.getHealthStatus();
        assert.ok(health.isHealthy || health.connectedServers >= 0, 'MCP client should initialize without errors');
        
        const servers = mcpClient.getConnectedServers();
        assert.ok(Array.isArray(servers), 'Should return array of servers');
        
        // Check for built-in servers
        const builtInServers = servers.filter(s => s.isBuiltIn);
        assert.ok(builtInServers.length >= 2, 'Should have at least 2 built-in servers (Exa and Firecrawl)');
        
        // Verify Exa server
        const exaServer = builtInServers.find(s => s.id === 'exa-ai');
        assert.ok(exaServer, 'Should have Exa AI server');
        assert.strictEqual(exaServer.name, 'Exa AI Search');
        assert.ok(exaServer.apiKeyConfigured, 'Exa server should have API key pre-configured');
        
        // Verify Firecrawl server
        const firecrawlServer = builtInServers.find(s => s.id === 'firecrawl');
        assert.ok(firecrawlServer, 'Should have Firecrawl server');
        assert.strictEqual(firecrawlServer.name, 'Firecrawl');
        assert.ok(firecrawlServer.apiKeyConfigured, 'Firecrawl server should have API key pre-configured');
    });

    test('Security Manager Functionality', async () => {
        const policy = securityManager.getSecurityPolicy();
        assert.ok(policy.requireConfirmation, 'Should require confirmation by default');
        assert.ok(policy.userConsentRequired, 'Should require user consent by default');
        assert.ok(policy.logAllActivities, 'Should log all activities by default');
        assert.ok(policy.riskAssessment, 'Should perform risk assessment by default');
        
        // Test policy updates
        await securityManager.updateSecurityPolicy({
            maxExecutionTime: 30000
        });
        
        const updatedPolicy = securityManager.getSecurityPolicy();
        assert.strictEqual(updatedPolicy.maxExecutionTime, 30000, 'Should update max execution time');
        
        // Test metrics
        const metrics = securityManager.getSecurityMetrics();
        assert.ok(typeof metrics.totalConsents === 'number', 'Should return consent metrics');
        assert.ok(typeof metrics.recentActivity === 'number', 'Should return activity metrics');
    });

    test('External Server Management', async () => {
        await mcpClient.initialize();
        
        const initialServers = mcpClient.getConnectedServers();
        const initialCount = initialServers.length;
        
        // Add external server
        const externalServerConfig: Omit<ExtendedMCPServerConfig, 'id' | 'isBuiltIn'> = {
            name: 'Test External Server',
            description: 'Test server for integration testing',
            transport: 'stdio',
            command: 'echo',
            args: ['test'],
            enabled: true,
            autoStart: false,
            timeout: 30000,
            retryAttempts: 1,
            retryDelay: 1000,
            requiresApiKey: false,
            apiKeyConfigured: true,
            category: 'test',
            icon: '🧪'
        };
        
        const serverId = await mcpClient.addExternalServer(externalServerConfig);
        assert.ok(serverId, 'Should return server ID');
        assert.ok(serverId.startsWith('external-'), 'External server ID should have correct prefix');
        
        const updatedServers = mcpClient.getConnectedServers();
        assert.strictEqual(updatedServers.length, initialCount + 1, 'Should have one more server');
        
        const addedServer = updatedServers.find(s => s.id === serverId);
        assert.ok(addedServer, 'Should find the added server');
        assert.strictEqual(addedServer.name, externalServerConfig.name);
        assert.strictEqual(addedServer.isBuiltIn, false);
        
        // Remove external server
        await mcpClient.removeServer(serverId);
        
        const finalServers = mcpClient.getConnectedServers();
        assert.strictEqual(finalServers.length, initialCount, 'Should be back to original count');
        
        const removedServer = finalServers.find(s => s.id === serverId);
        assert.ok(!removedServer, 'Server should be removed');
    });

    test('Tool Risk Assessment', async () => {
        await mcpClient.initialize();
        
        const tools = mcpClient.getAvailableTools();
        assert.ok(tools.length > 0, 'Should have available tools');
        
        // Test risk assessment
        for (const tool of tools.slice(0, 5)) { // Test first 5 tools
            assert.ok(['low', 'medium', 'high'].includes(tool.riskLevel || 'low'), 
                `Tool ${tool.name} should have valid risk level`);
        }
        
        // Test specific risk patterns
        const mockHighRiskTool = {
            name: 'delete_file',
            description: 'Delete a file from the system',
            serverId: 'test',
            serverName: 'Test Server'
        };
        
        const highRisk = securityManager.assessToolRisk(mockHighRiskTool as any);
        assert.strictEqual(highRisk, 'high', 'Delete tool should be high risk');
        
        const mockLowRiskTool = {
            name: 'get_weather',
            description: 'Get current weather information',
            serverId: 'test',
            serverName: 'Test Server'
        };
        
        const lowRisk = securityManager.assessToolRisk(mockLowRiskTool as any);
        assert.strictEqual(lowRisk, 'low', 'Weather tool should be low risk');
    });

    test('Settings Persistence', async () => {
        await mcpClient.initialize();
        
        // Test security policy persistence
        const originalPolicy = securityManager.getSecurityPolicy();
        
        await securityManager.updateSecurityPolicy({
            requireConfirmation: false,
            maxExecutionTime: 45000
        });
        
        // Create new security manager to test persistence
        const newSecurityManager = new MCPSecurityManager({
            context,
            enableTelemetry: false
        });
        
        // Wait a bit for async loading
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const loadedPolicy = newSecurityManager.getSecurityPolicy();
        assert.strictEqual(loadedPolicy.requireConfirmation, false, 'Should persist confirmation setting');
        assert.strictEqual(loadedPolicy.maxExecutionTime, 45000, 'Should persist execution time setting');
        
        // Restore original policy
        await securityManager.updateSecurityPolicy(originalPolicy);
        await newSecurityManager.dispose();
    });

    test('Health Status Monitoring', async () => {
        await mcpClient.initialize();
        
        const health = mcpClient.getHealthStatus();
        
        assert.ok(typeof health.isHealthy === 'boolean', 'Should have health status');
        assert.ok(typeof health.connectedServers === 'number', 'Should have connected servers count');
        assert.ok(typeof health.totalServers === 'number', 'Should have total servers count');
        assert.ok(typeof health.totalTools === 'number', 'Should have total tools count');
        assert.ok(typeof health.totalResources === 'number', 'Should have total resources count');
        assert.ok(typeof health.totalPrompts === 'number', 'Should have total prompts count');
        assert.ok(Array.isArray(health.errors), 'Should have errors array');
        
        // Health should be true if no errors and at least some servers connected
        if (health.errors.length === 0 && health.connectedServers > 0) {
            assert.ok(health.isHealthy, 'Should be healthy with no errors and connected servers');
        }
    });

    test('Activity Logging', async () => {
        await mcpClient.initialize();
        
        const initialLogs = securityManager.getActivityLogs();
        const initialCount = initialLogs.length;
        
        // Trigger an activity by testing server connection
        const servers = mcpClient.getConnectedServers();
        if (servers.length > 0) {
            try {
                await mcpClient.testServerConnection(servers[0].id);
            } catch (error) {
                // Connection test might fail, but should still log activity
            }
            
            // Wait a bit for async logging
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const updatedLogs = securityManager.getActivityLogs();
            assert.ok(updatedLogs.length >= initialCount, 'Should have logged activity');
            
            if (updatedLogs.length > initialCount) {
                const latestLog = updatedLogs[0]; // Most recent first
                assert.ok(latestLog.id, 'Log should have ID');
                assert.ok(latestLog.timestamp, 'Log should have timestamp');
                assert.ok(latestLog.serverId, 'Log should have server ID');
                assert.ok(latestLog.action, 'Log should have action');
                assert.ok(typeof latestLog.success === 'boolean', 'Log should have success status');
            }
        }
    });

    test('Error Handling', async () => {
        // Test invalid server removal
        try {
            await mcpClient.removeServer('non-existent-server');
            assert.fail('Should throw error for non-existent server');
        } catch (error) {
            assert.ok(error instanceof Error, 'Should throw proper error');
        }
        
        // Test built-in server removal protection
        await mcpClient.initialize();
        const servers = mcpClient.getConnectedServers();
        const builtInServer = servers.find(s => s.isBuiltIn);
        
        if (builtInServer) {
            try {
                await mcpClient.removeServer(builtInServer.id);
                assert.fail('Should not allow removal of built-in server');
            } catch (error) {
                assert.ok(error instanceof Error, 'Should throw error for built-in server removal');
            }
        }
    });

    test('Settings Provider Integration', async () => {
        // Test settings provider initialization
        assert.ok(settingsProvider, 'Settings provider should be created');
        
        // Settings provider should be able to open settings
        // Note: In test environment, webview creation might not work
        // but we can test that the method exists and doesn't throw immediately
        try {
            // This might fail in test environment, but shouldn't throw synchronously
            settingsProvider.openSettings();
        } catch (error) {
            // Expected in test environment
        }
        
        assert.ok(true, 'Settings provider should handle webview creation gracefully');
    });
});

// Helper function to wait for async operations
function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}
