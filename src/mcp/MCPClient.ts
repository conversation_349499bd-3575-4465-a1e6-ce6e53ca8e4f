/**
 * Aizen AI MCP Client - Comprehensive Model Context Protocol Implementation
 * 
 * This is a complete rewrite of the MCP client with the following improvements:
 * - Support for multiple transport types (STDIO, HTTP, Streamable HTTP)
 * - Built-in security and user consent management
 * - Proper error handling and recovery
 * - Session management and connection lifecycle
 * - Support for both built-in and external MCP servers
 * - Real-time status monitoring and logging
 */

import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';

import {
    ExtendedMCPServerConfig,
    MCPServerStatus,
    MCPConnectionStatus,
    ExtendedMCPTool,
    ExtendedMCPResource,
    ExtendedMCPPrompt,
    MCPEvent,
    MCPTransportConfig,
    MCPError,
    MCPConnectionError,
    MCPSecurityError,
    MCPToolError,
    MCPUserConsent,
    MCPActivityLog,
    MCPSecurityPolicy,
    ToolResult
} from './types';

export interface MCPClientOptions {
    context: vscode.ExtensionContext;
    securityPolicy?: MCPSecurityPolicy;
    maxConcurrentConnections?: number;
    connectionTimeout?: number;
    debugMode?: boolean;
}

export class AizenMCPClient extends EventEmitter {
    private context: vscode.ExtensionContext;
    private clients: Map<string, Client> = new Map();
    private servers: Map<string, ExtendedMCPServerConfig> = new Map();
    private serverStatus: Map<string, MCPServerStatus> = new Map();
    private tools: Map<string, ExtendedMCPTool> = new Map();
    private resources: Map<string, ExtendedMCPResource> = new Map();
    private prompts: Map<string, ExtendedMCPPrompt> = new Map();
    private userConsents: Map<string, MCPUserConsent> = new Map();
    private activityLogs: MCPActivityLog[] = [];
    private securityPolicy: MCPSecurityPolicy;
    private isInitialized = false;
    private connectionTimeout: number;
    private maxConcurrentConnections: number;
    private debugMode: boolean;

    constructor(options: MCPClientOptions) {
        super();
        this.context = options.context;
        this.connectionTimeout = options.connectionTimeout || 120000; // Increased to 2 minutes for remote servers
        this.maxConcurrentConnections = options.maxConcurrentConnections || 10;
        this.debugMode = options.debugMode || false;
        
        // Default security policy
        this.securityPolicy = options.securityPolicy || {
            requireConfirmation: true,
            maxExecutionTime: 60000,
            userConsentRequired: true,
            logAllActivities: true,
            riskAssessment: true
        };

        this.initializeBuiltinServers();
    }

    private initializeBuiltinServers(): void {
        // Built-in Exa AI server with pre-configured API key using remote hosted MCP
        const exaServer: ExtendedMCPServerConfig = {
            id: 'exa-ai',
            name: 'Exa AI Search',
            description: 'Advanced web search and research capabilities powered by Exa AI',
            transport: 'stdio',
            command: 'npx',
            args: ['-y', 'mcp-remote', 'https://mcp.exa.ai/mcp?exaApiKey=f01e507f-cdd2-454d-adcf-545d24035692'],
            env: {},
            enabled: true,
            autoStart: true,
            timeout: 120000, // 2 minutes for remote server connection
            retryAttempts: 5,
            retryDelay: 3000,
            isBuiltIn: true,
            requiresApiKey: false, // Pre-configured
            apiKeyConfigured: true,
            category: 'search',
            icon: '🔍'
        };

        // Built-in Firecrawl server with pre-configured API key using remote hosted MCP
        const firecrawlServer: ExtendedMCPServerConfig = {
            id: 'firecrawl',
            name: 'Firecrawl',
            description: 'Web scraping and content extraction capabilities',
            transport: 'stdio',
            command: 'npx',
            args: ['-y', 'mcp-remote', 'https://mcp.firecrawl.dev/fc-73581888d5374a1a99893178925cc8bb/sse'],
            env: {},
            enabled: true,
            autoStart: true,
            timeout: 120000, // 2 minutes for remote server connection
            retryAttempts: 5,
            retryDelay: 3000,
            isBuiltIn: true,
            requiresApiKey: false, // Pre-configured
            apiKeyConfigured: true,
            category: 'scraping',
            icon: '🕷️'
        };

        this.servers.set(exaServer.id, exaServer);
        this.servers.set(firecrawlServer.id, firecrawlServer);

        // Initialize status for built-in servers
        this.serverStatus.set(exaServer.id, {
            id: exaServer.id,
            name: exaServer.name,
            status: MCPConnectionStatus.Disconnected,
            toolCount: 0,
            resourceCount: 0,
            promptCount: 0,
            isBuiltIn: true
        });

        this.serverStatus.set(firecrawlServer.id, {
            id: firecrawlServer.id,
            name: firecrawlServer.name,
            status: MCPConnectionStatus.Disconnected,
            toolCount: 0,
            resourceCount: 0,
            promptCount: 0,
            isBuiltIn: true
        });
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        this.log('info', '🔌 Initializing Aizen MCP Client...');

        try {
            // Load saved configuration
            await this.loadConfiguration();

            // Connect to enabled servers
            const enabledServers = Array.from(this.servers.values()).filter(s => s.enabled);
            
            for (const server of enabledServers) {
                if (this.clients.size >= this.maxConcurrentConnections) {
                    this.log('warn', `Maximum concurrent connections (${this.maxConcurrentConnections}) reached. Skipping ${server.name}`);
                    continue;
                }

                try {
                    await this.connectToServer(server);
                } catch (error) {
                    this.log('error', `Failed to connect to ${server.name}:`, error);
                }
            }

            this.isInitialized = true;
            this.log('info', '✅ Aizen MCP Client initialized successfully');
            
            this.emit('initialized', {
                connectedServers: this.clients.size,
                totalTools: this.tools.size,
                totalResources: this.resources.size,
                totalPrompts: this.prompts.size
            });

        } catch (error) {
            this.log('error', '❌ Failed to initialize MCP Client:', error);
            throw new MCPError('Failed to initialize MCP Client', 'INITIALIZATION_ERROR', undefined, error);
        }
    }

    private async loadConfiguration(): Promise<void> {
        try {
            const config = this.context.globalState.get<any>('mcpConfiguration');
            if (config) {
                // Load external servers from configuration
                if (config.servers) {
                    for (const serverConfig of config.servers) {
                        if (!serverConfig.isBuiltIn) {
                            this.servers.set(serverConfig.id, serverConfig);
                            this.serverStatus.set(serverConfig.id, {
                                id: serverConfig.id,
                                name: serverConfig.name,
                                status: MCPConnectionStatus.Disconnected,
                                toolCount: 0,
                                resourceCount: 0,
                                promptCount: 0,
                                isBuiltIn: false
                            });
                        }
                    }
                }

                // Load security policy
                if (config.security) {
                    this.securityPolicy = { ...this.securityPolicy, ...config.security };
                }

                // Load user consents
                if (config.userConsents) {
                    for (const consent of config.userConsents) {
                        this.userConsents.set(`${consent.serverId}:${consent.toolName}`, consent);
                    }
                }
            }
        } catch (error) {
            this.log('warn', 'Failed to load configuration, using defaults:', error);
        }
    }

    private async saveConfiguration(): Promise<void> {
        try {
            const config = {
                servers: Array.from(this.servers.values()),
                security: this.securityPolicy,
                userConsents: Array.from(this.userConsents.values()),
                lastUpdated: new Date().toISOString()
            };

            await this.context.globalState.update('mcpConfiguration', config);
        } catch (error) {
            this.log('error', 'Failed to save configuration:', error);
        }
    }

    private log(level: 'info' | 'warn' | 'error' | 'debug', message: string, ...args: any[]): void {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [MCP] ${message}`;

        if (this.debugMode || level !== 'debug') {
            console[level](logMessage, ...args);
        }

        // Add to activity log if it's an important event
        if (level === 'error' || level === 'warn') {
            this.activityLogs.push({
                id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                timestamp: new Date(),
                serverId: 'system',
                serverName: 'MCP Client',
                action: level === 'error' ? 'server_disconnect' : 'server_connect',
                details: { message, args },
                success: level !== 'error',
                error: level === 'error' ? message : undefined
            });
        }
    }

    private async connectToServer(serverConfig: ExtendedMCPServerConfig): Promise<void> {
        const startTime = Date.now();

        try {
            this.log('info', `🔗 Connecting to MCP server: ${serverConfig.name} (${serverConfig.transport})`);
            this.log('debug', `Server config:`, {
                command: serverConfig.command,
                args: serverConfig.args,
                env: Object.keys(serverConfig.env || {}),
                timeout: serverConfig.timeout
            });

            // Update status to connecting
            this.updateServerStatus(serverConfig.id, {
                status: MCPConnectionStatus.Connecting
            });

            // Create client
            const client = new Client({
                name: 'aizen-ai-extension',
                version: '2.0.0'
            });

            // Create transport based on configuration
            this.log('debug', `Creating transport for ${serverConfig.name}...`);
            const transport = await this.createTransport(serverConfig);
            this.log('debug', `Transport created successfully for ${serverConfig.name}`);

            // Set connection timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error(`Connection timeout after ${this.connectionTimeout}ms`)), this.connectionTimeout);
            });

            // Connect with timeout
            this.log('debug', `Attempting to connect to ${serverConfig.name} with ${this.connectionTimeout}ms timeout...`);
            await Promise.race([
                client.connect(transport),
                timeoutPromise
            ]);
            this.log('debug', `Successfully connected to ${serverConfig.name}`);

            // Store client
            this.clients.set(serverConfig.id, client);

            // Load server capabilities and resources
            await this.loadServerCapabilities(serverConfig.id, client);

            // Update status to connected
            this.updateServerStatus(serverConfig.id, {
                status: MCPConnectionStatus.Connected,
                lastConnected: new Date(),
                lastError: undefined
            });

            // Log successful connection
            const duration = Date.now() - startTime;
            this.logActivity({
                serverId: serverConfig.id,
                serverName: serverConfig.name,
                action: 'server_connect',
                details: { transport: serverConfig.transport, duration },
                success: true,
                duration
            });

            this.log('info', `✅ Connected to ${serverConfig.name} in ${duration}ms`);

            // Emit connection event
            this.emit('server_connected', {
                serverId: serverConfig.id,
                serverName: serverConfig.name,
                transport: serverConfig.transport,
                duration
            });

        } catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';

            this.log('error', `❌ Failed to connect to ${serverConfig.name}:`, error);

            // Update status to error
            this.updateServerStatus(serverConfig.id, {
                status: MCPConnectionStatus.Error,
                lastError: errorMessage
            });

            // Log failed connection
            this.logActivity({
                serverId: serverConfig.id,
                serverName: serverConfig.name,
                action: 'server_connect',
                details: { transport: serverConfig.transport, error: errorMessage },
                success: false,
                error: errorMessage,
                duration
            });

            // Emit error event
            this.emit('server_error', {
                serverId: serverConfig.id,
                serverName: serverConfig.name,
                error: errorMessage
            });

            throw new MCPConnectionError(
                `Failed to connect to ${serverConfig.name}: ${errorMessage}`,
                serverConfig.id,
                { transport: serverConfig.transport, originalError: error }
            );
        }
    }

    private async createTransport(serverConfig: ExtendedMCPServerConfig): Promise<any> {
        switch (serverConfig.transport) {
            case 'stdio':
                if (!serverConfig.command) {
                    throw new Error('Command is required for stdio transport');
                }
                return new StdioClientTransport({
                    command: serverConfig.command,
                    args: serverConfig.args || [],
                    env: { ...process.env, ...serverConfig.env }
                });

            case 'http':
            case 'sse':
                if (!serverConfig.url) {
                    throw new Error('URL is required for HTTP/SSE transport');
                }
                return new SSEClientTransport(new URL(serverConfig.url));

            case 'streamable-http':
                if (!serverConfig.url) {
                    throw new Error('URL is required for Streamable HTTP transport');
                }
                return new StreamableHTTPClientTransport(new URL(serverConfig.url));

            default:
                throw new Error(`Unsupported transport type: ${serverConfig.transport}`);
        }
    }

    private async loadServerCapabilities(serverId: string, client: Client): Promise<void> {
        try {
            // Load tools
            const toolsResponse = await client.listTools();
            for (const tool of toolsResponse.tools) {
                if (!tool.name) continue; // Skip tools without names

                const extendedTool: ExtendedMCPTool = {
                    ...tool,
                    name: tool.name, // Ensure name is defined
                    serverId,
                    serverName: this.servers.get(serverId)?.name || 'Unknown',
                    riskLevel: this.assessToolRisk(tool),
                    requiresConfirmation: this.securityPolicy.requireConfirmation
                };
                this.tools.set(`${serverId}:${tool.name}`, extendedTool);
            }

            // Load resources
            try {
                const resourcesResponse = await client.listResources();
                for (const resource of resourcesResponse.resources) {
                    if (!resource.uri) continue; // Skip resources without URIs

                    const extendedResource: ExtendedMCPResource = {
                        ...resource,
                        uri: resource.uri, // Ensure uri is defined
                        serverId,
                        serverName: this.servers.get(serverId)?.name || 'Unknown'
                    };
                    this.resources.set(`${serverId}:${resource.uri}`, extendedResource);
                }
            } catch (error) {
                // Resources might not be supported
                this.log('debug', `Server ${serverId} does not support resources:`, error);
            }

            // Load prompts
            try {
                const promptsResponse = await client.listPrompts();
                for (const prompt of promptsResponse.prompts) {
                    if (!prompt.name) continue; // Skip prompts without names

                    const extendedPrompt: ExtendedMCPPrompt = {
                        name: prompt.name, // Ensure name is defined
                        description: prompt.description,
                        arguments: prompt.arguments?.map(arg => ({
                            name: arg.name || '',
                            description: arg.description,
                            required: arg.required
                        })),
                        serverId,
                        serverName: this.servers.get(serverId)?.name
                    };
                    this.prompts.set(`${serverId}:${prompt.name}`, extendedPrompt);
                }
            } catch (error) {
                // Prompts might not be supported
                this.log('debug', `Server ${serverId} does not support prompts:`, error);
            }

            // Update server status with counts
            const status = this.serverStatus.get(serverId);
            if (status) {
                this.updateServerStatus(serverId, {
                    toolCount: toolsResponse.tools.length,
                    resourceCount: Array.from(this.resources.keys()).filter(k => k.startsWith(`${serverId}:`)).length,
                    promptCount: Array.from(this.prompts.keys()).filter(k => k.startsWith(`${serverId}:`)).length
                });
            }

            this.log('info', `📋 Loaded capabilities for ${serverId}: ${toolsResponse.tools.length} tools, ${Array.from(this.resources.keys()).filter(k => k.startsWith(`${serverId}:`)).length} resources, ${Array.from(this.prompts.keys()).filter(k => k.startsWith(`${serverId}:`)).length} prompts`);

        } catch (error) {
            this.log('error', `Failed to load capabilities for ${serverId}:`, error);
            throw error;
        }
    }

    private assessToolRisk(tool: any): 'low' | 'medium' | 'high' {
        if (!this.securityPolicy.riskAssessment) {
            return 'low';
        }

        const toolName = tool.name.toLowerCase();
        const description = (tool.description || '').toLowerCase();

        // High risk indicators
        const highRiskKeywords = ['delete', 'remove', 'destroy', 'execute', 'run', 'shell', 'command', 'admin', 'root', 'sudo'];
        if (highRiskKeywords.some(keyword => toolName.includes(keyword) || description.includes(keyword))) {
            return 'high';
        }

        // Medium risk indicators
        const mediumRiskKeywords = ['write', 'create', 'modify', 'update', 'change', 'edit', 'upload', 'download'];
        if (mediumRiskKeywords.some(keyword => toolName.includes(keyword) || description.includes(keyword))) {
            return 'medium';
        }

        return 'low';
    }

    private updateServerStatus(serverId: string, updates: Partial<MCPServerStatus>): void {
        const currentStatus = this.serverStatus.get(serverId);
        if (currentStatus) {
            const newStatus = { ...currentStatus, ...updates };
            this.serverStatus.set(serverId, newStatus);

            // Emit status change event
            this.emit('server_status_changed', {
                serverId,
                status: newStatus,
                changes: updates
            });
        }
    }

    private logActivity(activity: Omit<MCPActivityLog, 'id' | 'timestamp'>): void {
        const log: MCPActivityLog = {
            id: `activity-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date(),
            ...activity
        };

        this.activityLogs.push(log);

        // Keep only last 1000 logs to prevent memory issues
        if (this.activityLogs.length > 1000) {
            this.activityLogs = this.activityLogs.slice(-1000);
        }

        // Emit activity event
        this.emit('activity_logged', log);
    }

    // Public API Methods

    async callTool(toolName: string, args: any, serverId?: string): Promise<ToolResult> {
        const startTime = Date.now();

        try {
            // Find the tool
            const toolKey = serverId ? `${serverId}:${toolName}` :
                Array.from(this.tools.keys()).find(key => key.endsWith(`:${toolName}`));

            if (!toolKey) {
                throw new MCPToolError(`Tool '${toolName}' not found`, toolName, serverId || 'unknown');
            }

            const tool = this.tools.get(toolKey)!;
            const client = this.clients.get(tool.serverId);

            if (!client) {
                throw new MCPConnectionError(`Server '${tool.serverId}' not connected`, tool.serverId);
            }

            // Check user consent if required
            if (this.securityPolicy.userConsentRequired && tool.requiresConfirmation) {
                const hasConsent = await this.checkUserConsent(tool.serverId, toolName, tool.riskLevel || 'medium');
                if (!hasConsent) {
                    throw new MCPSecurityError(`User consent required for tool '${toolName}'`, tool.serverId);
                }
            }

            this.log('info', `🔧 Calling tool: ${toolName} on server ${tool.serverId}`);

            // Execute tool with timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Tool execution timeout')), this.securityPolicy.maxExecutionTime);
            });

            const result = await Promise.race([
                client.callTool({ name: toolName, arguments: args }),
                timeoutPromise
            ]) as ToolResult;

            const duration = Date.now() - startTime;

            // Update tool usage statistics
            tool.lastUsed = new Date();
            tool.usageCount = (tool.usageCount || 0) + 1;
            tool.averageExecutionTime = tool.averageExecutionTime ?
                (tool.averageExecutionTime + duration) / 2 : duration;

            // Log successful execution
            this.logActivity({
                serverId: tool.serverId,
                serverName: tool.serverName || tool.serverId,
                action: 'tool_call',
                details: { toolName, args, duration },
                success: true,
                duration
            });

            this.log('info', `✅ Tool ${toolName} completed successfully in ${duration}ms`);

            return result;

        } catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';

            this.log('error', `❌ Tool ${toolName} failed:`, error);

            // Log failed execution
            this.logActivity({
                serverId: serverId || 'unknown',
                serverName: this.servers.get(serverId || '')?.name || 'Unknown',
                action: 'tool_call',
                details: { toolName, args, error: errorMessage },
                success: false,
                error: errorMessage,
                duration
            });

            throw error;
        }
    }

    async readResource(uri: string, serverId?: string): Promise<any> {
        try {
            const resourceKey = serverId ? `${serverId}:${uri}` :
                Array.from(this.resources.keys()).find(key => key.endsWith(`:${uri}`));

            if (!resourceKey) {
                throw new MCPError(`Resource '${uri}' not found`, 'RESOURCE_NOT_FOUND', serverId);
            }

            const resource = this.resources.get(resourceKey)!;
            const client = this.clients.get(resource.serverId);

            if (!client) {
                throw new MCPConnectionError(`Server '${resource.serverId}' not connected`, resource.serverId);
            }

            this.log('info', `📖 Reading resource: ${uri} from server ${resource.serverId}`);

            const result = await client.readResource({ uri });

            // Update resource access statistics
            resource.accessCount = (resource.accessCount || 0) + 1;

            // Log successful access
            this.logActivity({
                serverId: resource.serverId,
                serverName: resource.serverName || resource.serverId,
                action: 'resource_read',
                details: { uri },
                success: true
            });

            return result;

        } catch (error) {
            this.log('error', `❌ Failed to read resource ${uri}:`, error);
            throw error;
        }
    }

    async getPrompt(promptName: string, args?: any, serverId?: string): Promise<any> {
        try {
            const promptKey = serverId ? `${serverId}:${promptName}` :
                Array.from(this.prompts.keys()).find(key => key.endsWith(`:${promptName}`));

            if (!promptKey) {
                throw new MCPError(`Prompt '${promptName}' not found`, 'PROMPT_NOT_FOUND', serverId);
            }

            const prompt = this.prompts.get(promptKey)!;
            const client = this.clients.get(prompt.serverId);

            if (!client) {
                throw new MCPConnectionError(`Server '${prompt.serverId}' not connected`, prompt.serverId);
            }

            this.log('info', `📝 Getting prompt: ${promptName} from server ${prompt.serverId}`);

            const result = await client.getPrompt({ name: promptName, arguments: args });

            // Update prompt usage statistics
            prompt.lastUsed = new Date();
            prompt.usageCount = (prompt.usageCount || 0) + 1;

            // Log successful access
            this.logActivity({
                serverId: prompt.serverId,
                serverName: prompt.serverName || prompt.serverId,
                action: 'prompt_get',
                details: { promptName, args },
                success: true
            });

            return result;

        } catch (error) {
            this.log('error', `❌ Failed to get prompt ${promptName}:`, error);
            throw error;
        }
    }

    private async checkUserConsent(serverId: string, toolName: string, riskLevel: 'low' | 'medium' | 'high'): Promise<boolean> {
        const consentKey = `${serverId}:${toolName}`;
        const existingConsent = this.userConsents.get(consentKey);

        // Check if we have valid consent
        if (existingConsent && existingConsent.granted) {
            if (!existingConsent.expiresAt || existingConsent.expiresAt > new Date()) {
                return true;
            }
        }

        // Request user consent
        const serverName = this.servers.get(serverId)?.name || serverId;
        const message = `The tool "${toolName}" from server "${serverName}" wants to execute.\n\nRisk Level: ${riskLevel.toUpperCase()}\n\nDo you want to allow this?`;

        const choice = await vscode.window.showWarningMessage(
            message,
            { modal: true },
            'Allow Once',
            'Allow Always',
            'Deny'
        );

        const granted = choice === 'Allow Once' || choice === 'Allow Always';
        const expiresAt = choice === 'Allow Always' ? undefined : new Date(Date.now() + 3600000); // 1 hour

        // Store consent decision
        const consent: MCPUserConsent = {
            toolName,
            serverId,
            granted,
            timestamp: new Date(),
            expiresAt,
            riskLevel
        };

        this.userConsents.set(consentKey, consent);
        await this.saveConfiguration();

        return granted;
    }

    // Server Management Methods

    async addExternalServer(config: Omit<ExtendedMCPServerConfig, 'id' | 'isBuiltIn'>): Promise<string> {
        const serverId = `external-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        const serverConfig: ExtendedMCPServerConfig = {
            ...config,
            id: serverId,
            isBuiltIn: false,
            apiKeyConfigured: !config.requiresApiKey
        };

        this.servers.set(serverId, serverConfig);
        this.serverStatus.set(serverId, {
            id: serverId,
            name: serverConfig.name,
            status: MCPConnectionStatus.Disconnected,
            toolCount: 0,
            resourceCount: 0,
            promptCount: 0,
            isBuiltIn: false
        });

        await this.saveConfiguration();

        // Auto-connect if enabled
        if (serverConfig.enabled && serverConfig.autoStart) {
            try {
                await this.connectToServer(serverConfig);
            } catch (error) {
                this.log('warn', `Failed to auto-connect to new server ${serverConfig.name}:`, error);
            }
        }

        this.emit('server_added', { serverId, serverConfig });
        return serverId;
    }

    async removeServer(serverId: string): Promise<void> {
        const serverConfig = this.servers.get(serverId);
        if (!serverConfig) {
            throw new MCPError(`Server '${serverId}' not found`, 'SERVER_NOT_FOUND', serverId);
        }

        if (serverConfig.isBuiltIn) {
            throw new MCPSecurityError(`Cannot remove built-in server '${serverId}'`, serverId);
        }

        // Disconnect if connected
        await this.disconnectFromServer(serverId);

        // Remove from maps
        this.servers.delete(serverId);
        this.serverStatus.delete(serverId);

        // Remove associated tools, resources, and prompts
        for (const [key, tool] of this.tools.entries()) {
            if (tool.serverId === serverId) {
                this.tools.delete(key);
            }
        }

        for (const [key, resource] of this.resources.entries()) {
            if (resource.serverId === serverId) {
                this.resources.delete(key);
            }
        }

        for (const [key, prompt] of this.prompts.entries()) {
            if (prompt.serverId === serverId) {
                this.prompts.delete(key);
            }
        }

        await this.saveConfiguration();
        this.emit('server_removed', { serverId, serverConfig });
    }

    async disconnectFromServer(serverId: string): Promise<void> {
        const client = this.clients.get(serverId);
        if (!client) {
            return; // Already disconnected
        }

        try {
            this.log('info', `🔌 Disconnecting from server: ${serverId}`);

            await client.close();
            this.clients.delete(serverId);

            this.updateServerStatus(serverId, {
                status: MCPConnectionStatus.Disconnected
            });

            this.logActivity({
                serverId,
                serverName: this.servers.get(serverId)?.name || serverId,
                action: 'server_disconnect',
                details: {},
                success: true
            });

            this.emit('server_disconnected', { serverId });

        } catch (error) {
            this.log('error', `Failed to disconnect from server ${serverId}:`, error);
            throw error;
        }
    }

    async reconnectToServer(serverId: string): Promise<void> {
        const serverConfig = this.servers.get(serverId);
        if (!serverConfig) {
            throw new MCPError(`Server '${serverId}' not found`, 'SERVER_NOT_FOUND', serverId);
        }

        // Disconnect first if connected
        await this.disconnectFromServer(serverId);

        // Reconnect
        await this.connectToServer(serverConfig);
    }

    // Getter Methods

    getAvailableTools(): ExtendedMCPTool[] {
        return Array.from(this.tools.values());
    }

    getAvailableResources(): ExtendedMCPResource[] {
        return Array.from(this.resources.values());
    }

    getAvailablePrompts(): ExtendedMCPPrompt[] {
        return Array.from(this.prompts.values());
    }

    getServerStatus(serverId?: string): MCPServerStatus | MCPServerStatus[] {
        if (serverId) {
            const status = this.serverStatus.get(serverId);
            if (!status) {
                throw new MCPError(`Server '${serverId}' not found`, 'SERVER_NOT_FOUND', serverId);
            }
            return status;
        }
        return Array.from(this.serverStatus.values());
    }

    getConnectedServers(): ExtendedMCPServerConfig[] {
        return Array.from(this.servers.values()).filter(server =>
            this.clients.has(server.id)
        );
    }

    getActivityLogs(limit?: number): MCPActivityLog[] {
        const logs = [...this.activityLogs].reverse(); // Most recent first
        return limit ? logs.slice(0, limit) : logs;
    }

    getUserConsents(): MCPUserConsent[] {
        return Array.from(this.userConsents.values());
    }

    getSecurityPolicy(): MCPSecurityPolicy {
        return { ...this.securityPolicy };
    }

    async updateSecurityPolicy(policy: Partial<MCPSecurityPolicy>): Promise<void> {
        this.securityPolicy = { ...this.securityPolicy, ...policy };
        await this.saveConfiguration();
        this.emit('security_policy_updated', this.securityPolicy);
    }

    // Status and Health Methods

    async testServerConnection(serverId: string): Promise<boolean> {
        try {
            const client = this.clients.get(serverId);
            if (!client) {
                return false;
            }

            // Try to ping the server or list tools as a health check
            await client.listTools();
            return true;

        } catch (error) {
            this.log('warn', `Health check failed for server ${serverId}:`, error);
            return false;
        }
    }

    getHealthStatus(): {
        isHealthy: boolean;
        connectedServers: number;
        totalServers: number;
        totalTools: number;
        totalResources: number;
        totalPrompts: number;
        errors: string[];
    } {
        const errors: string[] = [];
        const totalServers = this.servers.size;
        const connectedServers = this.clients.size;

        // Check for servers in error state
        for (const [serverId, status] of this.serverStatus.entries()) {
            if (status.status === MCPConnectionStatus.Error && status.lastError) {
                errors.push(`${status.name}: ${status.lastError}`);
            }
        }

        return {
            isHealthy: errors.length === 0 && connectedServers > 0,
            connectedServers,
            totalServers,
            totalTools: this.tools.size,
            totalResources: this.resources.size,
            totalPrompts: this.prompts.size,
            errors
        };
    }

    // Cleanup and Disposal

    async dispose(): Promise<void> {
        this.log('info', '🔄 Disposing MCP client...');

        // Disconnect from all servers
        const disconnectPromises = Array.from(this.clients.keys()).map(serverId =>
            this.disconnectFromServer(serverId).catch(error =>
                this.log('warn', `Error disconnecting from ${serverId}:`, error)
            )
        );

        await Promise.all(disconnectPromises);

        // Clear all data
        this.clients.clear();
        this.tools.clear();
        this.resources.clear();
        this.prompts.clear();
        this.activityLogs.length = 0;

        // Save final configuration
        await this.saveConfiguration();

        this.isInitialized = false;
        this.log('info', '✅ MCP client disposed successfully');

        this.emit('disposed');
    }
}
