/**
 * Aizen AI MCP Client - Integrates MCP servers directly into the extension
 * Uses the official MCP TypeScript SDK to connect to external MCP servers
 */

import * as vscode from 'vscode';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

interface MCPServerConfig {
    id: string;
    name: string;
    command: string;
    args: string[];
    env?: Record<string, string>;
    enabled: boolean;
}

interface MCPTool {
    name: string;
    description: string;
    inputSchema: any;
    serverId: string;
}

export class AizenMCPClient {
    private context: vscode.ExtensionContext;
    private clients: Map<string, Client> = new Map();
    private servers: MCPServerConfig[] = [];
    private tools: MCPTool[] = [];

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.initializeBuiltinServers();
    }

    private initializeBuiltinServers(): void {
        // Built-in servers with Aizen AI's API keys - users cannot modify these
        // Using remote hosted MCP servers for better reliability
        this.servers = [
            {
                id: 'exa-ai',
                name: 'Exa AI Search',
                command: 'npx',
                args: ['-y', 'mcp-remote', 'https://mcp.exa.ai/mcp?exaApiKey=f01e507f-cdd2-454d-adcf-545d24035692'],
                env: {},
                enabled: true // Always enabled with built-in key
            },
            {
                id: 'firecrawl',
                name: 'Firecrawl',
                command: 'npx',
                args: ['-y', 'mcp-remote', 'https://mcp.firecrawl.dev/fc-73581888d5374a1a99893178925cc8bb/sse'],
                env: {},
                enabled: true // Always enabled with built-in key
            }
        ];
    }

    async initialize(): Promise<void> {
        console.log('🔌 Initializing Aizen MCP Client with built-in servers...');

        // Connect to all built-in servers (they already have API keys)
        for (const server of this.servers) {
            if (server.enabled) {
                await this.connectToServer(server);
            }
        }

        console.log('✅ Aizen MCP Client initialized with built-in Exa and Firecrawl servers');
    }

    // Method removed - built-in servers are always enabled with hardcoded API keys

    private async connectToServer(server: MCPServerConfig): Promise<void> {
        try {
            console.log(`🔗 Connecting to MCP server: ${server.name}`);

            const client = new Client({
                name: 'aizen-ai-extension',
                version: '2.0.0'
            });

            const transport = new StdioClientTransport({
                command: server.command,
                args: server.args,
                env: { ...process.env, ...server.env }
            });

            await client.connect(transport);
            this.clients.set(server.id, client);

            // Load tools from this server
            await this.loadServerTools(server.id, client);

            console.log(`✅ Connected to ${server.name}`);
        } catch (error) {
            console.error(`❌ Failed to connect to ${server.name}:`, error);
            server.enabled = false;
        }
    }

    private async loadServerTools(serverId: string, client: Client): Promise<void> {
        try {
            const toolsResponse = await client.listTools();

            for (const tool of toolsResponse.tools) {
                this.tools.push({
                    name: tool.name,
                    description: tool.description || '',
                    inputSchema: tool.inputSchema,
                    serverId: serverId
                });
            }

            console.log(`📋 Loaded ${toolsResponse.tools.length} tools from ${serverId}`);
        } catch (error) {
            console.error(`❌ Failed to load tools from ${serverId}:`, error);
        }
    }

    async callTool(toolName: string, args: any): Promise<any> {
        const tool = this.tools.find(t => t.name === toolName);
        if (!tool) {
            throw new Error(`Tool '${toolName}' not found`);
        }

        const client = this.clients.get(tool.serverId);
        if (!client) {
            throw new Error(`Server '${tool.serverId}' not connected`);
        }

        try {
            console.log(`🔧 Calling tool: ${toolName} with args:`, args);
            const result = await client.callTool({
                name: toolName,
                arguments: args
            });

            console.log(`✅ Tool ${toolName} completed successfully`);
            return result;
        } catch (error) {
            console.error(`❌ Tool ${toolName} failed:`, error);
            throw error;
        }
    }

    getAvailableTools(): MCPTool[] {
        return [...this.tools];
    }

    getConnectedServers(): MCPServerConfig[] {
        return this.servers.filter(s => s.enabled);
    }

    // API key methods removed - built-in servers use hardcoded keys

    public async addExternalMCPServer(): Promise<void> {
        const serverName = await vscode.window.showInputBox({
            prompt: 'Enter MCP Server Name',
            placeHolder: 'My Custom Server',
            ignoreFocusOut: true
        });

        if (!serverName) return;

        const command = await vscode.window.showInputBox({
            prompt: 'Enter MCP Server Command',
            placeHolder: 'npx my-mcp-server',
            ignoreFocusOut: true
        });

        if (!command) return;

        // Parse command and args
        const parts = command.split(' ');
        const cmd = parts[0];
        const args = parts.slice(1);

        // Add external server
        const externalServer: MCPServerConfig = {
            id: `external-${Date.now()}`,
            name: serverName,
            command: cmd,
            args: args,
            env: {},
            enabled: true
        };

        this.servers.push(externalServer);
        await this.connectToServer(externalServer);

        vscode.window.showInformationMessage(`✅ External MCP server "${serverName}" added and connected!`);
    }

    public async showMCPStatus(): Promise<void> {
        const connectedServers = this.getConnectedServers();
        const availableTools = this.getAvailableTools();

        const builtinServers = connectedServers.filter(s => s.id === 'exa-ai' || s.id === 'firecrawl');
        const externalServers = connectedServers.filter(s => s.id !== 'exa-ai' && s.id !== 'firecrawl');

        let message = `🔌 Aizen MCP Status:\n` +
            `• Total Servers: ${connectedServers.length}\n` +
            `• Available Tools: ${availableTools.length}\n\n`;

        if (builtinServers.length > 0) {
            message += `Built-in Servers (Aizen AI):\n${builtinServers.map(s => `  ✅ ${s.name} (${this.tools.filter(t => t.serverId === s.id).length} tools)`).join('\n')}\n\n`;
        }

        if (externalServers.length > 0) {
            message += `External Servers:\n${externalServers.map(s => `  • ${s.name} (${this.tools.filter(t => t.serverId === s.id).length} tools)`).join('\n')}\n\n`;
        }

        if (availableTools.length > 0) {
            message += `Available Tools:\n${availableTools.slice(0, 10).map(t => `  • ${t.name} (${t.serverId})`).join('\n')}`;
            if (availableTools.length > 10) {
                message += `\n  ... and ${availableTools.length - 10} more tools`;
            }
        }

        vscode.window.showInformationMessage(message);
    }

    public async dispose(): Promise<void> {
        console.log('🔄 Disposing MCP clients...');

        for (const [serverId, client] of this.clients) {
            try {
                await client.close();
                console.log(`✅ Closed connection to ${serverId}`);
            } catch (error) {
                console.error(`❌ Error closing ${serverId}:`, error);
            }
        }

        this.clients.clear();
        this.tools = [];
        console.log('✅ MCP clients disposed');
    }
}
