// MCP Module Exports - Generic Model Context Protocol Implementation
// Works with ANY MCP server, not just specific ones

// Core MCP Components
export { AizenMCPClient } from './OfficialMCPProvider';

// Built-in Server Configurations (for reference)
export { ExaMCPServerConfig, ExaMCPTools, ExaMCPHelper } from './servers/exa';
export { FirecrawlMCPServerConfig, FirecrawlMCPTools, FirecrawlMCPHelper } from './servers/firecrawl';

// Types and Interfaces
export * from './types';

// Version and Protocol Information
export const MCP_IMPLEMENTATION_VERSION = '2.0.0';
export const MCP_PROTOCOL_VERSION = '2025-06-18';
export const MCP_IMPLEMENTATION_NAME = 'Aizen AI MCP';

/**
 * Aizen AI MCP Implementation
 * 
 * This is a comprehensive implementation of the Model Context Protocol (MCP)
 * designed to integrate external AI services and tools into VS Code extensions.
 * 
 * Features:
 * - Protocol 2025-06-18 compliance
 * - Security-first design with user consent flows
 * - Support for multiple transport types (STDIO, HTTP, Streamable HTTP)
 * - Integration with external services (Exa AI, Firecrawl)
 * - Comprehensive testing suite
 * - TypeScript implementation with full type safety
 * - React UI components for server management
 * 
 * Security Features:
 * - Explicit user consent for all operations
 * - Risk assessment for tools and resources
 * - Permission management and expiration
 * - Activity logging and monitoring
 * - Domain-based access controls
 * 
 * Supported External Services:
 * - Exa AI: Web search, research papers, company research
 * - Firecrawl: Web scraping, crawling, content extraction
 * 
 * Usage:
 * ```typescript
 * import { MCPHub, MCPSecurityManager } from './mcp';
 * 
 * const mcpHub = new MCPHub(context);
 * const mcpSecurity = new MCPSecurityManager(context);
 * 
 * await mcpHub.initialize();
 * 
 * // Add external servers
 * const exaServerId = await mcpHub.addServer(ExaMCPServerConfig);
 * 
 * // Execute tools with security
 * const result = await mcpHub.executeTool(exaServerId, 'web_search_exa', {
 *   query: 'latest AI developments',
 *   numResults: 5
 * });
 * ```
 */
