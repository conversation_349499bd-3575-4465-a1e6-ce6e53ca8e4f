/**
 * MCP Security Manager - Handles security policies, user consent, and risk assessment
 * 
 * This component provides:
 * - User consent management for tool execution
 * - Risk assessment for MCP operations
 * - Security policy enforcement
 * - Activity monitoring and logging
 * - Permission management
 */

import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import {
    MCPSecurityPolicy,
    MCPUserConsent,
    MCPActivityLog,
    ExtendedMCPTool,
    MCPSecurityError,
    MCPError
} from './types';

export interface SecurityManagerOptions {
    context: vscode.ExtensionContext;
    defaultPolicy?: Partial<MCPSecurityPolicy>;
    enableTelemetry?: boolean;
}

export class MCPSecurityManager extends EventEmitter {
    private context: vscode.ExtensionContext;
    private securityPolicy: MCPSecurityPolicy;
    private userConsents: Map<string, MCPUserConsent> = new Map();
    private activityLogs: MCPActivityLog[] = [];
    private enableTelemetry: boolean;

    constructor(options: SecurityManagerOptions) {
        super();
        this.context = options.context;
        this.enableTelemetry = options.enableTelemetry || false;
        
        // Initialize default security policy
        this.securityPolicy = {
            requireConfirmation: true,
            maxExecutionTime: 60000, // 1 minute
            userConsentRequired: true,
            logAllActivities: true,
            riskAssessment: true,
            allowedDomains: [],
            blockedDomains: [],
            allowedTools: [],
            blockedTools: [],
            ...options.defaultPolicy
        };

        this.loadStoredData();
    }

    private async loadStoredData(): Promise<void> {
        try {
            // Load security policy
            const storedPolicy = this.context.globalState.get<MCPSecurityPolicy>('mcpSecurityPolicy');
            if (storedPolicy) {
                this.securityPolicy = { ...this.securityPolicy, ...storedPolicy };
            }

            // Load user consents
            const storedConsents = this.context.globalState.get<MCPUserConsent[]>('mcpUserConsents', []);
            for (const consent of storedConsents) {
                // Only load non-expired consents
                if (!consent.expiresAt || consent.expiresAt > new Date()) {
                    this.userConsents.set(`${consent.serverId}:${consent.toolName}`, consent);
                }
            }

            // Load activity logs (last 100)
            const storedLogs = this.context.globalState.get<MCPActivityLog[]>('mcpActivityLogs', []);
            this.activityLogs = storedLogs.slice(-100);

        } catch (error) {
            console.warn('Failed to load MCP security data:', error);
        }
    }

    private async saveData(): Promise<void> {
        try {
            await this.context.globalState.update('mcpSecurityPolicy', this.securityPolicy);
            await this.context.globalState.update('mcpUserConsents', Array.from(this.userConsents.values()));
            await this.context.globalState.update('mcpActivityLogs', this.activityLogs.slice(-100));
        } catch (error) {
            console.error('Failed to save MCP security data:', error);
        }
    }

    // Risk Assessment Methods

    assessToolRisk(tool: ExtendedMCPTool): 'low' | 'medium' | 'high' {
        if (!this.securityPolicy.riskAssessment) {
            return 'low';
        }

        const toolName = tool.name.toLowerCase();
        const description = (tool.description || '').toLowerCase();

        // Check blocked tools
        if (this.securityPolicy.blockedTools?.some(blocked => 
            toolName.includes(blocked.toLowerCase()) || description.includes(blocked.toLowerCase())
        )) {
            return 'high';
        }

        // High risk indicators
        const highRiskKeywords = [
            'delete', 'remove', 'destroy', 'execute', 'run', 'shell', 'command', 
            'admin', 'root', 'sudo', 'kill', 'terminate', 'format', 'wipe'
        ];
        
        if (highRiskKeywords.some(keyword => 
            toolName.includes(keyword) || description.includes(keyword)
        )) {
            return 'high';
        }

        // Medium risk indicators
        const mediumRiskKeywords = [
            'write', 'create', 'modify', 'update', 'change', 'edit', 
            'upload', 'download', 'install', 'configure', 'set'
        ];
        
        if (mediumRiskKeywords.some(keyword => 
            toolName.includes(keyword) || description.includes(keyword)
        )) {
            return 'medium';
        }

        // Check if tool is explicitly allowed
        if (this.securityPolicy.allowedTools?.some(allowed => 
            toolName.includes(allowed.toLowerCase())
        )) {
            return 'low';
        }

        return 'low';
    }

    assessDomainRisk(url: string): 'low' | 'medium' | 'high' {
        try {
            const domain = new URL(url).hostname.toLowerCase();

            // Check blocked domains
            if (this.securityPolicy.blockedDomains?.some(blocked => 
                domain.includes(blocked.toLowerCase())
            )) {
                return 'high';
            }

            // Check allowed domains
            if (this.securityPolicy.allowedDomains?.some(allowed => 
                domain.includes(allowed.toLowerCase())
            )) {
                return 'low';
            }

            // Known safe domains
            const safeDomains = [
                'github.com', 'stackoverflow.com', 'wikipedia.org', 
                'google.com', 'microsoft.com', 'openai.com', 'anthropic.com'
            ];
            
            if (safeDomains.some(safe => domain.includes(safe))) {
                return 'low';
            }

            // Unknown domains are medium risk by default
            return 'medium';

        } catch (error) {
            // Invalid URL is high risk
            return 'high';
        }
    }

    // User Consent Management

    async requestUserConsent(
        serverId: string, 
        toolName: string, 
        riskLevel: 'low' | 'medium' | 'high',
        context?: {
            serverName?: string;
            toolDescription?: string;
            args?: any;
        }
    ): Promise<boolean> {
        const consentKey = `${serverId}:${toolName}`;
        
        // Check existing consent
        const existingConsent = this.userConsents.get(consentKey);
        if (existingConsent && existingConsent.granted) {
            if (!existingConsent.expiresAt || existingConsent.expiresAt > new Date()) {
                return true;
            }
        }

        // Skip consent for low-risk tools if policy allows
        if (riskLevel === 'low' && !this.securityPolicy.requireConfirmation) {
            const autoConsent: MCPUserConsent = {
                toolName,
                serverId,
                granted: true,
                timestamp: new Date(),
                riskLevel,
                scope: ['auto-approved']
            };
            this.userConsents.set(consentKey, autoConsent);
            await this.saveData();
            return true;
        }

        // Build consent request message
        const serverName = context?.serverName || serverId;
        const description = context?.toolDescription || 'No description available';
        
        let message = `🔧 Tool Execution Request\n\n`;
        message += `Server: ${serverName}\n`;
        message += `Tool: ${toolName}\n`;
        message += `Risk Level: ${riskLevel.toUpperCase()}\n`;
        message += `Description: ${description}\n\n`;
        
        if (context?.args && Object.keys(context.args).length > 0) {
            message += `Arguments:\n${JSON.stringify(context.args, null, 2)}\n\n`;
        }

        message += `Do you want to allow this tool to execute?`;

        // Show consent dialog with appropriate options based on risk level
        const options = riskLevel === 'high' ? 
            ['Allow Once', 'Deny'] : 
            ['Allow Once', 'Allow for Session', 'Allow Always', 'Deny'];

        const choice = await vscode.window.showWarningMessage(
            message,
            { modal: true },
            ...options
        );

        const granted = choice !== 'Deny' && choice !== undefined;
        let expiresAt: Date | undefined;

        if (choice === 'Allow for Session') {
            // Expires when VS Code session ends (approximated as 8 hours)
            expiresAt = new Date(Date.now() + 8 * 60 * 60 * 1000);
        } else if (choice === 'Allow Once') {
            // Expires in 1 hour
            expiresAt = new Date(Date.now() + 60 * 60 * 1000);
        }
        // 'Allow Always' has no expiration (expiresAt remains undefined)

        // Store consent decision
        const consent: MCPUserConsent = {
            toolName,
            serverId,
            granted,
            timestamp: new Date(),
            expiresAt,
            riskLevel,
            scope: choice ? [choice.toLowerCase().replace(' ', '-')] : ['denied']
        };

        this.userConsents.set(consentKey, consent);
        await this.saveData();

        // Log consent decision
        this.logActivity({
            serverId,
            serverName: context?.serverName || serverId,
            action: 'tool_call',
            details: { 
                toolName, 
                consentGranted: granted, 
                consentType: choice,
                riskLevel 
            },
            success: granted,
            error: granted ? undefined : 'User denied consent'
        });

        this.emit('consent_decision', {
            serverId,
            toolName,
            granted,
            riskLevel,
            choice
        });

        return granted;
    }

    hasValidConsent(serverId: string, toolName: string): boolean {
        const consentKey = `${serverId}:${toolName}`;
        const consent = this.userConsents.get(consentKey);
        
        if (!consent || !consent.granted) {
            return false;
        }

        if (consent.expiresAt && consent.expiresAt <= new Date()) {
            // Consent has expired, remove it
            this.userConsents.delete(consentKey);
            this.saveData();
            return false;
        }

        return true;
    }

    revokeConsent(serverId: string, toolName?: string): void {
        if (toolName) {
            // Revoke specific tool consent
            const consentKey = `${serverId}:${toolName}`;
            this.userConsents.delete(consentKey);
        } else {
            // Revoke all consents for server
            for (const [key, consent] of this.userConsents.entries()) {
                if (consent.serverId === serverId) {
                    this.userConsents.delete(key);
                }
            }
        }
        
        this.saveData();
        this.emit('consent_revoked', { serverId, toolName });
    }

    // Activity Logging

    logActivity(activity: Omit<MCPActivityLog, 'id' | 'timestamp'>): void {
        const log: MCPActivityLog = {
            id: `activity-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date(),
            ...activity
        };

        this.activityLogs.push(log);

        // Keep only last 1000 logs
        if (this.activityLogs.length > 1000) {
            this.activityLogs = this.activityLogs.slice(-1000);
        }

        // Save periodically (every 10 logs)
        if (this.activityLogs.length % 10 === 0) {
            this.saveData();
        }

        this.emit('activity_logged', log);

        // Send telemetry if enabled (anonymized)
        if (this.enableTelemetry && this.securityPolicy.logAllActivities) {
            this.sendTelemetry(log);
        }
    }

    private sendTelemetry(log: MCPActivityLog): void {
        // Anonymize and send telemetry data
        // This would integrate with VS Code's telemetry system
        try {
            const anonymizedLog = {
                action: log.action,
                success: log.success,
                duration: log.duration,
                riskLevel: log.riskLevel,
                timestamp: log.timestamp.toISOString()
            };
            
            // VS Code telemetry would be sent here
            console.debug('Telemetry:', anonymizedLog);
        } catch (error) {
            // Silently fail telemetry
        }
    }

    // Policy Management

    getSecurityPolicy(): MCPSecurityPolicy {
        return { ...this.securityPolicy };
    }

    async updateSecurityPolicy(updates: Partial<MCPSecurityPolicy>): Promise<void> {
        const oldPolicy = { ...this.securityPolicy };
        this.securityPolicy = { ...this.securityPolicy, ...updates };
        
        await this.saveData();
        
        this.emit('policy_updated', {
            oldPolicy,
            newPolicy: this.securityPolicy,
            changes: updates
        });
    }

    // Data Access Methods

    getUserConsents(): MCPUserConsent[] {
        return Array.from(this.userConsents.values());
    }

    getActivityLogs(limit?: number): MCPActivityLog[] {
        const logs = [...this.activityLogs].reverse(); // Most recent first
        return limit ? logs.slice(0, limit) : logs;
    }

    getSecurityMetrics(): {
        totalConsents: number;
        activeConsents: number;
        expiredConsents: number;
        deniedRequests: number;
        highRiskActions: number;
        recentActivity: number;
    } {
        const now = new Date();
        const consents = Array.from(this.userConsents.values());
        
        const activeConsents = consents.filter(c => 
            c.granted && (!c.expiresAt || c.expiresAt > now)
        ).length;
        
        const expiredConsents = consents.filter(c => 
            c.expiresAt && c.expiresAt <= now
        ).length;
        
        const deniedRequests = this.activityLogs.filter(log => 
            log.action === 'tool_call' && !log.success && 
            log.error?.includes('consent')
        ).length;
        
        const highRiskActions = this.activityLogs.filter(log => 
            log.riskLevel === 'high'
        ).length;
        
        const recentActivity = this.activityLogs.filter(log => 
            log.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        ).length;

        return {
            totalConsents: consents.length,
            activeConsents,
            expiredConsents,
            deniedRequests,
            highRiskActions,
            recentActivity
        };
    }

    // Cleanup

    async clearExpiredConsents(): Promise<number> {
        const now = new Date();
        let removedCount = 0;
        
        for (const [key, consent] of this.userConsents.entries()) {
            if (consent.expiresAt && consent.expiresAt <= now) {
                this.userConsents.delete(key);
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            await this.saveData();
            this.emit('consents_cleaned', { removedCount });
        }
        
        return removedCount;
    }

    async dispose(): Promise<void> {
        await this.saveData();
        this.userConsents.clear();
        this.activityLogs.length = 0;
        this.emit('disposed');
    }
}
