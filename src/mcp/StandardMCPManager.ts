/**
 * Standard MCP Manager
 * 
 * Manages MCP servers following industry standards (<PERSON>, VS Code, Cursor, etc.)
 * Provides unified interface for STDIO, HTTP, and SSE transports with proper lifecycle management
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';
import { StandardMCPConfig, StandardMCPServerConfig } from './MCPConfigEditorProvider';
import { AizenMCPClient } from './MCPClient';
import { MCPSecurityManager } from './MCPSecurityManager';

export interface MCPServerInstance {
    id: string;
    config: StandardMCPServerConfig;
    status: 'starting' | 'running' | 'stopped' | 'error';
    process?: ChildProcess;
    lastError?: string;
    startTime?: Date;
    tools?: string[];
    resources?: string[];
}

export class StandardMCPManager {
    private context: vscode.ExtensionContext;
    private mcpClient: AizenMCPClient;
    private securityManager: MCPSecurityManager;
    private configPath: string;
    private servers: Map<string, MCPServerInstance> = new Map();
    private fileWatcher: vscode.FileSystemWatcher | undefined;
    private onServerStatusChangedEmitter = new vscode.EventEmitter<MCPServerInstance>();
    public readonly onServerStatusChanged = this.onServerStatusChangedEmitter.event;

    constructor(
        context: vscode.ExtensionContext,
        mcpClient: AizenMCPClient,
        securityManager: MCPSecurityManager
    ) {
        this.context = context;
        this.mcpClient = mcpClient;
        this.securityManager = securityManager;
        this.configPath = this.getConfigPath();
        this.setupFileWatcher();
    }

    private getConfigPath(): string {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return path.join(workspaceFolders[0].uri.fsPath, '.vscode', 'mcp.json');
        }
        
        const globalStoragePath = this.context.globalStorageUri.fsPath;
        if (!fs.existsSync(globalStoragePath)) {
            fs.mkdirSync(globalStoragePath, { recursive: true });
        }
        return path.join(globalStoragePath, 'mcp.json');
    }

    private setupFileWatcher(): void {
        const configDir = path.dirname(this.configPath);
        this.fileWatcher = vscode.workspace.createFileSystemWatcher(
            new vscode.RelativePattern(configDir, 'mcp.json')
        );

        this.fileWatcher.onDidChange(() => this.reloadConfiguration());
        this.fileWatcher.onDidCreate(() => this.reloadConfiguration());
        this.fileWatcher.onDidDelete(() => this.stopAllServers());
    }

    async initialize(): Promise<void> {
        try {
            console.log('🔧 Initializing Standard MCP Manager...');
            await this.loadConfiguration();
            console.log('✅ Standard MCP Manager initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize Standard MCP Manager:', error);
            vscode.window.showErrorMessage(`Failed to initialize MCP manager: ${error}`);
        }
    }

    async loadConfiguration(): Promise<void> {
        try {
            if (!fs.existsSync(this.configPath)) {
                console.log('📝 No MCP configuration found, creating default...');
                await this.createDefaultConfiguration();
                return;
            }

            const content = fs.readFileSync(this.configPath, 'utf8');
            const config: StandardMCPConfig = JSON.parse(content);

            // Validate configuration
            this.validateConfiguration(config);

            // Stop existing servers
            await this.stopAllServers();

            // Start new servers
            await this.startServersFromConfig(config);

            console.log(`✅ Loaded ${Object.keys(config.mcpServers).length} MCP servers from configuration`);

        } catch (error) {
            console.error('❌ Error loading MCP configuration:', error);
            vscode.window.showErrorMessage(`Failed to load MCP configuration: ${error}`);
        }
    }

    private async createDefaultConfiguration(): Promise<void> {
        const defaultConfig: StandardMCPConfig = {
            mcpServers: {
                "example-stdio": {
                    command: "npx",
                    args: ["@modelcontextprotocol/server-everything"],
                    disabled: true
                },
                "example-http": {
                    url: "https://api.example.com/mcp",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    disabled: true
                }
            },
            globalSettings: {
                autoStart: true,
                maxConcurrentServers: 10,
                defaultTimeout: 30000,
                logLevel: 'info'
            },
            security: {
                requireConfirmation: true,
                allowedCommands: ['node', 'python', 'python3', 'npx', 'bun'],
                blockedCommands: ['rm', 'del', 'sudo', 'format', 'shutdown'],
                allowedDomains: ['api.github.com', 'api.openai.com'],
                maxExecutionTime: 60000
            },
            metadata: {
                name: 'Aizen AI MCP Configuration',
                description: 'Standard MCP server configuration',
                author: 'Aizen AI',
                created: new Date().toISOString(),
                lastModified: new Date().toISOString()
            }
        };

        const configDir = path.dirname(this.configPath);
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }

        fs.writeFileSync(this.configPath, JSON.stringify(defaultConfig, null, 2), 'utf8');
        console.log('📝 Created default MCP configuration at:', this.configPath);
    }

    private validateConfiguration(config: StandardMCPConfig): void {
        if (!config.mcpServers || typeof config.mcpServers !== 'object') {
            throw new Error('Configuration must have mcpServers object');
        }

        for (const [serverId, serverConfig] of Object.entries(config.mcpServers)) {
            this.validateServerConfig(serverId, serverConfig);
        }
    }

    private validateServerConfig(serverId: string, config: StandardMCPServerConfig): void {
        if (!serverId.match(/^[a-zA-Z0-9_-]+$/)) {
            throw new Error(`Invalid server ID: ${serverId}`);
        }

        const hasStdio = !!config.command;
        const hasHttp = !!(config.url || config.serverUrl);

        if (!hasStdio && !hasHttp) {
            throw new Error(`Server ${serverId}: Must have either command or url/serverUrl`);
        }

        if (hasStdio && hasHttp) {
            throw new Error(`Server ${serverId}: Cannot have both STDIO and HTTP transport`);
        }

        if (hasStdio && (!config.command || config.command.trim().length === 0)) {
            throw new Error(`Server ${serverId}: command cannot be empty`);
        }

        if (hasHttp) {
            const url = config.url || config.serverUrl;
            if (!url || !url.startsWith('http')) {
                throw new Error(`Server ${serverId}: URL must start with http:// or https://`);
            }
        }
    }

    private async startServersFromConfig(config: StandardMCPConfig): Promise<void> {
        const globalSettings = config.globalSettings || {};
        const maxConcurrent = globalSettings.maxConcurrentServers || 10;
        let startedCount = 0;

        for (const [serverId, serverConfig] of Object.entries(config.mcpServers)) {
            if (serverConfig.disabled) {
                console.log(`⏸️ Skipping disabled server: ${serverId}`);
                continue;
            }

            if (startedCount >= maxConcurrent) {
                console.log(`⚠️ Reached maximum concurrent servers (${maxConcurrent}), skipping: ${serverId}`);
                continue;
            }

            try {
                await this.startServer(serverId, serverConfig);
                startedCount++;
            } catch (error) {
                console.error(`❌ Failed to start server ${serverId}:`, error);
                vscode.window.showWarningMessage(`Failed to start MCP server "${serverId}": ${error}`);
            }
        }
    }

    async startServer(serverId: string, config: StandardMCPServerConfig): Promise<void> {
        if (this.servers.has(serverId)) {
            await this.stopServer(serverId);
        }

        const serverInstance: MCPServerInstance = {
            id: serverId,
            config: config,
            status: 'starting',
            startTime: new Date()
        };

        this.servers.set(serverId, serverInstance);
        this.onServerStatusChangedEmitter.fire(serverInstance);

        try {
            if (config.command) {
                await this.startStdioServer(serverInstance);
            } else if (config.url || config.serverUrl) {
                await this.startHttpServer(serverInstance);
            }

            serverInstance.status = 'running';
            console.log(`✅ Started MCP server: ${serverId}`);

        } catch (error) {
            serverInstance.status = 'error';
            serverInstance.lastError = error.message;
            console.error(`❌ Failed to start server ${serverId}:`, error);
            throw error;
        } finally {
            this.onServerStatusChangedEmitter.fire(serverInstance);
        }
    }

    private async startStdioServer(serverInstance: MCPServerInstance): Promise<void> {
        const config = serverInstance.config;
        
        // Security validation
        await this.validateStdioSecurity(config);

        // Prepare environment
        const env = { ...process.env, ...config.env };

        // Spawn process
        const serverProcess = spawn(config.command!, config.args || [], {
            cwd: config.cwd || process.cwd(),
            env: env,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        serverInstance.process = serverProcess;

        // Handle process events
        serverProcess.on('error', (error) => {
            console.error(`Process error for ${serverInstance.id}:`, error);
            serverInstance.status = 'error';
            serverInstance.lastError = error.message;
            this.onServerStatusChangedEmitter.fire(serverInstance);
        });

        serverProcess.on('exit', (code, signal) => {
            console.log(`Process exited for ${serverInstance.id}: code=${code}, signal=${signal}`);
            serverInstance.status = 'stopped';
            if (code !== 0) {
                serverInstance.lastError = `Process exited with code ${code}`;
            }
            this.onServerStatusChangedEmitter.fire(serverInstance);
        });

        // Connect to MCP client
        await this.connectServerToClient(serverInstance);
    }

    private async startHttpServer(serverInstance: MCPServerInstance): Promise<void> {
        const config = serverInstance.config;
        
        // Security validation
        await this.validateHttpSecurity(config);

        // Connect to MCP client
        await this.connectServerToClient(serverInstance);
    }

    private async connectServerToClient(serverInstance: MCPServerInstance): Promise<void> {
        // Convert to extended config format for MCP client
        const extendedConfig = {
            id: serverInstance.id,
            name: serverInstance.id,
            description: `MCP server: ${serverInstance.id}`,
            transport: this.getTransportType(serverInstance.config),
            command: serverInstance.config.command,
            args: serverInstance.config.args,
            env: serverInstance.config.env,
            url: serverInstance.config.url || serverInstance.config.serverUrl,
            enabled: !serverInstance.config.disabled,
            autoStart: true,
            timeout: 30000,
            retryAttempts: 3,
            retryDelay: 1000,
            isBuiltIn: false,
            requiresApiKey: false,
            apiKeyConfigured: true,
            category: 'external',
            icon: '🔧',
            riskLevel: 'medium' as const,
            priority: 50
        };

        await this.mcpClient.addExternalServer(extendedConfig);
    }

    private getTransportType(config: StandardMCPServerConfig): 'stdio' | 'http' | 'sse' {
        if (config.command) return 'stdio';
        if (config.serverUrl) return 'sse';
        return 'http';
    }

    private async validateStdioSecurity(config: StandardMCPServerConfig): Promise<void> {
        // Load security settings
        const securityConfig = await this.loadSecurityConfig();
        
        if (securityConfig?.blockedCommands?.some(blocked => 
            config.command!.toLowerCase().includes(blocked.toLowerCase())
        )) {
            throw new Error(`Command "${config.command}" is blocked by security policy`);
        }

        if (securityConfig?.allowedCommands && securityConfig.allowedCommands.length > 0) {
            const isAllowed = securityConfig.allowedCommands.some(allowed => 
                config.command!.toLowerCase().startsWith(allowed.toLowerCase())
            );
            if (!isAllowed) {
                throw new Error(`Command "${config.command}" is not in allowed commands list`);
            }
        }
    }

    private async validateHttpSecurity(config: StandardMCPServerConfig): Promise<void> {
        const url = config.url || config.serverUrl;
        if (!url) return;

        const securityConfig = await this.loadSecurityConfig();
        const urlObj = new URL(url);
        const hostname = urlObj.hostname.toLowerCase();

        if (securityConfig?.allowedDomains && securityConfig.allowedDomains.length > 0) {
            const isAllowed = securityConfig.allowedDomains.some(allowed => 
                hostname.includes(allowed.toLowerCase())
            );
            if (!isAllowed) {
                throw new Error(`Domain "${hostname}" is not in allowed domains list`);
            }
        }

        // Require HTTPS for external domains
        if (urlObj.protocol === 'http:' && !this.isLocalDomain(hostname)) {
            console.warn(`Warning: Using HTTP for external domain: ${hostname}`);
        }
    }

    private async loadSecurityConfig(): Promise<StandardMCPConfig['security'] | undefined> {
        try {
            if (fs.existsSync(this.configPath)) {
                const content = fs.readFileSync(this.configPath, 'utf8');
                const config: StandardMCPConfig = JSON.parse(content);
                return config.security;
            }
        } catch (error) {
            console.error('Error loading security config:', error);
        }
        return undefined;
    }

    private isLocalDomain(hostname: string): boolean {
        return ['localhost', '127.0.0.1', '::1'].includes(hostname) ||
               hostname.startsWith('192.168.') ||
               hostname.startsWith('10.') ||
               hostname.startsWith('172.');
    }

    async stopServer(serverId: string): Promise<void> {
        const serverInstance = this.servers.get(serverId);
        if (!serverInstance) return;

        try {
            // Remove from MCP client
            await this.mcpClient.removeServer(serverId);

            // Stop process if it exists
            if (serverInstance.process) {
                serverInstance.process.kill('SIGTERM');
                
                // Force kill after timeout
                setTimeout(() => {
                    if (serverInstance.process && !serverInstance.process.killed) {
                        serverInstance.process.kill('SIGKILL');
                    }
                }, 5000);
            }

            serverInstance.status = 'stopped';
            this.onServerStatusChangedEmitter.fire(serverInstance);
            
            console.log(`🛑 Stopped MCP server: ${serverId}`);

        } catch (error) {
            console.error(`Error stopping server ${serverId}:`, error);
        }
    }

    async stopAllServers(): Promise<void> {
        const stopPromises = Array.from(this.servers.keys()).map(serverId => 
            this.stopServer(serverId)
        );
        await Promise.all(stopPromises);
        this.servers.clear();
    }

    async reloadConfiguration(): Promise<void> {
        console.log('🔄 Reloading MCP configuration...');
        await this.loadConfiguration();
    }

    getServerStatus(serverId: string): MCPServerInstance | undefined {
        return this.servers.get(serverId);
    }

    getAllServers(): MCPServerInstance[] {
        return Array.from(this.servers.values());
    }

    getConfigurationPath(): string {
        return this.configPath;
    }

    dispose(): void {
        this.stopAllServers();
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
        }
        this.onServerStatusChangedEmitter.dispose();
    }
}
