// MCP Types and Interfaces - Updated for Protocol 2025-06-18
import { z } from 'zod';

// MCP Protocol Version - Latest
export const MCP_PROTOCOL_VERSION = "2025-06-18";

// JSON-RPC 2.0 Base Types
export interface JsonRpcRequest {
    jsonrpc: "2.0";
    id: string | number;
    method: string;
    params?: any;
}

export interface JsonRpcResponse {
    jsonrpc: "2.0";
    id: string | number;
    result?: any;
    error?: JsonRpcError;
}

export interface JsonRpcNotification {
    jsonrpc: "2.0";
    method: string;
    params?: any;
}

export interface JsonRpcError {
    code: number;
    message: string;
    data?: any;
}

// MCP Error Codes
export enum MCPErrorCode {
    // Standard JSON-RPC errors
    ParseError = -32700,
    InvalidRequest = -32600,
    MethodNotFound = -32601,
    InvalidParams = -32602,
    InternalError = -32603,
    
    // MCP-specific errors
    InvalidCapabilities = -32000,
    InvalidTool = -32001,
    InvalidResource = -32002,
    InvalidPrompt = -32003,
    ResourceNotFound = -32004,
    ToolExecutionError = -32005,
    PromptNotFound = -32006,
    Unauthorized = -32007,
    RateLimited = -32008,
    ServerError = -32009
}

// MCP Capabilities - Updated for 2025-06-18
export interface MCPCapabilities {
    tools?: {
        listChanged?: boolean;
    };
    resources?: {
        subscribe?: boolean;
        listChanged?: boolean;
    };
    prompts?: {
        listChanged?: boolean;
    };
    sampling?: {};
    roots?: {
        listChanged?: boolean;
    };
    elicitation?: {};
    logging?: {};
}

// Client/Server Info
export interface ClientInfo {
    name: string;
    version: string;
}

export interface ServerInfo {
    name: string;
    version: string;
}

// MCP Content Types
export interface TextContent {
    type: "text";
    text: string;
}

export interface ImageContent {
    type: "image";
    data: string;
    mimeType: string;
}

export interface ResourceContent {
    type: "resource";
    resource: {
        uri: string;
        text?: string;
        mimeType?: string;
    };
}

export type MCPContent = TextContent | ImageContent | ResourceContent;

// MCP Tool Types
export interface MCPTool {
    name: string;
    description?: string;
    inputSchema: z.ZodSchema | object;
}

export interface ToolResult {
    content?: MCPContent[];
    isError?: boolean;
}

// MCP Resource Types
export interface MCPResource {
    uri: string;
    name: string;
    description?: string;
    mimeType?: string;
}

export interface ResourceContents {
    contents: Array<{
        uri: string;
        mimeType?: string;
        text?: string;
        blob?: string;
    }>;
}

// MCP Prompt Types
export interface MCPPrompt {
    name: string;
    description?: string;
    arguments?: Array<{
        name: string;
        description?: string;
        required?: boolean;
    }>;
}

export interface PromptMessage {
    role: "user" | "assistant" | "system";
    content: MCPContent;
}

export interface GetPromptResult {
    description?: string;
    messages: PromptMessage[];
}

// MCP Request Types
export interface InitializeRequest extends JsonRpcRequest {
    method: "initialize";
    params: {
        protocolVersion: string;
        capabilities: MCPCapabilities;
        clientInfo: ClientInfo;
    };
}

export interface InitializeResult {
    protocolVersion: string;
    capabilities: MCPCapabilities;
    serverInfo: ServerInfo;
}

export interface ListToolsRequest extends JsonRpcRequest {
    method: "tools/list";
    params?: {};
}

export interface ListToolsResult {
    tools: MCPTool[];
}

export interface CallToolRequest extends JsonRpcRequest {
    method: "tools/call";
    params: {
        name: string;
        arguments?: Record<string, any>;
    };
}

export interface CallToolResult extends ToolResult {}

export interface ListResourcesRequest extends JsonRpcRequest {
    method: "resources/list";
    params?: {};
}

export interface ListResourcesResult {
    resources: MCPResource[];
}

export interface ReadResourceRequest extends JsonRpcRequest {
    method: "resources/read";
    params: {
        uri: string;
    };
}

export interface ReadResourceResult extends ResourceContents {}

export interface ListPromptsRequest extends JsonRpcRequest {
    method: "prompts/list";
    params?: {};
}

export interface ListPromptsResult {
    prompts: MCPPrompt[];
}

export interface GetPromptRequest extends JsonRpcRequest {
    method: "prompts/get";
    params: {
        name: string;
        arguments?: Record<string, any>;
    };
}

export interface GetPromptResult {
    description?: string;
    messages: PromptMessage[];
}

// MCP Notification Types
export interface ToolsListChangedNotification extends JsonRpcNotification {
    method: "notifications/tools/list_changed";
}

export interface ResourcesListChangedNotification extends JsonRpcNotification {
    method: "notifications/resources/list_changed";
}

export interface PromptsListChangedNotification extends JsonRpcNotification {
    method: "notifications/prompts/list_changed";
}

// MCP Server Configuration
export interface MCPServerConfig {
    name: string;
    command?: string;
    args?: string[];
    env?: Record<string, string>;
    url?: string;
    transport: MCPTransportType;
    enabled: boolean;
    autoStart?: boolean;
    timeout?: number;
    retryAttempts?: number;
    retryDelay?: number;
    riskLevel?: 'low' | 'medium' | 'high';
    priority?: number;
}

// MCP Connection Status
export enum MCPConnectionStatus {
    Disconnected = 'disconnected',
    Connecting = 'connecting',
    Connected = 'connected',
    Error = 'error',
    Reconnecting = 'reconnecting'
}

// MCP Server Instance
export interface MCPServerInstance {
    id: string;
    config: MCPServerConfig;
    status: MCPConnectionStatus;
    capabilities?: MCPCapabilities;
    tools?: MCPTool[];
    resources?: MCPResource[];
    prompts?: MCPPrompt[];
    lastError?: string;
    connectedAt?: Date;
    lastActivity?: Date;
}

// Security and Permission Types
export interface MCPPermission {
    serverId: string;
    toolName?: string;
    resourceUri?: string;
    promptName?: string;
    granted: boolean;
    grantedAt: Date;
    expiresAt?: Date;
    scope: 'tool' | 'resource' | 'prompt' | 'server';
}

export interface MCPSecurityContext {
    userId: string;
    permissions: MCPPermission[];
    requireExplicitConsent: boolean;
    allowedDomains?: string[];
    blockedDomains?: string[];
}

// Extended types for Aizen AI MCP Implementation
export interface ExtendedMCPServerConfig extends MCPServerConfig {
    id: string;
    description?: string;
    version?: string;
    capabilities?: MCPCapabilities;
    metadata?: Record<string, any>;
    isBuiltIn?: boolean;
    requiresApiKey?: boolean;
    apiKeyConfigured?: boolean;
    category?: string;
    icon?: string;
}

export interface MCPServerStatus {
    id: string;
    name: string;
    status: MCPConnectionStatus;
    lastConnected?: Date;
    lastError?: string;
    toolCount: number;
    resourceCount: number;
    promptCount: number;
    uptime?: number;
    transport?: string;
    version?: string;
    isBuiltIn?: boolean;
}

export interface MCPSecurityPolicy {
    allowedDomains?: string[];
    blockedDomains?: string[];
    requireConfirmation: boolean;
    maxExecutionTime: number;
    allowedTools?: string[];
    blockedTools?: string[];
    userConsentRequired: boolean;
    logAllActivities: boolean;
    riskAssessment: boolean;
}

export interface MCPUserConsent {
    toolName: string;
    serverId: string;
    granted: boolean;
    timestamp: Date;
    expiresAt?: Date;
    scope?: string[];
    riskLevel: 'low' | 'medium' | 'high';
    userNote?: string;
}

export interface MCPActivityLog {
    id: string;
    timestamp: Date;
    serverId: string;
    serverName: string;
    action: 'tool_call' | 'resource_read' | 'prompt_get' | 'server_connect' | 'server_disconnect';
    details: any;
    userId?: string;
    success: boolean;
    error?: string;
    duration?: number;
    riskLevel?: 'low' | 'medium' | 'high';
}

export interface MCPConfiguration {
    servers: ExtendedMCPServerConfig[];
    security: MCPSecurityPolicy;
    ui: {
        theme: 'auto' | 'light' | 'dark';
        showNotifications: boolean;
        confirmHighRiskActions: boolean;
        autoConnectBuiltins: boolean;
    };
    advanced: {
        debugMode: boolean;
        logLevel: 'error' | 'warn' | 'info' | 'debug';
        maxConcurrentConnections: number;
        connectionTimeout: number;
        enableTelemetry: boolean;
    };
}

// Built-in Server Types
export interface BuiltinServerConfig extends Omit<ExtendedMCPServerConfig, 'id' | 'env'> {
    id: 'exa-ai' | 'firecrawl';
    apiKeyRequired: boolean;
    apiKeyConfigured: boolean;
    officialSupport: boolean;
    preConfiguredApiKey?: string;
}

// Event Types
export interface MCPEvent {
    type: 'server_connected' | 'server_disconnected' | 'tool_executed' | 'error' | 'consent_requested' | 'config_changed';
    serverId: string;
    data: any;
    timestamp: Date;
    severity?: 'info' | 'warning' | 'error';
}

// Transport Types
export type MCPTransportType = 'stdio' | 'http' | 'streamable-http' | 'sse';

// Transport Configuration
export interface MCPTransportConfig {
    type: MCPTransportType;
    options: {
        command?: string;
        args?: string[];
        env?: Record<string, string>;
        url?: string;
        headers?: Record<string, string>;
        timeout?: number;
        retries?: number;
        backoff?: number;
    };
}

// Enhanced Tool Types
export interface ExtendedMCPTool {
    name: string;
    description?: string;
    inputSchema?: z.ZodSchema | object;
    outputSchema?: z.ZodSchema | object;
    serverId: string;
    serverName?: string;
    category?: string;
    riskLevel?: 'low' | 'medium' | 'high';
    requiresConfirmation?: boolean;
    lastUsed?: Date;
    usageCount?: number;
    averageExecutionTime?: number;
}

// Enhanced Resource Types
export interface ExtendedMCPResource {
    uri: string;
    name?: string;
    description?: string;
    mimeType?: string;
    serverId: string;
    serverName?: string;
    lastModified?: string;
    size?: number;
    category?: string;
    accessCount?: number;
}

// Enhanced Prompt Types
export interface ExtendedMCPPrompt {
    name: string;
    description?: string;
    arguments?: Array<{
        name: string;
        description?: string;
        required?: boolean;
    }>;
    serverId: string;
    serverName?: string;
    category?: string;
    lastUsed?: Date;
    usageCount?: number;
}

// Error Types
export class MCPError extends Error {
    constructor(
        message: string,
        public code: string,
        public serverId?: string,
        public details?: any
    ) {
        super(message);
        this.name = 'MCPError';
    }
}

export class MCPConnectionError extends MCPError {
    constructor(message: string, serverId: string, details?: any) {
        super(message, 'CONNECTION_ERROR', serverId, details);
        this.name = 'MCPConnectionError';
    }
}

export class MCPSecurityError extends MCPError {
    constructor(message: string, serverId: string, details?: any) {
        super(message, 'SECURITY_ERROR', serverId, details);
        this.name = 'MCPSecurityError';
    }
}

export class MCPToolError extends MCPError {
    constructor(message: string, toolName: string, serverId: string, details?: any) {
        super(message, 'TOOL_ERROR', serverId, { toolName, ...details });
        this.name = 'MCPToolError';
    }
}
