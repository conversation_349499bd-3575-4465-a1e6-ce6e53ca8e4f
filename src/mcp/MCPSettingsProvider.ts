/**
 * MCP Settings Provider - Integrates MCP functionality with VS Code settings UI
 * 
 * This component provides:
 * - Settings webview integration
 * - Message handling between UI and MCP client
 * - Configuration persistence
 * - Real-time status updates
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { AizenMCPClient } from './MCPClient';
import { MCPSecurityManager } from './MCPSecurityManager';
import { MCPConfigEditorProvider } from './MCPConfigEditorProvider';
import {
    ExtendedMCPServerConfig,
    MCPSecurityPolicy
} from './types';

export class MCPSettingsProvider {
    private context: vscode.ExtensionContext;
    private mcpClient: AizenMCPClient;
    private securityManager: MCPSecurityManager;
    private configEditorProvider: MCPConfigEditorProvider;
    private webviewPanel: vscode.WebviewPanel | undefined;

    constructor(
        context: vscode.ExtensionContext,
        mcpClient: AizenMCPClient,
        securityManager: MCPSecurityManager,
        configEditorProvider: MCPConfigEditorProvider
    ) {
        this.context = context;
        this.mcpClient = mcpClient;
        this.securityManager = securityManager;
        this.configEditorProvider = configEditorProvider;

        // Listen for MCP events to update UI
        this.setupEventListeners();
    }

    private setupEventListeners(): void {
        // MCP Client events
        this.mcpClient.on('server_connected', (data) => {
            this.sendToWebview({
                type: 'serverStatusUpdate',
                serverId: data.serverId,
                status: 'connected',
                data
            });
        });

        this.mcpClient.on('server_disconnected', (data) => {
            this.sendToWebview({
                type: 'serverStatusUpdate',
                serverId: data.serverId,
                status: 'disconnected',
                data
            });
        });

        this.mcpClient.on('server_error', (data) => {
            this.sendToWebview({
                type: 'serverStatusUpdate',
                serverId: data.serverId,
                status: 'error',
                data
            });
        });

        this.mcpClient.on('server_status_changed', (data) => {
            this.sendToWebview({
                type: 'serverStatusUpdate',
                serverId: data.serverId,
                status: data.status.status,
                data
            });
        });

        // Security Manager events
        this.securityManager.on('consent_decision', (data) => {
            this.sendToWebview({
                type: 'consentDecision',
                data
            });
        });

        this.securityManager.on('activity_logged', (data) => {
            this.sendToWebview({
                type: 'activityLogged',
                data
            });
        });
    }

    async openSettings(): Promise<void> {
        if (this.webviewPanel) {
            this.webviewPanel.reveal();
            return;
        }

        this.webviewPanel = vscode.window.createWebviewPanel(
            'aizenMCPSettings',
            'Aizen AI - MCP Settings',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                enableForms: true,
                localResourceRoots: [
                    vscode.Uri.file(path.join(this.context.extensionPath, 'out', 'ui')),
                    vscode.Uri.file(path.join(this.context.extensionPath, 'media'))
                ]
            }
        );

        this.webviewPanel.webview.html = await this.getWebviewContent();
        this.webviewPanel.webview.onDidReceiveMessage(
            (message) => this.handleWebviewMessage(message),
            undefined,
            this.context.subscriptions
        );

        this.webviewPanel.onDidDispose(() => {
            this.webviewPanel = undefined;
        });
    }

    private async getWebviewContent(): Promise<string> {
        const webview = this.webviewPanel!.webview;
        
        // Get URIs for resources
        const cssUri = webview.asWebviewUri(
            vscode.Uri.file(path.join(this.context.extensionPath, 'out', 'ui', 'settings.css'))
        );
        
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.file(path.join(this.context.extensionPath, 'out', 'ui', 'settings.js'))
        );

        const logoUri = webview.asWebviewUri(
            vscode.Uri.file(path.join(this.context.extensionPath, 'media', 'icons', 'aizen-logo-white.svg'))
        );

        const nonce = this.generateNonce();

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src ${webview.cspSource} 'nonce-${nonce}' 'unsafe-inline'; font-src ${webview.cspSource}; img-src ${webview.cspSource} data:; form-action 'self';"">
    
    <title>Aizen AI - MCP Settings</title>
    <link rel="stylesheet" href="${cssUri}">
</head>
<body class="settings-body">
    <div class="settings-container">
        <header class="settings-header glass">
            <div class="header-content">
                <div class="header-left">
                    <img src="${logoUri}" alt="Aizen AI" class="logo" width="32" height="32">
                    <div class="header-title">
                        <h1>MCP Configuration</h1>
                        <p>Manage Model Context Protocol servers and security</p>
                    </div>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" id="saveSettings">
                        💾 Save Settings
                    </button>
                </div>
            </div>
        </header>

        <main class="settings-main">
            <div class="settings-grid">
                <!-- MCP Server Management -->
                <section class="settings-section glass">
                    <div class="section-header">
                        <div class="section-icon">🔧</div>
                        <h2>MCP Servers</h2>
                    </div>
                    <div class="section-content">
                        <div id="mcpServerManager">
                            <div class="loading-spinner">Loading MCP servers...</div>
                        </div>
                    </div>
                </section>

                <!-- Security Settings -->
                <section class="settings-section glass">
                    <div class="section-header">
                        <div class="section-icon">🔒</div>
                        <h2>Security & Permissions</h2>
                    </div>
                    <div class="section-content">
                        <div class="setting-group">
                            <div class="toggle-setting">
                                <div class="toggle-info">
                                    <label class="setting-label">Require User Consent</label>
                                    <p class="setting-description">Ask for permission before executing tools</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="requireConsent" checked>
                                    <span class="toggle-slider glass"></span>
                                </label>
                            </div>
                        </div>

                        <div class="setting-group">
                            <div class="toggle-setting">
                                <div class="toggle-info">
                                    <label class="setting-label">Risk Assessment</label>
                                    <p class="setting-description">Automatically assess tool risk levels</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="riskAssessment" checked>
                                    <span class="toggle-slider glass"></span>
                                </label>
                            </div>
                        </div>

                        <div class="setting-group">
                            <label class="setting-label">Maximum Execution Time</label>
                            <div class="slider-container">
                                <input type="range" id="maxExecutionTime" class="setting-slider" 
                                       min="5000" max="300000" step="5000" value="60000">
                                <span class="slider-value" id="maxExecutionTimeValue">60s</span>
                            </div>
                        </div>

                        <div class="setting-group">
                            <div class="toggle-setting">
                                <div class="toggle-info">
                                    <label class="setting-label">Activity Logging</label>
                                    <p class="setting-description">Log all MCP activities for security</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="activityLogging" checked>
                                    <span class="toggle-slider glass"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Activity Monitor -->
                <section class="settings-section glass">
                    <div class="section-header">
                        <div class="section-icon">📊</div>
                        <h2>Activity Monitor</h2>
                    </div>
                    <div class="section-content">
                        <div id="activityMonitor">
                            <div class="activity-stats">
                                <div class="stat-card">
                                    <div class="stat-value" id="connectedServers">0</div>
                                    <div class="stat-label">Connected Servers</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value" id="totalTools">0</div>
                                    <div class="stat-label">Available Tools</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value" id="recentActivity">0</div>
                                    <div class="stat-label">Recent Activities</div>
                                </div>
                            </div>
                            
                            <div class="activity-log">
                                <h4>Recent Activity</h4>
                                <div id="activityList" class="activity-list">
                                    <!-- Activity items will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- User Consents -->
                <section class="settings-section glass">
                    <div class="section-header">
                        <div class="section-icon">✅</div>
                        <h2>User Consents</h2>
                    </div>
                    <div class="section-content">
                        <div id="userConsents">
                            <div class="consents-list" id="consentsList">
                                <!-- Consent items will be populated here -->
                            </div>
                            <button class="btn btn-secondary" id="clearExpiredConsents">
                                🧹 Clear Expired Consents
                            </button>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <footer class="settings-footer glass">
            <div class="footer-content">
                <div class="footer-left">
                    <button class="btn btn-secondary" id="resetMCPSettings">
                        🔄 Reset to Defaults
                    </button>
                </div>
                <div class="footer-right">
                    <span class="version-info">MCP v2.0.0</span>
                </div>
            </div>
        </footer>
    </div>

    <script nonce="${nonce}">
        const vscode = acquireVsCodeApi();
        window.vscode = vscode;
        
        // Initialize MCP settings
        document.addEventListener('DOMContentLoaded', () => {
            vscode.postMessage({ type: 'getMCPConfiguration' });
        });
    </script>
    <script nonce="${nonce}" src="${scriptUri}"></script>
</body>
</html>`;
    }

    private async handleWebviewMessage(message: any): Promise<void> {
        try {
            switch (message.type) {
                case 'getMCPConfiguration':
                    await this.sendMCPConfiguration();
                    break;

                case 'getMCPServers':
                    await this.sendMCPServers();
                    break;

                case 'toggleMCPServer':
                    await this.toggleMCPServer(message.serverId, message.enabled);
                    break;

                case 'testMCPConnection':
                    await this.testMCPConnection(message.serverId);
                    break;

                case 'addMCPServer':
                    await this.addMCPServer(message.config);
                    break;

                case 'removeMCPServer':
                    await this.removeMCPServer(message.serverId);
                    break;

                case 'updateSecurityPolicy':
                    await this.updateSecurityPolicy(message.policy);
                    break;

                case 'clearExpiredConsents':
                    await this.clearExpiredConsents();
                    break;

                case 'revokeConsent':
                    await this.revokeConsent(message.serverId, message.toolName);
                    break;

                case 'resetMCPSettings':
                    await this.resetMCPSettings();
                    break;

                case 'openMCPConfigurationFile':
                    await this.openMCPConfigurationFile();
                    break;

                case 'validateMCPConfiguration':
                    await this.validateMCPConfiguration();
                    break;

                case 'requestServerInput':
                    await this.handleServerInputRequest(message.action);
                    break;

                case 'reloadMCPConfiguration':
                    await this.reloadMCPConfiguration();
                    break;

                default:
                    console.warn('Unknown message type:', message.type);
            }
        } catch (error) {
            console.error('Error handling webview message:', error);
            this.sendToWebview({
                type: 'error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    private async sendMCPConfiguration(): Promise<void> {
        const config = {
            servers: Array.from(this.mcpClient.getConnectedServers()),
            security: this.securityManager.getSecurityPolicy(),
            health: this.mcpClient.getHealthStatus(),
            metrics: this.securityManager.getSecurityMetrics()
        };

        this.sendToWebview({
            type: 'mcpConfiguration',
            config
        });
    }

    private async sendMCPServers(): Promise<void> {
        const servers = this.mcpClient.getConnectedServers();
        const serverData = servers.map(server => ({
            config: server,
            status: this.mcpClient.getServerStatus(server.id),
            tools: this.mcpClient.getAvailableTools().filter(t => t.serverId === server.id),
            resources: this.mcpClient.getAvailableResources().filter(r => r.serverId === server.id),
            prompts: this.mcpClient.getAvailablePrompts().filter(p => p.serverId === server.id)
        }));

        this.sendToWebview({
            type: 'mcpServers',
            servers: serverData
        });
    }

    private async toggleMCPServer(serverId: string, enabled: boolean): Promise<void> {
        // Implementation for toggling server
        if (enabled) {
            await this.mcpClient.reconnectToServer(serverId);
        } else {
            await this.mcpClient.disconnectFromServer(serverId);
        }

        vscode.window.showInformationMessage(
            `MCP server ${enabled ? 'enabled' : 'disabled'} successfully`
        );
    }

    private async testMCPConnection(serverId: string): Promise<void> {
        const isHealthy = await this.mcpClient.testServerConnection(serverId);
        
        this.sendToWebview({
            type: 'connectionTestResult',
            serverId,
            success: isHealthy
        });

        vscode.window.showInformationMessage(
            isHealthy ? 
                '✅ Connection test successful' : 
                '❌ Connection test failed'
        );
    }

    private async addMCPServer(config: Omit<ExtendedMCPServerConfig, 'id' | 'isBuiltIn'>): Promise<void> {
        const serverId = await this.mcpClient.addExternalServer(config);
        
        this.sendToWebview({
            type: 'serverAdded',
            serverId
        });

        vscode.window.showInformationMessage(
            `✅ MCP server "${config.name}" added successfully`
        );
    }

    private async removeMCPServer(serverId: string): Promise<void> {
        await this.mcpClient.removeServer(serverId);
        
        this.sendToWebview({
            type: 'serverRemoved',
            serverId
        });

        vscode.window.showInformationMessage(
            '✅ MCP server removed successfully'
        );
    }

    private async updateSecurityPolicy(policy: Partial<MCPSecurityPolicy>): Promise<void> {
        await this.securityManager.updateSecurityPolicy(policy);
        
        vscode.window.showInformationMessage(
            '✅ Security policy updated successfully'
        );
    }

    private async clearExpiredConsents(): Promise<void> {
        const removedCount = await this.securityManager.clearExpiredConsents();
        
        this.sendToWebview({
            type: 'consentsCleared',
            removedCount
        });

        vscode.window.showInformationMessage(
            `✅ Cleared ${removedCount} expired consents`
        );
    }

    private async revokeConsent(serverId: string, toolName?: string): Promise<void> {
        this.securityManager.revokeConsent(serverId, toolName);
        
        this.sendToWebview({
            type: 'consentRevoked',
            serverId,
            toolName
        });

        vscode.window.showInformationMessage(
            '✅ Consent revoked successfully'
        );
    }

    private async resetMCPSettings(): Promise<void> {
        const choice = await vscode.window.showWarningMessage(
            'Are you sure you want to reset all MCP settings to defaults? This will remove all external servers and clear all consents.',
            { modal: true },
            'Reset',
            'Cancel'
        );

        if (choice === 'Reset') {
            // Reset implementation would go here
            vscode.window.showInformationMessage(
                '✅ MCP settings reset to defaults'
            );
        }
    }

    private async openMCPConfigurationFile(): Promise<void> {
        try {
            await this.configEditorProvider.openConfigEditor();
        } catch (error) {
            console.error('Error opening MCP configuration file:', error);
            this.sendToWebview({
                type: 'error',
                message: `Failed to open configuration file: ${error}`
            });
        }
    }

    private async validateMCPConfiguration(): Promise<void> {
        try {
            // For now, just show a message that validation is handled in the editor
            vscode.window.showInformationMessage('✅ MCP configuration validation is handled in the configuration editor');

            this.sendToWebview({
                type: 'configurationValidation',
                isValid: true,
                errors: []
            });
        } catch (error) {
            console.error('Error validating MCP configuration:', error);
            this.sendToWebview({
                type: 'error',
                message: `Failed to validate configuration: ${error}`
            });
        }
    }

    private async reloadMCPConfiguration(): Promise<void> {
        try {
            // For now, just show a message that reload is handled automatically
            vscode.window.showInformationMessage('✅ MCP configuration is automatically reloaded when files change');

            this.sendToWebview({
                type: 'configurationReloaded'
            });
        } catch (error) {
            console.error('Error reloading MCP configuration:', error);
            this.sendToWebview({
                type: 'error',
                message: `Failed to reload configuration: ${error}`
            });
        }
    }

    private async handleServerInputRequest(action: string): Promise<void> {
        try {
            if (action === 'add') {
                const name = await vscode.window.showInputBox({
                    prompt: 'Enter MCP server name',
                    placeHolder: 'e.g., my-custom-server'
                });

                if (!name) return;

                const url = await vscode.window.showInputBox({
                    prompt: 'Enter MCP server URL',
                    placeHolder: 'e.g., http://localhost:3000/mcp'
                });

                if (!url) return;

                const server = {
                    id: Date.now().toString(),
                    name: name,
                    url: url,
                    enabled: true
                };

                this.sendToWebview({
                    type: 'serverAdded',
                    server: server
                });
            }
        } catch (error) {
            console.error('Error handling server input request:', error);
            this.sendToWebview({
                type: 'error',
                message: `Failed to add server: ${error}`
            });
        }
    }

    private sendToWebview(message: any): void {
        if (this.webviewPanel) {
            this.webviewPanel.webview.postMessage(message);
        }
    }

    private generateNonce(): string {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }

    dispose(): void {
        if (this.webviewPanel) {
            this.webviewPanel.dispose();
        }
    }
}
