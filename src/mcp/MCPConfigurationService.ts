/**
 * MCP Configuration Service
 * 
 * Bridges the configuration manager with the MCP client to provide:
 * - Hot-reloading of configuration changes
 * - Server lifecycle management based on configuration
 * - Security policy enforcement
 * - API key validation and management
 */

import * as vscode from 'vscode';
import { MCPConfigurationManager, AizenMCPConfig, AizenMCPServerConfig } from './MCPConfigurationManager';
import { AizenMCPClient } from './MCPClient';
import { MCPSecurityManager } from './MCPSecurityManager';
import { MCPSecurityValidator } from './MCPSecurityValidator';
import { ExtendedMCPServerConfig, MCPSecurityPolicy } from './types';

export class MCPConfigurationService {
    private configManager: MCPConfigurationManager;
    private mcpClient: AizenMCPClient;
    private securityManager: MCPSecurityManager;
    private context: vscode.ExtensionContext;
    private isInitialized = false;

    constructor(
        context: vscode.ExtensionContext,
        mcpClient: AizenMCPClient,
        securityManager: MCPSecurityManager
    ) {
        this.context = context;
        this.mcpClient = mcpClient;
        this.securityManager = securityManager;
        this.configManager = new MCPConfigurationManager(context);
        
        this.setupEventHandlers();
    }

    private setupEventHandlers(): void {
        // Listen for configuration changes
        this.configManager.onConfigChanged(async (config) => {
            if (this.isInitialized) {
                await this.handleConfigurationChange(config);
            }
        });
    }

    async initialize(): Promise<void> {
        try {
            console.log('🔧 Initializing MCP Configuration Service...');
            
            // Load current configuration
            const config = await this.configManager.loadConfiguration();
            
            // Apply global security settings
            await this.applyGlobalSecuritySettings(config);
            
            // Load external servers from configuration
            await this.loadExternalServers(config);
            
            this.isInitialized = true;
            console.log('✅ MCP Configuration Service initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize MCP Configuration Service:', error);
            vscode.window.showErrorMessage(`Failed to initialize MCP configuration: ${error}`);
        }
    }

    private async applyGlobalSecuritySettings(config: AizenMCPConfig): Promise<void> {
        if (!config.security) return;

        const securityPolicy: Partial<MCPSecurityPolicy> = {};

        if (config.security.requireConfirmation !== undefined) {
            securityPolicy.requireConfirmation = config.security.requireConfirmation;
        }

        if (config.security.maxExecutionTime !== undefined) {
            securityPolicy.maxExecutionTime = config.security.maxExecutionTime;
        }

        if (config.security.allowedDomains) {
            securityPolicy.allowedDomains = config.security.allowedDomains;
        }

        if (config.security.blockedDomains) {
            securityPolicy.blockedDomains = config.security.blockedDomains;
        }

        if (config.security.allowedCommands) {
            securityPolicy.allowedTools = config.security.allowedCommands;
        }

        if (config.security.blockedCommands) {
            securityPolicy.blockedTools = config.security.blockedCommands;
        }

        await this.securityManager.updateSecurityPolicy(securityPolicy);
        console.log('🔒 Applied global security settings from configuration');
    }

    private async loadExternalServers(config: AizenMCPConfig): Promise<void> {
        const externalServers = this.configManager.convertToExtendedServerConfigs(config);
        
        for (const serverConfig of externalServers) {
            try {
                // Validate server configuration
                await this.validateServerConfiguration(serverConfig, config);
                
                // Add server to MCP client
                await this.mcpClient.addExternalServer(serverConfig);
                
                console.log(`✅ Loaded external server: ${serverConfig.name}`);
                
            } catch (error) {
                console.error(`❌ Failed to load server ${serverConfig.name}:`, error);
                vscode.window.showWarningMessage(
                    `Failed to load MCP server "${serverConfig.name}": ${error}`
                );
            }
        }
    }

    private async validateServerConfiguration(
        serverConfig: ExtendedMCPServerConfig,
        globalConfig: AizenMCPConfig
    ): Promise<void> {
        // Find the original server configuration
        const originalServer = globalConfig.servers.find(s => s.id === serverConfig.id);
        if (!originalServer) {
            throw new Error(`Server configuration not found: ${serverConfig.id}`);
        }

        // Use the comprehensive security validator
        const validationResult = MCPSecurityValidator.validateServerConfiguration(originalServer, globalConfig);

        // Handle validation errors
        if (!validationResult.isValid) {
            const errorMessage = validationResult.errors.join('; ');
            throw new Error(`Security validation failed: ${errorMessage}`);
        }

        // Show warnings to user if any
        if (validationResult.warnings.length > 0) {
            const warningMessage = `Security warnings for server "${serverConfig.name}":\n${validationResult.warnings.join('\n')}`;
            console.warn(warningMessage);

            // Show warning notification for high-risk servers
            if (validationResult.riskLevel === 'high') {
                vscode.window.showWarningMessage(
                    `High-risk MCP server detected: ${serverConfig.name}. Check the console for details.`,
                    'View Details'
                ).then(choice => {
                    if (choice === 'View Details') {
                        vscode.window.showInformationMessage(warningMessage);
                    }
                });
            }
        }

        // Check if API key is required and configured
        if (serverConfig.requiresApiKey && !serverConfig.apiKeyConfigured) {
            const envVar = originalServer.apiKey?.envVar;

            if (envVar) {
                throw new Error(`Missing API key: Environment variable ${envVar} is not set`);
            } else {
                throw new Error('API key is required but not configured');
            }
        }

        console.log(`✅ Security validation passed for server: ${serverConfig.name} (Risk: ${validationResult.riskLevel})`);
    }



    private async handleConfigurationChange(config: AizenMCPConfig): Promise<void> {
        try {
            console.log('🔄 Configuration changed, reloading MCP servers...');
            
            // Apply updated security settings
            await this.applyGlobalSecuritySettings(config);
            
            // Get current external servers
            const currentServers = this.mcpClient.getConnectedServers().filter(s => !s.isBuiltIn);
            const newServerConfigs = this.configManager.convertToExtendedServerConfigs(config);
            
            // Remove servers that are no longer in configuration
            for (const currentServer of currentServers) {
                const stillExists = newServerConfigs.some(newServer => newServer.id === currentServer.id);
                if (!stillExists) {
                    try {
                        await this.mcpClient.removeServer(currentServer.id);
                        console.log(`🗑️ Removed server: ${currentServer.name}`);
                    } catch (error) {
                        console.error(`Failed to remove server ${currentServer.name}:`, error);
                    }
                }
            }
            
            // Add or update servers
            for (const newServerConfig of newServerConfigs) {
                try {
                    const existingServer = currentServers.find(s => s.id === newServerConfig.id);
                    
                    if (existingServer) {
                        // Update existing server (remove and re-add for simplicity)
                        await this.mcpClient.removeServer(existingServer.id);
                        await this.validateServerConfiguration(newServerConfig, config);
                        await this.mcpClient.addExternalServer(newServerConfig);
                        console.log(`🔄 Updated server: ${newServerConfig.name}`);
                    } else {
                        // Add new server
                        await this.validateServerConfiguration(newServerConfig, config);
                        await this.mcpClient.addExternalServer(newServerConfig);
                        console.log(`➕ Added server: ${newServerConfig.name}`);
                    }
                    
                } catch (error) {
                    console.error(`Failed to configure server ${newServerConfig.name}:`, error);
                    vscode.window.showWarningMessage(
                        `Failed to configure MCP server "${newServerConfig.name}": ${error}`
                    );
                }
            }
            
            vscode.window.showInformationMessage('MCP configuration reloaded successfully');
            
        } catch (error) {
            console.error('Error handling configuration change:', error);
            vscode.window.showErrorMessage(`Error reloading MCP configuration: ${error}`);
        }
    }

    async openConfigurationFile(): Promise<void> {
        await this.configManager.openConfigurationFile();
    }

    async createDefaultConfiguration(): Promise<void> {
        await this.configManager.createDefaultConfiguration();
    }

    async reloadConfiguration(): Promise<void> {
        const config = await this.configManager.loadConfiguration();
        await this.handleConfigurationChange(config);
    }

    async validateCurrentConfiguration(): Promise<{ isValid: boolean; errors: string[] }> {
        try {
            const config = await this.configManager.loadConfiguration();
            const errors: string[] = [];
            
            // Validate each server
            const serverConfigs = this.configManager.convertToExtendedServerConfigs(config);
            for (const serverConfig of serverConfigs) {
                try {
                    await this.validateServerConfiguration(serverConfig, config);
                } catch (error) {
                    errors.push(`Server "${serverConfig.name}": ${error}`);
                }
            }
            
            return {
                isValid: errors.length === 0,
                errors
            };
            
        } catch (error) {
            return {
                isValid: false,
                errors: [`Configuration file error: ${error}`]
            };
        }
    }

    getConfigurationPath(): string {
        return this.configManager.getConfigurationPath();
    }

    async exportConfiguration(): Promise<string> {
        const config = await this.configManager.loadConfiguration();
        return JSON.stringify(config, null, 2);
    }

    async importConfiguration(configJson: string): Promise<void> {
        try {
            const config = JSON.parse(configJson) as AizenMCPConfig;
            await this.configManager.saveConfiguration(config);
            vscode.window.showInformationMessage('MCP configuration imported successfully');
        } catch (error) {
            throw new Error(`Failed to import configuration: ${error}`);
        }
    }

    dispose(): void {
        this.configManager.dispose();
    }
}
