/**
 * MCP Configuration Manager
 * 
 * Handles the aizen_mcp.json configuration file including:
 * - File creation and management
 * - JSON schema validation
 * - VS Code integration for editing
 * - Hot-reloading and change detection
 * - Security validation
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { ExtendedMCPServerConfig, MCPTransportType } from './types';

export interface AizenMCPConfig {
    $schema?: string;
    version: string;
    metadata?: {
        name?: string;
        description?: string;
        author?: string;
        created?: string;
        lastModified?: string;
    };
    globalSettings?: {
        autoStart?: boolean;
        maxConcurrentServers?: number;
        defaultTimeout?: number;
        retryAttempts?: number;
        retryDelay?: number;
        logLevel?: 'debug' | 'info' | 'warn' | 'error';
    };
    security?: {
        requireConfirmation?: boolean;
        allowedCommands?: string[];
        blockedCommands?: string[];
        allowedDomains?: string[];
        blockedDomains?: string[];
        maxExecutionTime?: number;
        sandboxMode?: boolean;
    };
    servers: AizenMCPServerConfig[];
}

export interface AizenMCPServerConfig {
    id: string;
    name: string;
    description?: string;
    icon?: string;
    category?: string;
    transport: {
        type: MCPTransportType;
        command?: string;
        args?: string[];
        cwd?: string;
        env?: Record<string, string>;
        url?: string;
        headers?: Record<string, string>;
    };
    apiKey?: {
        required?: boolean;
        envVar?: string;
        description?: string;
        url?: string;
    };
    settings?: {
        enabled?: boolean;
        autoStart?: boolean;
        timeout?: number;
        retryAttempts?: number;
        retryDelay?: number;
        priority?: number;
    };
    security?: {
        riskLevel?: 'low' | 'medium' | 'high';
        requireConfirmation?: boolean;
        allowedTools?: string[];
        blockedTools?: string[];
        maxExecutionTime?: number;
    };
    metadata?: {
        version?: string;
        author?: string;
        homepage?: string;
        repository?: string;
        documentation?: string;
        license?: string;
        tags?: string[];
    };
}

export class MCPConfigurationManager {
    private context: vscode.ExtensionContext;
    private configPath: string;
    private schemaPath: string;
    private fileWatcher: vscode.FileSystemWatcher | undefined;
    private onConfigChangedEmitter = new vscode.EventEmitter<AizenMCPConfig>();
    public readonly onConfigChanged = this.onConfigChangedEmitter.event;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.configPath = this.getConfigPath();
        this.schemaPath = path.join(context.extensionPath, 'schemas', 'aizen_mcp.schema.json');
        this.setupFileWatcher();
        this.registerSchemaProvider();
    }

    private getConfigPath(): string {
        // Try workspace folder first, then fallback to global storage
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return path.join(workspaceFolders[0].uri.fsPath, 'aizen_mcp.json');
        }
        
        // Fallback to global storage path
        const globalStoragePath = this.context.globalStorageUri.fsPath;
        if (!fs.existsSync(globalStoragePath)) {
            fs.mkdirSync(globalStoragePath, { recursive: true });
        }
        return path.join(globalStoragePath, 'aizen_mcp.json');
    }

    private setupFileWatcher(): void {
        const configDir = path.dirname(this.configPath);
        this.fileWatcher = vscode.workspace.createFileSystemWatcher(
            new vscode.RelativePattern(configDir, 'aizen_mcp.json')
        );

        this.fileWatcher.onDidChange(() => {
            this.handleConfigFileChange();
        });

        this.fileWatcher.onDidCreate(() => {
            this.handleConfigFileChange();
        });

        this.fileWatcher.onDidDelete(() => {
            this.onConfigChangedEmitter.fire({ version: '1.0.0', servers: [] });
        });
    }

    private async handleConfigFileChange(): Promise<void> {
        try {
            const config = await this.loadConfiguration();
            this.onConfigChangedEmitter.fire(config);
        } catch (error) {
            console.error('Error handling config file change:', error);
            vscode.window.showErrorMessage(`Error loading MCP configuration: ${error}`);
        }
    }

    private registerSchemaProvider(): void {
        // Register JSON schema for aizen_mcp.json files
        const schemaUri = vscode.Uri.file(this.schemaPath);
        
        // Configure VS Code to use our schema for aizen_mcp.json files
        vscode.workspace.getConfiguration('json').update(
            'schemas',
            [
                {
                    fileMatch: ['**/aizen_mcp.json'],
                    url: schemaUri.toString()
                }
            ],
            vscode.ConfigurationTarget.Global
        );
    }

    async createDefaultConfiguration(): Promise<void> {
        if (fs.existsSync(this.configPath)) {
            const choice = await vscode.window.showWarningMessage(
                'An aizen_mcp.json file already exists. Do you want to overwrite it?',
                'Overwrite',
                'Cancel'
            );
            
            if (choice !== 'Overwrite') {
                return;
            }
        }

        const defaultConfig: AizenMCPConfig = {
            $schema: './schemas/aizen_mcp.schema.json',
            version: '1.0.0',
            metadata: {
                name: 'My MCP Servers',
                description: 'External MCP servers configuration for Aizen AI',
                author: 'User',
                created: new Date().toISOString(),
                lastModified: new Date().toISOString()
            },
            globalSettings: {
                autoStart: true,
                maxConcurrentServers: 10,
                defaultTimeout: 30000,
                retryAttempts: 3,
                retryDelay: 1000,
                logLevel: 'info'
            },
            security: {
                requireConfirmation: true,
                allowedCommands: ['node', 'python', 'python3', 'npx'],
                blockedCommands: ['rm', 'del', 'format', 'sudo'],
                maxExecutionTime: 60000,
                sandboxMode: false
            },
            servers: [
                {
                    id: 'example-server',
                    name: 'Example MCP Server',
                    description: 'An example MCP server configuration - replace with your own',
                    icon: '🔧',
                    category: 'other',
                    transport: {
                        type: 'stdio',
                        command: 'npx',
                        args: ['-y', '@modelcontextprotocol/server-example']
                    },
                    settings: {
                        enabled: false,
                        autoStart: false,
                        timeout: 30000,
                        priority: 50
                    },
                    security: {
                        riskLevel: 'medium',
                        requireConfirmation: true
                    },
                    metadata: {
                        version: '1.0.0',
                        author: 'Example Author',
                        license: 'MIT',
                        tags: ['example', 'template']
                    }
                }
            ]
        };

        await this.saveConfiguration(defaultConfig);
    }

    async loadConfiguration(): Promise<AizenMCPConfig> {
        try {
            if (!fs.existsSync(this.configPath)) {
                return { version: '1.0.0', servers: [] };
            }

            const content = fs.readFileSync(this.configPath, 'utf8');
            const config = JSON.parse(content) as AizenMCPConfig;
            
            // Validate configuration
            this.validateConfiguration(config);
            
            return config;
        } catch (error) {
            console.error('Error loading MCP configuration:', error);
            throw new Error(`Failed to load MCP configuration: ${error}`);
        }
    }

    async saveConfiguration(config: AizenMCPConfig): Promise<void> {
        try {
            // Update lastModified timestamp
            if (config.metadata) {
                config.metadata.lastModified = new Date().toISOString();
            }

            // Validate before saving
            this.validateConfiguration(config);

            // Ensure directory exists
            const configDir = path.dirname(this.configPath);
            if (!fs.existsSync(configDir)) {
                fs.mkdirSync(configDir, { recursive: true });
            }

            // Write configuration with pretty formatting
            const content = JSON.stringify(config, null, 2);
            fs.writeFileSync(this.configPath, content, 'utf8');

            console.log('MCP configuration saved successfully');
        } catch (error) {
            console.error('Error saving MCP configuration:', error);
            throw new Error(`Failed to save MCP configuration: ${error}`);
        }
    }

    private validateConfiguration(config: AizenMCPConfig): void {
        // Basic validation
        if (!config.version) {
            throw new Error('Configuration must have a version');
        }

        if (!Array.isArray(config.servers)) {
            throw new Error('Configuration must have a servers array');
        }

        // Validate each server
        const serverIds = new Set<string>();
        for (const server of config.servers) {
            this.validateServerConfig(server);
            
            // Check for duplicate IDs
            if (serverIds.has(server.id)) {
                throw new Error(`Duplicate server ID: ${server.id}`);
            }
            serverIds.add(server.id);
        }

        // Validate security settings
        if (config.security) {
            this.validateSecurityConfig(config.security);
        }
    }

    private validateServerConfig(server: AizenMCPServerConfig): void {
        if (!server.id || !/^[a-zA-Z0-9_-]+$/.test(server.id)) {
            throw new Error(`Invalid server ID: ${server.id}. Must contain only alphanumeric characters, underscores, and hyphens.`);
        }

        if (!server.name || server.name.trim().length === 0) {
            throw new Error(`Server ${server.id} must have a name`);
        }

        if (!server.transport || !server.transport.type) {
            throw new Error(`Server ${server.id} must have transport configuration`);
        }

        // Validate transport-specific requirements
        if (server.transport.type === 'stdio') {
            if (!server.transport.command) {
                throw new Error(`STDIO server ${server.id} must have a command`);
            }
        } else if (['http', 'sse', 'streamable-http'].includes(server.transport.type)) {
            if (!server.transport.url) {
                throw new Error(`HTTP server ${server.id} must have a URL`);
            }
            
            try {
                new URL(server.transport.url);
            } catch {
                throw new Error(`Server ${server.id} has invalid URL: ${server.transport.url}`);
            }
        }

        // Validate security settings
        if (server.security?.riskLevel && !['low', 'medium', 'high'].includes(server.security.riskLevel)) {
            throw new Error(`Server ${server.id} has invalid risk level: ${server.security.riskLevel}`);
        }
    }

    private validateSecurityConfig(security: NonNullable<AizenMCPConfig['security']>): void {
        if (security.maxExecutionTime && (security.maxExecutionTime < 1000 || security.maxExecutionTime > 600000)) {
            throw new Error('maxExecutionTime must be between 1000 and 600000 milliseconds');
        }

        // Validate command lists
        if (security.allowedCommands) {
            for (const cmd of security.allowedCommands) {
                if (typeof cmd !== 'string' || cmd.trim().length === 0) {
                    throw new Error('All allowed commands must be non-empty strings');
                }
            }
        }

        if (security.blockedCommands) {
            for (const cmd of security.blockedCommands) {
                if (typeof cmd !== 'string' || cmd.trim().length === 0) {
                    throw new Error('All blocked commands must be non-empty strings');
                }
            }
        }
    }

    async openConfigurationFile(): Promise<void> {
        try {
            // Create default configuration if it doesn't exist
            if (!fs.existsSync(this.configPath)) {
                await this.createDefaultConfiguration();
            }

            // Open the file in VS Code
            const document = await vscode.workspace.openTextDocument(this.configPath);
            await vscode.window.showTextDocument(document, {
                preview: false,
                viewColumn: vscode.ViewColumn.One
            });

            // Show helpful message
            vscode.window.showInformationMessage(
                'Edit your MCP server configuration. Changes will be automatically detected and applied.',
                'View Documentation'
            ).then(choice => {
                if (choice === 'View Documentation') {
                    vscode.env.openExternal(vscode.Uri.parse('https://docs.aizen.ai/mcp-configuration'));
                }
            });

        } catch (error) {
            console.error('Error opening configuration file:', error);
            vscode.window.showErrorMessage(`Failed to open MCP configuration: ${error}`);
        }
    }

    convertToExtendedServerConfigs(config: AizenMCPConfig): ExtendedMCPServerConfig[] {
        return config.servers.map(server => this.convertServerConfig(server, config));
    }

    private convertServerConfig(server: AizenMCPServerConfig, globalConfig: AizenMCPConfig): ExtendedMCPServerConfig {
        const globalSettings = globalConfig.globalSettings || {};
        const globalSecurity = globalConfig.security || {};
        
        return {
            id: server.id,
            name: server.name,
            description: server.description || '',
            transport: server.transport.type,
            command: server.transport.command,
            args: server.transport.args,
            env: server.transport.env,
            url: server.transport.url,
            enabled: server.settings?.enabled ?? true,
            autoStart: server.settings?.autoStart ?? globalSettings.autoStart ?? true,
            timeout: server.settings?.timeout ?? globalSettings.defaultTimeout ?? 30000,
            retryAttempts: server.settings?.retryAttempts ?? globalSettings.retryAttempts ?? 3,
            retryDelay: server.settings?.retryDelay ?? globalSettings.retryDelay ?? 1000,
            isBuiltIn: false,
            requiresApiKey: server.apiKey?.required ?? false,
            apiKeyConfigured: server.apiKey?.required ? this.checkApiKeyConfigured(server.apiKey.envVar) : true,
            category: server.category || 'other',
            icon: server.icon || '🔧',
            version: server.metadata?.version,
            riskLevel: server.security?.riskLevel || 'medium',
            priority: server.settings?.priority || 50
        };
    }

    private checkApiKeyConfigured(envVar?: string): boolean {
        if (!envVar) return true;
        return !!process.env[envVar];
    }

    getConfigurationPath(): string {
        return this.configPath;
    }

    dispose(): void {
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
        }
        this.onConfigChangedEmitter.dispose();
    }
}
