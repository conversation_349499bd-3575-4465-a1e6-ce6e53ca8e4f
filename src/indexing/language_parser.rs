use std::path::Path;
use anyhow::Result;
use syn::{visit::Visit, File, Item, ItemFn, ItemStruct, ItemEnum, ItemImpl, ItemMod, ItemUse};
use crate::indexing::{CodeNode, CodeNodeType, Dependency, DependencyType, SemanticChunk, ChunkType, LanguageParser};

/// Rust language parser using syn
pub struct RustParser {
    visitor: RustVisitor,
}

impl RustParser {
    pub fn new() -> Self {
        Self {
            visitor: RustVisitor::new(),
        }
    }
}

impl LanguageParser for RustParser {
    fn parse_file(&self, content: &str, file_path: &Path) -> Result<Vec<CodeNode>> {
        let syntax_tree: File = syn::parse_str(content)?;
        let mut visitor = RustVisitor::new();
        visitor.file_path = file_path.to_path_buf();
        visitor.visit_file(&syntax_tree);
        Ok(visitor.nodes)
    }

    fn extract_dependencies(&self, content: &str) -> Result<Vec<Dependency>> {
        let syntax_tree: File = syn::parse_str(content)?;
        let mut dependencies = Vec::new();

        for item in syntax_tree.items {
            if let Item::Use(use_item) = item {
                let dep_name = quote::quote!(#use_item).to_string();
                dependencies.push(Dependency {
                    source: "current_file".to_string(),
                    target: dep_name,
                    dependency_type: DependencyType::Direct,
                    version: None,
                    is_dev: false,
                });
            }
        }

        Ok(dependencies)
    }

    fn get_semantic_chunks(&self, content: &str, file_path: &Path) -> Result<Vec<SemanticChunk>> {
        let lines: Vec<&str> = content.lines().collect();
        let mut chunks = Vec::new();
        let mut current_chunk_start = 0;
        let mut current_chunk_type = ChunkType::Module;

        for (i, line) in lines.iter().enumerate() {
            let trimmed = line.trim();
            
            // Detect chunk boundaries
            if trimmed.starts_with("fn ") || trimmed.starts_with("pub fn ") {
                // End previous chunk and start function chunk
                if i > current_chunk_start {
                    chunks.push(self.create_chunk(
                        &lines[current_chunk_start..i],
                        file_path,
                        current_chunk_start,
                        i - 1,
                        current_chunk_type.clone(),
                    ));
                }
                current_chunk_start = i;
                current_chunk_type = ChunkType::Function;
            } else if trimmed.starts_with("struct ") || trimmed.starts_with("pub struct ") {
                if i > current_chunk_start {
                    chunks.push(self.create_chunk(
                        &lines[current_chunk_start..i],
                        file_path,
                        current_chunk_start,
                        i - 1,
                        current_chunk_type.clone(),
                    ));
                }
                current_chunk_start = i;
                current_chunk_type = ChunkType::Class;
            } else if trimmed.starts_with("//") || trimmed.starts_with("///") {
                if current_chunk_type != ChunkType::Comment {
                    if i > current_chunk_start {
                        chunks.push(self.create_chunk(
                            &lines[current_chunk_start..i],
                            file_path,
                            current_chunk_start,
                            i - 1,
                            current_chunk_type.clone(),
                        ));
                    }
                    current_chunk_start = i;
                    current_chunk_type = ChunkType::Comment;
                }
            }
        }

        // Add final chunk
        if current_chunk_start < lines.len() {
            chunks.push(self.create_chunk(
                &lines[current_chunk_start..],
                file_path,
                current_chunk_start,
                lines.len() - 1,
                current_chunk_type,
            ));
        }

        Ok(chunks)
    }

    fn calculate_complexity(&self, content: &str) -> Result<f32> {
        let mut complexity = 1.0; // Base complexity
        
        for line in content.lines() {
            let trimmed = line.trim();
            
            // Cyclomatic complexity indicators
            if trimmed.contains("if ") || trimmed.contains("else if ") {
                complexity += 1.0;
            }
            if trimmed.contains("while ") || trimmed.contains("for ") {
                complexity += 1.0;
            }
            if trimmed.contains("match ") {
                complexity += 1.0;
            }
            if trimmed.contains("&&") || trimmed.contains("||") {
                complexity += 0.5;
            }
        }

        Ok(complexity)
    }
}

impl RustParser {
    fn create_chunk(
        &self,
        lines: &[&str],
        file_path: &Path,
        start_line: usize,
        end_line: usize,
        chunk_type: ChunkType,
    ) -> SemanticChunk {
        let content = lines.join("\n");
        let id = uuid::Uuid::new_v4().to_string();

        SemanticChunk {
            id,
            content,
            file_path: file_path.to_path_buf(),
            start_line,
            end_line,
            chunk_type,
            embedding: None,
            related_nodes: vec![],
            context_score: 1.0,
            language: "rust".to_string(),
        }
    }
}

/// Visitor for traversing Rust AST
struct RustVisitor {
    nodes: Vec<CodeNode>,
    file_path: std::path::PathBuf,
    current_line: usize,
}

impl RustVisitor {
    fn new() -> Self {
        Self {
            nodes: Vec::new(),
            file_path: std::path::PathBuf::new(),
            current_line: 0,
        }
    }

    fn create_node(
        &self,
        name: String,
        node_type: CodeNodeType,
        start_line: usize,
        end_line: usize,
        signature: Option<String>,
    ) -> CodeNode {
        CodeNode {
            id: uuid::Uuid::new_v4().to_string(),
            node_type,
            name,
            file_path: self.file_path.clone(),
            start_line,
            end_line,
            signature,
            docstring: None,
            complexity_score: 1.0,
            embedding: None,
            metadata: std::collections::HashMap::new(),
        }
    }
}

impl<'ast> Visit<'ast> for RustVisitor {
    fn visit_item_fn(&mut self, node: &'ast ItemFn) {
        let name = node.sig.ident.to_string();
        let signature = quote::quote!(#node).to_string();
        
        let code_node = self.create_node(
            name,
            CodeNodeType::Function,
            0, // TODO: Get actual line numbers from span
            0,
            Some(signature),
        );
        
        self.nodes.push(code_node);
        syn::visit::visit_item_fn(self, node);
    }

    fn visit_item_struct(&mut self, node: &'ast ItemStruct) {
        let name = node.ident.to_string();
        let signature = quote::quote!(#node).to_string();
        
        let code_node = self.create_node(
            name,
            CodeNodeType::Struct,
            0,
            0,
            Some(signature),
        );
        
        self.nodes.push(code_node);
        syn::visit::visit_item_struct(self, node);
    }

    fn visit_item_enum(&mut self, node: &'ast ItemEnum) {
        let name = node.ident.to_string();
        let signature = quote::quote!(#node).to_string();
        
        let code_node = self.create_node(
            name,
            CodeNodeType::Enum,
            0,
            0,
            Some(signature),
        );
        
        self.nodes.push(code_node);
        syn::visit::visit_item_enum(self, node);
    }

    fn visit_item_impl(&mut self, node: &'ast ItemImpl) {
        if let syn::Type::Path(type_path) = &*node.self_ty {
            if let Some(segment) = type_path.path.segments.last() {
                let name = format!("impl {}", segment.ident);
                let signature = quote::quote!(#node).to_string();
                
                let code_node = self.create_node(
                    name,
                    CodeNodeType::Class,
                    0,
                    0,
                    Some(signature),
                );
                
                self.nodes.push(code_node);
            }
        }
        syn::visit::visit_item_impl(self, node);
    }

    fn visit_item_mod(&mut self, node: &'ast ItemMod) {
        let name = node.ident.to_string();
        let signature = quote::quote!(#node).to_string();
        
        let code_node = self.create_node(
            name,
            CodeNodeType::Module,
            0,
            0,
            Some(signature),
        );
        
        self.nodes.push(code_node);
        syn::visit::visit_item_mod(self, node);
    }

    fn visit_item_use(&mut self, node: &'ast ItemUse) {
        let signature = quote::quote!(#node).to_string();
        let name = signature.clone();
        
        let code_node = self.create_node(
            name,
            CodeNodeType::Import,
            0,
            0,
            Some(signature),
        );
        
        self.nodes.push(code_node);
        syn::visit::visit_item_use(self, node);
    }
}

/// TypeScript parser (placeholder - would use tree-sitter in production)
pub struct TypeScriptParser;

impl TypeScriptParser {
    pub fn new() -> Self {
        Self
    }
}

impl LanguageParser for TypeScriptParser {
    fn parse_file(&self, _content: &str, _file_path: &Path) -> Result<Vec<CodeNode>> {
        // TODO: Implement using tree-sitter-typescript
        Ok(vec![])
    }

    fn extract_dependencies(&self, _content: &str) -> Result<Vec<Dependency>> {
        // TODO: Parse import statements
        Ok(vec![])
    }

    fn get_semantic_chunks(&self, _content: &str, _file_path: &Path) -> Result<Vec<SemanticChunk>> {
        // TODO: Implement semantic chunking for TypeScript
        Ok(vec![])
    }

    fn calculate_complexity(&self, _content: &str) -> Result<f32> {
        // TODO: Calculate cyclomatic complexity
        Ok(1.0)
    }
}

/// JavaScript parser (placeholder)
pub struct JavaScriptParser;

impl JavaScriptParser {
    pub fn new() -> Self {
        Self
    }
}

impl LanguageParser for JavaScriptParser {
    fn parse_file(&self, _content: &str, _file_path: &Path) -> Result<Vec<CodeNode>> {
        Ok(vec![])
    }

    fn extract_dependencies(&self, _content: &str) -> Result<Vec<Dependency>> {
        Ok(vec![])
    }

    fn get_semantic_chunks(&self, _content: &str, _file_path: &Path) -> Result<Vec<SemanticChunk>> {
        Ok(vec![])
    }

    fn calculate_complexity(&self, _content: &str) -> Result<f32> {
        Ok(1.0)
    }
}

/// Python parser (placeholder)
pub struct PythonParser;

impl PythonParser {
    pub fn new() -> Self {
        Self
    }
}

impl LanguageParser for PythonParser {
    fn parse_file(&self, _content: &str, _file_path: &Path) -> Result<Vec<CodeNode>> {
        Ok(vec![])
    }

    fn extract_dependencies(&self, _content: &str) -> Result<Vec<Dependency>> {
        Ok(vec![])
    }

    fn get_semantic_chunks(&self, _content: &str, _file_path: &Path) -> Result<Vec<SemanticChunk>> {
        Ok(vec![])
    }

    fn calculate_complexity(&self, _content: &str) -> Result<f32> {
        Ok(1.0)
    }
}
