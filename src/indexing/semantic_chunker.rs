use std::collections::HashMap;
use std::path::Path;
use anyhow::Result;
use regex::Regex;
use unicode_segmentation::UnicodeSegmentation;
use crate::indexing::{SemanticChunk, ChunkType};

/// Advanced semantic chunker that creates meaningful code segments
/// Inspired by CodeFuse's intelligent chunking strategy
pub struct SemanticChunker {
    /// Maximum chunk size in tokens
    max_chunk_size: usize,
    /// Overlap between chunks for context preservation
    chunk_overlap: usize,
    /// Language-specific patterns
    language_patterns: HashMap<String, LanguagePatterns>,
}

/// Language-specific patterns for semantic boundaries
#[derive(Debug, Clone)]
pub struct LanguagePatterns {
    /// Function/method definition patterns
    pub function_patterns: Vec<Regex>,
    /// Class/struct definition patterns
    pub class_patterns: Vec<Regex>,
    /// Comment patterns
    pub comment_patterns: Vec<Regex>,
    /// Import/include patterns
    pub import_patterns: Vec<Regex>,
    /// Block delimiters
    pub block_start: Vec<String>,
    pub block_end: Vec<String>,
}

/// Chunk boundary detection result
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ChunkBoundary {
    pub line_number: usize,
    pub boundary_type: BoundaryType,
    pub confidence: f32,
}

/// Types of semantic boundaries
#[derive(Debug, Clone, PartialEq)]
pub enum BoundaryType {
    FunctionStart,
    FunctionEnd,
    ClassStart,
    ClassEnd,
    ModuleStart,
    ModuleEnd,
    CommentBlock,
    ImportBlock,
    LogicalBreak,
}

impl SemanticChunker {
    /// Create a new semantic chunker
    pub fn new(max_chunk_size: usize, chunk_overlap: usize) -> Self {
        let mut chunker = Self {
            max_chunk_size,
            chunk_overlap,
            language_patterns: HashMap::new(),
        };
        
        chunker.initialize_patterns();
        chunker
    }

    /// Initialize language-specific patterns
    fn initialize_patterns(&mut self) {
        // Rust patterns
        let rust_patterns = LanguagePatterns {
            function_patterns: vec![
                Regex::new(r"^\s*(pub\s+)?fn\s+\w+").unwrap(),
                Regex::new(r"^\s*(pub\s+)?async\s+fn\s+\w+").unwrap(),
            ],
            class_patterns: vec![
                Regex::new(r"^\s*(pub\s+)?struct\s+\w+").unwrap(),
                Regex::new(r"^\s*(pub\s+)?enum\s+\w+").unwrap(),
                Regex::new(r"^\s*(pub\s+)?trait\s+\w+").unwrap(),
                Regex::new(r"^\s*impl\s+").unwrap(),
            ],
            comment_patterns: vec![
                Regex::new(r"^\s*//").unwrap(),
                Regex::new(r"^\s*/\*").unwrap(),
                Regex::new(r"^\s*\*/").unwrap(),
            ],
            import_patterns: vec![
                Regex::new(r"^\s*use\s+").unwrap(),
                Regex::new(r"^\s*extern\s+crate").unwrap(),
            ],
            block_start: vec!["{".to_string()],
            block_end: vec!["}".to_string()],
        };
        self.language_patterns.insert("rust".to_string(), rust_patterns);

        // TypeScript/JavaScript patterns
        let ts_patterns = LanguagePatterns {
            function_patterns: vec![
                Regex::new(r"^\s*(export\s+)?(async\s+)?function\s+\w+").unwrap(),
                Regex::new(r"^\s*(export\s+)?const\s+\w+\s*=\s*(async\s+)?\(").unwrap(),
                Regex::new(r"^\s*\w+\s*:\s*(async\s+)?\(").unwrap(),
            ],
            class_patterns: vec![
                Regex::new(r"^\s*(export\s+)?(abstract\s+)?class\s+\w+").unwrap(),
                Regex::new(r"^\s*(export\s+)?interface\s+\w+").unwrap(),
                Regex::new(r"^\s*(export\s+)?type\s+\w+").unwrap(),
            ],
            comment_patterns: vec![
                Regex::new(r"^\s*//").unwrap(),
                Regex::new(r"^\s*/\*").unwrap(),
                Regex::new(r"^\s*\*/").unwrap(),
            ],
            import_patterns: vec![
                Regex::new(r"^\s*import\s+").unwrap(),
                Regex::new(r"^\s*export\s+").unwrap(),
                Regex::new(r"^\s*require\s*\(").unwrap(),
            ],
            block_start: vec!["{".to_string()],
            block_end: vec!["}".to_string()],
        };
        self.language_patterns.insert("typescript".to_string(), ts_patterns);
        self.language_patterns.insert("javascript".to_string(), ts_patterns.clone());

        // Python patterns
        let python_patterns = LanguagePatterns {
            function_patterns: vec![
                Regex::new(r"^\s*(async\s+)?def\s+\w+").unwrap(),
                Regex::new(r"^\s*@\w+").unwrap(), // decorators
            ],
            class_patterns: vec![
                Regex::new(r"^\s*class\s+\w+").unwrap(),
            ],
            comment_patterns: vec![
                Regex::new(r"^\s*#").unwrap(),
                Regex::new(r"^\s*\"\"\"").unwrap(),
                Regex::new(r"^\s*'''").unwrap(),
            ],
            import_patterns: vec![
                Regex::new(r"^\s*import\s+").unwrap(),
                Regex::new(r"^\s*from\s+").unwrap(),
            ],
            block_start: vec![":".to_string()],
            block_end: vec![], // Python uses indentation
        };
        self.language_patterns.insert("python".to_string(), python_patterns);
    }

    /// Create semantic chunks from file content
    pub fn create_chunks(&self, content: &str, file_path: &Path, language: &str) -> Result<Vec<SemanticChunk>> {
        let lines: Vec<&str> = content.lines().collect();
        let boundaries = self.detect_boundaries(&lines, language)?;
        let chunks = self.create_chunks_from_boundaries(&lines, &boundaries, file_path, language)?;
        
        Ok(chunks)
    }

    /// Detect semantic boundaries in the code
    fn detect_boundaries(&self, lines: &[&str], language: &str) -> Result<Vec<ChunkBoundary>> {
        let mut boundaries = Vec::new();
        let patterns = self.language_patterns.get(language);

        if let Some(patterns) = patterns {
            let mut brace_depth = 0;
            let mut in_comment_block = false;

            for (i, line) in lines.iter().enumerate() {
                let trimmed = line.trim();

                // Skip empty lines
                if trimmed.is_empty() {
                    continue;
                }

                // Check for comment blocks
                if self.is_comment_start(trimmed, patterns) {
                    if !in_comment_block {
                        boundaries.push(ChunkBoundary {
                            line_number: i,
                            boundary_type: BoundaryType::CommentBlock,
                            confidence: 0.8,
                        });
                        in_comment_block = true;
                    }
                    continue;
                }

                if self.is_comment_end(trimmed, patterns) {
                    in_comment_block = false;
                    continue;
                }

                if in_comment_block {
                    continue;
                }

                // Check for function definitions
                if self.is_function_definition(trimmed, patterns) {
                    boundaries.push(ChunkBoundary {
                        line_number: i,
                        boundary_type: BoundaryType::FunctionStart,
                        confidence: 0.9,
                    });
                }

                // Check for class definitions
                if self.is_class_definition(trimmed, patterns) {
                    boundaries.push(ChunkBoundary {
                        line_number: i,
                        boundary_type: BoundaryType::ClassStart,
                        confidence: 0.9,
                    });
                }

                // Check for import statements
                if self.is_import_statement(trimmed, patterns) {
                    boundaries.push(ChunkBoundary {
                        line_number: i,
                        boundary_type: BoundaryType::ImportBlock,
                        confidence: 0.7,
                    });
                }

                // Track brace depth for block boundaries
                for ch in trimmed.chars() {
                    match ch {
                        '{' => brace_depth += 1,
                        '}' => {
                            brace_depth -= 1;
                            if brace_depth == 0 {
                                boundaries.push(ChunkBoundary {
                                    line_number: i,
                                    boundary_type: BoundaryType::FunctionEnd,
                                    confidence: 0.8,
                                });
                            }
                        }
                        _ => {}
                    }
                }

                // Detect logical breaks (empty lines, significant indentation changes)
                if i > 0 && i < lines.len() - 1 {
                    let prev_indent = self.get_indentation(lines[i - 1]);
                    let curr_indent = self.get_indentation(line);
                    let next_indent = self.get_indentation(lines[i + 1]);

                    if (curr_indent < prev_indent && curr_indent < next_indent) ||
                       (curr_indent > prev_indent && curr_indent > next_indent) {
                        boundaries.push(ChunkBoundary {
                            line_number: i,
                            boundary_type: BoundaryType::LogicalBreak,
                            confidence: 0.5,
                        });
                    }
                }
            }
        }

        // Sort boundaries by line number
        boundaries.sort_by_key(|b| b.line_number);
        Ok(boundaries)
    }

    /// Create chunks based on detected boundaries
    fn create_chunks_from_boundaries(
        &self,
        lines: &[&str],
        boundaries: &[ChunkBoundary],
        file_path: &Path,
        language: &str,
    ) -> Result<Vec<SemanticChunk>> {
        let mut chunks = Vec::new();
        let mut current_start = 0;

        for boundary in boundaries {
            if boundary.line_number > current_start {
                let chunk_type = self.determine_chunk_type(&boundary.boundary_type);
                let content = lines[current_start..boundary.line_number].join("\n");
                
                if !content.trim().is_empty() {
                    let chunk = SemanticChunk {
                        id: uuid::Uuid::new_v4().to_string(),
                        content,
                        file_path: file_path.to_path_buf(),
                        start_line: current_start,
                        end_line: boundary.line_number - 1,
                        chunk_type,
                        embedding: None,
                        related_nodes: vec![],
                        context_score: boundary.confidence,
                        language: language.to_string(),
                    };
                    chunks.push(chunk);
                }
            }
            current_start = boundary.line_number;
        }

        // Add final chunk
        if current_start < lines.len() {
            let content = lines[current_start..].join("\n");
            if !content.trim().is_empty() {
                let chunk = SemanticChunk {
                    id: uuid::Uuid::new_v4().to_string(),
                    content,
                    file_path: file_path.to_path_buf(),
                    start_line: current_start,
                    end_line: lines.len() - 1,
                    chunk_type: ChunkType::Module,
                    embedding: None,
                    related_nodes: vec![],
                    context_score: 0.6,
                    language: language.to_string(),
                };
                chunks.push(chunk);
            }
        }

        Ok(chunks)
    }

    /// Helper methods for pattern matching
    fn is_function_definition(&self, line: &str, patterns: &LanguagePatterns) -> bool {
        patterns.function_patterns.iter().any(|pattern| pattern.is_match(line))
    }

    fn is_class_definition(&self, line: &str, patterns: &LanguagePatterns) -> bool {
        patterns.class_patterns.iter().any(|pattern| pattern.is_match(line))
    }

    fn is_import_statement(&self, line: &str, patterns: &LanguagePatterns) -> bool {
        patterns.import_patterns.iter().any(|pattern| pattern.is_match(line))
    }

    fn is_comment_start(&self, line: &str, patterns: &LanguagePatterns) -> bool {
        patterns.comment_patterns.iter().any(|pattern| pattern.is_match(line))
    }

    fn is_comment_end(&self, line: &str, _patterns: &LanguagePatterns) -> bool {
        line.trim().ends_with("*/")
    }

    fn get_indentation(&self, line: &str) -> usize {
        line.len() - line.trim_start().len()
    }

    fn determine_chunk_type(&self, boundary_type: &BoundaryType) -> ChunkType {
        match boundary_type {
            BoundaryType::FunctionStart | BoundaryType::FunctionEnd => ChunkType::Function,
            BoundaryType::ClassStart | BoundaryType::ClassEnd => ChunkType::Class,
            BoundaryType::ModuleStart | BoundaryType::ModuleEnd => ChunkType::Module,
            BoundaryType::CommentBlock => ChunkType::Comment,
            BoundaryType::ImportBlock => ChunkType::Import,
            BoundaryType::LogicalBreak => ChunkType::Module,
        }
    }

    /// Split large chunks that exceed max size
    pub fn split_large_chunks(&self, chunks: Vec<SemanticChunk>) -> Vec<SemanticChunk> {
        let mut result = Vec::new();

        for chunk in chunks {
            let token_count = self.estimate_token_count(&chunk.content);
            
            if token_count <= self.max_chunk_size {
                result.push(chunk);
            } else {
                // Split large chunk into smaller pieces
                let sub_chunks = self.split_chunk_by_size(&chunk);
                result.extend(sub_chunks);
            }
        }

        result
    }

    /// Estimate token count for a text
    fn estimate_token_count(&self, text: &str) -> usize {
        // Simple estimation: ~4 characters per token
        text.graphemes(true).count() / 4
    }

    /// Split a chunk by size while preserving semantic boundaries
    fn split_chunk_by_size(&self, chunk: &SemanticChunk) -> Vec<SemanticChunk> {
        let lines: Vec<&str> = chunk.content.lines().collect();
        let mut sub_chunks = Vec::new();
        let mut current_start = 0;
        let mut current_size = 0;

        for (i, line) in lines.iter().enumerate() {
            let line_tokens = self.estimate_token_count(line);
            
            if current_size + line_tokens > self.max_chunk_size && current_size > 0 {
                // Create sub-chunk
                let content = lines[current_start..i].join("\n");
                let sub_chunk = SemanticChunk {
                    id: uuid::Uuid::new_v4().to_string(),
                    content,
                    file_path: chunk.file_path.clone(),
                    start_line: chunk.start_line + current_start,
                    end_line: chunk.start_line + i - 1,
                    chunk_type: chunk.chunk_type.clone(),
                    embedding: None,
                    related_nodes: vec![],
                    context_score: chunk.context_score,
                    language: chunk.language.clone(),
                };
                sub_chunks.push(sub_chunk);
                
                current_start = i.saturating_sub(self.chunk_overlap);
                current_size = 0;
            }
            
            current_size += line_tokens;
        }

        // Add final sub-chunk
        if current_start < lines.len() {
            let content = lines[current_start..].join("\n");
            let sub_chunk = SemanticChunk {
                id: uuid::Uuid::new_v4().to_string(),
                content,
                file_path: chunk.file_path.clone(),
                start_line: chunk.start_line + current_start,
                end_line: chunk.end_line,
                chunk_type: chunk.chunk_type.clone(),
                embedding: None,
                related_nodes: vec![],
                context_score: chunk.context_score,
                language: chunk.language.clone(),
            };
            sub_chunks.push(sub_chunk);
        }

        sub_chunks
    }
}
