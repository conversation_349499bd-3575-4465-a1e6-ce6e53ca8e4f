use std::collections::HashMap;
use std::path::Path;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use regex::Regex;
use crate::indexing::code_digital_twin::*;

/// Tacit Knowledge Extractor - Extracts implicit knowledge from code
/// Based on 2025 research on understanding tacit knowledge in software systems
pub struct TacitKnowledgeExtractor {
    comment_patterns: HashMap<String, Regex>,
    naming_patterns: HashMap<String, Regex>,
    structure_patterns: HashMap<String, Regex>,
    domain_vocabularies: HashMap<String, Vec<String>>,
    architectural_patterns: Vec<ArchitecturalPatternMatcher>,
}

/// Architectural pattern matcher
#[derive(Debug, Clone)]
pub struct ArchitecturalPatternMatcher {
    pub pattern_name: String,
    pub indicators: Vec<String>,
    pub structure_requirements: Vec<String>,
    pub naming_conventions: Vec<String>,
}

/// Extracted tacit knowledge from code analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractedTacitKnowledge {
    pub responsibility_allocation: String,
    pub implicit_assumptions: Vec<String>,
    pub domain_concepts: Vec<DomainConcept>,
    pub architectural_decisions: Vec<ArchitecturalDecision>,
    pub performance_considerations: Vec<PerformanceNote>,
    pub security_implications: Vec<SecurityNote>,
    pub maintainability_factors: Vec<MaintainabilityFactor>,
    pub design_patterns: Vec<String>,
    pub code_smells: Vec<CodeSmell>,
    pub quality_indicators: QualityIndicators,
}

/// Code smell detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeSmell {
    pub smell_type: String,
    pub description: String,
    pub severity: SmellSeverity,
    pub location: String,
    pub suggested_refactoring: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SmellSeverity {
    Critical,
    Major,
    Minor,
    Info,
}

/// Quality indicators extracted from code
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityIndicators {
    pub complexity_indicators: Vec<ComplexityIndicator>,
    pub maintainability_indicators: Vec<MaintainabilityIndicator>,
    pub testability_indicators: Vec<TestabilityIndicator>,
    pub reusability_indicators: Vec<ReusabilityIndicator>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplexityIndicator {
    pub metric: String,
    pub value: f64,
    pub threshold: f64,
    pub interpretation: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintainabilityIndicator {
    pub factor: String,
    pub score: f64,
    pub impact: String,
    pub improvement_suggestions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestabilityIndicator {
    pub aspect: String,
    pub score: f64,
    pub barriers: Vec<String>,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReusabilityIndicator {
    pub component: String,
    pub reusability_score: f64,
    pub reuse_barriers: Vec<String>,
    pub reuse_opportunities: Vec<String>,
}

impl TacitKnowledgeExtractor {
    pub fn new() -> Self {
        let mut extractor = Self {
            comment_patterns: HashMap::new(),
            naming_patterns: HashMap::new(),
            structure_patterns: HashMap::new(),
            domain_vocabularies: HashMap::new(),
            architectural_patterns: Vec::new(),
        };

        extractor.initialize_patterns();
        extractor.initialize_architectural_patterns();
        extractor
    }

    fn initialize_patterns(&mut self) {
        // Comment patterns for extracting tacit knowledge
        self.comment_patterns.insert(
            "todo".to_string(),
            Regex::new(r"(?i)todo:?\s*(.+)").unwrap(),
        );
        self.comment_patterns.insert(
            "fixme".to_string(),
            Regex::new(r"(?i)fixme:?\s*(.+)").unwrap(),
        );
        self.comment_patterns.insert(
            "hack".to_string(),
            Regex::new(r"(?i)hack:?\s*(.+)").unwrap(),
        );
        self.comment_patterns.insert(
            "assumption".to_string(),
            Regex::new(r"(?i)assume[sd]?:?\s*(.+)").unwrap(),
        );
        self.comment_patterns.insert(
            "performance".to_string(),
            Regex::new(r"(?i)performance:?\s*(.+)").unwrap(),
        );
        self.comment_patterns.insert(
            "security".to_string(),
            Regex::new(r"(?i)security:?\s*(.+)").unwrap(),
        );

        // Naming patterns that indicate design decisions
        self.naming_patterns.insert(
            "factory".to_string(),
            Regex::new(r"(?i)\w*factory\w*").unwrap(),
        );
        self.naming_patterns.insert(
            "builder".to_string(),
            Regex::new(r"(?i)\w*builder\w*").unwrap(),
        );
        self.naming_patterns.insert(
            "manager".to_string(),
            Regex::new(r"(?i)\w*manager\w*").unwrap(),
        );
        self.naming_patterns.insert(
            "handler".to_string(),
            Regex::new(r"(?i)\w*handler\w*").unwrap(),
        );
        self.naming_patterns.insert(
            "service".to_string(),
            Regex::new(r"(?i)\w*service\w*").unwrap(),
        );

        // Structure patterns
        self.structure_patterns.insert(
            "singleton".to_string(),
            Regex::new(r"(?i)static\s+\w+\s+instance").unwrap(),
        );
        self.structure_patterns.insert(
            "observer".to_string(),
            Regex::new(r"(?i)add_?listener|subscribe|notify").unwrap(),
        );
    }

    fn initialize_architectural_patterns(&mut self) {
        // MVC Pattern
        self.architectural_patterns.push(ArchitecturalPatternMatcher {
            pattern_name: "Model-View-Controller".to_string(),
            indicators: vec![
                "model".to_string(),
                "view".to_string(),
                "controller".to_string(),
            ],
            structure_requirements: vec![
                "separation of concerns".to_string(),
                "data binding".to_string(),
            ],
            naming_conventions: vec![
                "*Model".to_string(),
                "*View".to_string(),
                "*Controller".to_string(),
            ],
        });

        // Repository Pattern
        self.architectural_patterns.push(ArchitecturalPatternMatcher {
            pattern_name: "Repository".to_string(),
            indicators: vec![
                "repository".to_string(),
                "data access".to_string(),
                "persistence".to_string(),
            ],
            structure_requirements: vec![
                "data abstraction".to_string(),
                "query interface".to_string(),
            ],
            naming_conventions: vec![
                "*Repository".to_string(),
                "*DAO".to_string(),
            ],
        });

        // Strategy Pattern
        self.architectural_patterns.push(ArchitecturalPatternMatcher {
            pattern_name: "Strategy".to_string(),
            indicators: vec![
                "strategy".to_string(),
                "algorithm".to_string(),
                "policy".to_string(),
            ],
            structure_requirements: vec![
                "interface segregation".to_string(),
                "runtime selection".to_string(),
            ],
            naming_conventions: vec![
                "*Strategy".to_string(),
                "*Policy".to_string(),
                "*Algorithm".to_string(),
            ],
        });
    }

    /// Extract tacit knowledge from source code
    pub async fn extract_from_code(
        &self,
        content: &str,
        file_path: &Path,
        language: &str,
    ) -> Result<ExtractedTacitKnowledge> {
        let mut knowledge = ExtractedTacitKnowledge {
            responsibility_allocation: self.extract_responsibility(content, file_path)?,
            implicit_assumptions: self.extract_assumptions(content)?,
            domain_concepts: self.extract_domain_concepts(content, language)?,
            architectural_decisions: self.extract_architectural_decisions(content)?,
            performance_considerations: self.extract_performance_notes(content)?,
            security_implications: self.extract_security_notes(content)?,
            maintainability_factors: self.extract_maintainability_factors(content)?,
            design_patterns: self.detect_design_patterns(content)?,
            code_smells: self.detect_code_smells(content, language)?,
            quality_indicators: self.analyze_quality_indicators(content, language)?,
        };

        Ok(knowledge)
    }

    fn extract_responsibility(&self, content: &str, file_path: &Path) -> Result<String> {
        // Extract responsibility from file name, class names, and comments
        let file_name = file_path.file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("unknown");

        // Look for class/struct definitions and their purposes
        let class_pattern = Regex::new(r"(?i)class\s+(\w+)|struct\s+(\w+)|interface\s+(\w+)")?;
        let mut responsibilities = Vec::new();

        for cap in class_pattern.captures_iter(content) {
            if let Some(class_name) = cap.get(1).or(cap.get(2)).or(cap.get(3)) {
                responsibilities.push(format!("Defines {}", class_name.as_str()));
            }
        }

        // Look for main function or entry points
        if content.contains("fn main") || content.contains("function main") {
            responsibilities.push("Application entry point".to_string());
        }

        // Extract from comments
        let comment_pattern = Regex::new(r"//\s*(.+)|/\*\s*(.+?)\s*\*/")?;
        for cap in comment_pattern.captures_iter(content) {
            if let Some(comment) = cap.get(1).or(cap.get(2)) {
                let comment_text = comment.as_str().trim();
                if comment_text.len() > 20 && !comment_text.starts_with("TODO") {
                    responsibilities.push(comment_text.to_string());
                    break; // Take the first meaningful comment
                }
            }
        }

        if responsibilities.is_empty() {
            responsibilities.push(format!("Handles {}-related functionality", file_name));
        }

        Ok(responsibilities.join("; "))
    }

    fn extract_assumptions(&self, content: &str) -> Result<Vec<String>> {
        let mut assumptions = Vec::new();

        if let Some(pattern) = self.comment_patterns.get("assumption") {
            for cap in pattern.captures_iter(content) {
                if let Some(assumption) = cap.get(1) {
                    assumptions.push(assumption.as_str().trim().to_string());
                }
            }
        }

        // Look for assert statements
        let assert_pattern = Regex::new(r"assert[!]?\s*\((.+?)\)")?;
        for cap in assert_pattern.captures_iter(content) {
            if let Some(assertion) = cap.get(1) {
                assumptions.push(format!("Assumes: {}", assertion.as_str().trim()));
            }
        }

        // Look for unwrap() calls which indicate assumptions
        let unwrap_pattern = Regex::new(r"\.unwrap\(\)")?;
        if unwrap_pattern.is_match(content) {
            assumptions.push("Assumes operations will not fail (uses unwrap)".to_string());
        }

        Ok(assumptions)
    }

    fn extract_domain_concepts(&self, content: &str, language: &str) -> Result<Vec<DomainConcept>> {
        let mut concepts = Vec::new();

        // Extract from type definitions
        let type_pattern = match language {
            "rust" => Regex::new(r"(?i)struct\s+(\w+)|enum\s+(\w+)|type\s+(\w+)")?,
            "typescript" | "javascript" => Regex::new(r"(?i)interface\s+(\w+)|class\s+(\w+)|type\s+(\w+)")?,
            "python" => Regex::new(r"(?i)class\s+(\w+)")?,
            _ => Regex::new(r"(?i)class\s+(\w+)")?,
        };

        for cap in type_pattern.captures_iter(content) {
            if let Some(type_name) = cap.get(1).or(cap.get(2)).or(cap.get(3)) {
                let name = type_name.as_str();
                concepts.push(DomainConcept {
                    name: name.to_string(),
                    description: format!("Domain entity: {}", name),
                    business_rules: Vec::new(),
                    constraints: Vec::new(),
                    relationships: Vec::new(),
                });
            }
        }

        Ok(concepts)
    }

    fn extract_architectural_decisions(&self, content: &str) -> Result<Vec<ArchitecturalDecision>> {
        let mut decisions = Vec::new();

        // Look for architectural patterns
        for pattern in &self.architectural_patterns {
            let mut found_indicators = 0;
            for indicator in &pattern.indicators {
                if content.to_lowercase().contains(indicator) {
                    found_indicators += 1;
                }
            }

            if found_indicators >= pattern.indicators.len() / 2 {
                decisions.push(ArchitecturalDecision {
                    decision: format!("Uses {} pattern", pattern.pattern_name),
                    rationale: "Inferred from code structure and naming".to_string(),
                    alternatives_considered: Vec::new(),
                    consequences: Vec::new(),
                    status: DecisionStatus::Accepted,
                    date: chrono::Utc::now(),
                });
            }
        }

        Ok(decisions)
    }

    fn extract_performance_notes(&self, content: &str) -> Result<Vec<PerformanceNote>> {
        let mut notes = Vec::new();

        if let Some(pattern) = self.comment_patterns.get("performance") {
            for cap in pattern.captures_iter(content) {
                if let Some(note) = cap.get(1) {
                    notes.push(PerformanceNote {
                        aspect: "Performance consideration".to_string(),
                        impact: PerformanceImpact::Medium,
                        measurement: None,
                        optimization_opportunities: vec![note.as_str().trim().to_string()],
                    });
                }
            }
        }

        // Look for performance-related patterns
        if content.contains("cache") || content.contains("Cache") {
            notes.push(PerformanceNote {
                aspect: "Caching".to_string(),
                impact: PerformanceImpact::High,
                measurement: None,
                optimization_opportunities: vec!["Uses caching for performance".to_string()],
            });
        }

        if content.contains("async") || content.contains("await") {
            notes.push(PerformanceNote {
                aspect: "Asynchronous processing".to_string(),
                impact: PerformanceImpact::High,
                measurement: None,
                optimization_opportunities: vec!["Uses async/await for non-blocking operations".to_string()],
            });
        }

        Ok(notes)
    }

    fn extract_security_notes(&self, content: &str) -> Result<Vec<SecurityNote>> {
        let mut notes = Vec::new();

        if let Some(pattern) = self.comment_patterns.get("security") {
            for cap in pattern.captures_iter(content) {
                if let Some(note) = cap.get(1) {
                    notes.push(SecurityNote {
                        vulnerability_type: "Security consideration".to_string(),
                        risk_level: RiskLevel::Medium,
                        mitigation_strategies: vec![note.as_str().trim().to_string()],
                        compliance_requirements: Vec::new(),
                    });
                }
            }
        }

        // Look for security-related patterns
        if content.contains("password") || content.contains("secret") || content.contains("token") {
            notes.push(SecurityNote {
                vulnerability_type: "Credential handling".to_string(),
                risk_level: RiskLevel::High,
                mitigation_strategies: vec!["Ensure proper credential protection".to_string()],
                compliance_requirements: Vec::new(),
            });
        }

        Ok(notes)
    }

    fn extract_maintainability_factors(&self, content: &str) -> Result<Vec<MaintainabilityFactor>> {
        let mut factors = Vec::new();

        // Check for TODO/FIXME comments
        if let Some(todo_pattern) = self.comment_patterns.get("todo") {
            let todo_count = todo_pattern.find_iter(content).count();
            if todo_count > 0 {
                factors.push(MaintainabilityFactor {
                    factor: "Technical debt".to_string(),
                    impact: format!("{} TODO items found", todo_count),
                    improvement_suggestions: vec!["Address TODO items".to_string()],
                    technical_debt: Some(TechnicalDebt {
                        debt_type: "Incomplete implementation".to_string(),
                        severity: if todo_count > 5 { DebtSeverity::High } else { DebtSeverity::Medium },
                        estimated_effort: format!("{} hours", todo_count * 2),
                        business_impact: "Potential feature gaps".to_string(),
                    }),
                });
            }
        }

        // Check for code complexity indicators
        let line_count = content.lines().count();
        if line_count > 500 {
            factors.push(MaintainabilityFactor {
                factor: "File size".to_string(),
                impact: format!("Large file with {} lines", line_count),
                improvement_suggestions: vec!["Consider splitting into smaller modules".to_string()],
                technical_debt: None,
            });
        }

        Ok(factors)
    }

    fn detect_design_patterns(&self, content: &str) -> Result<Vec<String>> {
        let mut patterns = Vec::new();

        for (pattern_name, regex) in &self.naming_patterns {
            if regex.is_match(content) {
                patterns.push(format!("{} pattern", pattern_name));
            }
        }

        for (pattern_name, regex) in &self.structure_patterns {
            if regex.is_match(content) {
                patterns.push(format!("{} pattern", pattern_name));
            }
        }

        Ok(patterns)
    }

    fn detect_code_smells(&self, content: &str, language: &str) -> Result<Vec<CodeSmell>> {
        let mut smells = Vec::new();

        // Long method detection
        let method_pattern = match language {
            "rust" => Regex::new(r"fn\s+\w+[^{]*\{([^{}]*\{[^{}]*\}[^{}]*)*[^{}]*\}")?,
            "typescript" | "javascript" => Regex::new(r"function\s+\w+[^{]*\{([^{}]*\{[^{}]*\}[^{}]*)*[^{}]*\}")?,
            _ => Regex::new(r"def\s+\w+[^:]*:([^{}]*\{[^{}]*\}[^{}]*)*[^{}]*")?,
        };

        for method_match in method_pattern.find_iter(content) {
            let method_lines = method_match.as_str().lines().count();
            if method_lines > 50 {
                smells.push(CodeSmell {
                    smell_type: "Long Method".to_string(),
                    description: format!("Method has {} lines", method_lines),
                    severity: SmellSeverity::Major,
                    location: format!("Line {}", content[..method_match.start()].lines().count() + 1),
                    suggested_refactoring: "Extract smaller methods".to_string(),
                });
            }
        }

        // Duplicate code detection (simplified)
        let lines: Vec<&str> = content.lines().collect();
        let mut line_counts: HashMap<&str, usize> = HashMap::new();
        for line in &lines {
            let trimmed = line.trim();
            if trimmed.len() > 10 && !trimmed.starts_with("//") {
                *line_counts.entry(trimmed).or_insert(0) += 1;
            }
        }

        for (line, count) in line_counts {
            if count > 3 {
                smells.push(CodeSmell {
                    smell_type: "Duplicate Code".to_string(),
                    description: format!("Line repeated {} times: {}", count, line),
                    severity: SmellSeverity::Minor,
                    location: "Multiple locations".to_string(),
                    suggested_refactoring: "Extract common functionality".to_string(),
                });
            }
        }

        Ok(smells)
    }

    fn analyze_quality_indicators(&self, content: &str, language: &str) -> Result<QualityIndicators> {
        let line_count = content.lines().count();
        let comment_lines = content.lines().filter(|line| {
            let trimmed = line.trim();
            trimmed.starts_with("//") || trimmed.starts_with("/*") || trimmed.starts_with("*")
        }).count();

        let comment_ratio = if line_count > 0 {
            comment_lines as f64 / line_count as f64
        } else {
            0.0
        };

        Ok(QualityIndicators {
            complexity_indicators: vec![
                ComplexityIndicator {
                    metric: "Lines of Code".to_string(),
                    value: line_count as f64,
                    threshold: 500.0,
                    interpretation: if line_count > 500 {
                        "High complexity - consider refactoring".to_string()
                    } else {
                        "Acceptable complexity".to_string()
                    },
                },
            ],
            maintainability_indicators: vec![
                MaintainabilityIndicator {
                    factor: "Documentation".to_string(),
                    score: comment_ratio * 100.0,
                    impact: if comment_ratio > 0.2 {
                        "Well documented".to_string()
                    } else {
                        "Needs more documentation".to_string()
                    },
                    improvement_suggestions: if comment_ratio < 0.1 {
                        vec!["Add more comments and documentation".to_string()]
                    } else {
                        Vec::new()
                    },
                },
            ],
            testability_indicators: vec![
                TestabilityIndicator {
                    aspect: "Test presence".to_string(),
                    score: if content.contains("test") || content.contains("Test") { 80.0 } else { 20.0 },
                    barriers: if !content.contains("test") {
                        vec!["No visible test code".to_string()]
                    } else {
                        Vec::new()
                    },
                    recommendations: vec!["Ensure comprehensive test coverage".to_string()],
                },
            ],
            reusability_indicators: vec![
                ReusabilityIndicator {
                    component: "Module".to_string(),
                    reusability_score: if content.contains("pub ") || content.contains("export ") { 70.0 } else { 30.0 },
                    reuse_barriers: Vec::new(),
                    reuse_opportunities: vec!["Consider extracting reusable components".to_string()],
                },
            ],
        })
    }
}

impl Default for TacitKnowledgeExtractor {
    fn default() -> Self {
        Self::new()
    }
}
