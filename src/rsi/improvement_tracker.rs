// Improvement Tracker
// Track and analyze improvement patterns over time

use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use anyhow::Result;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImprovementRecord {
    pub id: uuid::Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub original_code: String,
    pub improved_code: String,
    pub improvement_factor: f64,
    pub improvement_type: ImprovementType,
    pub verification_status: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ImprovementType {
    Performance,
    Readability,
    Maintainability,
    Security,
    Complexity,
    Style,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImprovementTrend {
    pub trend_type: ImprovementType,
    pub average_improvement: f64,
    pub improvement_count: u32,
    pub success_rate: f64,
    pub trend_direction: TrendDirection,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum TrendDirection {
    Improving,
    Stable,
    Declining,
}

pub struct ImprovementTracker {
    records: Arc<RwLock<Vec<ImprovementRecord>>>,
    trends: Arc<RwLock<Vec<ImprovementTrend>>>,
}

impl ImprovementTracker {
    pub async fn new() -> Result<Self> {
        Ok(Self {
            records: Arc::new(RwLock::new(Vec::new())),
            trends: Arc::new(RwLock::new(Vec::new())),
        })
    }

    pub async fn record_improvement(
        &self,
        original: &str,
        improved: &str,
        improvement_factor: f64,
        improvement_type: ImprovementType,
        verified: bool,
    ) -> Result<()> {
        let record = ImprovementRecord {
            id: uuid::Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            original_code: original.to_string(),
            improved_code: improved.to_string(),
            improvement_factor,
            improvement_type: improvement_type.clone(),
            verification_status: verified,
        };

        self.records.write().await.push(record);
        self.update_trends(&improvement_type, improvement_factor, verified).await?;

        Ok(())
    }

    async fn update_trends(
        &self,
        improvement_type: &ImprovementType,
        improvement_factor: f64,
        verified: bool,
    ) -> Result<()> {
        let mut trends = self.trends.write().await;
        
        // Find existing trend or create new one
        if let Some(trend) = trends.iter_mut().find(|t| std::mem::discriminant(&t.trend_type) == std::mem::discriminant(improvement_type)) {
            // Update existing trend
            let total_improvement = trend.average_improvement * trend.improvement_count as f64 + improvement_factor;
            trend.improvement_count += 1;
            trend.average_improvement = total_improvement / trend.improvement_count as f64;
            
            if verified {
                trend.success_rate = (trend.success_rate * (trend.improvement_count - 1) as f64 + 1.0) / trend.improvement_count as f64;
            } else {
                trend.success_rate = (trend.success_rate * (trend.improvement_count - 1) as f64) / trend.improvement_count as f64;
            }
            
            // Update trend direction
            trend.trend_direction = self.calculate_trend_direction(&trend).await?;
        } else {
            // Create new trend
            let new_trend = ImprovementTrend {
                trend_type: improvement_type.clone(),
                average_improvement: improvement_factor,
                improvement_count: 1,
                success_rate: if verified { 1.0 } else { 0.0 },
                trend_direction: TrendDirection::Stable,
            };
            trends.push(new_trend);
        }

        Ok(())
    }

    async fn calculate_trend_direction(&self, trend: &ImprovementTrend) -> Result<TrendDirection> {
        // Simple trend calculation based on recent performance
        if trend.average_improvement > 1.1 && trend.success_rate > 0.8 {
            Ok(TrendDirection::Improving)
        } else if trend.average_improvement < 1.05 || trend.success_rate < 0.6 {
            Ok(TrendDirection::Declining)
        } else {
            Ok(TrendDirection::Stable)
        }
    }

    pub async fn get_improvement_trends(&self) -> Result<Vec<ImprovementTrend>> {
        Ok(self.trends.read().await.clone())
    }

    pub async fn get_recent_improvements(&self, limit: usize) -> Result<Vec<ImprovementRecord>> {
        let records = self.records.read().await;
        let recent: Vec<ImprovementRecord> = records
            .iter()
            .rev()
            .take(limit)
            .cloned()
            .collect();
        Ok(recent)
    }

    pub async fn get_improvement_statistics(&self) -> Result<ImprovementStatistics> {
        let records = self.records.read().await;
        
        if records.is_empty() {
            return Ok(ImprovementStatistics::default());
        }

        let total_improvements = records.len();
        let successful_improvements = records.iter().filter(|r| r.verification_status).count();
        let average_improvement_factor: f64 = records.iter().map(|r| r.improvement_factor).sum::<f64>() / total_improvements as f64;
        
        let improvement_types: std::collections::HashMap<String, u32> = records
            .iter()
            .fold(std::collections::HashMap::new(), |mut acc, record| {
                let type_name = format!("{:?}", record.improvement_type);
                *acc.entry(type_name).or_insert(0) += 1;
                acc
            });

        Ok(ImprovementStatistics {
            total_improvements,
            successful_improvements,
            success_rate: successful_improvements as f64 / total_improvements as f64,
            average_improvement_factor,
            improvement_types,
        })
    }

    pub async fn analyze_improvement_patterns(&self) -> Result<Vec<ImprovementPattern>> {
        let records = self.records.read().await;
        let mut patterns = Vec::new();

        // Analyze patterns in the improvement data
        if records.len() >= 10 {
            // Pattern 1: Consistent improvement in specific areas
            let performance_improvements: Vec<_> = records
                .iter()
                .filter(|r| matches!(r.improvement_type, ImprovementType::Performance))
                .collect();

            if performance_improvements.len() >= 5 {
                let avg_performance_improvement: f64 = performance_improvements
                    .iter()
                    .map(|r| r.improvement_factor)
                    .sum::<f64>() / performance_improvements.len() as f64;

                if avg_performance_improvement > 1.15 {
                    patterns.push(ImprovementPattern {
                        pattern_type: "consistent_performance_improvement".to_string(),
                        description: "Consistent performance improvements detected".to_string(),
                        confidence: 0.9,
                        impact: avg_performance_improvement,
                    });
                }
            }

            // Pattern 2: Learning acceleration
            let recent_improvements: Vec<_> = records.iter().rev().take(5).collect();
            let older_improvements: Vec<_> = records.iter().rev().skip(5).take(5).collect();

            if !recent_improvements.is_empty() && !older_improvements.is_empty() {
                let recent_avg: f64 = recent_improvements.iter().map(|r| r.improvement_factor).sum::<f64>() / recent_improvements.len() as f64;
                let older_avg: f64 = older_improvements.iter().map(|r| r.improvement_factor).sum::<f64>() / older_improvements.len() as f64;

                if recent_avg > older_avg * 1.1 {
                    patterns.push(ImprovementPattern {
                        pattern_type: "learning_acceleration".to_string(),
                        description: "Improvement quality is accelerating over time".to_string(),
                        confidence: 0.8,
                        impact: recent_avg / older_avg,
                    });
                }
            }
        }

        Ok(patterns)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImprovementStatistics {
    pub total_improvements: usize,
    pub successful_improvements: usize,
    pub success_rate: f64,
    pub average_improvement_factor: f64,
    pub improvement_types: std::collections::HashMap<String, u32>,
}

impl Default for ImprovementStatistics {
    fn default() -> Self {
        Self {
            total_improvements: 0,
            successful_improvements: 0,
            success_rate: 0.0,
            average_improvement_factor: 1.0,
            improvement_types: std::collections::HashMap::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImprovementPattern {
    pub pattern_type: String,
    pub description: String,
    pub confidence: f64,
    pub impact: f64,
}
