/* Aizen AI Settings Page Styles */

/* CSS Variables */
:root {
    /* Colors */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --text-muted: #666666;
    --border-color: #333333;
    --accent-primary: #3b82f6;
    --accent-secondary: #8b5cf6;
    --accent-success: #10b981;
    --accent-warning: #f59e0b;
    --accent-danger: #ef4444;

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

    /* Glass effect */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
}

/* Settings Body */
.settings-body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow-x: hidden;
    min-height: 100vh;
}

/* Settings Container */
.settings-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0;
}

/* Settings Header */
.settings-header {
    position: sticky;
    top: 0;
    z-index: 100;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(20px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.back-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border: none;
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(-2px);
}

.header-title h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-title p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.save-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    background: var(--accent-primary);
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.save-btn:hover {
    background: var(--accent-hover);
    transform: translateY(-1px);
}

/* Settings Main */
.settings-main {
    flex: 1;
    padding: 1.5rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    max-width: none;
}

/* Settings Section */
.settings-section {
    padding: 1.5rem;
    border-radius: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(20px);
    transition: all var(--transition-normal);
}

.settings-section:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.section-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.75rem;
    background: var(--accent-primary);
    color: white;
}

.section-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Setting Groups */
.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.setting-description {
    margin: 0.25rem 0 0 0;
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Form Controls */
.setting-input,
.setting-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.setting-input:focus,
.setting-select:focus {
    outline: none;
    border-color: var(--accent-primary);
    background: rgba(255, 255, 255, 0.1);
}

/* Slider Controls */
.slider-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.setting-slider {
    flex: 1;
    height: 0.5rem;
    border-radius: 0.25rem;
    background: var(--bg-secondary);
    outline: none;
    cursor: pointer;
}

.setting-slider::-webkit-slider-thumb {
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background: var(--accent-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.setting-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

.slider-value {
    min-width: 3rem;
    text-align: right;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--accent-primary);
}

/* Radio Groups */
.radio-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    position: relative;
    transition: all var(--transition-fast);
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--accent-primary);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: var(--accent-primary);
}

/* Color Picker */
.color-picker-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.color-picker {
    width: 100%;
    height: 3rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: transparent;
    cursor: pointer;
}

.color-presets {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.color-preset {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.color-preset:hover {
    transform: scale(1.1);
    border-color: var(--text-primary);
}

/* Toggle Switches */
.toggle-setting {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 1rem;
}

.toggle-info {
    flex: 1;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 3rem;
    height: 1.5rem;
    flex-shrink: 0;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    transition: all var(--transition-fast);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 1rem;
    width: 1rem;
    left: 0.125rem;
    bottom: 0.125rem;
    background: white;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

input:checked + .toggle-slider {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(1.5rem);
}

/* MCP Servers List */
.setting-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.add-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: 0.5rem;
    background: var(--accent-primary);
    color: white;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.add-btn:hover {
    background: var(--accent-hover);
    transform: scale(1.05);
}

.mcp-servers-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.mcp-server-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.03);
}

.mcp-server-info {
    flex: 1;
}

.mcp-server-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.mcp-server-url {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.mcp-server-actions {
    display: flex;
    gap: 0.5rem;
}

.mcp-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.75rem;
    height: 1.75rem;
    border: none;
    border-radius: 0.375rem;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.mcp-action-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
}

.mcp-action-btn.delete:hover {
    background: var(--accent-danger);
    color: white;
}

/* Shortcut Input */
.shortcut-input {
    display: flex;
    gap: 0.5rem;
}

.shortcut-input .setting-input {
    flex: 1;
}

.change-shortcut-btn {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.change-shortcut-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Settings Footer */
.settings-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    backdrop-filter: blur(20px);
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.reset-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.reset-btn:hover {
    background: var(--accent-danger);
    color: white;
    border-color: var(--accent-danger);
}

.version-info {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .settings-main {
        padding: 1rem;
    }
    
    .settings-header {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .radio-group {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .toggle-setting {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .shortcut-input {
        flex-direction: column;
    }
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-section {
    animation: slideIn 0.3s ease-out;
}

.settings-section:nth-child(1) { animation-delay: 0.1s; }
.settings-section:nth-child(2) { animation-delay: 0.2s; }
.settings-section:nth-child(3) { animation-delay: 0.3s; }
.settings-section:nth-child(4) { animation-delay: 0.4s; }
.settings-section:nth-child(5) { animation-delay: 0.5s; }

/* Focus States */
.setting-input:focus,
.setting-select:focus,
.color-picker:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.toggle-switch:focus-within .toggle-slider {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* MCP Server Cards */
.mcp-server-card {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 1rem;
    transition: all var(--transition-fast);
}

.mcp-server-card:hover {
    border-color: var(--accent-primary);
    background: rgba(255, 255, 255, 0.08);
}

.server-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.server-info h4 {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.server-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-unconfigured {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-configured {
    background: rgba(16, 185, 129, 0.2);
    color: #6ee7b7;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-error {
    background: rgba(245, 158, 11, 0.2);
    color: #fcd34d;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.server-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-small {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
}

.btn-danger {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    background: rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.5);
}

.status-testing {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 4rem;
}

/* Loading States */
.settings-section.loading {
    opacity: 0.6;
    pointer-events: none;
}

.settings-section.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
