use std::collections::{HashMap, HashSet};
use tree_sitter::{Language, Parser, Query, QueryCursor, Tree, Node};
use serde::{Deserialize, Serialize};

use super::{AstNode, Position, Import, ImportType, Export, ExportType, Function, Parameter, Class, Property, Visibility};

pub struct CodeAnalyzer {
    parsers: HashMap<String, Parser>,
    queries: HashMap<String, HashMap<String, Query>>,
}

impl CodeAnalyzer {
    pub fn new() -> Self {
        let mut analyzer = Self {
            parsers: HashMap::new(),
            queries: HashMap::new(),
        };
        
        analyzer.initialize_parsers();
        analyzer.initialize_queries();
        
        analyzer
    }

    fn initialize_parsers(&mut self) {
        // TypeScript parser
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_typescript::language_typescript()).is_ok() {
                self.parsers.insert("typescript".to_string(), parser);
            }
        }

        // JavaScript parser
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_javascript::language()).is_ok() {
                self.parsers.insert("javascript".to_string(), parser);
            }
        }

        // Rust parser
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_rust::language()).is_ok() {
                self.parsers.insert("rust".to_string(), parser);
            }
        }

        // Python parser
        if let Ok(mut parser) = Parser::new() {
            if parser.set_language(tree_sitter_python::language()).is_ok() {
                self.parsers.insert("python".to_string(), parser);
            }
        }
    }

    fn initialize_queries(&mut self) {
        // TypeScript/JavaScript queries
        let ts_imports_query = r#"
            (import_statement
              source: (string (string_fragment) @import_path))
            
            (import_statement
              (import_clause
                (named_imports
                  (import_specifier
                    name: (identifier) @import_name))))
        "#;

        let ts_exports_query = r#"
            (export_statement
              (variable_declaration
                (variable_declarator
                  name: (identifier) @export_name)))
            
            (export_statement
              declaration: (function_declaration
                name: (identifier) @export_name))
            
            (export_statement
              declaration: (class_declaration
                name: (type_identifier) @export_name))
        "#;

        let ts_functions_query = r#"
            (function_declaration
              name: (identifier) @function_name
              parameters: (formal_parameters) @parameters
              body: (statement_block) @body)
            
            (method_definition
              name: (property_identifier) @method_name
              parameters: (formal_parameters) @parameters
              body: (statement_block) @body)
            
            (arrow_function
              parameters: (formal_parameters) @parameters
              body: (_) @body)
        "#;

        let ts_classes_query = r#"
            (class_declaration
              name: (type_identifier) @class_name
              (class_heritage (extends_clause (identifier) @superclass))?
              body: (class_body) @class_body)
        "#;

        // Rust queries
        let rust_functions_query = r#"
            (function_item
              name: (identifier) @function_name
              parameters: (parameters) @parameters
              body: (block) @body)
            
            (impl_item
              (function_item
                name: (identifier) @method_name
                parameters: (parameters) @parameters
                body: (block) @body))
        "#;

        let rust_structs_query = r#"
            (struct_item
              name: (type_identifier) @struct_name
              body: (field_declaration_list) @fields)
        "#;

        let rust_imports_query = r#"
            (use_declaration
              argument: (scoped_identifier
                path: (identifier) @module
                name: (identifier) @import_name))
            
            (use_declaration
              argument: (identifier) @module)
        "#;

        // Python queries
        let python_functions_query = r#"
            (function_definition
              name: (identifier) @function_name
              parameters: (parameters) @parameters
              body: (block) @body)
        "#;

        let python_classes_query = r#"
            (class_definition
              name: (identifier) @class_name
              superclasses: (argument_list)? @superclasses
              body: (block) @class_body)
        "#;

        let python_imports_query = r#"
            (import_statement
              name: (dotted_name) @module)
            
            (import_from_statement
              module_name: (dotted_name) @module
              name: (dotted_name) @import_name)
        "#;

        // Store queries for each language
        let mut ts_queries = HashMap::new();
        if let Ok(query) = Query::new(tree_sitter_typescript::language_typescript(), ts_imports_query) {
            ts_queries.insert("imports".to_string(), query);
        }
        if let Ok(query) = Query::new(tree_sitter_typescript::language_typescript(), ts_exports_query) {
            ts_queries.insert("exports".to_string(), query);
        }
        if let Ok(query) = Query::new(tree_sitter_typescript::language_typescript(), ts_functions_query) {
            ts_queries.insert("functions".to_string(), query);
        }
        if let Ok(query) = Query::new(tree_sitter_typescript::language_typescript(), ts_classes_query) {
            ts_queries.insert("classes".to_string(), query);
        }
        self.queries.insert("typescript".to_string(), ts_queries);
        self.queries.insert("javascript".to_string(), ts_queries.clone());

        let mut rust_queries = HashMap::new();
        if let Ok(query) = Query::new(tree_sitter_rust::language(), rust_functions_query) {
            rust_queries.insert("functions".to_string(), query);
        }
        if let Ok(query) = Query::new(tree_sitter_rust::language(), rust_structs_query) {
            rust_queries.insert("structs".to_string(), query);
        }
        if let Ok(query) = Query::new(tree_sitter_rust::language(), rust_imports_query) {
            rust_queries.insert("imports".to_string(), query);
        }
        self.queries.insert("rust".to_string(), rust_queries);

        let mut python_queries = HashMap::new();
        if let Ok(query) = Query::new(tree_sitter_python::language(), python_functions_query) {
            python_queries.insert("functions".to_string(), query);
        }
        if let Ok(query) = Query::new(tree_sitter_python::language(), python_classes_query) {
            python_queries.insert("classes".to_string(), query);
        }
        if let Ok(query) = Query::new(tree_sitter_python::language(), python_imports_query) {
            python_queries.insert("imports".to_string(), query);
        }
        self.queries.insert("python".to_string(), python_queries);
    }

    pub async fn parse_ast(&self, content: &str, language: &str) -> Result<Vec<AstNode>, anyhow::Error> {
        let parser = self.parsers.get(language)
            .ok_or_else(|| anyhow::anyhow!("Parser not available for language: {}", language))?;

        let tree = parser.parse(content, None)
            .ok_or_else(|| anyhow::anyhow!("Failed to parse content"))?;

        let root_node = tree.root_node();
        Ok(vec![self.node_to_ast_node(&root_node, content)])
    }

    fn node_to_ast_node(&self, node: &Node, source: &str) -> AstNode {
        let start_pos = node.start_position();
        let end_pos = node.end_position();
        
        let text = if node.start_byte() < source.len() && node.end_byte() <= source.len() {
            source[node.start_byte()..node.end_byte()].to_string()
        } else {
            String::new()
        };

        let mut children = Vec::new();
        let mut cursor = node.walk();
        
        if cursor.goto_first_child() {
            loop {
                children.push(self.node_to_ast_node(&cursor.node(), source));
                if !cursor.goto_next_sibling() {
                    break;
                }
            }
        }

        AstNode {
            node_type: node.kind().to_string(),
            start_byte: node.start_byte(),
            end_byte: node.end_byte(),
            start_position: Position {
                row: start_pos.row,
                column: start_pos.column,
            },
            end_position: Position {
                row: end_pos.row,
                column: end_pos.column,
            },
            text,
            children,
        }
    }

    pub async fn extract_imports(&self, content: &str, language: &str) -> Result<Vec<Import>, anyhow::Error> {
        let parser = self.parsers.get(language)
            .ok_or_else(|| anyhow::anyhow!("Parser not available for language: {}", language))?;

        let tree = parser.parse(content, None)
            .ok_or_else(|| anyhow::anyhow!("Failed to parse content"))?;

        let queries = self.queries.get(language)
            .ok_or_else(|| anyhow::anyhow!("Queries not available for language: {}", language))?;

        let import_query = queries.get("imports")
            .ok_or_else(|| anyhow::anyhow!("Import query not available for language: {}", language))?;

        let mut imports = Vec::new();
        let mut cursor = QueryCursor::new();
        let matches = cursor.matches(import_query, tree.root_node(), content.as_bytes());

        for match_ in matches {
            let mut module = String::new();
            let mut import_names = Vec::new();
            let mut line = 0;

            for capture in match_.captures {
                let node = capture.node;
                let capture_name = import_query.capture_names()[capture.index as usize];
                let text = node.utf8_text(content.as_bytes()).unwrap_or("").to_string();

                match capture_name {
                    "import_path" | "module" => {
                        module = text.trim_matches('"').trim_matches('\'').to_string();
                        line = node.start_position().row + 1;
                    }
                    "import_name" => {
                        import_names.push(text);
                    }
                    _ => {}
                }
            }

            if !module.is_empty() {
                imports.push(Import {
                    module,
                    imports: if import_names.is_empty() { vec!["*".to_string()] } else { import_names },
                    import_type: ImportType::Named,
                    line,
                });
            }
        }

        Ok(imports)
    }

    pub async fn extract_exports(&self, content: &str, language: &str) -> Result<Vec<Export>, anyhow::Error> {
        let parser = self.parsers.get(language)
            .ok_or_else(|| anyhow::anyhow!("Parser not available for language: {}", language))?;

        let tree = parser.parse(content, None)
            .ok_or_else(|| anyhow::anyhow!("Failed to parse content"))?;

        let queries = self.queries.get(language)
            .ok_or_else(|| anyhow::anyhow!("Queries not available for language: {}", language))?;

        let export_query = queries.get("exports")
            .ok_or_else(|| anyhow::anyhow!("Export query not available for language: {}", language))?;

        let mut exports = Vec::new();
        let mut cursor = QueryCursor::new();
        let matches = cursor.matches(export_query, tree.root_node(), content.as_bytes());

        for match_ in matches {
            for capture in match_.captures {
                let node = capture.node;
                let text = node.utf8_text(content.as_bytes()).unwrap_or("").to_string();
                let line = node.start_position().row + 1;

                exports.push(Export {
                    name: text,
                    export_type: ExportType::Named,
                    line,
                });
            }
        }

        Ok(exports)
    }

    pub async fn extract_functions(&self, content: &str, language: &str) -> Result<Vec<Function>, anyhow::Error> {
        let parser = self.parsers.get(language)
            .ok_or_else(|| anyhow::anyhow!("Parser not available for language: {}", language))?;

        let tree = parser.parse(content, None)
            .ok_or_else(|| anyhow::anyhow!("Failed to parse content"))?;

        let queries = self.queries.get(language)
            .ok_or_else(|| anyhow::anyhow!("Queries not available for language: {}", language))?;

        let function_query = queries.get("functions")
            .ok_or_else(|| anyhow::anyhow!("Function query not available for language: {}", language))?;

        let mut functions = Vec::new();
        let mut cursor = QueryCursor::new();
        let matches = cursor.matches(function_query, tree.root_node(), content.as_bytes());

        for match_ in matches {
            let mut function_name = String::new();
            let mut start_line = 0;
            let mut end_line = 0;
            let mut parameters = Vec::new();

            for capture in match_.captures {
                let node = capture.node;
                let capture_name = function_query.capture_names()[capture.index as usize];
                let text = node.utf8_text(content.as_bytes()).unwrap_or("").to_string();

                match capture_name {
                    "function_name" | "method_name" => {
                        function_name = text;
                        start_line = node.start_position().row + 1;
                    }
                    "parameters" => {
                        parameters = self.extract_parameters(&node, content, language);
                        end_line = node.end_position().row + 1;
                    }
                    "body" => {
                        end_line = node.end_position().row + 1;
                    }
                    _ => {}
                }
            }

            if !function_name.is_empty() {
                let complexity = self.calculate_complexity(&function_name, content);
                
                functions.push(Function {
                    name: function_name,
                    parameters,
                    return_type: None, // Could be extracted from type annotations
                    is_async: content.contains("async"),
                    is_exported: false, // Could be determined from export analysis
                    docstring: None, // Could be extracted from comments
                    start_line,
                    end_line,
                    complexity,
                });
            }
        }

        Ok(functions)
    }

    pub async fn extract_classes(&self, content: &str, language: &str) -> Result<Vec<Class>, anyhow::Error> {
        let parser = self.parsers.get(language)
            .ok_or_else(|| anyhow::anyhow!("Parser not available for language: {}", language))?;

        let tree = parser.parse(content, None)
            .ok_or_else(|| anyhow::anyhow!("Failed to parse content"))?;

        let queries = self.queries.get(language)
            .ok_or_else(|| anyhow::anyhow!("Queries not available for language: {}", language))?;

        let class_query = queries.get("classes")
            .or_else(|| queries.get("structs"))
            .ok_or_else(|| anyhow::anyhow!("Class query not available for language: {}", language))?;

        let mut classes = Vec::new();
        let mut cursor = QueryCursor::new();
        let matches = cursor.matches(class_query, tree.root_node(), content.as_bytes());

        for match_ in matches {
            let mut class_name = String::new();
            let mut superclass = None;
            let mut start_line = 0;
            let mut end_line = 0;

            for capture in match_.captures {
                let node = capture.node;
                let capture_name = class_query.capture_names()[capture.index as usize];
                let text = node.utf8_text(content.as_bytes()).unwrap_or("").to_string();

                match capture_name {
                    "class_name" | "struct_name" => {
                        class_name = text;
                        start_line = node.start_position().row + 1;
                    }
                    "superclass" => {
                        superclass = Some(text);
                    }
                    "class_body" | "fields" => {
                        end_line = node.end_position().row + 1;
                    }
                    _ => {}
                }
            }

            if !class_name.is_empty() {
                classes.push(Class {
                    name: class_name,
                    superclass,
                    interfaces: Vec::new(), // Could be extracted from implements clauses
                    methods: Vec::new(), // Could be extracted from method analysis
                    properties: Vec::new(), // Could be extracted from property analysis
                    is_exported: false, // Could be determined from export analysis
                    docstring: None, // Could be extracted from comments
                    start_line,
                    end_line,
                });
            }
        }

        Ok(classes)
    }

    pub async fn extract_dependencies(&self, content: &str, language: &str) -> Result<Vec<String>, anyhow::Error> {
        let imports = self.extract_imports(content, language).await?;
        let dependencies: Vec<String> = imports.into_iter()
            .map(|import| import.module)
            .collect::<HashSet<String>>()
            .into_iter()
            .collect();

        Ok(dependencies)
    }

    fn extract_parameters(&self, node: &Node, content: &str, _language: &str) -> Vec<Parameter> {
        let mut parameters = Vec::new();
        let mut cursor = node.walk();

        if cursor.goto_first_child() {
            loop {
                let child = cursor.node();
                if child.kind() == "identifier" || child.kind() == "parameter" {
                    if let Ok(param_text) = child.utf8_text(content.as_bytes()) {
                        parameters.push(Parameter {
                            name: param_text.to_string(),
                            param_type: None,
                            default_value: None,
                            is_optional: false,
                        });
                    }
                }

                if !cursor.goto_next_sibling() {
                    break;
                }
            }
        }

        parameters
    }

    fn calculate_complexity(&self, _function_name: &str, content: &str) -> u32 {
        // Simple cyclomatic complexity calculation
        let mut complexity = 1; // Base complexity
        
        // Count decision points
        complexity += content.matches("if").count() as u32;
        complexity += content.matches("else").count() as u32;
        complexity += content.matches("while").count() as u32;
        complexity += content.matches("for").count() as u32;
        complexity += content.matches("switch").count() as u32;
        complexity += content.matches("case").count() as u32;
        complexity += content.matches("catch").count() as u32;
        complexity += content.matches("&&").count() as u32;
        complexity += content.matches("||").count() as u32;

        complexity
    }

    pub async fn analyze_code_quality(&self, content: &str, language: &str) -> Result<CodeQualityReport, anyhow::Error> {
        let functions = self.extract_functions(content, language).await?;
        let classes = self.extract_classes(content, language).await?;
        
        let mut report = CodeQualityReport {
            total_lines: content.lines().count(),
            code_lines: content.lines().filter(|line| !line.trim().is_empty() && !line.trim().starts_with("//")).count(),
            comment_lines: content.lines().filter(|line| line.trim().starts_with("//")).count(),
            function_count: functions.len(),
            class_count: classes.len(),
            average_function_complexity: 0.0,
            max_function_complexity: 0,
            maintainability_index: 0.0,
            issues: Vec::new(),
        };

        if !functions.is_empty() {
            let total_complexity: u32 = functions.iter().map(|f| f.complexity).sum();
            report.average_function_complexity = total_complexity as f64 / functions.len() as f64;
            report.max_function_complexity = functions.iter().map(|f| f.complexity).max().unwrap_or(0);
        }

        // Calculate maintainability index (simplified version)
        let halstead_volume = self.calculate_halstead_volume(content);
        let cyclomatic_complexity = report.average_function_complexity;
        let lines_of_code = report.code_lines as f64;

        report.maintainability_index = 171.0 
            - 5.2 * halstead_volume.ln() 
            - 0.23 * cyclomatic_complexity 
            - 16.2 * lines_of_code.ln();

        // Add quality issues
        if report.average_function_complexity > 10.0 {
            report.issues.push("High cyclomatic complexity detected".to_string());
        }
        
        if report.maintainability_index < 85.0 {
            report.issues.push("Low maintainability index".to_string());
        }

        Ok(report)
    }

    fn calculate_halstead_volume(&self, content: &str) -> f64 {
        // Simplified Halstead volume calculation
        let operators = ["=", "+", "-", "*", "/", "==", "!=", "<", ">", "&&", "||"];
        let mut operator_count = 0;
        let mut operand_count = 0;

        for operator in &operators {
            operator_count += content.matches(operator).count();
        }

        // Count words as operands (simplified)
        operand_count = content.split_whitespace().count();

        let vocabulary = operators.len() + operand_count;
        let length = operator_count + operand_count;

        if vocabulary == 0 {
            return 1.0;
        }

        length as f64 * (vocabulary as f64).log2()
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeQualityReport {
    pub total_lines: usize,
    pub code_lines: usize,
    pub comment_lines: usize,
    pub function_count: usize,
    pub class_count: usize,
    pub average_function_complexity: f64,
    pub max_function_complexity: u32,
    pub maintainability_index: f64,
    pub issues: Vec<String>,
}