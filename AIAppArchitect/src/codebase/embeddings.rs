use std::collections::HashMap;
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use fastembed::{EmbeddingModel, InitOptions, TextEmbedding};

#[derive(Debug, <PERSON><PERSON>)]
pub struct EmbeddingsEngine {
    model: Arc<TextEmbedding>,
    embedding_cache: Arc<dashmap::DashMap<String, Vec<f32>>>,
    similarity_threshold: f32,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct EmbeddingMetadata {
    pub model_name: String,
    pub dimension: usize,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub text_length: usize,
    pub hash: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SimilarityResult {
    pub text: String,
    pub similarity_score: f32,
    pub metadata: EmbeddingMetadata,
    pub file_path: Option<String>,
    pub line_number: Option<usize>,
}

impl EmbeddingsEngine {
    pub async fn new() -> Result<Self, anyhow::Error> {
        let model = TextEmbedding::try_new(InitOptions {
            model_name: EmbeddingModel::AllMiniLML6V2,
            show_download_progress: false,
            ..Default::default()
        })?;

        Ok(Self {
            model: Arc::new(model),
            embedding_cache: Arc::new(dashmap::DashMap::new()),
            similarity_threshold: 0.7,
        })
    }

    pub async fn generate_embeddings(&self, text: &str) -> Result<Vec<f32>, anyhow::Error> {
        let text_hash = sha256::digest(text);
        
        // Check cache first
        if let Some(cached_embedding) = self.embedding_cache.get(&text_hash) {
            return Ok(cached_embedding.clone());
        }

        // Generate new embedding
        let embeddings = self.model.embed(vec![text], None)?;
        let embedding = embeddings.into_iter().next()
            .ok_or_else(|| anyhow::anyhow!("Failed to generate embedding"))?;

        // Cache the result
        self.embedding_cache.insert(text_hash, embedding.clone());

        Ok(embedding)
    }

    pub async fn find_similar_texts(&self, query: &str, corpus: &[String]) -> Result<Vec<SimilarityResult>, anyhow::Error> {
        let query_embedding = self.generate_embeddings(query).await?;
        let mut results = Vec::new();

        for (index, text) in corpus.iter().enumerate() {
            let text_embedding = self.generate_embeddings(text).await?;
            let similarity = self.cosine_similarity(&query_embedding, &text_embedding);

            if similarity >= self.similarity_threshold {
                results.push(SimilarityResult {
                    text: text.clone(),
                    similarity_score: similarity,
                    metadata: EmbeddingMetadata {
                        model_name: "all-MiniLM-L6-v2".to_string(),
                        dimension: query_embedding.len(),
                        created_at: chrono::Utc::now(),
                        text_length: text.len(),
                        hash: sha256::digest(text),
                    },
                    file_path: None,
                    line_number: Some(index),
                });
            }
        }

        // Sort by similarity score descending
        results.sort_by(|a, b| b.similarity_score.partial_cmp(&a.similarity_score).unwrap_or(std::cmp::Ordering::Equal));

        Ok(results)
    }

    pub async fn semantic_search_codebase(&self, query: &str, code_snippets: &[(String, String, usize)]) -> Result<Vec<SimilarityResult>, anyhow::Error> {
        let query_embedding = self.generate_embeddings(query).await?;
        let mut results = Vec::new();

        for (file_path, code, line_number) in code_snippets {
            let code_embedding = self.generate_embeddings(code).await?;
            let similarity = self.cosine_similarity(&query_embedding, &code_embedding);

            if similarity >= self.similarity_threshold {
                results.push(SimilarityResult {
                    text: code.clone(),
                    similarity_score: similarity,
                    metadata: EmbeddingMetadata {
                        model_name: "all-MiniLM-L6-v2".to_string(),
                        dimension: query_embedding.len(),
                        created_at: chrono::Utc::now(),
                        text_length: code.len(),
                        hash: sha256::digest(code),
                    },
                    file_path: Some(file_path.clone()),
                    line_number: Some(*line_number),
                });
            }
        }

        results.sort_by(|a, b| b.similarity_score.partial_cmp(&a.similarity_score).unwrap_or(std::cmp::Ordering::Equal));

        Ok(results)
    }

    pub async fn cluster_similar_code(&self, code_snippets: &[String]) -> Result<Vec<Vec<usize>>, anyhow::Error> {
        let mut embeddings = Vec::new();
        
        for snippet in code_snippets {
            let embedding = self.generate_embeddings(snippet).await?;
            embeddings.push(embedding);
        }

        // Simple clustering algorithm based on similarity threshold
        let mut clusters = Vec::new();
        let mut assigned = vec![false; embeddings.len()];

        for i in 0..embeddings.len() {
            if assigned[i] {
                continue;
            }

            let mut cluster = vec![i];
            assigned[i] = true;

            for j in (i + 1)..embeddings.len() {
                if assigned[j] {
                    continue;
                }

                let similarity = self.cosine_similarity(&embeddings[i], &embeddings[j]);
                if similarity >= self.similarity_threshold {
                    cluster.push(j);
                    assigned[j] = true;
                }
            }

            clusters.push(cluster);
        }

        Ok(clusters)
    }

    fn cosine_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        if a.len() != b.len() {
            return 0.0;
        }

        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();

        if norm_a == 0.0 || norm_b == 0.0 {
            return 0.0;
        }

        dot_product / (norm_a * norm_b)
    }

    pub fn set_similarity_threshold(&mut self, threshold: f32) {
        self.similarity_threshold = threshold.clamp(0.0, 1.0);
    }

    pub async fn get_cache_stats(&self) -> HashMap<String, usize> {
        let mut stats = HashMap::new();
        stats.insert("cached_embeddings".to_string(), self.embedding_cache.len());
        stats.insert("cache_capacity".to_string(), self.embedding_cache.capacity());
        stats
    }

    pub async fn clear_cache(&self) {
        self.embedding_cache.clear();
    }
}