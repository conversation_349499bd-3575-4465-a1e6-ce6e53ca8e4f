use std::collections::{HashMap, HashSet};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use tree_sitter::{Language, Parser, Query, QueryCursor, Tree};
use walkdir::WalkDir;
use ignore::gitignore::{Gitignore, GitignoreBuilder};
use git2::Repository;

pub mod indexer;
pub mod analyzer;
pub mod embeddings;
pub mod context_retrieval;

pub use indexer::*;
pub use analyzer::*;
pub use embeddings::*;
pub use context_retrieval::*;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodebaseFile {
    pub path: PathBuf,
    pub content: String,
    pub language: String,
    pub size: u64,
    pub last_modified: chrono::DateTime<chrono::Utc>,
    pub hash: String,
    pub ast_nodes: Vec<AstNode>,
    pub imports: Vec<Import>,
    pub exports: Vec<Export>,
    pub functions: Vec<Function>,
    pub classes: Vec<Class>,
    pub dependencies: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AstNode {
    pub node_type: String,
    pub start_byte: usize,
    pub end_byte: usize,
    pub start_position: Position,
    pub end_position: Position,
    pub text: String,
    pub children: Vec<AstNode>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub row: usize,
    pub column: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Import {
    pub module: String,
    pub imports: Vec<String>,
    pub import_type: ImportType,
    pub line: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ImportType {
    Default,
    Named,
    Namespace,
    Dynamic,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Export {
    pub name: String,
    pub export_type: ExportType,
    pub line: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExportType {
    Default,
    Named,
    Class,
    Function,
    Variable,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Function {
    pub name: String,
    pub parameters: Vec<Parameter>,
    pub return_type: Option<String>,
    pub is_async: bool,
    pub is_exported: bool,
    pub docstring: Option<String>,
    pub start_line: usize,
    pub end_line: usize,
    pub complexity: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Parameter {
    pub name: String,
    pub param_type: Option<String>,
    pub default_value: Option<String>,
    pub is_optional: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Class {
    pub name: String,
    pub superclass: Option<String>,
    pub interfaces: Vec<String>,
    pub methods: Vec<Function>,
    pub properties: Vec<Property>,
    pub is_exported: bool,
    pub docstring: Option<String>,
    pub start_line: usize,
    pub end_line: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Property {
    pub name: String,
    pub property_type: Option<String>,
    pub visibility: Visibility,
    pub is_static: bool,
    pub line: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Visibility {
    Public,
    Private,
    Protected,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodebaseIndex {
    pub files: HashMap<PathBuf, CodebaseFile>,
    pub symbols: HashMap<String, Vec<SymbolLocation>>,
    pub dependencies: HashMap<String, HashSet<String>>,
    pub embeddings: HashMap<String, Vec<f32>>,
    pub last_updated: chrono::DateTime<chrono::Utc>,
    pub total_files: usize,
    pub total_lines: usize,
    pub languages: HashMap<String, usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SymbolLocation {
    pub file: PathBuf,
    pub line: usize,
    pub column: usize,
    pub symbol_type: SymbolType,
    pub context: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SymbolType {
    Function,
    Class,
    Variable,
    Import,
    Export,
    Type,
    Interface,
}

pub struct CodebaseIndexer {
    workspace_path: PathBuf,
    index: Arc<RwLock<CodebaseIndex>>,
    parsers: HashMap<String, Parser>,
    languages: HashMap<String, Language>,
    gitignore: Option<Gitignore>,
    embeddings_engine: Arc<EmbeddingsEngine>,
    analyzer: Arc<CodeAnalyzer>,
}

impl CodebaseIndexer {
    pub async fn new(workspace_path: &str) -> Result<Self, anyhow::Error> {
        let workspace_path = PathBuf::from(workspace_path);
        
        // Initialize parsers for different languages
        let mut parsers = HashMap::new();
        let mut languages = HashMap::new();
        
        // Add language support
        let typescript = tree_sitter_typescript::language_typescript();
        let javascript = tree_sitter_javascript::language();
        let rust = tree_sitter_rust::language();
        let python = tree_sitter_python::language();
        
        languages.insert("typescript".to_string(), typescript);
        languages.insert("javascript".to_string(), javascript);
        languages.insert("rust".to_string(), rust);
        languages.insert("python".to_string(), python);
        
        for (name, language) in &languages {
            let mut parser = Parser::new();
            parser.set_language(language.clone())?;
            parsers.insert(name.clone(), parser);
        }
        
        // Load gitignore
        let gitignore = Self::load_gitignore(&workspace_path)?;
        
        let index = Arc::new(RwLock::new(CodebaseIndex {
            files: HashMap::new(),
            symbols: HashMap::new(),
            dependencies: HashMap::new(),
            embeddings: HashMap::new(),
            last_updated: chrono::Utc::now(),
            total_files: 0,
            total_lines: 0,
            languages: HashMap::new(),
        }));
        
        let embeddings_engine = Arc::new(EmbeddingsEngine::new().await?);
        let analyzer = Arc::new(CodeAnalyzer::new());
        
        Ok(Self {
            workspace_path,
            index,
            parsers,
            languages,
            gitignore,
            embeddings_engine,
            analyzer,
        })
    }

    pub async fn start_indexing(&mut self) -> Result<(), anyhow::Error> {
        tracing::info!("Starting codebase indexing for {:?}", self.workspace_path);
        
        let files = self.discover_files().await?;
        let total_files = files.len();
        
        tracing::info!("Found {} files to index", total_files);
        
        // Process files in parallel batches
        let batch_size = 10;
        for chunk in files.chunks(batch_size) {
            let mut tasks = Vec::new();
            
            for file_path in chunk {
                let file_path = file_path.clone();
                let analyzer = self.analyzer.clone();
                let embeddings_engine = self.embeddings_engine.clone();
                
                tasks.push(tokio::spawn(async move {
                    Self::process_file(file_path, analyzer, embeddings_engine).await
                }));
            }
            
            // Wait for batch completion
            for task in tasks {
                match task.await? {
                    Ok(codebase_file) => {
                        self.add_file_to_index(codebase_file).await?;
                    }
                    Err(e) => {
                        tracing::warn!("Failed to process file: {}", e);
                    }
                }
            }
        }
        
        // Update index metadata
        let mut index = self.index.write().await;
        index.last_updated = chrono::Utc::now();
        index.total_files = total_files;
        
        tracing::info!("Codebase indexing completed");
        Ok(())
    }

    async fn discover_files(&self) -> Result<Vec<PathBuf>, anyhow::Error> {
        let mut files = Vec::new();
        
        for entry in WalkDir::new(&self.workspace_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            let path = entry.path();
            
            if path.is_file() && self.should_index_file(path) {
                files.push(path.to_path_buf());
            }
        }
        
        Ok(files)
    }

    fn should_index_file(&self, path: &Path) -> bool {
        // Check gitignore
        if let Some(gitignore) = &self.gitignore {
            if gitignore.matched(path, path.is_dir()).is_ignore() {
                return false;
            }
        }
        
        // Check file extension
        if let Some(ext) = path.extension().and_then(|s| s.to_str()) {
            matches!(ext, "ts" | "tsx" | "js" | "jsx" | "rs" | "py" | "json" | "md" | "toml" | "yaml" | "yml")
        } else {
            false
        }
    }

    fn load_gitignore(workspace_path: &Path) -> Result<Option<Gitignore>, anyhow::Error> {
        let gitignore_path = workspace_path.join(".gitignore");
        
        if gitignore_path.exists() {
            let mut builder = GitignoreBuilder::new(workspace_path);
            builder.add(&gitignore_path);
            Ok(Some(builder.build()?))
        } else {
            Ok(None)
        }
    }

    async fn process_file(
        file_path: PathBuf,
        analyzer: Arc<CodeAnalyzer>,
        embeddings_engine: Arc<EmbeddingsEngine>,
    ) -> Result<CodebaseFile, anyhow::Error> {
        let content = tokio::fs::read_to_string(&file_path).await?;
        let metadata = tokio::fs::metadata(&file_path).await?;
        
        let language = Self::detect_language(&file_path);
        let hash = sha256::digest(&content);
        
        // Parse AST and extract symbols
        let ast_nodes = analyzer.parse_ast(&content, &language).await?;
        let imports = analyzer.extract_imports(&content, &language).await?;
        let exports = analyzer.extract_exports(&content, &language).await?;
        let functions = analyzer.extract_functions(&content, &language).await?;
        let classes = analyzer.extract_classes(&content, &language).await?;
        let dependencies = analyzer.extract_dependencies(&content, &language).await?;
        
        // Generate embeddings for semantic search
        let _embeddings = embeddings_engine.generate_embeddings(&content).await?;
        
        Ok(CodebaseFile {
            path: file_path,
            content,
            language,
            size: metadata.len(),
            last_modified: metadata.modified()?
                .duration_since(std::time::UNIX_EPOCH)?
                .as_secs()
                .try_into()
                .map(chrono::DateTime::from_timestamp)
                .unwrap_or(None)
                .unwrap_or_else(chrono::Utc::now),
            hash,
            ast_nodes,
            imports,
            exports,
            functions,
            classes,
            dependencies,
        })
    }

    fn detect_language(file_path: &Path) -> String {
        if let Some(ext) = file_path.extension().and_then(|s| s.to_str()) {
            match ext {
                "ts" | "tsx" => "typescript".to_string(),
                "js" | "jsx" => "javascript".to_string(),
                "rs" => "rust".to_string(),
                "py" => "python".to_string(),
                _ => "text".to_string(),
            }
        } else {
            "text".to_string()
        }
    }

    async fn add_file_to_index(&self, file: CodebaseFile) -> Result<(), anyhow::Error> {
        let mut index = self.index.write().await;
        
        // Update language statistics
        *index.languages.entry(file.language.clone()).or_insert(0) += 1;
        
        // Add symbols to index
        for function in &file.functions {
            let symbol_location = SymbolLocation {
                file: file.path.clone(),
                line: function.start_line,
                column: 0,
                symbol_type: SymbolType::Function,
                context: function.name.clone(),
            };
            
            index.symbols
                .entry(function.name.clone())
                .or_insert_with(Vec::new)
                .push(symbol_location);
        }
        
        for class in &file.classes {
            let symbol_location = SymbolLocation {
                file: file.path.clone(),
                line: class.start_line,
                column: 0,
                symbol_type: SymbolType::Class,
                context: class.name.clone(),
            };
            
            index.symbols
                .entry(class.name.clone())
                .or_insert_with(Vec::new)
                .push(symbol_location);
        }
        
        // Update dependencies graph
        for dep in &file.dependencies {
            index.dependencies
                .entry(file.path.to_string_lossy().to_string())
                .or_insert_with(HashSet::new)
                .insert(dep.clone());
        }
        
        // Add file to index
        index.files.insert(file.path.clone(), file);
        
        Ok(())
    }

    pub async fn analyze_paths(&self, paths: Vec<String>) -> Result<serde_json::Value, anyhow::Error> {
        let index = self.index.read().await;
        let mut analysis = Vec::new();
        
        for path_str in paths {
            let path = PathBuf::from(&path_str);
            
            if let Some(file) = index.files.get(&path) {
                analysis.push(serde_json::json!({
                    "path": path_str,
                    "language": file.language,
                    "size": file.size,
                    "functions": file.functions.len(),
                    "classes": file.classes.len(),
                    "imports": file.imports.len(),
                    "exports": file.exports.len(),
                    "dependencies": file.dependencies,
                    "last_modified": file.last_modified
                }));
            } else {
                // Analyze directory
                let dir_analysis = self.analyze_directory(&path, &index).await?;
                analysis.push(dir_analysis);
            }
        }
        
        Ok(serde_json::json!({
            "analysis": analysis,
            "timestamp": chrono::Utc::now()
        }))
    }

    async fn analyze_directory(&self, dir_path: &Path, index: &CodebaseIndex) -> Result<serde_json::Value, anyhow::Error> {
        let mut files_in_dir = Vec::new();
        let mut total_size = 0u64;
        let mut language_counts = HashMap::new();
        
        for (file_path, file) in &index.files {
            if file_path.starts_with(dir_path) {
                files_in_dir.push(file_path.clone());
                total_size += file.size;
                *language_counts.entry(file.language.clone()).or_insert(0) += 1;
            }
        }
        
        Ok(serde_json::json!({
            "path": dir_path,
            "type": "directory",
            "files_count": files_in_dir.len(),
            "total_size": total_size,
            "languages": language_counts,
            "files": files_in_dir.iter().map(|p| p.to_string_lossy()).collect::<Vec<_>>()
        }))
    }

    pub async fn find_symbol(&self, symbol_name: &str) -> Result<Vec<SymbolLocation>, anyhow::Error> {
        let index = self.index.read().await;
        Ok(index.symbols.get(symbol_name).cloned().unwrap_or_default())
    }

    pub async fn get_file_dependencies(&self, file_path: &str) -> Result<HashSet<String>, anyhow::Error> {
        let index = self.index.read().await;
        Ok(index.dependencies.get(file_path).cloned().unwrap_or_default())
    }

    pub async fn search_code(&self, query: &str) -> Result<Vec<SearchResult>, anyhow::Error> {
        let index = self.index.read().await;
        let mut results = Vec::new();
        
        // Simple text search for now - could be enhanced with semantic search
        for (file_path, file) in &index.files {
            if file.content.contains(query) {
                let lines: Vec<&str> = file.content.lines().collect();
                for (line_num, line) in lines.iter().enumerate() {
                    if line.contains(query) {
                        results.push(SearchResult {
                            file: file_path.clone(),
                            line: line_num + 1,
                            column: line.find(query).unwrap_or(0),
                            context: line.to_string(),
                            relevance_score: 1.0,
                        });
                    }
                }
            }
        }
        
        Ok(results)
    }

    pub async fn get_context_for_files(&self, file_paths: &[String]) -> Result<serde_json::Value, anyhow::Error> {
        let index = self.index.read().await;
        let mut context = Vec::new();
        
        for file_path_str in file_paths {
            let file_path = PathBuf::from(file_path_str);
            
            if let Some(file) = index.files.get(&file_path) {
                context.push(serde_json::json!({
                    "file": file_path_str,
                    "content": file.content,
                    "functions": file.functions,
                    "classes": file.classes,
                    "imports": file.imports,
                    "exports": file.exports
                }));
            }
        }
        
        Ok(serde_json::json!({
            "context": context,
            "related_files": self.find_related_files(file_paths, &index).await?
        }))
    }

    async fn find_related_files(&self, file_paths: &[String], index: &CodebaseIndex) -> Result<Vec<String>, anyhow::Error> {
        let mut related = HashSet::new();
        
        for file_path in file_paths {
            // Find files that import or are imported by this file
            if let Some(deps) = index.dependencies.get(file_path) {
                related.extend(deps.clone());
            }
            
            // Find files that depend on this file
            for (dep_file, deps) in &index.dependencies {
                if deps.contains(file_path) {
                    related.insert(dep_file.clone());
                }
            }
        }
        
        Ok(related.into_iter().collect())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub file: PathBuf,
    pub line: usize,
    pub column: usize,
    pub context: String,
    pub relevance_score: f64,
}