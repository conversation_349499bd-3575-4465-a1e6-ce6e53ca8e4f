import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { AizenIntegrationService } from '../services/AizenIntegrationService';

export class AizenChatViewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'aizen.chatView';

    private _view?: vscode.WebviewView;
    private disposables: vscode.Disposable[] = [];

    constructor(
        private readonly extensionUri: vscode.Uri,
        private readonly integrationService: AizenIntegrationService
    ) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                vscode.Uri.file(path.join(this.extensionUri.fsPath, 'out')),
                vscode.Uri.file(path.join(this.extensionUri.fsPath, 'src')),
                vscode.Uri.file(path.join(this.extensionUri.fsPath, 'media')),
                vscode.Uri.file(path.join(this.extensionUri.fsPath, 'node_modules'))
            ]
        };

        webviewView.webview.html = this.getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(
            async (message) => {
                await this.handleWebviewMessage(message);
            },
            null,
            this.disposables
        );

        // Send initial data when webview becomes visible
        webviewView.onDidChangeVisibility(() => {
            if (webviewView.visible) {
                this.sendInitialData();
            }
        });

        // Send initial data immediately if visible
        if (webviewView.visible) {
            this.sendInitialData();
        }
    }

    private getHtmlForWebview(webview: vscode.Webview): string {
        // Get file URIs
        const scriptPathOnDisk = vscode.Uri.file(path.join(this.extensionUri.fsPath, 'out', 'ui', 'main.js'));
        const scriptUri = webview.asWebviewUri(scriptPathOnDisk);

        const stylesPathOnDisk = vscode.Uri.file(path.join(this.extensionUri.fsPath, 'out', 'ui', 'styles.css'));
        const stylesUri = webview.asWebviewUri(stylesPathOnDisk);

        const chatStylesPathOnDisk = vscode.Uri.file(path.join(this.extensionUri.fsPath, 'out', 'ui', 'chat.css'));
        const chatStylesUri = webview.asWebviewUri(chatStylesPathOnDisk);

        const htmlPathOnDisk = vscode.Uri.file(path.join(this.extensionUri.fsPath, 'out', 'ui', 'index.html'));

        const nonce = this.getNonce();

        // Get current VS Code theme
        const currentTheme = vscode.window.activeColorTheme.kind;
        const themeClass = currentTheme === vscode.ColorThemeKind.Light ? 'vscode-light' :
                          currentTheme === vscode.ColorThemeKind.HighContrast ? 'vscode-high-contrast' :
                          'vscode-dark';

        // Read HTML template
        try {
            console.log('🔍 Reading HTML template from:', htmlPathOnDisk.fsPath);
            console.log('🔍 File exists:', fs.existsSync(htmlPathOnDisk.fsPath));

            const htmlContent = fs.readFileSync(htmlPathOnDisk.fsPath, 'utf8');
            console.log('✅ HTML template loaded successfully');

            // Replace placeholders
            const processedHtml = htmlContent
                .replace(/{{CSS_URI}}/g, stylesUri.toString())
                .replace(/{{CHAT_CSS_URI}}/g, chatStylesUri.toString())
                .replace(/{{SCRIPT_URI}}/g, scriptUri.toString())
                .replace(/{{THEME_CLASS}}/g, themeClass)
                .replace(/{{NONCE}}/g, nonce)
                .replace(/{{CSP_SOURCE}}/g, webview.cspSource);

            console.log('✅ HTML placeholders replaced');
            return processedHtml;

        } catch (error) {
            console.error('❌ Failed to read HTML template:', error);
            console.log('🔄 Using fallback HTML');
            // Fallback to inline HTML
            return this.getFallbackHtml(webview, scriptUri, stylesUri, chatStylesUri, themeClass, nonce);
        }
    }

    private getFallbackHtml(webview: vscode.Webview, scriptUri: vscode.Uri, stylesUri: vscode.Uri, chatStylesUri: vscode.Uri, themeClass: string, nonce: string): string {
        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; script-src 'nonce-${nonce}'; font-src ${webview.cspSource} data: https://fonts.gstatic.com; img-src ${webview.cspSource} data: https:; connect-src https:;">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Aizen AI Chat</title>
                <link rel="preconnect" href="https://fonts.googleapis.com">
                <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@hugeicons/icons@latest/css/hugeicons.css">
                <link rel="stylesheet" href="${stylesUri}">
                <link rel="stylesheet" href="${chatStylesUri}">
            </head>
            <body class="${themeClass}">
                <div id="root">
                    <div class="loading-container">
                        <div class="loading-spinner"></div>
                        <p>Loading Aizen AI...</p>
                    </div>
                </div>
                <script nonce="${nonce}" src="${scriptUri}"></script>
                <style>
                    .loading-container {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        height: 100vh;
                        gap: 1rem;
                    }
                    .loading-spinner {
                        width: 40px;
                        height: 40px;
                        border: 3px solid rgba(255, 255, 255, 0.1);
                        border-top: 3px solid #4fc3f7;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            </body>
            </html>`;
    }

    private getNonce(): string {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }

    private async sendInitialData() {
        if (!this._view) return;

        try {
            // Get initial data from integration service
            const agents = await this.integrationService.getAllAgents();
            const metrics = await this.integrationService.getMetrics();

            await this._view.webview.postMessage({
                command: 'updateData',
                data: {
                    agents,
                    metrics,
                    capabilities: {
                        swarmIntelligence: true,
                        evolution: true,
                        taskWeaver: true,
                        langgraph: true,
                        pydanticAI: true,
                        ag2: true
                    }
                }
            });

        } catch (error) {
            console.error('Failed to send initial data:', error);
            await this._view.webview.postMessage({
                command: 'error',
                message: 'Failed to load data'
            });
        }
    }

    private async handleWebviewMessage(message: any) {
        console.log('📨 Received webview message:', message);

        switch (message.command) {
            case 'test':
                console.log('🧪 Test message received:', message.message);
                vscode.window.showInformationMessage(`Test successful: ${message.message}`);
                break;

            case 'getInitialData':
                await this.sendInitialData();
                break;

            case 'sendMessage':
                await this.handleChatMessage(message.data.message);
                break;

            case 'sendChatMessage':
                await this.handleChatMessage(message.message);
                break;

            case 'createAgent':
                await this.handleCreateAgent(message.agentType);
                break;

            case 'enableSwarmIntelligence':
                await this.handleEnableSwarmIntelligence();
                break;

            case 'enableEvolution':
                await this.handleEnableEvolution();
                break;

            case 'executeTask':
                await this.handleExecuteTask(message.description, message.taskType);
                break;

            case 'tabChanged':
                await this.handleTabChange(message.data.tab);
                break;

            case 'modeChanged':
                await this.handleModeChange(message.data.mode);
                break;

            case 'agentModeChanged':
                await this.handleAgentModeChange(message.data.mode);
                break;

            case 'autoModeChanged':
                await this.handleAutoModeChange(message.data.autoMode);
                break;

            case 'newChat':
                await this.handleNewChat();
                break;

            case 'showHistory':
                await this.handleShowHistory();
                break;

            case 'openMCPStore':
                await this.handleOpenMCPStore();
                break;

            case 'toggleAdvancedMode':
                await this.handleToggleAdvancedMode();
                break;

            case 'navAction':
                await this.handleNavAction(message.data.action);
                break;

            default:
                console.warn('Unknown webview message:', message);
        }
    }

    private async handleChatMessage(message: string) {
        if (!this._view) return;

        try {
            // Show user message immediately
            await this._view.webview.postMessage({
                command: 'chatMessage',
                data: {
                    type: 'user',
                    message: message,
                    timestamp: new Date().toISOString()
                }
            });

            // Process with AI backend (mock response for now)
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';

            // Simulate AI processing
            setTimeout(async () => {
                await this._view?.webview.postMessage({
                    command: 'chatMessage',
                    data: {
                        type: 'assistant',
                        message: `I understand you want me to: "${message}". I'm processing this with the AI backend and will provide a detailed response shortly.`,
                        timestamp: new Date().toISOString()
                    }
                });
            }, 1000);

        } catch (error) {
            console.error('Failed to handle chat message:', error);
            await this._view.webview.postMessage({
                command: 'chatMessage',
                data: {
                    type: 'error',
                    message: `Error: ${error}`,
                    timestamp: new Date().toISOString()
                }
            });
        }
    }

    private async handleCreateAgent(agentType: string) {
        try {
            const agent = await this.integrationService.createAgent(agentType);
            await this.sendInitialData();

            vscode.window.showInformationMessage(`${agentType} agent created successfully`);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create agent: ${error}`);
        }
    }

    private async handleEnableSwarmIntelligence() {
        try {
            await this.integrationService.enableSwarmIntelligence();
            vscode.window.showInformationMessage('Swarm Intelligence enabled');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to enable swarm intelligence: ${error}`);
        }
    }

    private async handleEnableEvolution() {
        try {
            await this.integrationService.enableEvolution();
            vscode.window.showInformationMessage('Agent Evolution enabled');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to enable evolution: ${error}`);
        }
    }

    private async handleExecuteTask(description: string, taskType: string = 'code-generation') {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
            const task = await this.integrationService.executeTask(description, taskType, {
                workingDirectory: workspaceRoot
            });

            await this._view?.webview.postMessage({
                command: 'taskStarted',
                data: task
            });

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to execute task: ${error}`);
        }
    }

    private async handleTabChange(tab: string) {
        console.log(`Tab changed to: ${tab}`);
        // Handle tab switching logic here
    }

    private async handleModeChange(mode: string) {
        console.log(`Mode changed to: ${mode}`);
        // Handle mode switching logic here
    }

    private async handleAgentModeChange(mode: string) {
        console.log(`Agent mode changed to: ${mode}`);
        // Handle agent mode switching logic here
    }

    private async handleAutoModeChange(autoMode: boolean) {
        console.log(`Auto mode changed to: ${autoMode}`);
        // Handle auto mode toggle logic here
    }

    private async handleNewChat() {
        console.log('New chat started');
        // Handle new chat creation logic here
    }

    private async handleShowHistory() {
        console.log('Show history requested');
        // Handle showing query history logic here
    }

    private async handleOpenMCPStore() {
        console.log('MCP Store requested');
        // Handle opening MCP store logic here
    }

    private async handleToggleAdvancedMode() {
        console.log('Advanced mode toggled');
        // Handle advanced mode toggle logic here
    }

    private async handleNavAction(action: string) {
        console.log(`Navigation action: ${action}`);

        switch (action) {
            case 'settings':
                // Open settings webview
                vscode.commands.executeCommand('aizen.showSettings');
                break;

            default:
                console.log(`Unhandled navigation action: ${action}`);
        }
    }

    public reveal() {
        if (this._view) {
            this._view.show?.(true);
        }
    }

    public dispose() {
        this.disposables.forEach(d => d.dispose());
    }
}