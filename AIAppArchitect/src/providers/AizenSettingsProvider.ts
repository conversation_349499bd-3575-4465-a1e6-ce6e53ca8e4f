/**
 * Aizen AI Settings Provider
 * Handles settings webview panel creation and management
 */

import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { AizenSettingsService } from '../services/AizenSettingsService';
import { MCPHub } from '../mcp/hub';

export class AizenSettingsProvider {
    public static readonly viewType = 'aizenSettings';

    private _panel: vscode.WebviewPanel | undefined;
    private readonly _extensionUri: vscode.Uri;
    private readonly _context: vscode.ExtensionContext;
    private readonly _settingsService: AizenSettingsService;
    private _mcpHub: MCPHub | undefined;
    private _disposables: vscode.Disposable[] = [];

    constructor(extensionUri: vscode.Uri, context: vscode.ExtensionContext, mcpHub?: MCPHub) {
        this._extensionUri = extensionUri;
        this._context = context;
        this._settingsService = AizenSettingsService.getInstance();
        this._mcpHub = mcpHub;
    }

    public static createOrShow(extensionUri: vscode.Uri, context: vscode.ExtensionContext, mcpHub?: MCPHub) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        // If we already have a panel, show it
        if (AizenSettingsProvider._currentPanel) {
            AizenSettingsProvider._currentPanel._panel?.reveal(column);
            return;
        }

        // Otherwise, create a new panel
        const panel = vscode.window.createWebviewPanel(
            AizenSettingsProvider.viewType,
            'Aizen AI Settings',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.file(path.join(extensionUri.fsPath, 'out')),
                    vscode.Uri.file(path.join(extensionUri.fsPath, 'src')),
                    vscode.Uri.file(path.join(extensionUri.fsPath, 'media'))
                ]
            }
        );

        AizenSettingsProvider._currentPanel = new AizenSettingsProvider(extensionUri, context, mcpHub);
        AizenSettingsProvider._currentPanel._panel = panel;
        AizenSettingsProvider._currentPanel._update();
        AizenSettingsProvider._currentPanel._setupEventListeners();
    }

    private static _currentPanel: AizenSettingsProvider | undefined;

    public dispose() {
        AizenSettingsProvider._currentPanel = undefined;

        // Clean up our resources
        this._panel?.dispose();

        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }

    private _setupEventListeners() {
        if (!this._panel) return;

        // Handle panel disposal
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        // Handle messages from the webview
        this._panel.webview.onDidReceiveMessage(
            async (message) => {
                await this._handleMessage(message);
            },
            null,
            this._disposables
        );
    }

    private async _handleMessage(message: any) {
        switch (message.type) {
            case 'getSettings':
                await this._sendSettings();
                break;
            
            case 'saveSettings':
                await this._saveSettings(message.settings);
                break;
            
            case 'resetSettings':
                await this._resetSettings();
                break;
            
            case 'closeSettings':
                this._panel?.dispose();
                break;

            case 'getMcpStatus':
                await this._sendMcpStatus();
                break;

            case 'configureApiKey':
                await this._handleConfigureApiKey(message.serverType);
                break;

            case 'command':
                // Execute VS Code command
                if (message.command) {
                    try {
                        await vscode.commands.executeCommand(message.command);
                    } catch (error) {
                        console.error('Failed to execute command:', message.command, error);
                    }
                }
                break;

            default:
                console.log('Unknown settings message:', message.type);
        }
    }

    private async _sendSettings() {
        if (!this._panel) return;

        // Get current settings from VS Code configuration
        const config = vscode.workspace.getConfiguration('aizenAI');
        const globalState = this._context.globalState;

        const settings = {
            aiModel: {
                provider: config.get('aiModel.provider', 'openai'),
                name: config.get('aiModel.name', 'gpt-4'),
                temperature: config.get('aiModel.temperature', 0.7),
                maxTokens: config.get('aiModel.maxTokens', 4096)
            },
            mcpServers: globalState.get('mcpServers', []),
            theme: {
                mode: config.get('theme.mode', 'auto'),
                accentColor: config.get('theme.accentColor', '#3b82f6'),
                glassIntensity: config.get('theme.glassIntensity', 80)
            },
            advanced: {
                debugMode: config.get('advanced.debugMode', false),
                autoSave: config.get('advanced.autoSave', true),
                streamingMode: config.get('advanced.streamingMode', true)
            },
            extension: {
                defaultMode: config.get('extension.defaultMode', 'auto'),
                shortcutKey: config.get('extension.shortcutKey', 'Ctrl+Shift+A'),
                startupOpen: config.get('extension.startupOpen', false)
            }
        };

        await this._panel.webview.postMessage({
            type: 'settingsData',
            settings: settings
        });
    }

    private async _sendMcpStatus() {
        if (!this._panel) return;

        try {
            // Check if MCP Hub is available and get server status
            let exaStatus = 'unconfigured';
            let firecrawlStatus = 'unconfigured';

            if (this._mcpHub) {
                const servers = this._mcpHub.getServers();
                const connectedServers = this._mcpHub.getConnectedServers();
                const connectedServerIds = connectedServers.map(s => s.id);

                // Check Exa server
                const exaServer = servers.find(s => s.config.name.includes('Exa'));
                if (exaServer) {
                    exaStatus = connectedServerIds.includes(exaServer.id) ? 'connected' : 'configured';
                }

                // Check Firecrawl server
                const firecrawlServer = servers.find(s => s.config.name.includes('Firecrawl'));
                if (firecrawlServer) {
                    firecrawlStatus = connectedServerIds.includes(firecrawlServer.id) ? 'connected' : 'configured';
                }
            }

            await this._panel.webview.postMessage({
                type: 'mcpStatus',
                status: {
                    exa: {
                        configured: exaStatus !== 'unconfigured',
                        status: exaStatus
                    },
                    firecrawl: {
                        configured: firecrawlStatus !== 'unconfigured',
                        status: firecrawlStatus
                    }
                }
            });
        } catch (error) {
            console.error('Failed to get MCP status:', error);
            // Fallback to default status
            await this._panel.webview.postMessage({
                type: 'mcpStatus',
                status: {
                    exa: { configured: true, status: 'configured' },
                    firecrawl: { configured: true, status: 'configured' }
                }
            });
        }
    }

    private async _handleConfigureApiKey(serverType: string) {
        try {
            console.log('🔧 Configuring API key for:', serverType);

            if (serverType === 'exa') {
                // Exa is already configured with built-in API key
                vscode.window.showInformationMessage('✅ Exa AI server is already configured with built-in API key!');
                await this._sendMcpStatus(); // Refresh status
                return;
            }

            if (serverType === 'firecrawl') {
                // Firecrawl is already configured with built-in API key
                vscode.window.showInformationMessage('✅ Firecrawl server is already configured with built-in API key!');
                await this._sendMcpStatus(); // Refresh status
                return;
            }

            // For custom servers, show input dialog
            const apiKey = await vscode.window.showInputBox({
                prompt: `Enter API key for ${serverType}`,
                password: true,
                placeHolder: 'Enter your API key...'
            });

            if (apiKey) {
                // Handle custom server configuration here
                vscode.window.showInformationMessage(`✅ ${serverType} server configured successfully!`);
                await this._sendMcpStatus();
            }

        } catch (error) {
            console.error('Failed to configure API key:', error);
            vscode.window.showErrorMessage(`Failed to configure ${serverType}: ${error}`);
        }
    }

    private async _saveSettings(settings: any) {
        if (!this._panel) return;

        try {
            const config = vscode.workspace.getConfiguration('aizenAI');
            const globalState = this._context.globalState;

            // Save AI model settings
            if (settings.aiModel) {
                await config.update('aiModel.provider', settings.aiModel.provider, vscode.ConfigurationTarget.Global);
                await config.update('aiModel.name', settings.aiModel.name, vscode.ConfigurationTarget.Global);
                await config.update('aiModel.temperature', settings.aiModel.temperature, vscode.ConfigurationTarget.Global);
                await config.update('aiModel.maxTokens', settings.aiModel.maxTokens, vscode.ConfigurationTarget.Global);
            }

            // Save MCP servers to global state
            if (settings.mcpServers) {
                await globalState.update('mcpServers', settings.mcpServers);
            }

            // Save theme settings
            if (settings.theme) {
                await config.update('theme.mode', settings.theme.mode, vscode.ConfigurationTarget.Global);
                await config.update('theme.accentColor', settings.theme.accentColor, vscode.ConfigurationTarget.Global);
                await config.update('theme.glassIntensity', settings.theme.glassIntensity, vscode.ConfigurationTarget.Global);
            }

            // Save advanced settings
            if (settings.advanced) {
                await config.update('advanced.debugMode', settings.advanced.debugMode, vscode.ConfigurationTarget.Global);
                await config.update('advanced.autoSave', settings.advanced.autoSave, vscode.ConfigurationTarget.Global);
                await config.update('advanced.streamingMode', settings.advanced.streamingMode, vscode.ConfigurationTarget.Global);
            }

            // Save extension preferences
            if (settings.extension) {
                await config.update('extension.defaultMode', settings.extension.defaultMode, vscode.ConfigurationTarget.Global);
                await config.update('extension.shortcutKey', settings.extension.shortcutKey, vscode.ConfigurationTarget.Global);
                await config.update('extension.startupOpen', settings.extension.startupOpen, vscode.ConfigurationTarget.Global);
            }

            // Notify webview that settings were saved
            await this._panel.webview.postMessage({
                type: 'settingsSaved'
            });

            vscode.window.showInformationMessage('Aizen AI settings saved successfully!');

        } catch (error) {
            console.error('Failed to save settings:', error);
            vscode.window.showErrorMessage(`Failed to save settings: ${error}`);
        }
    }

    private async _resetSettings() {
        if (!this._panel) return;

        try {
            const config = vscode.workspace.getConfiguration('aizenAI');
            const globalState = this._context.globalState;

            // Reset all settings to defaults
            await config.update('aiModel.provider', undefined, vscode.ConfigurationTarget.Global);
            await config.update('aiModel.name', undefined, vscode.ConfigurationTarget.Global);
            await config.update('aiModel.temperature', undefined, vscode.ConfigurationTarget.Global);
            await config.update('aiModel.maxTokens', undefined, vscode.ConfigurationTarget.Global);

            await globalState.update('mcpServers', []);

            await config.update('theme.mode', undefined, vscode.ConfigurationTarget.Global);
            await config.update('theme.accentColor', undefined, vscode.ConfigurationTarget.Global);
            await config.update('theme.glassIntensity', undefined, vscode.ConfigurationTarget.Global);

            await config.update('advanced.debugMode', undefined, vscode.ConfigurationTarget.Global);
            await config.update('advanced.autoSave', undefined, vscode.ConfigurationTarget.Global);
            await config.update('advanced.streamingMode', undefined, vscode.ConfigurationTarget.Global);

            await config.update('extension.defaultMode', undefined, vscode.ConfigurationTarget.Global);
            await config.update('extension.shortcutKey', undefined, vscode.ConfigurationTarget.Global);
            await config.update('extension.startupOpen', undefined, vscode.ConfigurationTarget.Global);

            // Notify webview that settings were reset
            await this._panel.webview.postMessage({
                type: 'settingsReset'
            });

            vscode.window.showInformationMessage('Aizen AI settings reset to defaults!');

        } catch (error) {
            console.error('Failed to reset settings:', error);
            vscode.window.showErrorMessage(`Failed to reset settings: ${error}`);
        }
    }

    private _update() {
        if (!this._panel) return;

        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        // Get file URIs
        const settingsScriptPathOnDisk = vscode.Uri.file(path.join(this._extensionUri.fsPath, 'out', 'ui', 'settings.js'));
        const settingsScriptUri = webview.asWebviewUri(settingsScriptPathOnDisk);

        const stylesPathOnDisk = vscode.Uri.file(path.join(this._extensionUri.fsPath, 'out', 'ui', 'styles.css'));
        const stylesUri = webview.asWebviewUri(stylesPathOnDisk);

        const settingsStylesPathOnDisk = vscode.Uri.file(path.join(this._extensionUri.fsPath, 'out', 'ui', 'settings.css'));
        const settingsStylesUri = webview.asWebviewUri(settingsStylesPathOnDisk);

        const settingsHtmlPathOnDisk = vscode.Uri.file(path.join(this._extensionUri.fsPath, 'out', 'ui', 'settings.html'));

        const nonce = this._getNonce();

        // Get current VS Code theme
        const currentTheme = vscode.window.activeColorTheme.kind;
        const themeClass = currentTheme === vscode.ColorThemeKind.Dark ? 'dark-theme' : 'light-theme';

        // Read HTML template
        try {
            console.log('🔍 Reading Settings HTML template from:', settingsHtmlPathOnDisk.fsPath);
            console.log('🔍 File exists:', fs.existsSync(settingsHtmlPathOnDisk.fsPath));

            const htmlContent = fs.readFileSync(settingsHtmlPathOnDisk.fsPath, 'utf8');
            console.log('✅ Settings HTML template loaded successfully');

            // Replace placeholders
            const processedHtml = htmlContent
                .replace(/{{CSS_URI}}/g, stylesUri.toString())
                .replace(/{{SETTINGS_CSS_URI}}/g, settingsStylesUri.toString())
                .replace(/{{SETTINGS_SCRIPT_URI}}/g, settingsScriptUri.toString())
                .replace(/{{THEME_CLASS}}/g, themeClass)
                .replace(/{{NONCE}}/g, nonce)
                .replace(/{{CSP_SOURCE}}/g, webview.cspSource);

            console.log('✅ Settings HTML placeholders replaced');
            return processedHtml;

        } catch (error) {
            console.error('❌ Failed to load settings HTML template:', error);
            return this._getFallbackSettingsHtml(webview, settingsScriptUri, stylesUri, settingsStylesUri, themeClass, nonce);
        }
    }

    private _getFallbackSettingsHtml(
        webview: vscode.Webview,
        settingsScriptUri: vscode.Uri,
        stylesUri: vscode.Uri,
        settingsStylesUri: vscode.Uri,
        themeClass: string,
        nonce: string
    ): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline' https://fonts.googleapis.com; script-src 'nonce-${nonce}'; font-src ${webview.cspSource} data: https://fonts.gstatic.com; img-src ${webview.cspSource} data: https:;">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Aizen AI Settings</title>
            <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
            <link rel="stylesheet" href="${stylesUri}">
            <link rel="stylesheet" href="${settingsStylesUri}">
        </head>
        <body class="settings-body ${themeClass}">
            <div class="settings-container">
                <header class="settings-header glass">
                    <h1>Aizen AI Settings</h1>
                    <p>Settings page is loading...</p>
                </header>
                <main class="settings-main">
                    <div class="loading-message">
                        <p>Loading settings interface...</p>
                    </div>
                </main>
            </div>
            <script nonce="${nonce}" src="${settingsScriptUri}"></script>
        </body>
        </html>`;
    }

    private _getNonce(): string {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
}
