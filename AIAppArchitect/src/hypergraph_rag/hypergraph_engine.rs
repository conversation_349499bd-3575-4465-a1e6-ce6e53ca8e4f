// HyperGraph Engine - Core hypergraph operations and algorithms
// Advanced N-ary relational processing with semantic clustering

use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use anyhow::Result;
use uuid::Uuid;
use std::collections::{HashMap, HashSet, VecDeque};

use crate::hypergraph_rag::{HyperGraphConfig, HyperNode, HyperEdge, CodebaseHyperGraph, RelationType};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HyperGraphEngine {
    config: HyperGraphConfig,
    incidence_matrix: Arc<RwLock<IncidenceMatrix>>,
    semantic_clusters: Arc<RwLock<Vec<SemanticCluster>>>,
    graph_statistics: Arc<RwLock<GraphStatistics>>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct IncidenceMatrix {
    pub node_to_edges: HashMap<Uuid, HashSet<Uuid>>,
    pub edge_to_nodes: HashMap<Uuid, HashSet<Uuid>>,
    pub adjacency_cache: HashMap<(Uuid, Uuid), f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SemanticCluster {
    pub cluster_id: Uuid,
    pub centroid: Vec<f32>,
    pub nodes: HashSet<Uuid>,
    pub coherence_score: f64,
    pub cluster_type: ClusterType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ClusterType {
    Functional,
    Conceptual,
    Structural,
    Temporal,
    Semantic,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GraphStatistics {
    pub node_count: usize,
    pub edge_count: usize,
    pub max_edge_arity: usize,
    pub average_node_degree: f64,
    pub clustering_coefficient: f64,
    pub semantic_density: f64,
    pub connectivity_index: f64,
}

impl HyperGraphEngine {
    pub async fn new(config: &HyperGraphConfig) -> Result<Self> {
        let engine = Self {
            config: config.clone(),
            incidence_matrix: Arc::new(RwLock::new(IncidenceMatrix {
                node_to_edges: HashMap::new(),
                edge_to_nodes: HashMap::new(),
                adjacency_cache: HashMap::new(),
            })),
            semantic_clusters: Arc::new(RwLock::new(Vec::new())),
            graph_statistics: Arc::new(RwLock::new(GraphStatistics {
                node_count: 0,
                edge_count: 0,
                max_edge_arity: 0,
                average_node_degree: 0.0,
                clustering_coefficient: 0.0,
                semantic_density: 0.0,
                connectivity_index: 0.0,
            })),
        };

        Ok(engine)
    }

    pub async fn build_hypergraph(&self, nodes: Vec<HyperNode>, edges: Vec<HyperEdge>) -> Result<CodebaseHyperGraph> {
        // Build the hypergraph from nodes and edges
        let mut graph = CodebaseHyperGraph {
            nodes: HashMap::new(),
            hyperedges: HashMap::new(),
            node_index: HashMap::new(),
            relation_index: HashMap::new(),
            embedding_index: Vec::new(),
        };

        // Add nodes
        for node in nodes {
            self.add_node_to_graph(&mut graph, node).await?;
        }

        // Add hyperedges
        for edge in edges {
            self.add_edge_to_graph(&mut graph, edge).await?;
        }

        // Build indices
        self.build_indices(&mut graph).await?;

        // Update incidence matrix
        self.update_incidence_matrix(&graph).await?;

        // Perform semantic clustering
        if self.config.enable_semantic_clustering {
            self.perform_semantic_clustering(&graph).await?;
        }

        // Update statistics
        self.update_graph_statistics(&graph).await?;

        Ok(graph)
    }

    async fn add_node_to_graph(&self, graph: &mut CodebaseHyperGraph, node: HyperNode) -> Result<()> {
        // Add to embedding index
        graph.embedding_index.push((node.id, node.embedding.clone()));

        // Add to node index by content type
        let content_key = format!("{:?}", node.node_type);
        graph.node_index.entry(content_key).or_insert_with(Vec::new).push(node.id);

        // Add to main nodes collection
        graph.nodes.insert(node.id, node);

        Ok(())
    }

    async fn add_edge_to_graph(&self, graph: &mut CodebaseHyperGraph, edge: HyperEdge) -> Result<()> {
        // Add to relation index
        graph.relation_index.entry(edge.relation_type.clone()).or_insert_with(Vec::new).push(edge.id);

        // Add to main edges collection
        graph.hyperedges.insert(edge.id, edge);

        Ok(())
    }

    async fn build_indices(&self, graph: &mut CodebaseHyperGraph) -> Result<()> {
        // Build additional indices for fast lookup
        
        // Content-based index
        for (node_id, node) in &graph.nodes {
            let content_words: Vec<&str> = node.content.split_whitespace().collect();
            for word in content_words {
                if word.len() > 3 { // Skip short words
                    graph.node_index.entry(word.to_lowercase()).or_insert_with(Vec::new).push(*node_id);
                }
            }
        }

        // Metadata-based index
        for (node_id, node) in &graph.nodes {
            for (key, value) in &node.metadata {
                let index_key = format!("{}:{}", key, value);
                graph.node_index.entry(index_key).or_insert_with(Vec::new).push(*node_id);
            }
        }

        Ok(())
    }

    async fn update_incidence_matrix(&self, graph: &CodebaseHyperGraph) -> Result<()> {
        let mut matrix = self.incidence_matrix.write().await;
        
        // Clear existing matrix
        matrix.node_to_edges.clear();
        matrix.edge_to_nodes.clear();
        matrix.adjacency_cache.clear();

        // Build incidence relationships
        for (edge_id, edge) in &graph.hyperedges {
            let node_set: HashSet<Uuid> = edge.nodes.iter().cloned().collect();
            matrix.edge_to_nodes.insert(*edge_id, node_set.clone());

            // Update node-to-edges mapping
            for &node_id in &edge.nodes {
                matrix.node_to_edges.entry(node_id).or_insert_with(HashSet::new).insert(*edge_id);
            }

            // Build adjacency cache for node pairs in this hyperedge
            for i in 0..edge.nodes.len() {
                for j in (i + 1)..edge.nodes.len() {
                    let node1 = edge.nodes[i];
                    let node2 = edge.nodes[j];
                    let key = if node1 < node2 { (node1, node2) } else { (node2, node1) };
                    
                    // Weight based on edge weight and inverse of edge arity
                    let adjacency_weight = edge.weight / edge.nodes.len() as f64;
                    
                    *matrix.adjacency_cache.entry(key).or_insert(0.0) += adjacency_weight;
                }
            }
        }

        Ok(())
    }

    async fn perform_semantic_clustering(&self, graph: &CodebaseHyperGraph) -> Result<()> {
        if graph.nodes.is_empty() {
            return Ok(());
        }

        let mut clusters = self.semantic_clusters.write().await;
        clusters.clear();

        // Extract embeddings
        let embeddings: Vec<(Uuid, &Vec<f32>)> = graph.nodes.iter()
            .map(|(id, node)| (*id, &node.embedding))
            .collect();

        // Perform k-means clustering
        let k = (embeddings.len() as f64).sqrt().ceil() as usize; // Heuristic for k
        let cluster_assignments = self.kmeans_clustering(&embeddings, k).await?;

        // Build semantic clusters
        for cluster_id in 0..k {
            let cluster_nodes: HashSet<Uuid> = cluster_assignments.iter()
                .enumerate()
                .filter(|(_, &assignment)| assignment == cluster_id)
                .map(|(idx, _)| embeddings[idx].0)
                .collect();

            if !cluster_nodes.is_empty() {
                let centroid = self.calculate_centroid(&cluster_nodes, graph).await?;
                let coherence_score = self.calculate_cluster_coherence(&cluster_nodes, graph).await?;

                let cluster = SemanticCluster {
                    cluster_id: Uuid::new_v4(),
                    centroid,
                    nodes: cluster_nodes,
                    coherence_score,
                    cluster_type: ClusterType::Semantic,
                };

                clusters.push(cluster);
            }
        }

        tracing::info!("Created {} semantic clusters", clusters.len());
        Ok(())
    }

    async fn kmeans_clustering(&self, embeddings: &[(Uuid, &Vec<f32>)], k: usize) -> Result<Vec<usize>> {
        if embeddings.is_empty() || k == 0 {
            return Ok(vec![]);
        }

        let embedding_dim = embeddings[0].1.len();
        let mut centroids = self.initialize_centroids(k, embedding_dim);
        let mut assignments = vec![0; embeddings.len()];
        
        // K-means iterations
        for _ in 0..10 { // Max 10 iterations
            let mut changed = false;

            // Assign points to nearest centroids
            for (i, (_, embedding)) in embeddings.iter().enumerate() {
                let mut best_cluster = 0;
                let mut best_distance = f64::INFINITY;

                for (cluster_id, centroid) in centroids.iter().enumerate() {
                    let distance = self.euclidean_distance(embedding, centroid);
                    if distance < best_distance {
                        best_distance = distance;
                        best_cluster = cluster_id;
                    }
                }

                if assignments[i] != best_cluster {
                    assignments[i] = best_cluster;
                    changed = true;
                }
            }

            if !changed {
                break;
            }

            // Update centroids
            for cluster_id in 0..k {
                let cluster_embeddings: Vec<&Vec<f32>> = embeddings.iter()
                    .enumerate()
                    .filter(|(i, _)| assignments[*i] == cluster_id)
                    .map(|(_, (_, embedding))| *embedding)
                    .collect();

                if !cluster_embeddings.is_empty() {
                    centroids[cluster_id] = self.calculate_embedding_centroid(&cluster_embeddings);
                }
            }
        }

        Ok(assignments)
    }

    fn initialize_centroids(&self, k: usize, dim: usize) -> Vec<Vec<f32>> {
        (0..k).map(|_| {
            (0..dim).map(|_| rand::random::<f32>() * 2.0 - 1.0).collect()
        }).collect()
    }

    fn euclidean_distance(&self, a: &[f32], b: &[f32]) -> f64 {
        a.iter().zip(b.iter())
            .map(|(x, y)| (x - y).powi(2))
            .sum::<f32>()
            .sqrt() as f64
    }

    fn calculate_embedding_centroid(&self, embeddings: &[&Vec<f32>]) -> Vec<f32> {
        if embeddings.is_empty() {
            return vec![];
        }

        let dim = embeddings[0].len();
        let mut centroid = vec![0.0; dim];

        for embedding in embeddings {
            for (i, &value) in embedding.iter().enumerate() {
                centroid[i] += value;
            }
        }

        for value in &mut centroid {
            *value /= embeddings.len() as f32;
        }

        centroid
    }

    async fn calculate_centroid(&self, cluster_nodes: &HashSet<Uuid>, graph: &CodebaseHyperGraph) -> Result<Vec<f32>> {
        let embeddings: Vec<&Vec<f32>> = cluster_nodes.iter()
            .filter_map(|node_id| graph.nodes.get(node_id))
            .map(|node| &node.embedding)
            .collect();

        Ok(self.calculate_embedding_centroid(&embeddings))
    }

    async fn calculate_cluster_coherence(&self, cluster_nodes: &HashSet<Uuid>, graph: &CodebaseHyperGraph) -> Result<f64> {
        if cluster_nodes.len() < 2 {
            return Ok(1.0);
        }

        let matrix = self.incidence_matrix.read().await;
        let mut total_similarity = 0.0;
        let mut pair_count = 0;

        // Calculate average pairwise similarity within cluster
        for &node1 in cluster_nodes {
            for &node2 in cluster_nodes {
                if node1 < node2 {
                    let key = (node1, node2);
                    if let Some(&similarity) = matrix.adjacency_cache.get(&key) {
                        total_similarity += similarity;
                    }
                    pair_count += 1;
                }
            }
        }

        if pair_count > 0 {
            Ok(total_similarity / pair_count as f64)
        } else {
            Ok(0.0)
        }
    }

    async fn update_graph_statistics(&self, graph: &CodebaseHyperGraph) -> Result<()> {
        let mut stats = self.graph_statistics.write().await;
        
        stats.node_count = graph.nodes.len();
        stats.edge_count = graph.hyperedges.len();
        
        // Calculate max edge arity
        stats.max_edge_arity = graph.hyperedges.values()
            .map(|edge| edge.nodes.len())
            .max()
            .unwrap_or(0);

        // Calculate average node degree
        let matrix = self.incidence_matrix.read().await;
        if !matrix.node_to_edges.is_empty() {
            let total_degree: usize = matrix.node_to_edges.values()
                .map(|edges| edges.len())
                .sum();
            stats.average_node_degree = total_degree as f64 / matrix.node_to_edges.len() as f64;
        }

        // Calculate clustering coefficient
        stats.clustering_coefficient = self.calculate_clustering_coefficient(&matrix).await?;

        // Calculate semantic density
        stats.semantic_density = if stats.node_count > 0 {
            stats.edge_count as f64 / stats.node_count as f64
        } else {
            0.0
        };

        // Calculate connectivity index
        stats.connectivity_index = self.calculate_connectivity_index(&matrix).await?;

        Ok(())
    }

    async fn calculate_clustering_coefficient(&self, matrix: &IncidenceMatrix) -> Result<f64> {
        if matrix.node_to_edges.len() < 3 {
            return Ok(0.0);
        }

        let mut total_coefficient = 0.0;
        let mut node_count = 0;

        for (&node_id, node_edges) in &matrix.node_to_edges {
            if node_edges.len() < 2 {
                continue;
            }

            // Find neighbors of this node
            let mut neighbors = HashSet::new();
            for &edge_id in node_edges {
                if let Some(edge_nodes) = matrix.edge_to_nodes.get(&edge_id) {
                    for &neighbor_id in edge_nodes {
                        if neighbor_id != node_id {
                            neighbors.insert(neighbor_id);
                        }
                    }
                }
            }

            if neighbors.len() < 2 {
                continue;
            }

            // Count triangles (connections between neighbors)
            let mut triangle_count = 0;
            let neighbors_vec: Vec<Uuid> = neighbors.into_iter().collect();
            
            for i in 0..neighbors_vec.len() {
                for j in (i + 1)..neighbors_vec.len() {
                    let key = if neighbors_vec[i] < neighbors_vec[j] {
                        (neighbors_vec[i], neighbors_vec[j])
                    } else {
                        (neighbors_vec[j], neighbors_vec[i])
                    };
                    
                    if matrix.adjacency_cache.contains_key(&key) {
                        triangle_count += 1;
                    }
                }
            }

            let possible_triangles = neighbors_vec.len() * (neighbors_vec.len() - 1) / 2;
            if possible_triangles > 0 {
                total_coefficient += triangle_count as f64 / possible_triangles as f64;
                node_count += 1;
            }
        }

        if node_count > 0 {
            Ok(total_coefficient / node_count as f64)
        } else {
            Ok(0.0)
        }
    }

    async fn calculate_connectivity_index(&self, matrix: &IncidenceMatrix) -> Result<f64> {
        // Calculate connectivity as ratio of actual connections to possible connections
        let node_count = matrix.node_to_edges.len();
        if node_count < 2 {
            return Ok(0.0);
        }

        let actual_connections = matrix.adjacency_cache.len();
        let possible_connections = node_count * (node_count - 1) / 2;

        if possible_connections > 0 {
            Ok(actual_connections as f64 / possible_connections as f64)
        } else {
            Ok(0.0)
        }
    }

    pub async fn find_shortest_path(&self, start: Uuid, end: Uuid, graph: &CodebaseHyperGraph) -> Result<Option<Vec<Uuid>>> {
        // Find shortest path between two nodes using BFS
        let matrix = self.incidence_matrix.read().await;
        
        if !matrix.node_to_edges.contains_key(&start) || !matrix.node_to_edges.contains_key(&end) {
            return Ok(None);
        }

        let mut queue = VecDeque::new();
        let mut visited = HashSet::new();
        let mut parent = HashMap::new();

        queue.push_back(start);
        visited.insert(start);

        while let Some(current) = queue.pop_front() {
            if current == end {
                // Reconstruct path
                let mut path = Vec::new();
                let mut node = end;
                
                while let Some(&prev) = parent.get(&node) {
                    path.push(node);
                    node = prev;
                }
                path.push(start);
                path.reverse();
                
                return Ok(Some(path));
            }

            // Find neighbors through hyperedges
            if let Some(node_edges) = matrix.node_to_edges.get(&current) {
                for &edge_id in node_edges {
                    if let Some(edge_nodes) = matrix.edge_to_nodes.get(&edge_id) {
                        for &neighbor in edge_nodes {
                            if neighbor != current && !visited.contains(&neighbor) {
                                visited.insert(neighbor);
                                parent.insert(neighbor, current);
                                queue.push_back(neighbor);
                            }
                        }
                    }
                }
            }
        }

        Ok(None)
    }

    pub async fn get_node_neighbors(&self, node_id: Uuid, max_distance: usize) -> Result<Vec<(Uuid, usize)>> {
        // Get all neighbors within max_distance
        let matrix = self.incidence_matrix.read().await;
        let mut neighbors = Vec::new();
        let mut visited = HashSet::new();
        let mut queue = VecDeque::new();

        queue.push_back((node_id, 0));
        visited.insert(node_id);

        while let Some((current, distance)) = queue.pop_front() {
            if distance > 0 {
                neighbors.push((current, distance));
            }

            if distance < max_distance {
                if let Some(node_edges) = matrix.node_to_edges.get(&current) {
                    for &edge_id in node_edges {
                        if let Some(edge_nodes) = matrix.edge_to_nodes.get(&edge_id) {
                            for &neighbor in edge_nodes {
                                if neighbor != current && !visited.contains(&neighbor) {
                                    visited.insert(neighbor);
                                    queue.push_back((neighbor, distance + 1));
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(neighbors)
    }

    pub async fn get_semantic_clusters(&self) -> Result<Vec<SemanticCluster>> {
        Ok(self.semantic_clusters.read().await.clone())
    }

    pub async fn get_graph_statistics(&self) -> Result<GraphStatistics> {
        Ok(self.graph_statistics.read().await.clone())
    }

    pub async fn find_similar_nodes(&self, target_embedding: &[f32], threshold: f64, graph: &CodebaseHyperGraph) -> Result<Vec<(Uuid, f64)>> {
        let mut similar_nodes = Vec::new();

        for (node_id, node) in &graph.nodes {
            let similarity = self.cosine_similarity(target_embedding, &node.embedding);
            if similarity >= threshold {
                similar_nodes.push((*node_id, similarity));
            }
        }

        // Sort by similarity (descending)
        similar_nodes.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        Ok(similar_nodes)
    }

    fn cosine_similarity(&self, a: &[f32], b: &[f32]) -> f64 {
        if a.len() != b.len() {
            return 0.0;
        }

        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();

        if norm_a == 0.0 || norm_b == 0.0 {
            0.0
        } else {
            (dot_product / (norm_a * norm_b)) as f64
        }
    }
}
