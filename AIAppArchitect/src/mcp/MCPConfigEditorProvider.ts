/**
 * MCP Configuration Editor Provider
 * 
 * Provides a beautiful webview-based UI for configuring MCP servers
 * with real-time validation, form-based editing, and JSON preview
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface StandardMCPConfig {
    mcpServers: Record<string, StandardMCPServerConfig>;
    globalSettings?: {
        autoStart?: boolean;
        maxConcurrentServers?: number;
        defaultTimeout?: number;
        logLevel?: 'debug' | 'info' | 'warn' | 'error';
    };
    security?: {
        requireConfirmation?: boolean;
        allowedCommands?: string[];
        blockedCommands?: string[];
        allowedDomains?: string[];
        maxExecutionTime?: number;
    };
    metadata?: {
        name?: string;
        description?: string;
        author?: string;
        created?: string;
        lastModified?: string;
    };
}

export interface StandardMCPServerConfig {
    // STDIO transport
    command?: string;
    args?: string[];
    cwd?: string;
    env?: Record<string, string>;
    
    // HTTP transport
    url?: string;
    headers?: Record<string, string>;
    
    // SSE transport (Windsurf format)
    serverUrl?: string;
    
    // Common properties
    disabled?: boolean;
    autoApprove?: string[];
}

export class MCPConfigEditorProvider {
    private context: vscode.ExtensionContext;
    private panel: vscode.WebviewPanel | undefined;
    private configPath: string;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.configPath = this.getConfigPath();
    }

    private getConfigPath(): string {
        // Try workspace folder first, then fallback to global storage
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return path.join(workspaceFolders[0].uri.fsPath, '.vscode', 'mcp.json');
        }
        
        // Fallback to global storage path
        const globalStoragePath = this.context.globalStorageUri.fsPath;
        if (!fs.existsSync(globalStoragePath)) {
            fs.mkdirSync(globalStoragePath, { recursive: true });
        }
        return path.join(globalStoragePath, 'mcp.json');
    }

    async openConfigEditor(): Promise<void> {
        if (this.panel) {
            this.panel.reveal();
            return;
        }

        this.panel = vscode.window.createWebviewPanel(
            'mcpConfigEditor',
            'MCP Configuration Editor',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.file(path.join(this.context.extensionPath, 'src', 'ui'))
                ]
            }
        );

        this.panel.webview.html = await this.getWebviewContent();
        this.setupWebviewMessageHandling();

        this.panel.onDidDispose(() => {
            this.panel = undefined;
        });

        // Load initial configuration
        await this.loadAndSendConfiguration();
    }

    private async getWebviewContent(): Promise<string> {
        const htmlPath = path.join(this.context.extensionPath, 'out', 'ui', 'modern-mcp-config.html');
        let html = fs.readFileSync(htmlPath, 'utf8');

        // Replace script src with webview URI
        const jsPath = vscode.Uri.file(path.join(this.context.extensionPath, 'src', 'ui', 'mcp-config-editor.js'));
        const jsUri = this.panel!.webview.asWebviewUri(jsPath);
        
        html = html.replace(
            '<script src="mcp-config-editor.js"></script>',
            `<script src="${jsUri}"></script>`
        );

        return html;
    }

    private setupWebviewMessageHandling(): void {
        this.panel!.webview.onDidReceiveMessage(async (message) => {
            switch (message.type) {
                case 'loadConfiguration':
                    await this.loadAndSendConfiguration();
                    break;

                case 'saveConfiguration':
                    await this.saveConfiguration(message.config);
                    break;

                case 'validateConfiguration':
                    await this.validateConfiguration(message.config);
                    break;

                default:
                    console.warn('Unknown message type:', message.type);
            }
        });
    }

    private async loadAndSendConfiguration(): Promise<void> {
        try {
            let config: StandardMCPConfig;

            if (fs.existsSync(this.configPath)) {
                const content = fs.readFileSync(this.configPath, 'utf8');
                config = JSON.parse(content);
            } else {
                // Create default configuration
                config = {
                    mcpServers: {},
                    globalSettings: {
                        autoStart: true,
                        maxConcurrentServers: 10,
                        defaultTimeout: 30000,
                        logLevel: 'info'
                    },
                    security: {
                        requireConfirmation: true,
                        allowedCommands: ['node', 'python', 'npx', 'bun'],
                        blockedCommands: ['rm', 'del', 'sudo', 'format'],
                        maxExecutionTime: 60000
                    },
                    metadata: {
                        name: 'Aizen AI MCP Configuration',
                        description: 'MCP server configuration for Aizen AI',
                        author: 'User',
                        created: new Date().toISOString(),
                        lastModified: new Date().toISOString()
                    }
                };
            }

            this.panel!.webview.postMessage({
                type: 'configurationLoaded',
                config: config
            });

        } catch (error) {
            console.error('Error loading MCP configuration:', error);
            vscode.window.showErrorMessage(`Failed to load MCP configuration: ${error}`);
        }
    }

    private async saveConfiguration(config: StandardMCPConfig): Promise<void> {
        try {
            // Update metadata
            if (config.metadata) {
                config.metadata.lastModified = new Date().toISOString();
            }

            // Validate configuration before saving
            this.validateConfigurationSync(config);

            // Ensure directory exists
            const configDir = path.dirname(this.configPath);
            if (!fs.existsSync(configDir)) {
                fs.mkdirSync(configDir, { recursive: true });
            }

            // Write configuration with pretty formatting
            const content = JSON.stringify(config, null, 2);
            fs.writeFileSync(this.configPath, content, 'utf8');

            this.panel!.webview.postMessage({
                type: 'configurationSaved'
            });

            vscode.window.showInformationMessage('✅ MCP configuration saved successfully');
            console.log('MCP configuration saved to:', this.configPath);

        } catch (error) {
            console.error('Error saving MCP configuration:', error);
            vscode.window.showErrorMessage(`Failed to save MCP configuration: ${error}`);
        }
    }

    private async validateConfiguration(config: StandardMCPConfig): Promise<void> {
        try {
            const result = this.validateConfigurationSync(config);
            
            this.panel!.webview.postMessage({
                type: 'validationResult',
                result: {
                    isValid: true,
                    errors: [],
                    warnings: []
                }
            });

        } catch (error) {
            this.panel!.webview.postMessage({
                type: 'validationResult',
                result: {
                    isValid: false,
                    errors: [error.message],
                    warnings: []
                }
            });
        }
    }

    private validateConfigurationSync(config: StandardMCPConfig): void {
        // Basic validation
        if (!config.mcpServers) {
            throw new Error('mcpServers property is required');
        }

        if (typeof config.mcpServers !== 'object') {
            throw new Error('mcpServers must be an object');
        }

        // Validate each server
        for (const [serverId, server] of Object.entries(config.mcpServers)) {
            this.validateServerConfig(serverId, server);
        }

        // Validate global settings
        if (config.globalSettings) {
            this.validateGlobalSettings(config.globalSettings);
        }

        // Validate security settings
        if (config.security) {
            this.validateSecuritySettings(config.security);
        }
    }

    private validateServerConfig(serverId: string, server: StandardMCPServerConfig): void {
        if (!serverId.match(/^[a-zA-Z0-9_-]+$/)) {
            throw new Error(`Invalid server ID: ${serverId}. Must contain only alphanumeric characters, underscores, and hyphens.`);
        }

        // Check transport configuration
        const hasStdio = !!server.command;
        const hasHttp = !!(server.url || server.serverUrl);

        if (!hasStdio && !hasHttp) {
            throw new Error(`Server ${serverId}: Must have either command (STDIO) or url/serverUrl (HTTP/SSE) transport`);
        }

        if (hasStdio && hasHttp) {
            throw new Error(`Server ${serverId}: Cannot have both STDIO and HTTP transport configurations`);
        }

        // Validate STDIO transport
        if (hasStdio) {
            if (!server.command || server.command.trim().length === 0) {
                throw new Error(`Server ${serverId}: command cannot be empty`);
            }

            // Validate environment variables
            if (server.env) {
                for (const [key, value] of Object.entries(server.env)) {
                    if (!key.match(/^[A-Z_][A-Z0-9_]*$/)) {
                        throw new Error(`Server ${serverId}: Invalid environment variable name: ${key}`);
                    }
                }
            }
        }

        // Validate HTTP transport
        if (hasHttp) {
            const url = server.url || server.serverUrl;
            if (!url || !url.startsWith('http')) {
                throw new Error(`Server ${serverId}: URL must start with http:// or https://`);
            }

            try {
                new URL(url);
            } catch {
                throw new Error(`Server ${serverId}: Invalid URL format: ${url}`);
            }

            // Validate headers
            if (server.headers) {
                for (const [key, value] of Object.entries(server.headers)) {
                    if (!key.match(/^[A-Za-z0-9-]+$/)) {
                        throw new Error(`Server ${serverId}: Invalid header name: ${key}`);
                    }
                }
            }
        }

        // Validate autoApprove
        if (server.autoApprove && !Array.isArray(server.autoApprove)) {
            throw new Error(`Server ${serverId}: autoApprove must be an array`);
        }
    }

    private validateGlobalSettings(settings: NonNullable<StandardMCPConfig['globalSettings']>): void {
        if (settings.maxConcurrentServers !== undefined) {
            if (typeof settings.maxConcurrentServers !== 'number' || 
                settings.maxConcurrentServers < 1 || 
                settings.maxConcurrentServers > 50) {
                throw new Error('maxConcurrentServers must be a number between 1 and 50');
            }
        }

        if (settings.defaultTimeout !== undefined) {
            if (typeof settings.defaultTimeout !== 'number' || 
                settings.defaultTimeout < 5000 || 
                settings.defaultTimeout > 300000) {
                throw new Error('defaultTimeout must be a number between 5000 and 300000 milliseconds');
            }
        }

        if (settings.logLevel !== undefined) {
            if (!['debug', 'info', 'warn', 'error'].includes(settings.logLevel)) {
                throw new Error('logLevel must be one of: debug, info, warn, error');
            }
        }
    }

    private validateSecuritySettings(security: NonNullable<StandardMCPConfig['security']>): void {
        if (security.maxExecutionTime !== undefined) {
            if (typeof security.maxExecutionTime !== 'number' || 
                security.maxExecutionTime < 1000 || 
                security.maxExecutionTime > 600000) {
                throw new Error('maxExecutionTime must be a number between 1000 and 600000 milliseconds');
            }
        }

        if (security.allowedCommands && !Array.isArray(security.allowedCommands)) {
            throw new Error('allowedCommands must be an array');
        }

        if (security.blockedCommands && !Array.isArray(security.blockedCommands)) {
            throw new Error('blockedCommands must be an array');
        }

        if (security.allowedDomains && !Array.isArray(security.allowedDomains)) {
            throw new Error('allowedDomains must be an array');
        }
    }

    getConfigurationPath(): string {
        return this.configPath;
    }

    dispose(): void {
        if (this.panel) {
            this.panel.dispose();
        }
    }
}
