// MCP Client Implementation - Protocol 2025-06-18
import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import {
    JsonRpcRequest,
    JsonRpcResponse,
    JsonRpcError,
    MCPCapabilities,
    ClientInfo,
    InitializeRequest,
    InitializeResult,
    ListToolsRequest,
    ListToolsResult,
    CallToolRequest,
    CallToolResult,
    ListResourcesRequest,
    ListResourcesResult,
    ReadResourceRequest,
    ReadResourceResult,
    ListPromptsRequest,
    ListPromptsResult,
    GetPromptRequest,
    GetPromptResult,
    MCPConnectionStatus,
    MCPServerConfig,
    MCPServerInstance,
    MCP_PROTOCOL_VERSION,
    MCPErrorCode
} from './types';

export interface MCPTransport {
    send(message: JsonRpcRequest): Promise<JsonRpcResponse>;
    close(): Promise<void>;
    onMessage(handler: (message: <PERSON>sonRpcResponse) => void): void;
    onError(handler: (error: Error) => void): void;
    onClose(handler: () => void): void;
}

export class MCPClient extends EventEmitter {
    private transport: MCPTransport | null = null;
    private requestId = 0;
    private pendingRequests = new Map<string | number, {
        resolve: (value: any) => void;
        reject: (error: Error) => void;
        timeout: NodeJS.Timeout;
    }>();
    private serverInfo: any = null;
    private serverCapabilities: MCPCapabilities = {};
    private connectionStatus: MCPConnectionStatus = MCPConnectionStatus.Disconnected;
    private readonly requestTimeout = 30000; // 30 seconds

    constructor(
        private readonly clientInfo: ClientInfo,
        private readonly capabilities: MCPCapabilities = {
            tools: { listChanged: true },
            resources: { subscribe: true, listChanged: true },
            prompts: { listChanged: true },
            sampling: {},
            elicitation: {}
        }
    ) {
        super();
    }

    async connect(transport: MCPTransport): Promise<void> {
        if (this.transport) {
            await this.disconnect();
        }

        this.transport = transport;
        this.connectionStatus = MCPConnectionStatus.Connecting;
        this.emit('statusChanged', this.connectionStatus);

        // Set up transport event handlers
        this.transport.onMessage((message) => this.handleMessage(message));
        this.transport.onError((error) => this.handleError(error));
        this.transport.onClose(() => this.handleClose());

        try {
            // Send initialize request
            const initRequest: InitializeRequest = {
                jsonrpc: "2.0",
                id: this.getNextRequestId(),
                method: "initialize",
                params: {
                    protocolVersion: MCP_PROTOCOL_VERSION,
                    capabilities: this.capabilities,
                    clientInfo: this.clientInfo
                }
            };

            const response = await this.sendRequest<InitializeResult>(initRequest);
            this.serverInfo = response.serverInfo;
            this.serverCapabilities = response.capabilities;
            this.connectionStatus = MCPConnectionStatus.Connected;
            this.emit('statusChanged', this.connectionStatus);
            this.emit('connected', { serverInfo: this.serverInfo, capabilities: this.serverCapabilities });

        } catch (error) {
            this.connectionStatus = MCPConnectionStatus.Error;
            this.emit('statusChanged', this.connectionStatus);
            throw error;
        }
    }

    async disconnect(): Promise<void> {
        if (this.transport) {
            await this.transport.close();
            this.transport = null;
        }
        this.connectionStatus = MCPConnectionStatus.Disconnected;
        this.emit('statusChanged', this.connectionStatus);
        
        // Reject all pending requests
        for (const [id, request] of this.pendingRequests) {
            clearTimeout(request.timeout);
            request.reject(new Error('Connection closed'));
        }
        this.pendingRequests.clear();
    }

    async listTools(): Promise<ListToolsResult> {
        const request: ListToolsRequest = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "tools/list",
            params: {}
        };
        return this.sendRequest<ListToolsResult>(request);
    }

    async callTool(name: string, arguments_?: Record<string, any>): Promise<CallToolResult> {
        const request: CallToolRequest = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "tools/call",
            params: {
                name,
                arguments: arguments_
            }
        };
        return this.sendRequest<CallToolResult>(request);
    }

    async listResources(): Promise<ListResourcesResult> {
        const request: ListResourcesRequest = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "resources/list",
            params: {}
        };
        return this.sendRequest<ListResourcesResult>(request);
    }

    async readResource(uri: string): Promise<ReadResourceResult> {
        const request: ReadResourceRequest = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "resources/read",
            params: { uri }
        };
        return this.sendRequest<ReadResourceResult>(request);
    }

    async listPrompts(): Promise<ListPromptsResult> {
        const request: ListPromptsRequest = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "prompts/list",
            params: {}
        };
        return this.sendRequest<ListPromptsResult>(request);
    }

    async getPrompt(name: string, arguments_?: Record<string, any>): Promise<GetPromptResult> {
        const request: GetPromptRequest = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "prompts/get",
            params: {
                name,
                arguments: arguments_
            }
        };
        return this.sendRequest<GetPromptResult>(request);
    }

    private async sendRequest<T>(request: JsonRpcRequest): Promise<T> {
        if (!this.transport) {
            throw new Error('Not connected to MCP server');
        }

        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.pendingRequests.delete(request.id);
                reject(new Error(`Request timeout: ${request.method}`));
            }, this.requestTimeout);

            this.pendingRequests.set(request.id, {
                resolve,
                reject,
                timeout
            });

            this.transport!.send(request).catch((error) => {
                this.pendingRequests.delete(request.id);
                clearTimeout(timeout);
                reject(error);
            });
        });
    }

    private handleMessage(message: JsonRpcResponse): void {
        if ('id' in message && message.id !== undefined) {
            // This is a response to a request
            const pending = this.pendingRequests.get(message.id);
            if (pending) {
                this.pendingRequests.delete(message.id);
                clearTimeout(pending.timeout);

                if (message.error) {
                    pending.reject(new Error(`MCP Error ${message.error.code}: ${message.error.message}`));
                } else {
                    pending.resolve(message.result);
                }
            }
        } else {
            // This is a notification
            this.handleNotification(message as any);
        }
    }

    private handleNotification(notification: any): void {
        switch (notification.method) {
            case 'notifications/tools/list_changed':
                this.emit('toolsChanged');
                break;
            case 'notifications/resources/list_changed':
                this.emit('resourcesChanged');
                break;
            case 'notifications/prompts/list_changed':
                this.emit('promptsChanged');
                break;
            default:
                this.emit('notification', notification);
                break;
        }
    }

    private handleError(error: Error): void {
        this.connectionStatus = MCPConnectionStatus.Error;
        this.emit('statusChanged', this.connectionStatus);
        this.emit('error', error);
    }

    private handleClose(): void {
        this.connectionStatus = MCPConnectionStatus.Disconnected;
        this.emit('statusChanged', this.connectionStatus);
        this.emit('disconnected');
    }

    private getNextRequestId(): number {
        return ++this.requestId;
    }

    // Getters
    get status(): MCPConnectionStatus {
        return this.connectionStatus;
    }

    get isConnected(): boolean {
        return this.connectionStatus === MCPConnectionStatus.Connected;
    }

    get server(): { info: any; capabilities: MCPCapabilities } | null {
        if (!this.serverInfo) return null;
        return {
            info: this.serverInfo,
            capabilities: this.serverCapabilities
        };
    }
}
