/**
 * Aizen MCP Hub - Manages MCP server connections
 * Based on <PERSON><PERSON>'s implementation approach
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { CallToolResultSchema, ListToolsResultSchema } from '@modelcontextprotocol/sdk/types.js';
import * as vscode from 'vscode';

interface McpServerConfig {
    type: 'stdio';
    command: string;
    args?: string[];
    env?: Record<string, string>;
    disabled?: boolean;
    alwaysAllow?: string[];
}

interface McpConnection {
    server: {
        name: string;
        config: McpServerConfig;
        status: 'connected' | 'disconnected' | 'error';
    };
    client: Client;
    transport: StdioClientTransport;
}

export class AizenMCPHub {
    private connections: McpConnection[] = [];
    private context: vscode.ExtensionContext;
    private isDisposed = false;
    private _onDidChangeMcpServerDefinitions = new vscode.EventEmitter<void>();
    public readonly onDidChangeMcpServerDefinitions = this._onDidChangeMcpServerDefinitions.event;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
    }

    async initialize(): Promise<void> {
        console.log('🚀 Initializing Aizen MCP Hub...');

        // Initialize built-in servers
        await this.initializeBuiltinServers();

        console.log('✅ Aizen MCP Hub initialized');
    }

    private async initializeBuiltinServers(): Promise<void> {
        console.log('🔧 Initializing built-in MCP servers...');

        // Always initialize server definitions, even without API keys
        // This allows the UI to show them and configure them
        const servers: Record<string, McpServerConfig> = {
            'exa': {
                type: 'stdio',
                command: 'npx',
                args: ['-y', 'mcp-remote', 'https://mcp.exa.ai/mcp?exaApiKey=f01e507f-cdd2-454d-adcf-545d24035692'],
                env: {},
                disabled: false // Always enabled with built-in API key
            },
            'firecrawl': {
                type: 'stdio',
                command: 'npx',
                args: ['-y', 'mcp-remote', 'https://mcp.firecrawl.dev/fc-73581888d5374a1a99893178925cc8bb/sse'],
                env: {},
                disabled: false // Always enabled with built-in API key
            }
        };

        // Check if API keys are configured and enable servers
        const exaApiKey = await this.getApiKey('EXA_API_KEY');
        if (exaApiKey) {
            servers['exa'].env = { EXA_API_KEY: exaApiKey };
            servers['exa'].disabled = false;
        }

        const firecrawlApiKey = await this.getApiKey('FIRECRAWL_API_KEY');
        if (firecrawlApiKey) {
            servers['firecrawl'].env = { FIRECRAWL_API_KEY: firecrawlApiKey };
            servers['firecrawl'].disabled = false;
        }

        // Try to connect to enabled servers
        for (const [name, config] of Object.entries(servers)) {
            if (!config.disabled) {
                try {
                    await this.connectToServer(name, config);
                    console.log(`✅ Connected to ${name} MCP server`);
                } catch (error) {
                    console.error(`❌ Failed to connect to ${name}:`, error);
                }
            } else {
                console.log(`⏸️ ${name} server disabled (no API key)`);
            }
        }

        console.log('✅ Built-in MCP servers initialized');
    }

    private async connectToServer(name: string, config: McpServerConfig): Promise<void> {
        console.log(`🔌 Connecting to MCP server: ${name}`);

        try {
            const transport = new StdioClientTransport({
                command: config.command,
                args: config.args || [],
                env: { ...process.env, ...config.env }
            });

            const client = new Client({
                name: `aizen-${name}`,
                version: '1.0.0'
            }, {
                capabilities: {}
            });

            // Add timeout to prevent hanging
            const connectPromise = client.connect(transport);
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Connection timeout')), 10000)
            );

            await Promise.race([connectPromise, timeoutPromise]);

            const connection: McpConnection = {
                server: {
                    name,
                    config,
                    status: 'connected'
                },
                client,
                transport
            };

            this.connections.push(connection);
            console.log(`✅ Connected to MCP server: ${name}`);

        } catch (error) {
            console.error(`❌ Failed to connect to MCP server ${name}:`, error);
            // Don't throw - just log and continue
            const connection: McpConnection = {
                server: {
                    name,
                    config,
                    status: 'error'
                },
                client: null as any,
                transport: null as any
            };
            this.connections.push(connection);
        }
    }

    async callTool(serverName: string, toolName: string, args?: any): Promise<any> {
        const connection = this.connections.find(conn => conn.server.name === serverName);
        if (!connection) {
            throw new Error(`No connection found for server: ${serverName}`);
        }

        if (connection.server.status !== 'connected') {
            throw new Error(`Server ${serverName} is not connected`);
        }

        try {
            const result = await connection.client.request(
                {
                    method: 'tools/call',
                    params: {
                        name: toolName,
                        arguments: args || {}
                    }
                },
                CallToolResultSchema
            );

            console.log(`✅ Tool ${toolName} called successfully on server ${serverName}`);
            return result;
        } catch (error) {
            console.error(`❌ Error calling tool ${toolName} on server ${serverName}:`, error);
            throw error;
        }
    }

    getServers(): Array<{ name: string; status: string; tools?: string[] }> {
        return this.connections
            .filter(conn => !conn.server.config.disabled)
            .map(conn => ({
                name: conn.server.name,
                status: conn.server.status,
                tools: [] // Tools will be populated when we list them
            }));
    }

    async getServerTools(serverName: string): Promise<string[]> {
        const connection = this.connections.find(conn => conn.server.name === serverName);
        if (!connection) {
            throw new Error(`No connection found for server: ${serverName}`);
        }

        try {
            const result = await connection.client.request(
                { method: 'tools/list' },
                ListToolsResultSchema
            );

            return result.tools.map(tool => tool.name);
        } catch (error) {
            console.error(`Error listing tools for server ${serverName}:`, error);
            return [];
        }
    }

    private async getApiKey(envVar: string): Promise<string | undefined> {
        // First check environment variables
        const envValue = process.env[envVar];
        if (envValue) {
            return envValue;
        }

        // Then check VS Code settings
        const config = vscode.workspace.getConfiguration('aizen.mcp');
        const settingsKey = envVar.toLowerCase().replace('_', '.');
        const settingsValue = config.get<string>(settingsKey);
        if (settingsValue) {
            return settingsValue;
        }

        // Check if stored in extension context (secure storage)
        const storedValue = await this.context.secrets.get(envVar);
        if (storedValue) {
            return storedValue;
        }

        return undefined;
    }

    public async configureApiKey(service: 'exa' | 'firecrawl'): Promise<void> {
        const envVar = service === 'exa' ? 'EXA_API_KEY' : 'FIRECRAWL_API_KEY';
        const displayName = service === 'exa' ? 'Exa API Key' : 'Firecrawl API Key';
        const placeholder = service === 'exa' ? 'Enter your Exa API key...' : 'Enter your Firecrawl API key...';

        const apiKey = await vscode.window.showInputBox({
            prompt: `Enter your ${displayName}`,
            placeHolder: placeholder,
            password: true,
            ignoreFocusOut: true
        });

        if (apiKey) {
            // Store in secure storage
            await this.context.secrets.store(envVar, apiKey);

            // Try to connect to the server now that API key is configured
            try {
                const config: McpServerConfig = {
                    type: 'stdio',
                    command: 'npx',
                    args: service === 'exa' ?
                        ['-y', 'mcp-remote', `https://mcp.exa.ai/mcp?exaApiKey=${apiKey}`] :
                        ['-y', 'mcp-remote', `https://mcp.firecrawl.dev/${apiKey}/sse`],
                    env: {}
                };

                await this.connectToServer(service, config);
                vscode.window.showInformationMessage(`${displayName} configured and connected successfully!`);
            } catch (error) {
                console.error(`Failed to connect to ${service} after configuration:`, error);
                vscode.window.showWarningMessage(`${displayName} configured but connection failed. Check console for details.`);
            }

            // Notify that server definitions have changed
            this._onDidChangeMcpServerDefinitions.fire();
        }
    }

    public async removeApiKey(service: 'exa' | 'firecrawl'): Promise<void> {
        const envVar = service === 'exa' ? 'EXA_API_KEY' : 'FIRECRAWL_API_KEY';
        const displayName = service === 'exa' ? 'Exa API Key' : 'Firecrawl API Key';

        await this.context.secrets.delete(envVar);
        this._onDidChangeMcpServerDefinitions.fire();
        
        vscode.window.showInformationMessage(`${displayName} removed successfully!`);
    }

    public async checkServerStatus(): Promise<{ exa: boolean; firecrawl: boolean }> {
        const exaApiKey = await this.getApiKey('EXA_API_KEY');
        const firecrawlApiKey = await this.getApiKey('FIRECRAWL_API_KEY');

        // Check if servers are actually connected
        const exaConnected = this.connections.some(conn => conn.server.name === 'exa' && conn.server.status === 'connected');
        const firecrawlConnected = this.connections.some(conn => conn.server.name === 'firecrawl' && conn.server.status === 'connected');

        return {
            exa: !!exaApiKey && exaConnected,
            firecrawl: !!firecrawlApiKey && firecrawlConnected
        };
    }

    public async testServer(service: 'exa' | 'firecrawl'): Promise<boolean> {
        try {
            console.log(`🧪 Testing ${service} server connection...`);

            // Check if API key is configured
            const envVar = service === 'exa' ? 'EXA_API_KEY' : 'FIRECRAWL_API_KEY';
            const apiKey = await this.getApiKey(envVar);

            if (!apiKey) {
                console.log(`❌ ${service} API key not configured`);
                return false;
            }

            // Check if server connection exists
            const connection = this.connections.find(conn => conn.server.name === service);
            if (!connection) {
                console.log(`❌ ${service} server not connected`);
                return false;
            }

            // Try to get server tools as a basic connectivity test
            try {
                const tools = await this.getServerTools(service);
                console.log(`✅ ${service} server test successful. Available tools: ${tools.length}`);
                return true;
            } catch (error) {
                console.log(`❌ ${service} server test failed:`, error);
                return false;
            }

        } catch (error) {
            console.error(`❌ Error testing ${service} server:`, error);
            return false;
        }
    }

    public async dispose(): Promise<void> {
        if (this.isDisposed) {
            return;
        }

        console.log('🔌 Disposing MCP Hub...');
        this.isDisposed = true;

        // Close all connections
        for (const connection of this.connections) {
            try {
                await connection.transport.close();
                console.log(`✅ Closed connection to ${connection.server.name}`);
            } catch (error) {
                console.error(`❌ Error closing connection to ${connection.server.name}:`, error);
            }
        }

        this.connections = [];
        this._onDidChangeMcpServerDefinitions.dispose();
        console.log('✅ MCP Hub disposed');
    }
}
