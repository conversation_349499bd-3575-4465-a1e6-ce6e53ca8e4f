// MCP Hub - Central orchestration inspired by <PERSON><PERSON>'s McpHub
import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import { MCPManager } from './manager';
import { MCPServerConfig, MCPTool, MCPResource, MCPPrompt, CallToolResult, ReadResourceResult, GetPromptResult } from './types';

export interface MCPToolExecution {
    serverId: string;
    toolName: string;
    arguments: Record<string, any>;
    result?: CallToolResult;
    error?: string;
    timestamp: Date;
    duration?: number;
}

export interface MCPWorkflow {
    id: string;
    name: string;
    description?: string;
    steps: MCPWorkflowStep[];
    createdAt: Date;
    lastExecuted?: Date;
}

export interface MCPWorkflowStep {
    id: string;
    type: 'tool' | 'resource' | 'prompt' | 'condition' | 'loop';
    serverId: string;
    toolName?: string;
    resourceUri?: string;
    promptName?: string;
    arguments?: Record<string, any>;
    condition?: string;
    nextStepId?: string;
    errorStepId?: string;
}

export class MCPHub extends EventEmitter {
    private manager: MCPManager;
    private executionHistory: MCPToolExecution[] = [];
    private workflows: Map<string, MCPWorkflow> = new Map();
    private readonly context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        super();
        this.context = context;
        this.manager = new MCPManager(context);
        this.setupManagerEventHandlers();
    }

    async initialize(): Promise<void> {
        console.log('🚀 Initializing MCP Hub...');
        await this.manager.initialize();
        await this.loadWorkflows();
        
        // Register default external MCP servers
        await this.registerDefaultServers();
        
        console.log('✅ MCP Hub initialized successfully');
        this.emit('initialized');
    }

    // Server Management
    async addServer(config: MCPServerConfig): Promise<string> {
        const serverId = await this.manager.addServer(config);
        this.emit('serverAdded', serverId, config);
        return serverId;
    }

    async removeServer(serverId: string): Promise<void> {
        await this.manager.removeServer(serverId);
        this.emit('serverRemoved', serverId);
    }

    async connectServer(serverId: string): Promise<void> {
        await this.manager.connectServer(serverId);
    }

    async disconnectServer(serverId: string): Promise<void> {
        await this.manager.disconnectServer(serverId);
    }

    // Tool Execution with Enhanced Features
    async executeTool(serverId: string, toolName: string, arguments_: Record<string, any> = {}): Promise<CallToolResult> {
        const startTime = Date.now();
        const execution: MCPToolExecution = {
            serverId,
            toolName,
            arguments: arguments_,
            timestamp: new Date()
        };

        try {
            console.log(`🔧 Executing tool: ${toolName} on server: ${serverId}`);
            
            const result = await this.manager.callTool(serverId, toolName, arguments_);
            
            execution.result = result;
            execution.duration = Date.now() - startTime;
            
            this.executionHistory.push(execution);
            this.emit('toolExecuted', execution);
            
            console.log(`✅ Tool executed successfully: ${toolName} (${execution.duration}ms)`);
            return result;
            
        } catch (error) {
            execution.error = error instanceof Error ? error.message : String(error);
            execution.duration = Date.now() - startTime;
            
            this.executionHistory.push(execution);
            this.emit('toolExecutionFailed', execution);
            
            console.error(`❌ Tool execution failed: ${toolName}`, error);
            throw error;
        }
    }

    // Resource Access
    async readResource(serverId: string, uri: string): Promise<ReadResourceResult> {
        console.log(`📄 Reading resource: ${uri} from server: ${serverId}`);
        
        try {
            const result = await this.manager.readResource(serverId, uri);
            this.emit('resourceRead', { serverId, uri, result });
            return result;
        } catch (error) {
            console.error(`❌ Resource read failed: ${uri}`, error);
            this.emit('resourceReadFailed', { serverId, uri, error });
            throw error;
        }
    }

    // Prompt Management
    async getPrompt(serverId: string, promptName: string, arguments_: Record<string, any> = {}): Promise<GetPromptResult> {
        console.log(`💬 Getting prompt: ${promptName} from server: ${serverId}`);
        
        try {
            const result = await this.manager.getPrompt(serverId, promptName, arguments_);
            this.emit('promptRetrieved', { serverId, promptName, arguments: arguments_, result });
            return result;
        } catch (error) {
            console.error(`❌ Prompt retrieval failed: ${promptName}`, error);
            this.emit('promptRetrievalFailed', { serverId, promptName, error });
            throw error;
        }
    }

    // Workflow Management
    async createWorkflow(workflow: Omit<MCPWorkflow, 'id' | 'createdAt'>): Promise<string> {
        const id = `workflow-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const newWorkflow: MCPWorkflow = {
            ...workflow,
            id,
            createdAt: new Date()
        };
        
        this.workflows.set(id, newWorkflow);
        await this.saveWorkflows();
        this.emit('workflowCreated', newWorkflow);
        
        return id;
    }

    async executeWorkflow(workflowId: string, initialData: Record<string, any> = {}): Promise<any> {
        const workflow = this.workflows.get(workflowId);
        if (!workflow) {
            throw new Error(`Workflow ${workflowId} not found`);
        }

        console.log(`🔄 Executing workflow: ${workflow.name}`);
        
        try {
            const result = await this.executeWorkflowSteps(workflow.steps, initialData);
            
            workflow.lastExecuted = new Date();
            await this.saveWorkflows();
            
            this.emit('workflowExecuted', { workflow, result });
            return result;
            
        } catch (error) {
            console.error(`❌ Workflow execution failed: ${workflow.name}`, error);
            this.emit('workflowExecutionFailed', { workflow, error });
            throw error;
        }
    }

    private async executeWorkflowSteps(steps: MCPWorkflowStep[], data: Record<string, any>): Promise<any> {
        let currentData = { ...data };
        
        for (const step of steps) {
            try {
                switch (step.type) {
                    case 'tool':
                        if (step.toolName) {
                            const result = await this.executeTool(step.serverId, step.toolName, {
                                ...step.arguments,
                                ...currentData
                            });
                            currentData = { ...currentData, [`${step.id}_result`]: result };
                        }
                        break;
                        
                    case 'resource':
                        if (step.resourceUri) {
                            const result = await this.readResource(step.serverId, step.resourceUri);
                            currentData = { ...currentData, [`${step.id}_result`]: result };
                        }
                        break;
                        
                    case 'prompt':
                        if (step.promptName) {
                            const result = await this.getPrompt(step.serverId, step.promptName, {
                                ...step.arguments,
                                ...currentData
                            });
                            currentData = { ...currentData, [`${step.id}_result`]: result };
                        }
                        break;
                        
                    case 'condition':
                        // Simple condition evaluation (could be enhanced)
                        if (step.condition && !this.evaluateCondition(step.condition, currentData)) {
                            if (step.errorStepId) {
                                // Jump to error step
                                continue;
                            } else {
                                break; // Exit workflow
                            }
                        }
                        break;
                }
            } catch (error) {
                if (step.errorStepId) {
                    // Handle error by jumping to error step
                    currentData = { ...currentData, [`${step.id}_error`]: error };
                    continue;
                } else {
                    throw error;
                }
            }
        }
        
        return currentData;
    }

    private evaluateCondition(condition: string, data: Record<string, any>): boolean {
        // Simple condition evaluation - could be enhanced with a proper expression parser
        try {
            // Replace data references in condition
            let evaluatedCondition = condition;
            for (const [key, value] of Object.entries(data)) {
                evaluatedCondition = evaluatedCondition.replace(
                    new RegExp(`\\$\\{${key}\\}`, 'g'),
                    JSON.stringify(value)
                );
            }
            
            // Basic safety check - only allow simple comparisons
            if (!/^[a-zA-Z0-9_\s"'<>=!&|().,\[\]{}]+$/.test(evaluatedCondition)) {
                return false;
            }
            
            return eval(evaluatedCondition);
        } catch {
            return false;
        }
    }

    // Discovery and Marketplace Features
    async discoverAvailableServers(): Promise<MCPServerConfig[]> {
        // This could be enhanced to discover servers from a marketplace or registry
        return [
            {
                name: 'Exa Search',
                url: 'http://localhost:3001/mcp',
                transport: 'streamable-http',
                enabled: false,
                autoStart: false
            },
            {
                name: 'Firecrawl',
                url: 'http://localhost:3002/mcp',
                transport: 'streamable-http',
                enabled: false,
                autoStart: false
            }
        ];
    }

    // Analytics and Monitoring
    getExecutionHistory(limit: number = 100): MCPToolExecution[] {
        return this.executionHistory.slice(-limit);
    }

    getServerStats(): Array<{ serverId: string; toolCalls: number; lastActivity?: Date }> {
        const stats = new Map<string, { toolCalls: number; lastActivity?: Date }>();
        
        for (const execution of this.executionHistory) {
            const current = stats.get(execution.serverId) || { toolCalls: 0 };
            current.toolCalls++;
            current.lastActivity = execution.timestamp;
            stats.set(execution.serverId, current);
        }
        
        return Array.from(stats.entries()).map(([serverId, data]) => ({
            serverId,
            ...data
        }));
    }

    // Getters
    getServers() {
        return this.manager.getServers();
    }

    getConnectedServers() {
        return this.manager.getConnectedServers();
    }

    getAllTools() {
        return this.manager.getAllTools();
    }

    getAllResources() {
        return this.manager.getAllResources();
    }

    getAllPrompts() {
        return this.manager.getAllPrompts();
    }

    getWorkflows(): MCPWorkflow[] {
        return Array.from(this.workflows.values());
    }

    // Private Methods
    private setupManagerEventHandlers(): void {
        this.manager.on('serverAdded', (server) => this.emit('serverAdded', server));
        this.manager.on('serverRemoved', (serverId) => this.emit('serverRemoved', serverId));
        this.manager.on('serverStatusChanged', (serverId, status) => this.emit('serverStatusChanged', serverId, status));
        this.manager.on('toolsUpdated', (serverId, tools) => this.emit('toolsUpdated', serverId, tools));
        this.manager.on('resourcesUpdated', (serverId, resources) => this.emit('resourcesUpdated', serverId, resources));
        this.manager.on('promptsUpdated', (serverId, prompts) => this.emit('promptsUpdated', serverId, prompts));
    }

    private async registerDefaultServers(): Promise<void> {
        const defaultServers: MCPServerConfig[] = [
            {
                name: 'Exa AI',
                url: 'https://api.exa.ai/mcp',
                transport: 'streamable-http',
                enabled: false,
                autoStart: false,
                env: {
                    'EXA_API_KEY': process.env.EXA_API_KEY || ''
                }
            },
            {
                name: 'Firecrawl',
                url: 'https://api.firecrawl.dev/mcp',
                transport: 'streamable-http',
                enabled: false,
                autoStart: false,
                env: {
                    'FIRECRAWL_API_KEY': process.env.FIRECRAWL_API_KEY || ''
                }
            }
        ];

        for (const config of defaultServers) {
            try {
                await this.addServer(config);
                console.log(`📦 Registered default server: ${config.name}`);
            } catch (error) {
                console.warn(`⚠️ Failed to register default server ${config.name}:`, error);
            }
        }
    }

    private async loadWorkflows(): Promise<void> {
        const stored = this.context.globalState.get<MCPWorkflow[]>('mcpWorkflows', []);
        for (const workflow of stored) {
            this.workflows.set(workflow.id, workflow);
        }
    }

    private async saveWorkflows(): Promise<void> {
        const workflows = Array.from(this.workflows.values());
        await this.context.globalState.update('mcpWorkflows', workflows);
    }

    async dispose(): Promise<void> {
        console.log('🧹 Disposing MCP Hub...');
        await this.manager.dispose();
        this.workflows.clear();
        this.executionHistory.length = 0;
        console.log('✅ MCP Hub disposed');
    }
}
