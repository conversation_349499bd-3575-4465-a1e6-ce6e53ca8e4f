// MCP Security and Permission System - 2025 Security Guidelines
import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import {
    MCPPermission,
    MCPSecurityContext,
    MCPServerInstance,
    MCPTool,
    MCPResource,
    MCPPrompt
} from './types';

export interface SecurityPolicy {
    requireExplicitConsent: boolean;
    allowedDomains: string[];
    blockedDomains: string[];
    maxToolExecutionTime: number;
    maxResourceSize: number;
    allowDataTransmission: boolean;
    allowLLMSampling: boolean;
    logAllActivities: boolean;
}

export interface ConsentRequest {
    id: string;
    serverId: string;
    serverName: string;
    type: 'tool' | 'resource' | 'prompt' | 'data_access' | 'llm_sampling';
    target: string;
    description: string;
    riskLevel: 'low' | 'medium' | 'high';
    requestedAt: Date;
    expiresAt?: Date;
    metadata?: Record<string, any>;
}

export interface ConsentResponse {
    requestId: string;
    granted: boolean;
    conditions?: string[];
    expiresAt?: Date;
    respondedAt: Date;
}

export class MCPSecurityManager extends EventEmitter {
    private securityContext: MCPSecurityContext;
    private securityPolicy: SecurityPolicy;
    private pendingConsents = new Map<string, ConsentRequest>();
    private activityLog: Array<{
        timestamp: Date;
        serverId: string;
        action: string;
        target: string;
        granted: boolean;
        riskLevel: string;
    }> = [];

    constructor(private context: vscode.ExtensionContext) {
        super();
        this.loadSecurityContext();
        this.loadSecurityPolicy();
    }

    // Permission Management
    async requestPermission(
        serverId: string,
        serverName: string,
        type: ConsentRequest['type'],
        target: string,
        description: string,
        metadata?: Record<string, any>
    ): Promise<boolean> {
        // Check if permission already exists and is valid
        const existingPermission = this.findExistingPermission(serverId, type, target);
        if (existingPermission && this.isPermissionValid(existingPermission)) {
            return existingPermission.granted;
        }

        // Assess risk level
        const riskLevel = this.assessRiskLevel(type, target, metadata);

        // Create consent request
        const consentRequest: ConsentRequest = {
            id: `consent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            serverId,
            serverName,
            type,
            target,
            description,
            riskLevel,
            requestedAt: new Date(),
            expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
            metadata
        };

        this.pendingConsents.set(consentRequest.id, consentRequest);

        try {
            // Show consent dialog to user
            const response = await this.showConsentDialog(consentRequest);
            
            // Process response
            const granted = await this.processConsentResponse(consentRequest, response);
            
            // Log activity
            this.logActivity(serverId, `${type}_permission`, target, granted, riskLevel);
            
            return granted;
            
        } finally {
            this.pendingConsents.delete(consentRequest.id);
        }
    }

    private async showConsentDialog(request: ConsentRequest): Promise<ConsentResponse> {
        const message = this.formatConsentMessage(request);
        const options = this.getConsentOptions(request);

        const choice = await vscode.window.showWarningMessage(
            message,
            { modal: true },
            ...options
        );

        const granted = choice === 'Allow' || choice === 'Allow Once';
        const expiresAt = choice === 'Allow' ? 
            new Date(Date.now() + 24 * 60 * 60 * 1000) : // 24 hours for persistent
            undefined; // Session only for "Allow Once"

        return {
            requestId: request.id,
            granted,
            expiresAt,
            respondedAt: new Date()
        };
    }

    private formatConsentMessage(request: ConsentRequest): string {
        const riskEmoji = {
            low: '🟢',
            medium: '🟡',
            high: '🔴'
        };

        const typeDescriptions = {
            tool: 'execute a tool',
            resource: 'access a resource',
            prompt: 'use a prompt',
            data_access: 'access your data',
            llm_sampling: 'send data to an LLM'
        };

        const baseMessage = `${riskEmoji[request.riskLevel]} Security Consent Required\n\n` +
            `Server "${request.serverName}" wants to ${typeDescriptions[request.type]}:\n` +
            `"${request.target}"\n\n` +
            `${request.description}\n\n` +
            `Risk Level: ${request.riskLevel.toUpperCase()}`;

        // Add specific warnings based on type and risk level
        if (request.type === 'data_access' && request.riskLevel === 'high') {
            return baseMessage + '\n\n⚠️ WARNING: This will expose your workspace data to an external server.';
        }

        if (request.type === 'llm_sampling' && request.riskLevel === 'high') {
            return baseMessage + '\n\n⚠️ WARNING: This will send your data to an external LLM service.';
        }

        if (request.type === 'tool' && request.riskLevel === 'high') {
            return baseMessage + '\n\n⚠️ WARNING: This tool may perform system-level operations.';
        }

        return baseMessage;
    }

    private getConsentOptions(request: ConsentRequest): string[] {
        const baseOptions = ['Allow Once', 'Deny'];
        
        // Only offer persistent permission for low-risk operations
        if (request.riskLevel === 'low') {
            baseOptions.unshift('Allow');
        }

        return baseOptions;
    }

    private async processConsentResponse(
        request: ConsentRequest,
        response: ConsentResponse
    ): Promise<boolean> {
        if (response.granted) {
            // Create permission record
            const permission: MCPPermission = {
                serverId: request.serverId,
                toolName: request.type === 'tool' ? request.target : undefined,
                resourceUri: request.type === 'resource' ? request.target : undefined,
                promptName: request.type === 'prompt' ? request.target : undefined,
                granted: true,
                grantedAt: response.respondedAt,
                expiresAt: response.expiresAt,
                scope: request.type === 'data_access' || request.type === 'llm_sampling' ? 'server' : request.type
            };

            this.securityContext.permissions.push(permission);
            await this.saveSecurityContext();
            
            this.emit('permissionGranted', permission);
        } else {
            this.emit('permissionDenied', request);
        }

        return response.granted;
    }

    // Risk Assessment
    private assessRiskLevel(
        type: ConsentRequest['type'],
        target: string,
        metadata?: Record<string, any>
    ): 'low' | 'medium' | 'high' {
        // High-risk operations
        if (type === 'data_access' || type === 'llm_sampling') {
            return 'high';
        }

        // Tool-specific risk assessment
        if (type === 'tool') {
            const highRiskTools = [
                'execute_command',
                'file_write',
                'file_delete',
                'system_call',
                'network_request'
            ];
            
            const mediumRiskTools = [
                'file_read',
                'directory_list',
                'web_scrape',
                'api_call'
            ];

            if (highRiskTools.some(tool => target.toLowerCase().includes(tool))) {
                return 'high';
            }
            
            if (mediumRiskTools.some(tool => target.toLowerCase().includes(tool))) {
                return 'medium';
            }
        }

        // Resource-specific risk assessment
        if (type === 'resource') {
            if (target.includes('file://') || target.includes('workspace://')) {
                return 'medium';
            }
            
            if (target.startsWith('http://') || target.startsWith('https://')) {
                return 'low';
            }
        }

        // Default to low risk
        return 'low';
    }

    // Permission Validation
    private findExistingPermission(
        serverId: string,
        type: ConsentRequest['type'],
        target: string
    ): MCPPermission | undefined {
        return this.securityContext.permissions.find(p => 
            p.serverId === serverId &&
            p.granted &&
            (
                (type === 'tool' && p.toolName === target) ||
                (type === 'resource' && p.resourceUri === target) ||
                (type === 'prompt' && p.promptName === target) ||
                (type === 'data_access' && p.scope === 'server') ||
                (type === 'llm_sampling' && p.scope === 'server')
            )
        );
    }

    private isPermissionValid(permission: MCPPermission): boolean {
        if (!permission.granted) {
            return false;
        }

        if (permission.expiresAt && permission.expiresAt < new Date()) {
            return false;
        }

        return true;
    }

    // Security Policy Enforcement
    validateServerConnection(server: MCPServerInstance): boolean {
        const domain = this.extractDomain(server.config.url || '');
        
        // Check blocked domains
        if (this.securityPolicy.blockedDomains.includes(domain)) {
            return false;
        }

        // Check allowed domains (if specified)
        if (this.securityPolicy.allowedDomains.length > 0) {
            return this.securityPolicy.allowedDomains.includes(domain);
        }

        return true;
    }

    validateToolExecution(serverId: string, tool: MCPTool): boolean {
        // Check if data transmission is allowed
        if (!this.securityPolicy.allowDataTransmission) {
            const dataTransmissionTools = ['web_search', 'api_call', 'upload', 'send'];
            if (dataTransmissionTools.some(t => tool.name.toLowerCase().includes(t))) {
                return false;
            }
        }

        return true;
    }

    // Activity Logging
    private logActivity(
        serverId: string,
        action: string,
        target: string,
        granted: boolean,
        riskLevel: string
    ): void {
        if (!this.securityPolicy.logAllActivities) {
            return;
        }

        this.activityLog.push({
            timestamp: new Date(),
            serverId,
            action,
            target,
            granted,
            riskLevel
        });

        // Keep only last 1000 entries
        if (this.activityLog.length > 1000) {
            this.activityLog = this.activityLog.slice(-1000);
        }

        this.emit('activityLogged', this.activityLog[this.activityLog.length - 1]);
    }

    // Permission Management
    async revokePermission(serverId: string, scope: string, target?: string): Promise<void> {
        const index = this.securityContext.permissions.findIndex(p =>
            p.serverId === serverId &&
            p.scope === scope &&
            (
                !target ||
                p.toolName === target ||
                p.resourceUri === target ||
                p.promptName === target
            )
        );

        if (index !== -1) {
            this.securityContext.permissions.splice(index, 1);
            await this.saveSecurityContext();
            this.emit('permissionRevoked', { serverId, scope, target });
        }
    }

    async revokeAllPermissions(serverId: string): Promise<void> {
        const initialLength = this.securityContext.permissions.length;
        this.securityContext.permissions = this.securityContext.permissions.filter(
            p => p.serverId !== serverId
        );
        
        if (this.securityContext.permissions.length < initialLength) {
            await this.saveSecurityContext();
            this.emit('allPermissionsRevoked', serverId);
        }
    }

    // Getters
    getPermissions(serverId?: string): MCPPermission[] {
        if (serverId) {
            return this.securityContext.permissions.filter(p => p.serverId === serverId);
        }
        return [...this.securityContext.permissions];
    }

    getActivityLog(limit: number = 100): typeof this.activityLog {
        return this.activityLog.slice(-limit);
    }

    getSecurityPolicy(): SecurityPolicy {
        return { ...this.securityPolicy };
    }

    async updateSecurityPolicy(updates: Partial<SecurityPolicy>): Promise<void> {
        this.securityPolicy = { ...this.securityPolicy, ...updates };
        await this.context.globalState.update('mcpSecurityPolicy', this.securityPolicy);
        this.emit('securityPolicyUpdated', this.securityPolicy);
    }

    // Private Helper Methods
    private extractDomain(url: string): string {
        try {
            const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
            return urlObj.hostname;
        } catch {
            return '';
        }
    }

    private loadSecurityContext(): void {
        const stored = this.context.globalState.get<MCPSecurityContext>('mcpSecurityContext');
        this.securityContext = stored || {
            userId: 'default',
            permissions: [],
            requireExplicitConsent: true,
            allowedDomains: [],
            blockedDomains: []
        };
    }

    private async saveSecurityContext(): Promise<void> {
        await this.context.globalState.update('mcpSecurityContext', this.securityContext);
    }

    private loadSecurityPolicy(): void {
        const stored = this.context.globalState.get<SecurityPolicy>('mcpSecurityPolicy');
        this.securityPolicy = stored || {
            requireExplicitConsent: true,
            allowedDomains: [],
            blockedDomains: [],
            maxToolExecutionTime: 30000,
            maxResourceSize: 10 * 1024 * 1024, // 10MB
            allowDataTransmission: true,
            allowLLMSampling: true,
            logAllActivities: true
        };
    }

    async dispose(): Promise<void> {
        this.pendingConsents.clear();
        this.activityLog.length = 0;
    }
}
