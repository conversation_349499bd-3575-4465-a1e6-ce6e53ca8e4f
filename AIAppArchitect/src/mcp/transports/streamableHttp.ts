// MCP Streamable HTTP Transport Implementation - Protocol 2025-06-18
import { EventEmitter } from 'events';
// Use built-in fetch in Node.js 18+
// import fetch from 'node-fetch';
import { JsonRpcRequest, JsonRpcResponse } from '../types';
import { MCPTransport } from '../client';

export interface StreamableHttpTransportConfig {
    url: string;
    headers?: Record<string, string>;
    timeout?: number;
    sessionId?: string;
}

export class StreamableHttpTransport extends EventEmitter implements MCPTransport {
    private sessionId: string | null = null;
    private isConnected = false;
    private eventSource: EventSource | null = null;
    private baseUrl: string;
    private headers: Record<string, string>;

    constructor(private config: StreamableHttpTransportConfig) {
        super();
        this.baseUrl = config.url;
        this.headers = {
            'Content-Type': 'application/json',
            ...config.headers
        };
    }

    async connect(): Promise<void> {
        if (this.isConnected) {
            throw new Error('Already connected');
        }

        // For streamable HTTP, we don't need to establish a persistent connection
        // The connection is established on first request
        this.isConnected = true;
    }

    async send(message: JsonRpcRequest): Promise<JsonRpcResponse> {
        if (!this.isConnected) {
            throw new Error('Not connected');
        }

        const headers = { ...this.headers };
        if (this.sessionId) {
            headers['mcp-session-id'] = this.sessionId;
        }

        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers,
                body: JSON.stringify(message),
                // timeout: this.config.timeout || 30000 // Not supported in standard fetch
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Extract session ID from response headers if present
            const responseSessionId = response.headers.get('mcp-session-id');
            if (responseSessionId && !this.sessionId) {
                this.sessionId = responseSessionId;
                this.startEventStream();
            }

            const result = await response.json() as JsonRpcResponse;
            return result;

        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }

    async close(): Promise<void> {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        if (this.sessionId) {
            // Send DELETE request to terminate session
            try {
                await fetch(this.baseUrl, {
                    method: 'DELETE',
                    headers: {
                        'mcp-session-id': this.sessionId
                    }
                });
            } catch (error) {
                console.warn('Failed to terminate MCP session:', error);
            }
            this.sessionId = null;
        }

        this.isConnected = false;
        this.emit('close');
    }

    onMessage(handler: (message: JsonRpcResponse) => void): void {
        this.on('message', handler);
    }

    onError(handler: (error: Error) => void): void {
        this.on('error', handler);
    }

    onClose(handler: () => void): void {
        this.on('close', handler);
    }

    private startEventStream(): void {
        if (!this.sessionId || this.eventSource) {
            return;
        }

        // Use EventSource for server-sent events (notifications)
        const eventSourceUrl = new URL(this.baseUrl);
        eventSourceUrl.searchParams.set('sessionId', this.sessionId);

        try {
            // Note: In a real VS Code extension, you might need to use a different SSE implementation
            // as EventSource might not be available in the Node.js environment
            this.eventSource = new EventSource(eventSourceUrl.toString(), {
                headers: {
                    'mcp-session-id': this.sessionId
                }
            } as any);

            this.eventSource.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.emit('message', message);
                } catch (error) {
                    console.error('Failed to parse SSE message:', error);
                    this.emit('error', new Error(`Failed to parse SSE message: ${error}`));
                }
            };

            this.eventSource.onerror = (error) => {
                console.error('EventSource error:', error);
                this.emit('error', new Error('EventSource connection error'));
            };

        } catch (error) {
            console.error('Failed to create EventSource:', error);
            this.emit('error', error as Error);
        }
    }
}

// Alternative implementation using polling for environments without EventSource
export class StreamableHttpPollingTransport extends EventEmitter implements MCPTransport {
    private sessionId: string | null = null;
    private isConnected = false;
    private pollingInterval: NodeJS.Timeout | null = null;
    private baseUrl: string;
    private headers: Record<string, string>;

    constructor(private config: StreamableHttpTransportConfig) {
        super();
        this.baseUrl = config.url;
        this.headers = {
            'Content-Type': 'application/json',
            ...config.headers
        };
    }

    async connect(): Promise<void> {
        if (this.isConnected) {
            throw new Error('Already connected');
        }
        this.isConnected = true;
    }

    async send(message: JsonRpcRequest): Promise<JsonRpcResponse> {
        if (!this.isConnected) {
            throw new Error('Not connected');
        }

        const headers = { ...this.headers };
        if (this.sessionId) {
            headers['mcp-session-id'] = this.sessionId;
        }

        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers,
                body: JSON.stringify(message),
                // timeout: this.config.timeout || 30000 // Not supported in standard fetch
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseSessionId = response.headers.get('mcp-session-id');
            if (responseSessionId && !this.sessionId) {
                this.sessionId = responseSessionId;
                this.startPolling();
            }

            const result = await response.json() as JsonRpcResponse;
            return result;

        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }

    async close(): Promise<void> {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }

        if (this.sessionId) {
            try {
                await fetch(this.baseUrl, {
                    method: 'DELETE',
                    headers: {
                        'mcp-session-id': this.sessionId
                    }
                });
            } catch (error) {
                console.warn('Failed to terminate MCP session:', error);
            }
            this.sessionId = null;
        }

        this.isConnected = false;
        this.emit('close');
    }

    onMessage(handler: (message: JsonRpcResponse) => void): void {
        this.on('message', handler);
    }

    onError(handler: (error: Error) => void): void {
        this.on('error', handler);
    }

    onClose(handler: () => void): void {
        this.on('close', handler);
    }

    private startPolling(): void {
        if (!this.sessionId || this.pollingInterval) {
            return;
        }

        this.pollingInterval = setInterval(async () => {
            try {
                const response = await fetch(this.baseUrl, {
                    method: 'GET',
                    headers: {
                        'mcp-session-id': this.sessionId!
                    }
                });

                if (response.ok) {
                    const messages = await response.json();
                    if (Array.isArray(messages)) {
                        messages.forEach(message => this.emit('message', message));
                    }
                }
            } catch (error) {
                console.error('Polling error:', error);
            }
        }, 1000); // Poll every second
    }
}
