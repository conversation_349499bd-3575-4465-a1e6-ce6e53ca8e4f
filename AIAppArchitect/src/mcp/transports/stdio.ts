// MCP STDIO Transport Implementation
import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import { JsonRpcRequest, JsonRpcResponse } from '../types';
import { MCPTransport } from '../client';

export interface StdioTransportConfig {
    command: string;
    args?: string[];
    env?: Record<string, string>;
    cwd?: string;
    timeout?: number;
}

export class StdioTransport extends EventEmitter implements MCPTransport {
    private process: ChildProcess | null = null;
    private messageBuffer = '';
    private isConnected = false;

    constructor(private config: StdioTransportConfig) {
        super();
    }

    async connect(): Promise<void> {
        if (this.process) {
            throw new Error('Already connected');
        }

        return new Promise((resolve, reject) => {
            const env = { ...process.env, ...this.config.env };
            
            this.process = spawn(this.config.command, this.config.args || [], {
                env,
                cwd: this.config.cwd,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            this.process.on('error', (error) => {
                this.emit('error', error);
                reject(error);
            });

            this.process.on('exit', (code, signal) => {
                this.isConnected = false;
                this.emit('close');
            });

            if (this.process.stdout) {
                this.process.stdout.on('data', (data) => {
                    this.handleData(data.toString());
                });
            }

            if (this.process.stderr) {
                this.process.stderr.on('data', (data) => {
                    console.error('MCP Server stderr:', data.toString());
                });
            }

            // Give the process a moment to start
            setTimeout(() => {
                if (this.process && !this.process.killed) {
                    this.isConnected = true;
                    resolve();
                } else {
                    reject(new Error('Process failed to start'));
                }
            }, 100);
        });
    }

    async send(message: JsonRpcRequest): Promise<JsonRpcResponse> {
        if (!this.process || !this.process.stdin || !this.isConnected) {
            throw new Error('Not connected');
        }

        const messageStr = JSON.stringify(message) + '\n';
        
        return new Promise((resolve, reject) => {
            this.process!.stdin!.write(messageStr, (error) => {
                if (error) {
                    reject(error);
                } else {
                    // For stdio transport, we don't wait for response here
                    // The response will come through the data handler
                    resolve({} as JsonRpcResponse);
                }
            });
        });
    }

    async close(): Promise<void> {
        if (this.process) {
            this.isConnected = false;
            this.process.kill();
            this.process = null;
        }
    }

    onMessage(handler: (message: JsonRpcResponse) => void): void {
        this.on('message', handler);
    }

    onError(handler: (error: Error) => void): void {
        this.on('error', handler);
    }

    onClose(handler: () => void): void {
        this.on('close', handler);
    }

    private handleData(data: string): void {
        this.messageBuffer += data;
        
        // Process complete messages (separated by newlines)
        const lines = this.messageBuffer.split('\n');
        this.messageBuffer = lines.pop() || ''; // Keep the incomplete line in buffer

        for (const line of lines) {
            if (line.trim()) {
                try {
                    const message = JSON.parse(line);
                    this.emit('message', message);
                } catch (error) {
                    console.error('Failed to parse MCP message:', error, 'Line:', line);
                    this.emit('error', new Error(`Failed to parse message: ${error}`));
                }
            }
        }
    }
}
