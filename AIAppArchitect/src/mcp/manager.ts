// MCP Manager - Orchestrates MCP servers and connections
import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import { MCPClient } from './client';
import { StdioTransport } from './transports/stdio';
import { StreamableHttpTransport, StreamableHttpPollingTransport } from './transports/streamableHttp';
import {
    MCPServerConfig,
    MCPServerInstance,
    MCPConnectionStatus,
    MCPTool,
    MCPResource,
    MCPPrompt,
    MCPPermission,
    MCPSecurityContext,
    CallToolResult,
    ReadResourceResult,
    GetPromptResult
} from './types';

export class MCPManager extends EventEmitter {
    private servers = new Map<string, MCPServerInstance>();
    private clients = new Map<string, MCPClient>();
    private securityContext: MCPSecurityContext;
    private readonly context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        super();
        this.context = context;
        this.securityContext = {
            userId: 'default',
            permissions: [],
            requireExplicitConsent: true,
            allowedDomains: [],
            blockedDomains: []
        };
        this.loadConfiguration();
    }

    async initialize(): Promise<void> {
        console.log('🔌 Initializing MCP Manager...');
        
        // Load server configurations from settings
        const configs = this.getServerConfigurations();
        
        for (const config of configs) {
            if (config.enabled && config.autoStart !== false) {
                try {
                    await this.addServer(config);
                } catch (error) {
                    console.error(`Failed to initialize MCP server ${config.name}:`, error);
                }
            }
        }

        console.log(`✅ MCP Manager initialized with ${this.servers.size} servers`);
    }

    async addServer(config: MCPServerConfig): Promise<string> {
        const serverId = `${config.name}-${Date.now()}`;
        
        const serverInstance: MCPServerInstance = {
            id: serverId,
            config,
            status: MCPConnectionStatus.Disconnected,
            tools: [],
            resources: [],
            prompts: []
        };

        this.servers.set(serverId, serverInstance);
        this.emit('serverAdded', serverInstance);

        if (config.enabled) {
            await this.connectServer(serverId);
        }

        return serverId;
    }

    async removeServer(serverId: string): Promise<void> {
        const server = this.servers.get(serverId);
        if (!server) {
            throw new Error(`Server ${serverId} not found`);
        }

        await this.disconnectServer(serverId);
        this.servers.delete(serverId);
        this.emit('serverRemoved', serverId);
    }

    async connectServer(serverId: string): Promise<void> {
        const server = this.servers.get(serverId);
        if (!server) {
            throw new Error(`Server ${serverId} not found`);
        }

        if (server.status === MCPConnectionStatus.Connected) {
            return;
        }

        try {
            server.status = MCPConnectionStatus.Connecting;
            this.updateServerStatus(serverId, server.status);

            const client = new MCPClient(
                { name: 'Aizen AI Extension', version: '2.0.0' },
                {
                    tools: { listChanged: true },
                    resources: { subscribe: true, listChanged: true },
                    prompts: { listChanged: true },
                    sampling: {},
                    elicitation: {}
                }
            );

            // Set up client event handlers
            client.on('connected', async (info) => {
                server.capabilities = info.capabilities;
                server.connectedAt = new Date();
                server.status = MCPConnectionStatus.Connected;
                this.updateServerStatus(serverId, server.status);
                
                // Load available tools, resources, and prompts
                await this.refreshServerCapabilities(serverId);
            });

            client.on('error', (error) => {
                server.lastError = error.message;
                server.status = MCPConnectionStatus.Error;
                this.updateServerStatus(serverId, server.status);
            });

            client.on('disconnected', () => {
                server.status = MCPConnectionStatus.Disconnected;
                this.updateServerStatus(serverId, server.status);
            });

            client.on('toolsChanged', () => this.refreshServerTools(serverId));
            client.on('resourcesChanged', () => this.refreshServerResources(serverId));
            client.on('promptsChanged', () => this.refreshServerPrompts(serverId));

            // Create appropriate transport
            let transport;
            switch (server.config.transport) {
                case 'stdio':
                    if (!server.config.command) {
                        throw new Error('Command required for stdio transport');
                    }
                    transport = new StdioTransport({
                        command: server.config.command,
                        args: server.config.args,
                        env: server.config.env,
                        timeout: server.config.timeout
                    });
                    await transport.connect();
                    break;

                case 'streamable-http':
                    if (!server.config.url) {
                        throw new Error('URL required for streamable-http transport');
                    }
                    transport = new StreamableHttpPollingTransport({
                        url: server.config.url,
                        headers: server.config.env,
                        timeout: server.config.timeout
                    });
                    break;

                default:
                    throw new Error(`Unsupported transport: ${server.config.transport}`);
            }

            await client.connect(transport);
            this.clients.set(serverId, client);

        } catch (error) {
            server.status = MCPConnectionStatus.Error;
            server.lastError = error instanceof Error ? error.message : String(error);
            this.updateServerStatus(serverId, server.status);
            throw error;
        }
    }

    async disconnectServer(serverId: string): Promise<void> {
        const client = this.clients.get(serverId);
        if (client) {
            await client.disconnect();
            this.clients.delete(serverId);
        }

        const server = this.servers.get(serverId);
        if (server) {
            server.status = MCPConnectionStatus.Disconnected;
            this.updateServerStatus(serverId, server.status);
        }
    }

    async callTool(serverId: string, toolName: string, arguments_?: Record<string, any>): Promise<CallToolResult> {
        // Check permissions
        if (!await this.checkToolPermission(serverId, toolName)) {
            throw new Error(`Permission denied for tool ${toolName} on server ${serverId}`);
        }

        const client = this.clients.get(serverId);
        if (!client || !client.isConnected) {
            throw new Error(`Server ${serverId} is not connected`);
        }

        try {
            const result = await client.callTool(toolName, arguments_);
            this.updateServerActivity(serverId);
            return result;
        } catch (error) {
            console.error(`Tool call failed: ${toolName}`, error);
            throw error;
        }
    }

    async readResource(serverId: string, uri: string): Promise<ReadResourceResult> {
        // Check permissions
        if (!await this.checkResourcePermission(serverId, uri)) {
            throw new Error(`Permission denied for resource ${uri} on server ${serverId}`);
        }

        const client = this.clients.get(serverId);
        if (!client || !client.isConnected) {
            throw new Error(`Server ${serverId} is not connected`);
        }

        try {
            const result = await client.readResource(uri);
            this.updateServerActivity(serverId);
            return result;
        } catch (error) {
            console.error(`Resource read failed: ${uri}`, error);
            throw error;
        }
    }

    async getPrompt(serverId: string, promptName: string, arguments_?: Record<string, any>): Promise<GetPromptResult> {
        // Check permissions
        if (!await this.checkPromptPermission(serverId, promptName)) {
            throw new Error(`Permission denied for prompt ${promptName} on server ${serverId}`);
        }

        const client = this.clients.get(serverId);
        if (!client || !client.isConnected) {
            throw new Error(`Server ${serverId} is not connected`);
        }

        try {
            const result = await client.getPrompt(promptName, arguments_);
            this.updateServerActivity(serverId);
            return result;
        } catch (error) {
            console.error(`Prompt get failed: ${promptName}`, error);
            throw error;
        }
    }

    // Permission management
    async requestPermission(serverId: string, scope: 'tool' | 'resource' | 'prompt' | 'server', target?: string): Promise<boolean> {
        if (!this.securityContext.requireExplicitConsent) {
            return true;
        }

        const server = this.servers.get(serverId);
        if (!server) {
            return false;
        }

        // Show permission dialog to user
        const message = this.formatPermissionMessage(server, scope, target);
        const choice = await vscode.window.showWarningMessage(
            message,
            { modal: true },
            'Allow',
            'Deny'
        );

        const granted = choice === 'Allow';
        
        if (granted) {
            const permission: MCPPermission = {
                serverId,
                toolName: scope === 'tool' ? target : undefined,
                resourceUri: scope === 'resource' ? target : undefined,
                promptName: scope === 'prompt' ? target : undefined,
                granted: true,
                grantedAt: new Date(),
                scope
            };
            this.securityContext.permissions.push(permission);
            await this.saveSecurityContext();
        }

        return granted;
    }

    private async checkToolPermission(serverId: string, toolName: string): Promise<boolean> {
        return this.checkPermission(serverId, 'tool', toolName);
    }

    private async checkResourcePermission(serverId: string, uri: string): Promise<boolean> {
        return this.checkPermission(serverId, 'resource', uri);
    }

    private async checkPromptPermission(serverId: string, promptName: string): Promise<boolean> {
        return this.checkPermission(serverId, 'prompt', promptName);
    }

    private async checkPermission(serverId: string, scope: 'tool' | 'resource' | 'prompt' | 'server', target?: string): Promise<boolean> {
        // Check existing permissions
        const existingPermission = this.securityContext.permissions.find(p => 
            p.serverId === serverId && 
            p.scope === scope && 
            (scope === 'server' || 
             (scope === 'tool' && p.toolName === target) ||
             (scope === 'resource' && p.resourceUri === target) ||
             (scope === 'prompt' && p.promptName === target))
        );

        if (existingPermission && existingPermission.granted) {
            // Check if permission has expired
            if (existingPermission.expiresAt && existingPermission.expiresAt < new Date()) {
                return false;
            }
            return true;
        }

        // Request new permission
        return await this.requestPermission(serverId, scope, target);
    }

    // Getters
    getServers(): MCPServerInstance[] {
        return Array.from(this.servers.values());
    }

    getServer(serverId: string): MCPServerInstance | undefined {
        return this.servers.get(serverId);
    }

    getConnectedServers(): MCPServerInstance[] {
        return this.getServers().filter(s => s.status === MCPConnectionStatus.Connected);
    }

    getAllTools(): Array<{ serverId: string; tool: MCPTool }> {
        const tools: Array<{ serverId: string; tool: MCPTool }> = [];
        for (const [serverId, server] of this.servers) {
            if (server.tools) {
                server.tools.forEach(tool => tools.push({ serverId, tool }));
            }
        }
        return tools;
    }

    getAllResources(): Array<{ serverId: string; resource: MCPResource }> {
        const resources: Array<{ serverId: string; resource: MCPResource }> = [];
        for (const [serverId, server] of this.servers) {
            if (server.resources) {
                server.resources.forEach(resource => resources.push({ serverId, resource }));
            }
        }
        return resources;
    }

    getAllPrompts(): Array<{ serverId: string; prompt: MCPPrompt }> {
        const prompts: Array<{ serverId: string; prompt: MCPPrompt }> = [];
        for (const [serverId, server] of this.servers) {
            if (server.prompts) {
                server.prompts.forEach(prompt => prompts.push({ serverId, prompt }));
            }
        }
        return prompts;
    }

    // Private helper methods
    private getServerConfigurations(): MCPServerConfig[] {
        const config = vscode.workspace.getConfiguration('aizen.mcp');
        return config.get('servers', []);
    }

    private async refreshServerCapabilities(serverId: string): Promise<void> {
        await Promise.all([
            this.refreshServerTools(serverId),
            this.refreshServerResources(serverId),
            this.refreshServerPrompts(serverId)
        ]);
    }

    private async refreshServerTools(serverId: string): Promise<void> {
        const client = this.clients.get(serverId);
        const server = this.servers.get(serverId);
        
        if (client && server && client.isConnected) {
            try {
                const result = await client.listTools();
                server.tools = result.tools;
                this.emit('toolsUpdated', serverId, result.tools);
            } catch (error) {
                console.error(`Failed to refresh tools for server ${serverId}:`, error);
            }
        }
    }

    private async refreshServerResources(serverId: string): Promise<void> {
        const client = this.clients.get(serverId);
        const server = this.servers.get(serverId);
        
        if (client && server && client.isConnected) {
            try {
                const result = await client.listResources();
                server.resources = result.resources;
                this.emit('resourcesUpdated', serverId, result.resources);
            } catch (error) {
                console.error(`Failed to refresh resources for server ${serverId}:`, error);
            }
        }
    }

    private async refreshServerPrompts(serverId: string): Promise<void> {
        const client = this.clients.get(serverId);
        const server = this.servers.get(serverId);
        
        if (client && server && client.isConnected) {
            try {
                const result = await client.listPrompts();
                server.prompts = result.prompts;
                this.emit('promptsUpdated', serverId, result.prompts);
            } catch (error) {
                console.error(`Failed to refresh prompts for server ${serverId}:`, error);
            }
        }
    }

    private updateServerStatus(serverId: string, status: MCPConnectionStatus): void {
        const server = this.servers.get(serverId);
        if (server) {
            server.status = status;
            this.emit('serverStatusChanged', serverId, status);
        }
    }

    private updateServerActivity(serverId: string): void {
        const server = this.servers.get(serverId);
        if (server) {
            server.lastActivity = new Date();
        }
    }

    private formatPermissionMessage(server: MCPServerInstance, scope: string, target?: string): string {
        const serverName = server.config.name;
        switch (scope) {
            case 'tool':
                return `Allow "${serverName}" to execute tool "${target}"?`;
            case 'resource':
                return `Allow "${serverName}" to access resource "${target}"?`;
            case 'prompt':
                return `Allow "${serverName}" to use prompt "${target}"?`;
            case 'server':
                return `Allow connection to MCP server "${serverName}"?`;
            default:
                return `Allow "${serverName}" to perform this action?`;
        }
    }

    private loadConfiguration(): void {
        // Load security context from extension storage
        const stored = this.context.globalState.get<MCPSecurityContext>('mcpSecurityContext');
        if (stored) {
            this.securityContext = stored;
        }
    }

    private async saveSecurityContext(): Promise<void> {
        await this.context.globalState.update('mcpSecurityContext', this.securityContext);
    }

    async dispose(): Promise<void> {
        console.log('🧹 Disposing MCP Manager...');
        
        // Disconnect all servers
        const disconnectPromises = Array.from(this.servers.keys()).map(serverId => 
            this.disconnectServer(serverId).catch(error => 
                console.error(`Error disconnecting server ${serverId}:`, error)
            )
        );
        
        await Promise.all(disconnectPromises);
        
        this.servers.clear();
        this.clients.clear();
        
        console.log('✅ MCP Manager disposed');
    }
}
