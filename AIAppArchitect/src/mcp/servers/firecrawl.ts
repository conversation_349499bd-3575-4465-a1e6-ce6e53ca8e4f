// Firecrawl MCP Server Integration
import { MCPServerConfig } from '../types';

export const FirecrawlMCPServerConfig: MCPServerConfig = {
    name: 'Firecrawl',
    url: 'https://mcp.firecrawl.dev/fc-73581888d5374a1a99893178925cc8bb/sse',
    transport: 'stdio',
    enabled: true,
    autoStart: true,
    timeout: 120000, // Longer timeout for crawling operations
    retryAttempts: 5,
    retryDelay: 3000,
    env: {}
};

export interface FirecrawlScrapeParams {
    url: string;
    formats?: ('markdown' | 'html' | 'rawHtml' | 'screenshot' | 'links' | 'screenshot@fullPage' | 'extract')[];
    onlyMainContent?: boolean;
    removeBase64Images?: boolean;
    includeTags?: string[];
    excludeTags?: string[];
    waitFor?: number;
    timeout?: number;
    mobile?: boolean;
    skipTlsVerification?: boolean;
    extract?: {
        schema?: object;
        prompt?: string;
        systemPrompt?: string;
    };
    actions?: Array<{
        type: 'wait' | 'click' | 'screenshot' | 'write' | 'press' | 'scroll' | 'scrape' | 'executeJavascript';
        selector?: string;
        text?: string;
        key?: string;
        milliseconds?: number;
        direction?: 'up' | 'down';
        fullPage?: boolean;
        script?: string;
    }>;
    location?: {
        country?: string;
        languages?: string[];
    };
}

export interface FirecrawlMapParams {
    url: string;
    search?: string;
    ignoreSitemap?: boolean;
    sitemapOnly?: boolean;
    includeSubdomains?: boolean;
    limit?: number;
}

export interface FirecrawlCrawlParams {
    url: string;
    maxDepth?: number;
    limit?: number;
    allowBackwardLinks?: boolean;
    allowExternalLinks?: boolean;
    deduplicateSimilarURLs?: boolean;
    excludePaths?: string[];
    includePaths?: string[];
    ignoreQueryParameters?: boolean;
    ignoreSitemap?: boolean;
    scrapeOptions?: Omit<FirecrawlScrapeParams, 'url'>;
    webhook?: string | {
        url: string;
        headers?: Record<string, string>;
    };
}

export interface FirecrawlSearchParams {
    query: string;
    limit?: number;
    lang?: string;
    country?: string;
    tbs?: string;
    filter?: string;
    scrapeOptions?: {
        formats?: ('markdown' | 'html' | 'rawHtml')[];
        onlyMainContent?: boolean;
        waitFor?: number;
    };
    location?: {
        country?: string;
        languages?: string[];
    };
}

export interface FirecrawlExtractParams {
    urls: string[];
    prompt?: string;
    systemPrompt?: string;
    schema?: object;
    allowExternalLinks?: boolean;
    enableWebSearch?: boolean;
    includeSubdomains?: boolean;
}

export interface FirecrawlDeepResearchParams {
    query: string;
    maxDepth?: number;
    timeLimit?: number;
    maxUrls?: number;
}

// Firecrawl MCP Tool Definitions
export const FirecrawlMCPTools = [
    {
        name: 'firecrawl_scrape',
        description: 'Scrape content from a single URL with advanced options. Best for single page content extraction.',
        inputSchema: {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: 'The URL to scrape'
                },
                formats: {
                    type: 'array',
                    items: {
                        type: 'string',
                        enum: ['markdown', 'html', 'rawHtml', 'screenshot', 'links', 'screenshot@fullPage', 'extract']
                    },
                    description: 'Content formats to extract (default: [\'markdown\'])',
                    default: ['markdown']
                },
                onlyMainContent: {
                    type: 'boolean',
                    description: 'Extract only the main content, filtering out navigation, footers, etc.'
                },
                removeBase64Images: {
                    type: 'boolean',
                    description: 'Remove base64 encoded images from output'
                },
                includeTags: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'HTML tags to specifically include in extraction'
                },
                excludeTags: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'HTML tags to exclude from extraction'
                },
                waitFor: {
                    type: 'number',
                    description: 'Time in milliseconds to wait for dynamic content to load'
                },
                timeout: {
                    type: 'number',
                    description: 'Maximum time in milliseconds to wait for the page to load'
                },
                mobile: {
                    type: 'boolean',
                    description: 'Use mobile viewport'
                },
                extract: {
                    type: 'object',
                    description: 'Configuration for structured data extraction',
                    properties: {
                        schema: {
                            type: 'object',
                            description: 'Schema for structured data extraction'
                        },
                        prompt: {
                            type: 'string',
                            description: 'User prompt for LLM extraction'
                        },
                        systemPrompt: {
                            type: 'string',
                            description: 'System prompt for LLM extraction'
                        }
                    }
                }
            },
            required: ['url']
        }
    },
    {
        name: 'firecrawl_map',
        description: 'Map a website to discover all indexed URLs on the site. Best for discovering URLs before deciding what to scrape.',
        inputSchema: {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: 'Starting URL for URL discovery'
                },
                search: {
                    type: 'string',
                    description: 'Optional search term to filter URLs'
                },
                ignoreSitemap: {
                    type: 'boolean',
                    description: 'Skip sitemap.xml discovery and only use HTML links'
                },
                sitemapOnly: {
                    type: 'boolean',
                    description: 'Only use sitemap.xml for discovery, ignore HTML links'
                },
                includeSubdomains: {
                    type: 'boolean',
                    description: 'Include URLs from subdomains in results'
                },
                limit: {
                    type: 'number',
                    description: 'Maximum number of URLs to return'
                }
            },
            required: ['url']
        }
    },
    {
        name: 'firecrawl_crawl',
        description: 'Start an asynchronous crawl job on a website and extract content from all pages. Best for comprehensive coverage.',
        inputSchema: {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: 'Starting URL for the crawl'
                },
                maxDepth: {
                    type: 'number',
                    description: 'Maximum link depth to crawl'
                },
                limit: {
                    type: 'number',
                    description: 'Maximum number of pages to crawl'
                },
                allowBackwardLinks: {
                    type: 'boolean',
                    description: 'Allow crawling links that point to parent directories'
                },
                allowExternalLinks: {
                    type: 'boolean',
                    description: 'Allow crawling links to external domains'
                },
                deduplicateSimilarURLs: {
                    type: 'boolean',
                    description: 'Remove similar URLs during crawl'
                },
                excludePaths: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'URL paths to exclude from crawling'
                },
                includePaths: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Only crawl these URL paths'
                },
                ignoreQueryParameters: {
                    type: 'boolean',
                    description: 'Ignore query parameters when comparing URLs'
                },
                ignoreSitemap: {
                    type: 'boolean',
                    description: 'Skip sitemap.xml discovery'
                },
                webhook: {
                    oneOf: [
                        {
                            type: 'string',
                            description: 'Webhook URL to notify when crawl is complete'
                        },
                        {
                            type: 'object',
                            properties: {
                                url: { type: 'string' },
                                headers: { type: 'object' }
                            },
                            required: ['url']
                        }
                    ]
                }
            },
            required: ['url']
        }
    },
    {
        name: 'firecrawl_check_crawl_status',
        description: 'Check the status of a crawl job.',
        inputSchema: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                    description: 'Crawl job ID to check'
                }
            },
            required: ['id']
        }
    },
    {
        name: 'firecrawl_search',
        description: 'Search the web and optionally extract content from search results. Best for finding specific information across multiple websites.',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Search query string'
                },
                limit: {
                    type: 'number',
                    description: 'Maximum number of results to return (default: 5)',
                    default: 5
                },
                lang: {
                    type: 'string',
                    description: 'Language code for search results (default: en)',
                    default: 'en'
                },
                country: {
                    type: 'string',
                    description: 'Country code for search results (default: us)',
                    default: 'us'
                },
                tbs: {
                    type: 'string',
                    description: 'Time-based search filter'
                },
                filter: {
                    type: 'string',
                    description: 'Search filter'
                },
                scrapeOptions: {
                    type: 'object',
                    description: 'Options for scraping search results',
                    properties: {
                        formats: {
                            type: 'array',
                            items: {
                                type: 'string',
                                enum: ['markdown', 'html', 'rawHtml']
                            },
                            description: 'Content formats to extract from search results'
                        },
                        onlyMainContent: {
                            type: 'boolean',
                            description: 'Extract only the main content from results'
                        },
                        waitFor: {
                            type: 'number',
                            description: 'Time in milliseconds to wait for dynamic content'
                        }
                    }
                }
            },
            required: ['query']
        }
    },
    {
        name: 'firecrawl_extract',
        description: 'Extract structured information from web pages using LLM capabilities. Best for extracting specific structured data.',
        inputSchema: {
            type: 'object',
            properties: {
                urls: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'List of URLs to extract information from'
                },
                prompt: {
                    type: 'string',
                    description: 'Prompt for the LLM extraction'
                },
                systemPrompt: {
                    type: 'string',
                    description: 'System prompt for LLM extraction'
                },
                schema: {
                    type: 'object',
                    description: 'JSON schema for structured data extraction'
                },
                allowExternalLinks: {
                    type: 'boolean',
                    description: 'Allow extraction from external links'
                },
                enableWebSearch: {
                    type: 'boolean',
                    description: 'Enable web search for additional context'
                },
                includeSubdomains: {
                    type: 'boolean',
                    description: 'Include subdomains in extraction'
                }
            },
            required: ['urls']
        }
    },
    {
        name: 'firecrawl_deep_research',
        description: 'Conduct deep web research on a query using intelligent crawling, search, and LLM analysis. Best for complex research questions.',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'The research question or topic to explore'
                },
                maxDepth: {
                    type: 'number',
                    description: 'Maximum depth of research iterations (1-10)',
                    minimum: 1,
                    maximum: 10
                },
                timeLimit: {
                    type: 'number',
                    description: 'Time limit in seconds (30-300)',
                    minimum: 30,
                    maximum: 300
                },
                maxUrls: {
                    type: 'number',
                    description: 'Maximum number of URLs to analyze (1-1000)',
                    minimum: 1,
                    maximum: 1000
                }
            },
            required: ['query']
        }
    },
    {
        name: 'firecrawl_generate_llmstxt',
        description: 'Generate a standardized llms.txt file for a given domain. Best for creating machine-readable permission guidelines for AI models.',
        inputSchema: {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: 'The URL to generate LLMs.txt from'
                },
                maxUrls: {
                    type: 'number',
                    description: 'Maximum number of URLs to process (1-100, default: 10)',
                    minimum: 1,
                    maximum: 100,
                    default: 10
                },
                showFullText: {
                    type: 'boolean',
                    description: 'Whether to show the full LLMs-full.txt in the response'
                }
            },
            required: ['url']
        }
    }
];

// Helper functions for Firecrawl integration
export class FirecrawlMCPHelper {
    static validateApiKey(): boolean {
        return true; // API key is built-in
    }

    static buildScrapeParams(params: FirecrawlScrapeParams): Record<string, any> {
        const scrapeParams: Record<string, any> = {
            url: params.url,
            formats: params.formats || ['markdown']
        };

        if (params.onlyMainContent !== undefined) {
            scrapeParams.onlyMainContent = params.onlyMainContent;
        }

        if (params.removeBase64Images !== undefined) {
            scrapeParams.removeBase64Images = params.removeBase64Images;
        }

        if (params.includeTags?.length) {
            scrapeParams.includeTags = params.includeTags;
        }

        if (params.excludeTags?.length) {
            scrapeParams.excludeTags = params.excludeTags;
        }

        if (params.waitFor) {
            scrapeParams.waitFor = params.waitFor;
        }

        if (params.timeout) {
            scrapeParams.timeout = params.timeout;
        }

        if (params.mobile !== undefined) {
            scrapeParams.mobile = params.mobile;
        }

        if (params.skipTlsVerification !== undefined) {
            scrapeParams.skipTlsVerification = params.skipTlsVerification;
        }

        if (params.extract) {
            scrapeParams.extract = params.extract;
        }

        if (params.actions?.length) {
            scrapeParams.actions = params.actions;
        }

        if (params.location) {
            scrapeParams.location = params.location;
        }

        return scrapeParams;
    }

    static buildCrawlParams(params: FirecrawlCrawlParams): Record<string, any> {
        const crawlParams: Record<string, any> = {
            url: params.url
        };

        if (params.maxDepth !== undefined) {
            crawlParams.maxDepth = params.maxDepth;
        }

        if (params.limit !== undefined) {
            crawlParams.limit = params.limit;
        }

        if (params.allowBackwardLinks !== undefined) {
            crawlParams.allowBackwardLinks = params.allowBackwardLinks;
        }

        if (params.allowExternalLinks !== undefined) {
            crawlParams.allowExternalLinks = params.allowExternalLinks;
        }

        if (params.deduplicateSimilarURLs !== undefined) {
            crawlParams.deduplicateSimilarURLs = params.deduplicateSimilarURLs;
        }

        if (params.excludePaths?.length) {
            crawlParams.excludePaths = params.excludePaths;
        }

        if (params.includePaths?.length) {
            crawlParams.includePaths = params.includePaths;
        }

        if (params.ignoreQueryParameters !== undefined) {
            crawlParams.ignoreQueryParameters = params.ignoreQueryParameters;
        }

        if (params.ignoreSitemap !== undefined) {
            crawlParams.ignoreSitemap = params.ignoreSitemap;
        }

        if (params.scrapeOptions) {
            crawlParams.scrapeOptions = params.scrapeOptions;
        }

        if (params.webhook) {
            crawlParams.webhook = params.webhook;
        }

        return crawlParams;
    }

    static buildSearchParams(params: FirecrawlSearchParams): Record<string, any> {
        const searchParams: Record<string, any> = {
            query: params.query,
            limit: params.limit || 5,
            lang: params.lang || 'en',
            country: params.country || 'us'
        };

        if (params.tbs) {
            searchParams.tbs = params.tbs;
        }

        if (params.filter) {
            searchParams.filter = params.filter;
        }

        if (params.scrapeOptions) {
            searchParams.scrapeOptions = params.scrapeOptions;
        }

        if (params.location) {
            searchParams.location = params.location;
        }

        return searchParams;
    }

    static validateUrl(url: string): boolean {
        try {
            new URL(url.startsWith('http') ? url : `https://${url}`);
            return true;
        } catch {
            return false;
        }
    }

    static normalizeUrl(url: string): string {
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            return `https://${url}`;
        }
        return url;
    }

    static estimateCrawlTime(maxDepth: number = 1, limit: number = 10): number {
        // Rough estimation: 2 seconds per page + depth multiplier
        return Math.min((limit * 2 * maxDepth), 300); // Cap at 5 minutes
    }

    static formatCrawlStatus(status: any): string {
        if (!status) return 'Unknown';
        
        switch (status.status) {
            case 'scraping':
                return `Crawling... (${status.current || 0}/${status.total || '?'} pages)`;
            case 'completed':
                return `Completed (${status.total || 0} pages crawled)`;
            case 'failed':
                return `Failed: ${status.error || 'Unknown error'}`;
            default:
                return status.status || 'Unknown';
        }
    }
}
