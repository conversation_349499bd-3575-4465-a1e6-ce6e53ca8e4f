// MCP Module Exports - Comprehensive Model Context Protocol Implementation
// Based on Protocol 2025-06-18 with security best practices

// Core MCP Components
export { MCPClient } from './client';
export { MCPHub } from './hub';
export { MCPManager } from './manager';
export { MCPSecurityManager } from './security';

// Transport Implementations
export { StdioTransport } from './transports/stdio';
export { StreamableHttpTransport, StreamableHttpPollingTransport } from './transports/streamableHttp';

// External Server Configurations
export { ExaMCPServerConfig, ExaMCPTools, ExaMCPHelper } from './servers/exa';
export { FirecrawlMCPServerConfig, FirecrawlMCPTools, FirecrawlMCPHelper } from './servers/firecrawl';

// UI Components - TODO: Implement React components
// export { MCPServerManager } from '../ui/components/mcp/MCPServerManager';

// Testing
export { runMCPTests } from './test';

// Types and Interfaces
export * from './types';

// Version and Protocol Information
export const MCP_IMPLEMENTATION_VERSION = '2.0.0';
export const MCP_PROTOCOL_VERSION = '2025-06-18';
export const MCP_IMPLEMENTATION_NAME = 'Aizen AI MCP';

/**
 * Aizen AI MCP Implementation
 * 
 * This is a comprehensive implementation of the Model Context Protocol (MCP)
 * designed to integrate external AI services and tools into VS Code extensions.
 * 
 * Features:
 * - Protocol 2025-06-18 compliance
 * - Security-first design with user consent flows
 * - Support for multiple transport types (STDIO, HTTP, Streamable HTTP)
 * - Integration with external services (Exa AI, Firecrawl)
 * - Comprehensive testing suite
 * - TypeScript implementation with full type safety
 * - React UI components for server management
 * 
 * Security Features:
 * - Explicit user consent for all operations
 * - Risk assessment for tools and resources
 * - Permission management and expiration
 * - Activity logging and monitoring
 * - Domain-based access controls
 * 
 * Supported External Services:
 * - Exa AI: Web search, research papers, company research
 * - Firecrawl: Web scraping, crawling, content extraction
 * 
 * Usage:
 * ```typescript
 * import { MCPHub, MCPSecurityManager } from './mcp';
 * 
 * const mcpHub = new MCPHub(context);
 * const mcpSecurity = new MCPSecurityManager(context);
 * 
 * await mcpHub.initialize();
 * 
 * // Add external servers
 * const exaServerId = await mcpHub.addServer(ExaMCPServerConfig);
 * 
 * // Execute tools with security
 * const result = await mcpHub.executeTool(exaServerId, 'web_search_exa', {
 *   query: 'latest AI developments',
 *   numResults: 5
 * });
 * ```
 */
