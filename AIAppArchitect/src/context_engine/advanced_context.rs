use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use dashmap::DashMap;
use fastembed::{EmbeddingModel, InitOptions, TextEmbedding};
use qdrant_client::prelude::*;

/// Advanced context engine that outperforms competitors
/// Combines semantic search, conversation memory, and intelligent ranking
#[derive(Debug)]
pub struct AdvancedContextEngine {
    /// Vector store for semantic search
    vector_store: Arc<QdrantClient>,
    /// Embedding model for text vectorization
    embedding_model: Arc<TextEmbedding>,
    /// Context memory for conversation history
    memory: Arc<RwLock<ContextMemory>>,
    /// Context cache for performance
    context_cache: Arc<DashMap<String, CachedContext>>,
    /// Configuration
    config: ContextEngineConfig,
    /// Collection name for this workspace
    collection_name: String,
}

/// Configuration for context engine
#[derive(Debug, <PERSON>lone)]
pub struct ContextEngineConfig {
    pub max_context_length: usize,
    pub embedding_dimension: usize,
    pub cache_ttl_seconds: u64,
    pub max_conversation_history: usize,
    pub similarity_threshold: f32,
    pub rerank_top_k: usize,
    pub max_file_size_kb: usize,
    pub chunk_size: usize,
    pub chunk_overlap: usize,
}

impl Default for ContextEngineConfig {
    fn default() -> Self {
        Self {
            max_context_length: 8192,
            embedding_dimension: 384,
            cache_ttl_seconds: 3600,
            max_conversation_history: 50,
            similarity_threshold: 0.7,
            rerank_top_k: 20,
            max_file_size_kb: 1024,
            chunk_size: 512,
            chunk_overlap: 50,
        }
    }
}

/// Cached context with TTL
#[derive(Debug, Clone)]
pub struct CachedContext {
    pub context: Context,
    pub embedding: Vec<f32>,
    pub created_at: u64,
    pub access_count: u32,
    pub relevance_score: f32,
}

/// Enhanced context item with rich metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Context {
    pub id: String,
    pub content: String,
    pub context_type: ContextType,
    pub file_path: Option<String>,
    pub line_range: Option<(usize, usize)>,
    pub metadata: HashMap<String, String>,
    pub created_at: u64,
    pub last_accessed: u64,
    pub access_count: u32,
    pub language: Option<String>,
    pub complexity_score: Option<f32>,
    pub importance_score: f32,
    pub tags: Vec<String>,
}

/// Types of context with more granular categories
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ContextType {
    Function,
    Class,
    Interface,
    Type,
    Variable,
    Constant,
    Import,
    Export,
    Comment,
    Documentation,
    Test,
    Configuration,
    Error,
    Warning,
    Suggestion,
    Example,
    Conversation,
    FileHeader,
    ModuleDoc,
}

/// Ranked context with detailed scoring
#[derive(Debug, Clone)]
pub struct RankedContext {
    pub context: Context,
    pub relevance_score: f32,
    pub semantic_score: f32,
    pub recency_score: f32,
    pub popularity_score: f32,
    pub final_score: f32,
    pub reasoning: String,
    pub confidence: f32,
    pub source_type: ContextSourceType,
}

/// Source of context
#[derive(Debug, Clone, PartialEq)]
pub enum ContextSourceType {
    VectorSearch,
    ConversationHistory,
    ActiveFile,
    RelatedFiles,
    Documentation,
    Cache,
    RecentlyModified,
    FrequentlyUsed,
}

/// Context memory for maintaining conversation state
#[derive(Debug, Default)]
pub struct ContextMemory {
    /// Recent conversation history
    conversation_history: Vec<ConversationTurn>,
    /// Active code context
    active_context: Vec<Context>,
    /// User preferences and patterns
    user_patterns: HashMap<String, f32>,
    /// Working set of files
    working_set: HashSet<String>,
    /// Context usage statistics
    usage_stats: ContextUsageStats,
    /// Recently accessed contexts
    recent_contexts: Vec<String>,
    /// Frequently used contexts
    frequent_contexts: HashMap<String, u32>,
}

/// Context usage statistics
#[derive(Debug, Default)]
pub struct ContextUsageStats {
    pub total_queries: u64,
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub avg_response_time_ms: f64,
    pub most_used_contexts: HashMap<String, u32>,
    pub context_type_distribution: HashMap<ContextType, u32>,
}

/// Single turn in conversation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationTurn {
    pub id: String,
    pub timestamp: u64,
    pub user_message: String,
    pub assistant_response: String,
    pub context_used: Vec<String>,
    pub response_time_ms: u64,
    pub satisfaction_score: Option<f32>,
    pub query_type: QueryType,
}

/// Types of queries for better context selection
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum QueryType {
    CodeExplanation,
    CodeGeneration,
    Debugging,
    Refactoring,
    Documentation,
    Testing,
    General,
}

impl AdvancedContextEngine {
    /// Create new advanced context engine
    pub async fn new(workspace_path: &str, config: ContextEngineConfig) -> Result<Self> {
        // Initialize embedding model
        let embedding_model = Arc::new(TextEmbedding::try_new(InitOptions {
            model_name: EmbeddingModel::AllMiniLML6V2,
            show_download_progress: true,
            ..Default::default()
        })?);

        // Initialize Qdrant client
        let qdrant_client = Arc::new(QdrantClient::from_url("http://localhost:6333").build()?);

        // Create collection name
        let collection_name = format!("workspace_{}", 
            workspace_path.replace("/", "_").replace("\\", "_").replace(":", "_"));

        // Create collection if it doesn't exist
        let collections = qdrant_client.list_collections().await?;
        if !collections.collections.iter().any(|c| c.name == collection_name) {
            qdrant_client
                .create_collection(
                    qdrant_client::qdrant::CreateCollectionBuilder::new(&collection_name)
                        .vectors_config(qdrant_client::qdrant::VectorParamsBuilder::new(
                            config.embedding_dimension as u64, 
                            qdrant_client::qdrant::Distance::Cosine
                        ))
                )
                .await?;
        }

        Ok(Self {
            vector_store: qdrant_client,
            embedding_model,
            memory: Arc::new(RwLock::new(ContextMemory::default())),
            context_cache: Arc::new(DashMap::new()),
            config,
            collection_name,
        })
    }

    /// Retrieve relevant context with advanced ranking
    pub async fn get_context(&self, query: &str, limit: usize) -> Result<Vec<RankedContext>> {
        let start_time = std::time::Instant::now();
        
        // Classify query type
        let query_type = self.classify_query(query);
        
        // Check cache first
        let cache_key = format!("query:{}", md5::compute(query));
        if let Some(cached_contexts) = self.get_from_cache(&cache_key) {
            self.update_cache_stats(true).await;
            return Ok(cached_contexts);
        }

        // Generate embedding for query
        let query_embedding = self.generate_embedding(query).await?;
        
        // Multi-source context retrieval
        let mut all_contexts = Vec::new();
        
        // 1. Vector search
        let vector_contexts = self.vector_search(&query_embedding, limit * 2).await?;
        all_contexts.extend(vector_contexts);
        
        // 2. Conversation history
        let conversation_contexts = self.get_conversation_context(query, &query_type).await?;
        all_contexts.extend(conversation_contexts);
        
        // 3. Active file context
        let active_contexts = self.get_active_file_context(query).await?;
        all_contexts.extend(active_contexts);
        
        // 4. Recently modified files
        let recent_contexts = self.get_recent_file_context(query).await?;
        all_contexts.extend(recent_contexts);

        // Advanced ranking with multiple factors
        let ranked_contexts = self.rank_contexts_advanced(all_contexts, query, &query_type).await?;
        
        // Apply similarity threshold and limit
        let filtered_contexts: Vec<RankedContext> = ranked_contexts
            .into_iter()
            .filter(|ctx| ctx.final_score >= self.config.similarity_threshold)
            .take(limit)
            .collect();

        // Cache results
        self.cache_results(&cache_key, &filtered_contexts).await;
        
        // Update memory and statistics
        let response_time = start_time.elapsed().as_millis() as u64;
        self.update_context_memory(query, &filtered_contexts, response_time, query_type).await?;
        self.update_cache_stats(false).await;
        
        Ok(filtered_contexts)
    }

    /// Generate embedding for text
    async fn generate_embedding(&self, text: &str) -> Result<Vec<f32>> {
        let embeddings = self.embedding_model.embed(vec![text.to_string()], None)?;
        Ok(embeddings.into_iter().next().unwrap_or_default())
    }

    /// Vector search in Qdrant
    async fn vector_search(&self, query_embedding: &[f32], limit: usize) -> Result<Vec<Context>> {
        let search_result = self.vector_store
            .search_points(qdrant_client::qdrant::SearchPointsBuilder::new(
                &self.collection_name,
                query_embedding.to_vec(),
                limit as u64,
            ))
            .await?;

        let mut contexts = Vec::new();
        for point in search_result.result {
            if let Some(payload) = point.payload {
                if let Ok(context) = serde_json::from_value::<Context>(serde_json::Value::Object(payload)) {
                    contexts.push(context);
                }
            }
        }

        Ok(contexts)
    }

    /// Classify query type for better context selection
    fn classify_query(&self, query: &str) -> QueryType {
        let query_lower = query.to_lowercase();
        
        if query_lower.contains("explain") || query_lower.contains("what does") || query_lower.contains("how does") {
            QueryType::CodeExplanation
        } else if query_lower.contains("generate") || query_lower.contains("create") || query_lower.contains("write") {
            QueryType::CodeGeneration
        } else if query_lower.contains("debug") || query_lower.contains("error") || query_lower.contains("fix") {
            QueryType::Debugging
        } else if query_lower.contains("refactor") || query_lower.contains("improve") || query_lower.contains("optimize") {
            QueryType::Refactoring
        } else if query_lower.contains("test") || query_lower.contains("unit test") {
            QueryType::Testing
        } else if query_lower.contains("document") || query_lower.contains("comment") {
            QueryType::Documentation
        } else {
            QueryType::General
        }
    }

    /// Advanced ranking with multiple scoring factors
    async fn rank_contexts_advanced(
        &self, 
        contexts: Vec<Context>, 
        query: &str, 
        query_type: &QueryType
    ) -> Result<Vec<RankedContext>> {
        let mut ranked_contexts = Vec::new();
        let memory = self.memory.read().await;

        for context in contexts {
            // Semantic similarity score (from vector search)
            let semantic_score = self.calculate_semantic_similarity(&context, query).await?;
            
            // Recency score
            let recency_score = self.calculate_recency_score(&context);
            
            // Popularity score
            let popularity_score = self.calculate_popularity_score(&context, &memory);
            
            // Context type relevance for query type
            let type_relevance = self.calculate_type_relevance(&context.context_type, query_type);
            
            // File importance score
            let file_importance = self.calculate_file_importance(&context);
            
            // Final weighted score
            let final_score = (semantic_score * 0.4) + 
                             (recency_score * 0.2) + 
                             (popularity_score * 0.2) + 
                             (type_relevance * 0.1) + 
                             (file_importance * 0.1);

            let reasoning = format!(
                "Semantic: {:.2}, Recency: {:.2}, Popularity: {:.2}, Type: {:.2}, Importance: {:.2}",
                semantic_score, recency_score, popularity_score, type_relevance, file_importance
            );

            ranked_contexts.push(RankedContext {
                context,
                relevance_score: semantic_score,
                semantic_score,
                recency_score,
                popularity_score,
                final_score,
                reasoning,
                confidence: final_score,
                source_type: ContextSourceType::VectorSearch,
            });
        }

        // Sort by final score
        ranked_contexts.sort_by(|a, b| b.final_score.partial_cmp(&a.final_score).unwrap());
        
        Ok(ranked_contexts)
    }

    /// Calculate semantic similarity between context and query
    async fn calculate_semantic_similarity(&self, context: &Context, query: &str) -> Result<f32> {
        // This would use the actual embedding similarity
        // For now, use a simple text similarity
        let context_words: HashSet<&str> = context.content.split_whitespace().collect();
        let query_words: HashSet<&str> = query.split_whitespace().collect();
        
        let intersection = context_words.intersection(&query_words).count();
        let union = context_words.union(&query_words).count();
        
        if union == 0 {
            Ok(0.0)
        } else {
            Ok(intersection as f32 / union as f32)
        }
    }

    /// Calculate recency score based on last access time
    fn calculate_recency_score(&self, context: &Context) -> f32 {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        let age_hours = (now - context.last_accessed) / 3600;
        
        // Exponential decay with half-life of 24 hours
        (0.5_f32).powf(age_hours as f32 / 24.0)
    }

    /// Calculate popularity score based on usage frequency
    fn calculate_popularity_score(&self, context: &Context, memory: &ContextMemory) -> f32 {
        let usage_count = memory.usage_stats.most_used_contexts
            .get(&context.id)
            .unwrap_or(&0);
        
        // Logarithmic scaling
        (1.0 + *usage_count as f32).ln() / 10.0
    }

    /// Calculate type relevance for query type
    fn calculate_type_relevance(&self, context_type: &ContextType, query_type: &QueryType) -> f32 {
        match (query_type, context_type) {
            (QueryType::CodeExplanation, ContextType::Function) => 1.0,
            (QueryType::CodeExplanation, ContextType::Class) => 0.9,
            (QueryType::CodeGeneration, ContextType::Function) => 0.8,
            (QueryType::CodeGeneration, ContextType::Example) => 1.0,
            (QueryType::Debugging, ContextType::Error) => 1.0,
            (QueryType::Debugging, ContextType::Test) => 0.8,
            (QueryType::Testing, ContextType::Test) => 1.0,
            (QueryType::Documentation, ContextType::Documentation) => 1.0,
            (QueryType::Documentation, ContextType::Comment) => 0.8,
            _ => 0.5,
        }
    }

    /// Calculate file importance score
    fn calculate_file_importance(&self, context: &Context) -> f32 {
        context.importance_score
    }

    /// Get conversation context
    async fn get_conversation_context(&self, query: &str, query_type: &QueryType) -> Result<Vec<Context>> {
        let memory = self.memory.read().await;
        let mut contexts = Vec::new();
        
        // Get recent relevant conversation turns
        for turn in memory.conversation_history.iter().rev().take(5) {
            if turn.query_type == *query_type || self.is_related_query(&turn.user_message, query) {
                let context = Context {
                    id: format!("conv:{}", turn.id),
                    content: format!("Previous Q: {}\nPrevious A: {}", turn.user_message, turn.assistant_response),
                    context_type: ContextType::Conversation,
                    file_path: None,
                    line_range: None,
                    metadata: HashMap::new(),
                    created_at: turn.timestamp,
                    last_accessed: turn.timestamp,
                    access_count: 1,
                    language: None,
                    complexity_score: None,
                    importance_score: 0.7,
                    tags: vec!["conversation".to_string()],
                };
                contexts.push(context);
            }
        }
        
        Ok(contexts)
    }

    /// Check if queries are related
    fn is_related_query(&self, prev_query: &str, current_query: &str) -> bool {
        let prev_words: HashSet<&str> = prev_query.split_whitespace().collect();
        let curr_words: HashSet<&str> = current_query.split_whitespace().collect();
        
        let intersection = prev_words.intersection(&curr_words).count();
        intersection >= 2 // At least 2 common words
    }

    /// Get active file context
    async fn get_active_file_context(&self, _query: &str) -> Result<Vec<Context>> {
        // This would get context from currently open files
        // Implementation depends on VS Code integration
        Ok(vec![])
    }

    /// Get recent file context
    async fn get_recent_file_context(&self, _query: &str) -> Result<Vec<Context>> {
        // This would get context from recently modified files
        // Implementation depends on file system monitoring
        Ok(vec![])
    }

    /// Cache query results
    async fn cache_results(&self, cache_key: &str, contexts: &[RankedContext]) {
        // Cache top result for quick retrieval
        if let Some(top_context) = contexts.first() {
            let cached = CachedContext {
                context: top_context.context.clone(),
                embedding: vec![], // Would store actual embedding
                created_at: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
                access_count: 0,
                relevance_score: top_context.final_score,
            };
            
            self.context_cache.insert(cache_key.to_string(), cached);
        }
    }

    /// Get results from cache
    fn get_from_cache(&self, cache_key: &str) -> Option<Vec<RankedContext>> {
        if let Some(cached) = self.context_cache.get(cache_key) {
            Some(vec![RankedContext {
                context: cached.context.clone(),
                relevance_score: cached.relevance_score,
                semantic_score: cached.relevance_score,
                recency_score: 1.0,
                popularity_score: 0.5,
                final_score: cached.relevance_score,
                reasoning: "Retrieved from cache".to_string(),
                confidence: 0.9,
                source_type: ContextSourceType::Cache,
            }])
        } else {
            None
        }
    }

    /// Update cache statistics
    async fn update_cache_stats(&self, cache_hit: bool) {
        let mut memory = self.memory.write().await;
        if cache_hit {
            memory.usage_stats.cache_hits += 1;
        } else {
            memory.usage_stats.cache_misses += 1;
        }
    }

    /// Update context memory
    async fn update_context_memory(
        &self, 
        query: &str, 
        contexts: &[RankedContext], 
        response_time_ms: u64,
        query_type: QueryType
    ) -> Result<()> {
        let mut memory = self.memory.write().await;
        
        // Add conversation turn
        let turn = ConversationTurn {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            user_message: query.to_string(),
            assistant_response: String::new(),
            context_used: contexts.iter().map(|c| c.context.id.clone()).collect(),
            response_time_ms,
            satisfaction_score: None,
            query_type,
        };
        
        memory.conversation_history.push(turn);
        
        // Limit history size
        if memory.conversation_history.len() > self.config.max_conversation_history {
            memory.conversation_history.remove(0);
        }
        
        // Update statistics
        memory.usage_stats.total_queries += 1;
        memory.usage_stats.avg_response_time_ms = 
            (memory.usage_stats.avg_response_time_ms * (memory.usage_stats.total_queries - 1) as f64 + response_time_ms as f64) 
            / memory.usage_stats.total_queries as f64;
        
        for context in contexts {
            *memory.usage_stats.most_used_contexts
                .entry(context.context.id.clone())
                .or_insert(0) += 1;
            
            *memory.usage_stats.context_type_distribution
                .entry(context.context.context_type.clone())
                .or_insert(0) += 1;
        }
        
        Ok(())
    }
}
