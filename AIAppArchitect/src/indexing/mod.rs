use std::collections::{HashMap, HashSet};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use anyhow::{Result, Context};
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use uuid::Uuid;
use chrono::{DateTime, Utc};

// Advanced indexing features based on 2025 research
pub mod code_digital_twin;
pub mod tacit_knowledge_extractor;
pub mod cognitive_weave;
pub mod spatio_temporal_index;

pub mod graph_indexer;
pub mod semantic_chunker;
pub mod dependency_analyzer;
pub mod language_parser;
pub mod code_embeddings;

/// Advanced Code Indexing System with 2025 Research Enhancements
/// Integrates Code Digital Twin, Tacit Knowledge Extraction, and Cognitive Weave
#[derive(Debug, Clone)]
pub struct CodeIndexer {
    /// Graph-based code representation
    pub code_graph: Arc<RwLock<CodeGraph>>,
    /// Semantic chunks with embeddings
    pub semantic_chunks: Arc<DashMap<String, SemanticChunk>>,
    /// File-level metadata
    pub file_metadata: Arc<DashMap<PathBuf, FileMetadata>>,
    /// Dependency relationships
    pub dependencies: Arc<DashMap<String, Vec<Dependency>>>,
    /// Language-specific parsers
    pub parsers: Arc<DashMap<String, Box<dyn LanguageParser + Send + Sync>>>,

    // Advanced 2025 features
    /// Code Digital Twins for tacit knowledge
    pub digital_twins: Arc<DashMap<String, code_digital_twin::CodeDigitalTwin>>,
    /// Tacit knowledge extractor
    pub tacit_extractor: Arc<tacit_knowledge_extractor::TacitKnowledgeExtractor>,
    /// Cognitive weave for advanced reasoning
    pub cognitive_weave: Option<Arc<cognitive_weave::CognitiveWeave>>,
    /// Spatio-temporal index for time-aware queries
    pub spatio_temporal_index: Arc<spatio_temporal_index::SpatioTemporalIndex>,
}

/// Graph representation of code structure
#[derive(Debug, Clone, Default)]
pub struct CodeGraph {
    /// Nodes representing code entities (functions, classes, modules, etc.)
    pub nodes: HashMap<String, CodeNode>,
    /// Edges representing relationships between entities
    pub edges: HashMap<String, Vec<CodeEdge>>,
    /// Hierarchical structure for efficient traversal
    pub hierarchy: HashMap<String, Vec<String>>,
}

/// Individual code entity in the graph
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeNode {
    pub id: String,
    pub node_type: CodeNodeType,
    pub name: String,
    pub file_path: PathBuf,
    pub start_line: usize,
    pub end_line: usize,
    pub signature: Option<String>,
    pub docstring: Option<String>,
    pub complexity_score: f32,
    pub embedding: Option<Vec<f32>>,
    pub metadata: HashMap<String, String>,
}

/// Types of code entities
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CodeNodeType {
    Module,
    Class,
    Function,
    Method,
    Variable,
    Constant,
    Interface,
    Enum,
    Struct,
    Trait,
    Macro,
    Import,
    Export,
}

/// Relationship between code entities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeEdge {
    pub id: String,
    pub source: String,
    pub target: String,
    pub edge_type: CodeEdgeType,
    pub weight: f32,
    pub metadata: HashMap<String, String>,
}

/// Types of relationships between code entities
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CodeEdgeType {
    Calls,
    Inherits,
    Implements,
    Uses,
    Defines,
    Contains,
    Imports,
    Exports,
    References,
    Overrides,
    Extends,
}

/// Semantic chunk with contextual information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SemanticChunk {
    pub id: String,
    pub content: String,
    pub file_path: PathBuf,
    pub start_line: usize,
    pub end_line: usize,
    pub chunk_type: ChunkType,
    pub embedding: Option<Vec<f32>>,
    pub related_nodes: Vec<String>,
    pub context_score: f32,
    pub language: String,
}

/// Types of semantic chunks
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ChunkType {
    Function,
    Class,
    Module,
    Comment,
    Documentation,
    Test,
    Configuration,
    Import,
}

impl ToString for ChunkType {
    fn to_string(&self) -> String {
        match self {
            ChunkType::Function => "function".to_string(),
            ChunkType::Class => "class".to_string(),
            ChunkType::Module => "module".to_string(),
            ChunkType::Comment => "comment".to_string(),
            ChunkType::Documentation => "documentation".to_string(),
            ChunkType::Test => "test".to_string(),
            ChunkType::Configuration => "configuration".to_string(),
            ChunkType::Import => "import".to_string(),
        }
    }
}

/// File-level metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileMetadata {
    pub path: PathBuf,
    pub language: String,
    pub size: u64,
    pub last_modified: u64,
    pub hash: String,
    pub complexity_score: f32,
    pub test_coverage: Option<f32>,
    pub dependencies: Vec<String>,
    pub exports: Vec<String>,
    pub imports: Vec<String>,
}

/// Dependency relationship
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Dependency {
    pub source: String,
    pub target: String,
    pub dependency_type: DependencyType,
    pub version: Option<String>,
    pub is_dev: bool,
}

/// Types of dependencies
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DependencyType {
    Direct,
    Transitive,
    Peer,
    Optional,
    Dev,
    Runtime,
}

/// Language-specific parser trait
pub trait LanguageParser {
    fn parse_file(&self, content: &str, file_path: &Path) -> Result<Vec<CodeNode>>;
    fn extract_dependencies(&self, content: &str) -> Result<Vec<Dependency>>;
    fn get_semantic_chunks(&self, content: &str, file_path: &Path) -> Result<Vec<SemanticChunk>>;
    fn calculate_complexity(&self, content: &str) -> Result<f32>;
}

/// Codebase quality metrics aggregated from all elements
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodebaseQualityMetrics {
    pub average_complexity: f64,
    pub average_maintainability: f64,
    pub average_testability: f64,
    pub average_security: f64,
    pub total_elements: usize,
    pub technical_debt_items: usize,
    pub code_smells: usize,
}

/// Enhanced search result with tacit knowledge
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedSearchResult {
    pub node: CodeNode,
    pub digital_twin: Option<code_digital_twin::CodeDigitalTwin>,
    pub relevance_score: f64,
    pub tacit_knowledge_match: bool,
    pub temporal_relevance: f64,
    pub spatial_context: Vec<String>,
}

impl CodeIndexer {
    /// Create a new advanced code indexer with 2025 enhancements
    pub fn new() -> Self {
        let spatio_temporal_config = spatio_temporal_index::IndexConfig::default();

        Self {
            code_graph: Arc::new(RwLock::new(CodeGraph::default())),
            semantic_chunks: Arc::new(DashMap::new()),
            file_metadata: Arc::new(DashMap::new()),
            dependencies: Arc::new(DashMap::new()),
            parsers: Arc::new(DashMap::new()),

            // Advanced 2025 features
            digital_twins: Arc::new(DashMap::new()),
            tacit_extractor: Arc::new(tacit_knowledge_extractor::TacitKnowledgeExtractor::new()),
            cognitive_weave: None, // Will be initialized with LLM client
            spatio_temporal_index: Arc::new(spatio_temporal_index::SpatioTemporalIndex::new(spatio_temporal_config)),
        }
    }

    /// Initialize with LLM client for cognitive weave
    pub async fn initialize_with_llm(
        &mut self,
        llm_client: Arc<dyn cognitive_weave::LLMClient + Send + Sync>,
        embedding_model: Arc<dyn cognitive_weave::EmbeddingModel + Send + Sync>,
    ) -> Result<()> {
        self.cognitive_weave = Some(Arc::new(
            cognitive_weave::CognitiveWeave::new(llm_client, embedding_model)
        ));
        Ok(())
    }

    /// Initialize with language parsers
    pub async fn initialize(&self) -> Result<()> {
        // Register language parsers
        self.register_parser("rust", Box::new(language_parser::RustParser::new())).await?;
        self.register_parser("typescript", Box::new(language_parser::TypeScriptParser::new())).await?;
        self.register_parser("javascript", Box::new(language_parser::JavaScriptParser::new())).await?;
        self.register_parser("python", Box::new(language_parser::PythonParser::new())).await?;
        
        Ok(())
    }

    /// Register a language parser
    pub async fn register_parser(&self, language: &str, parser: Box<dyn LanguageParser + Send + Sync>) -> Result<()> {
        self.parsers.insert(language.to_string(), parser);
        Ok(())
    }

    /// Index a single file with advanced 2025 features
    pub async fn index_file(&self, file_path: &Path) -> Result<()> {
        let content = tokio::fs::read_to_string(file_path).await
            .with_context(|| format!("Failed to read file: {:?}", file_path))?;

        let language = self.detect_language(file_path)?;
        let timestamp = chrono::Utc::now();

        if let Some(parser) = self.parsers.get(&language) {
            // Parse code nodes
            let nodes = parser.parse_file(&content, file_path)?;

            // Extract semantic chunks
            let chunks = parser.get_semantic_chunks(&content, file_path)?;

            // Extract dependencies
            let deps = parser.extract_dependencies(&content)?;

            // Calculate complexity
            let complexity = parser.calculate_complexity(&content)?;

            // Extract tacit knowledge using 2025 research
            let tacit_knowledge = self.tacit_extractor
                .extract_from_code(&content, file_path, &language).await?;

            // Update graph
            let mut graph = self.code_graph.write().await;
            for node in &nodes {
                // Create Code Digital Twin for each significant node
                if matches!(node.node_type, CodeNodeType::Class | CodeNodeType::Function | CodeNodeType::Module) {
                    let mut digital_twin = code_digital_twin::CodeDigitalTwin::new(node.id.clone());

                    // Update with extracted tacit knowledge
                    digital_twin.update_tacit_knowledge(code_digital_twin::TacitKnowledge {
                        responsibility_allocation: tacit_knowledge.responsibility_allocation.clone(),
                        implicit_assumptions: tacit_knowledge.implicit_assumptions.clone(),
                        domain_knowledge: tacit_knowledge.domain_concepts.clone(),
                        architectural_decisions: tacit_knowledge.architectural_decisions.clone(),
                        performance_considerations: tacit_knowledge.performance_considerations.clone(),
                        security_implications: tacit_knowledge.security_implications.clone(),
                        maintainability_factors: tacit_knowledge.maintainability_factors.clone(),
                    });

                    // Update quality metrics
                    digital_twin.update_quality_metrics(code_digital_twin::QualityMetrics {
                        complexity_score: node.complexity_score as f64,
                        maintainability_index: tacit_knowledge.quality_indicators.maintainability_indicators
                            .first().map(|m| m.score).unwrap_or(0.0),
                        testability_score: tacit_knowledge.quality_indicators.testability_indicators
                            .first().map(|t| t.score).unwrap_or(0.0),
                        reusability_score: tacit_knowledge.quality_indicators.reusability_indicators
                            .first().map(|r| r.reusability_score).unwrap_or(0.0),
                        reliability_score: 0.0, // Would be calculated from other metrics
                        performance_score: 0.0, // Would be calculated from performance notes
                        security_score: 0.0,    // Would be calculated from security implications
                        documentation_quality: 0.0, // Would be calculated from comments
                    });

                    self.digital_twins.insert(node.id.clone(), digital_twin);
                }

                graph.nodes.insert(node.id.clone(), node.clone());
            }

            // Store semantic chunks and create insight particles
            for chunk in chunks {
                // Create insight particle for cognitive weave
                if let Some(cognitive_weave) = &self.cognitive_weave {
                    let insight_particle = cognitive_weave::InsightParticle {
                        id: chunk.id.clone(),
                        content: chunk.content.clone(),
                        particle_type: match chunk.chunk_type {
                            ChunkType::Function => cognitive_weave::ParticleType::Concept,
                            ChunkType::Class => cognitive_weave::ParticleType::Concept,
                            ChunkType::Documentation => cognitive_weave::ParticleType::Context,
                            ChunkType::Test => cognitive_weave::ParticleType::Evidence,
                            _ => cognitive_weave::ParticleType::Memory,
                        },
                        resonance_keys: vec![], // Would be extracted by semantic oracle
                        signifiers: vec![],     // Would be extracted by semantic oracle
                        situational_imprints: vec![cognitive_weave::SituationalImprint {
                            situation: format!("File: {}", file_path.display()),
                            context_factors: vec![language.clone(), chunk.chunk_type.to_string()],
                            environmental_conditions: HashMap::new(),
                            temporal_context: cognitive_weave::TemporalContext {
                                time_window: "current".to_string(),
                                sequence_position: None,
                                duration: None,
                                frequency: None,
                            },
                        }],
                        semantic_embedding: chunk.embedding.clone(),
                        creation_time: timestamp,
                        last_modified: timestamp,
                        access_count: 0,
                        relevance_score: chunk.context_score as f64,
                    };

                    cognitive_weave.add_insight_particle(insight_particle).await?;
                }

                self.semantic_chunks.insert(chunk.id.clone(), chunk);
            }

            // Add to spatio-temporal index
            let spatial_location = file_path.components()
                .map(|c| c.as_os_str().to_string_lossy().to_string())
                .collect::<Vec<_>>();

            let mut metadata_map = HashMap::new();
            metadata_map.insert("type".to_string(), "file".to_string());
            metadata_map.insert("language".to_string(), language.clone());
            metadata_map.insert("complexity".to_string(), complexity.to_string());

            self.spatio_temporal_index.add_element(
                file_path.to_string_lossy().to_string(),
                spatial_location,
                timestamp,
                metadata_map,
            ).await?;

            // Store dependencies
            self.dependencies.insert(file_path.to_string_lossy().to_string(), deps);

            // Store file metadata
            let metadata = FileMetadata {
                path: file_path.to_path_buf(),
                language,
                size: content.len() as u64,
                last_modified: timestamp.timestamp() as u64,
                hash: format!("{:x}", md5::compute(&content)),
                complexity_score: complexity,
                test_coverage: None,
                dependencies: vec![],
                exports: vec![],
                imports: vec![],
            };

            self.file_metadata.insert(file_path.to_path_buf(), metadata);
        }

        Ok(())
    }

    /// Detect programming language from file extension
    fn detect_language(&self, file_path: &Path) -> Result<String> {
        let extension = file_path.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");

        let language = match extension {
            "rs" => "rust",
            "ts" => "typescript",
            "js" | "jsx" => "javascript",
            "py" => "python",
            "go" => "go",
            "java" => "java",
            "cpp" | "cc" | "cxx" => "cpp",
            "c" => "c",
            "cs" => "csharp",
            _ => "text",
        };

        Ok(language.to_string())
    }

    /// Index entire codebase
    pub async fn index_codebase(&self, root_path: &Path) -> Result<()> {
        use walkdir::WalkDir;
        
        let mut tasks = vec![];
        
        for entry in WalkDir::new(root_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.file_type().is_file())
        {
            let path = entry.path().to_path_buf();
            let indexer = self.clone();
            
            let task = tokio::spawn(async move {
                if let Err(e) = indexer.index_file(&path).await {
                    log::warn!("Failed to index file {:?}: {}", path, e);
                }
            });
            
            tasks.push(task);
        }

        // Wait for all indexing tasks to complete
        futures::future::join_all(tasks).await;

        // Build relationships after all files are indexed
        self.build_relationships().await?;

        Ok(())
    }

    /// Build relationships between code entities
    async fn build_relationships(&self) -> Result<()> {
        // This will be implemented to analyze cross-references,
        // inheritance hierarchies, and other relationships
        Ok(())
    }

    /// Advanced semantic search using cognitive weave and tacit knowledge
    pub async fn search(&self, query: &str, limit: usize) -> Result<Vec<CodeNode>> {
        let mut results = vec![];

        // First try cognitive weave search if available
        if let Some(cognitive_weave) = &self.cognitive_weave {
            let insight_particles = cognitive_weave.query_insights(query, limit).await?;

            let graph = self.code_graph.read().await;
            for particle in insight_particles {
                if let Some(node) = graph.nodes.get(&particle.id) {
                    results.push(node.clone());
                }
            }
        }

        // Fallback to traditional search if needed
        if results.len() < limit {
            let graph = self.code_graph.read().await;
            for node in graph.nodes.values() {
                if results.len() >= limit {
                    break;
                }

                // Enhanced search including tacit knowledge
                let matches_name = node.name.contains(query);
                let matches_signature = node.signature.as_ref().map_or(false, |s| s.contains(query));
                let matches_docstring = node.docstring.as_ref().map_or(false, |d| d.contains(query));

                // Check digital twin for tacit knowledge matches
                let matches_tacit = if let Some(twin) = self.digital_twins.get(&node.id) {
                    twin.tacit_knowledge.responsibility_allocation.contains(query) ||
                    twin.tacit_knowledge.implicit_assumptions.iter().any(|a| a.contains(query)) ||
                    twin.design_rationale.primary_purpose.contains(query)
                } else {
                    false
                };

                if matches_name || matches_signature || matches_docstring || matches_tacit {
                    results.push(node.clone());
                }
            }
        }

        Ok(results)
    }

    /// Get Code Digital Twin for an element
    pub async fn get_digital_twin(&self, element_id: &str) -> Option<code_digital_twin::CodeDigitalTwin> {
        self.digital_twins.get(element_id).map(|twin| twin.clone())
    }

    /// Query by temporal range using spatio-temporal index
    pub async fn query_temporal_range(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<spatio_temporal_index::TemporalElement>> {
        self.spatio_temporal_index.query_temporal_range(start_time, end_time).await
    }

    /// Query by spatial hierarchy
    pub async fn query_spatial_hierarchy(&self, hierarchy_path: &[String]) -> Result<Vec<String>> {
        self.spatio_temporal_index.query_spatial_hierarchy(hierarchy_path).await
    }

    /// Perform cognitive refinement on the indexed knowledge
    pub async fn perform_cognitive_refinement(&self) -> Result<()> {
        if let Some(cognitive_weave) = &self.cognitive_weave {
            cognitive_weave.perform_cognitive_refinement().await?;
        }
        Ok(())
    }

    /// Get quality metrics for the entire codebase
    pub async fn get_codebase_quality_metrics(&self) -> Result<CodebaseQualityMetrics> {
        let mut total_complexity = 0.0;
        let mut total_maintainability = 0.0;
        let mut total_testability = 0.0;
        let mut total_security = 0.0;
        let mut element_count = 0;

        for twin in self.digital_twins.iter() {
            let metrics = &twin.quality_metrics;
            total_complexity += metrics.complexity_score;
            total_maintainability += metrics.maintainability_index;
            total_testability += metrics.testability_score;
            total_security += metrics.security_score;
            element_count += 1;
        }

        let avg_complexity = if element_count > 0 { total_complexity / element_count as f64 } else { 0.0 };
        let avg_maintainability = if element_count > 0 { total_maintainability / element_count as f64 } else { 0.0 };
        let avg_testability = if element_count > 0 { total_testability / element_count as f64 } else { 0.0 };
        let avg_security = if element_count > 0 { total_security / element_count as f64 } else { 0.0 };

        Ok(CodebaseQualityMetrics {
            average_complexity: avg_complexity,
            average_maintainability: avg_maintainability,
            average_testability: avg_testability,
            average_security: avg_security,
            total_elements: element_count,
            technical_debt_items: self.count_technical_debt_items().await,
            code_smells: self.count_code_smells().await,
        })
    }

    async fn count_technical_debt_items(&self) -> usize {
        self.digital_twins.iter()
            .map(|twin| twin.tacit_knowledge.maintainability_factors.iter()
                .filter(|factor| factor.technical_debt.is_some())
                .count())
            .sum()
    }

    async fn count_code_smells(&self) -> usize {
        // This would be implemented to count code smells from tacit knowledge extraction
        0
    }

    /// Record access to an element for temporal analysis
    pub async fn record_element_access(&self, element_id: &str) -> Result<()> {
        let access_time = chrono::Utc::now();
        self.spatio_temporal_index.record_access(element_id, access_time).await
    }
}
}

impl Default for CodeIndexer {
    fn default() -> Self {
        Self::new()
    }
}
