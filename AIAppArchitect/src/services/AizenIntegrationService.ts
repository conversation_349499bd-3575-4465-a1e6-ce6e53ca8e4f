/**
 * Aizen Integration Service
 * Bridges Rust backend, Python AI backend, and React frontend
 */

import * as vscode from 'vscode';
import * as path from 'path';
import * as child_process from 'child_process';
// import axios from 'axios'; // Temporarily disabled for UI testing
// import WebSocket from 'ws'; // Temporarily disabled for UI testing

export interface AgentMetrics {
    totalAgents: number;
    activeAgents: number;
    completedTasks: number;
    failedTasks: number;
    averageExecutionTime: number;
    swarmEfficiency: number;
    evolutionScore: number;
}

export interface AgentInfo {
    id: string;
    name: string;
    type: string;
    status: 'idle' | 'busy' | 'error';
    currentTask?: string;
    capabilities: string[];
}

export class AizenIntegrationService {
    private pythonProcess: child_process.ChildProcess | null = null;
    private rustProcess: child_process.ChildProcess | null = null;
    private isConnected: boolean = false;
    private pythonBaseUrl: string = 'http://localhost:8000';
    private rustBaseUrl: string = 'http://localhost:8001';
    private pythonBackendPath: string;
    private rustBackendPath: string;
    // private websocket: WebSocket | null = null; // Temporarily disabled

    constructor() {
        const extensionPath = vscode.extensions.getExtension('aizen-ai.aizen-revolutionary-ai')?.extensionPath ||
                             path.join(__dirname, '..', '..');

        this.pythonBackendPath = path.join(extensionPath, 'python_backend_ai');
        this.rustBackendPath = path.join(extensionPath, 'target', 'release');
    }

    async initialize(): Promise<void> {
        try {
            console.log('🚀 Initializing Aizen Integration Service...');

            // For now, initialize in mock mode for testing
            // Backend integration will be added later
            console.log('⚠️ Running in mock mode - backend integration pending');

            this.isConnected = true;
            console.log('✅ Aizen Integration Service initialized successfully (mock mode)');

        } catch (error) {
            console.error('❌ Failed to initialize Aizen Integration Service:', error);
            // Don't throw error in mock mode
            this.isConnected = true;
            console.log('✅ Aizen Integration Service initialized in fallback mode');
        }
    }

    private async startPythonBackend(): Promise<void> {
        try {
            console.log('🐍 Starting Python AI Backend...');
            
            // Check if already running
            const isRunning = await this.checkPythonBackendHealth();
            if (isRunning) {
                console.log('✅ Python backend already running');
                return;
            }

            this.pythonProcess = child_process.spawn('python', [
                path.join(this.pythonBackendPath, 'start_server.py'),
                '--host', '127.0.0.1',
                '--port', '8000',
                '--log-level', 'INFO'
            ], {
                cwd: this.pythonBackendPath,
                stdio: 'pipe'
            });

            this.pythonProcess.stdout?.on('data', (data) => {
                console.log(`Python Backend: ${data}`);
            });

            this.pythonProcess.stderr?.on('data', (data) => {
                console.error(`Python Backend Error: ${data}`);
            });

            this.pythonProcess.on('error', (error) => {
                console.error('Python Backend Process Error:', error);
            });

            // Wait for server to start
            await this.waitForPythonServerStart();
            console.log('✅ Python AI Backend started successfully');
            
        } catch (error) {
            throw new Error(`Failed to start Python backend: ${error}`);
        }
    }

    private async startRustBackend(): Promise<void> {
        try {
            console.log('🦀 Starting Rust Backend...');
            
            // Check if Rust binary exists
            const rustBinaryPath = path.join(this.rustBackendPath, 'aizen_ai_extension.exe');
            
            // For now, we'll skip Rust backend if not built
            console.log('⚠️ Rust backend not yet integrated - using Python backend only');
            
        } catch (error) {
            console.log('⚠️ Rust backend unavailable, continuing with Python backend only');
        }
    }

    private async connectWebSocket(): Promise<void> {
        try {
            // For now, we'll use HTTP polling instead of WebSocket
            // WebSocket integration can be added later
            console.log('📡 Using HTTP polling for real-time updates');
        } catch (error) {
            console.log('⚠️ WebSocket connection failed, using HTTP polling');
        }
    }

    private async waitForPythonServerStart(): Promise<void> {
        // Mock implementation for UI testing
        console.log('⚠️ Mock mode: Skipping Python server health check');
        return Promise.resolve();
    }

    private async checkPythonBackendHealth(): Promise<boolean> {
        // Mock implementation for UI testing
        console.log('⚠️ Mock mode: Python backend health check always returns true');
        return true;
    }

    // Agent Management Methods
    async createAgent(agentType: string, config: any = {}): Promise<any> {
        // Return mock agent creation for testing
        console.log(`Creating ${agentType} agent with config:`, config);
        return {
            id: `agent-${Date.now()}`,
            name: config.name || `${agentType}-agent-${Date.now()}`,
            type: agentType,
            status: 'idle',
            created: new Date().toISOString()
        };
    }

    async getAllAgents(): Promise<AgentInfo[]> {
        // Return mock data for testing
        return [
            {
                id: 'agent-1',
                name: 'Code Assistant',
                type: 'code-generation',
                status: 'idle',
                currentTask: undefined,
                capabilities: ['code-generation', 'debugging', 'optimization']
            },
            {
                id: 'agent-2',
                name: 'Chat Assistant',
                type: 'conversational',
                status: 'busy',
                currentTask: 'Processing user query',
                capabilities: ['chat', 'explanation', 'help']
            }
        ];
    }

    async getMetrics(): Promise<AgentMetrics> {
        // Return mock metrics for testing
        return {
            totalAgents: 2,
            activeAgents: 1,
            completedTasks: 15,
            failedTasks: 1,
            averageExecutionTime: 2.5,
            swarmEfficiency: 0.85,
            evolutionScore: 0.92
        };
    }

    async executeTask(description: string, taskType: string = 'code-generation', context: any = {}): Promise<any> {
        // Return mock task execution for testing
        console.log(`Executing ${taskType} task: ${description}`, context);
        return {
            taskId: `task-${Date.now()}`,
            description,
            taskType,
            status: 'started',
            created: new Date().toISOString()
        };
    }

    // Framework Integration Methods
    async enableSwarmIntelligence(): Promise<void> {
        console.log('🐝 Swarm Intelligence enabled (mock mode)');
    }

    async enableEvolution(): Promise<void> {
        console.log('🧠 Evolution enabled (mock mode)');
    }

    async executeWithTaskWeaver(userQuery: string, context: any = {}): Promise<any> {
        console.log('🔧 TaskWeaver execution (mock mode):', userQuery, context);
        return {
            result: `TaskWeaver processed: ${userQuery}`,
            status: 'completed',
            timestamp: new Date().toISOString()
        };
    }

    dispose(): void {
        if (this.pythonProcess) {
            this.pythonProcess.kill();
            this.pythonProcess = null;
        }
        
        if (this.rustProcess) {
            this.rustProcess.kill();
            this.rustProcess = null;
        }
        
        // WebSocket cleanup temporarily disabled
        // if (this.websocket) {
        //     this.websocket.close();
        //     this.websocket = null;
        // }
        
        this.isConnected = false;
        console.log('🔄 Aizen Integration Service disposed');
    }
}
