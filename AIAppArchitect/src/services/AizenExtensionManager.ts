/**
 * Aizen AI Extension Manager
 * Central coordinator for all extension functionality
 */

import * as vscode from 'vscode';
import { AizenChatViewProvider } from '../providers/AizenChatViewProvider';
import { AizenSettingsProvider } from '../providers/AizenSettingsProvider';
import { AizenSettingsService } from './AizenSettingsService';
import { AizenIntegrationService } from './AizenIntegrationService';
import { MCPHub } from '../mcp/hub';

export class AizenExtensionManager {
    private static _instance: AizenExtensionManager;
    
    private _context: vscode.ExtensionContext;
    private _chatProvider: AizenChatViewProvider | undefined;
    private _settingsService: AizenSettingsService;
    private _integrationService: AizenIntegrationService;
    private _mcpHub: MCPHub | undefined;
    private _disposables: vscode.Disposable[] = [];
    private _isActivated = false;

    private constructor(context: vscode.ExtensionContext) {
        this._context = context;
        this._settingsService = AizenSettingsService.getInstance();
        this._integrationService = new AizenIntegrationService();
    }

    public static getInstance(context?: vscode.ExtensionContext): AizenExtensionManager {
        if (!AizenExtensionManager._instance && context) {
            AizenExtensionManager._instance = new AizenExtensionManager(context);
        }
        return AizenExtensionManager._instance;
    }

    public setMCPHub(mcpHub: MCPHub): void {
        this._mcpHub = mcpHub;
    }

    /**
     * Activate the extension with all components
     */
    public async activate(): Promise<void> {
        if (this._isActivated) {
            console.log('🔄 Aizen AI Extension already activated');
            return;
        }

        try {
            console.log('🚀 Activating Aizen AI Extension...');

            // Initialize services
            await this._initializeServices();

            // Register providers
            this._registerProviders();

            // Register commands
            this._registerCommands();

            // Setup event listeners
            this._setupEventListeners();

            // Initialize status bar
            this._initializeStatusBar();

            this._isActivated = true;
            console.log('✅ Aizen AI Extension activated successfully');

            // Show welcome message if first time
            await this._showWelcomeMessage();

        } catch (error) {
            console.error('❌ Failed to activate Aizen AI Extension:', error);
            vscode.window.showErrorMessage(`Failed to activate Aizen AI: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }

    /**
     * Deactivate the extension and cleanup resources
     */
    public async deactivate(): Promise<void> {
        console.log('🔄 Deactivating Aizen AI Extension...');

        try {
            // Dispose all disposables
            this._disposables.forEach(disposable => {
                try {
                    disposable.dispose();
                } catch (error) {
                    console.error('Error disposing resource:', error);
                }
            });
            this._disposables = [];

            // Cleanup services
            if (this._integrationService) {
                await this._integrationService.dispose();
            }

            this._isActivated = false;
            console.log('✅ Aizen AI Extension deactivated successfully');

        } catch (error) {
            console.error('❌ Error during deactivation:', error);
        }
    }

    /**
     * Get the chat provider instance
     */
    public getChatProvider(): AizenChatViewProvider | undefined {
        return this._chatProvider;
    }

    /**
     * Get the settings service instance
     */
    public getSettingsService(): AizenSettingsService {
        return this._settingsService;
    }

    /**
     * Get the integration service instance
     */
    public getIntegrationService(): AizenIntegrationService {
        return this._integrationService;
    }

    private async _initializeServices(): Promise<void> {
        console.log('⚙️ Initializing services...');

        // Validate settings
        const validation = this._settingsService.validateSettings();
        if (!validation.isValid) {
            console.warn('⚠️ Settings validation warnings:', validation.warnings);
            
            // Show warnings to user
            for (const warning of validation.warnings) {
                vscode.window.showWarningMessage(`Aizen AI: ${warning}`);
            }
        }

        // Initialize integration service
        await this._integrationService.initialize();
        
        console.log('✅ Services initialized');
    }

    private _registerProviders(): void {
        console.log('📝 Registering providers...');

        // Register chat view provider
        this._chatProvider = new AizenChatViewProvider(this._context.extensionUri, this._integrationService);
        const chatProviderDisposable = vscode.window.registerWebviewViewProvider(
            'aizen.chatView',
            this._chatProvider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        );
        this._disposables.push(chatProviderDisposable);

        console.log('✅ Providers registered');
    }

    private _registerCommands(): void {
        console.log('🎯 Registering commands...');

        const commands = [
            // Settings commands
            {
                command: 'aizen.showSettings',
                handler: () => this._showSettings()
            },
            {
                command: 'aizen.resetSettings',
                handler: () => this._resetSettings()
            },
            
            // Chat commands
            {
                command: 'aizen.newChat',
                handler: () => this._newChat()
            },
            {
                command: 'aizen.clearChat',
                handler: () => this._clearChat()
            },
            
            // Integration commands
            {
                command: 'aizen.testConnection',
                handler: () => this._testConnection()
            },
            {
                command: 'aizen.refreshAgents',
                handler: () => this._refreshAgents()
            },

            // Development commands
            {
                command: 'aizen.showLogs',
                handler: () => this._showLogs()
            },
            {
                command: 'aizen.exportSettings',
                handler: () => this._exportSettings()
            },
            {
                command: 'aizen.importSettings',
                handler: () => this._importSettings()
            }
        ];

        commands.forEach(({ command, handler }) => {
            const disposable = vscode.commands.registerCommand(command, handler);
            this._disposables.push(disposable);
        });

        console.log(`✅ ${commands.length} commands registered`);
    }

    private _setupEventListeners(): void {
        console.log('👂 Setting up event listeners...');

        // Listen for settings changes
        const settingsListener = this._settingsService.onDidChangeSettings(settings => {
            console.log('⚙️ Settings changed:', settings);
            this._onSettingsChanged(settings);
        });
        this._disposables.push(settingsListener);

        // Listen for workspace changes
        const workspaceListener = vscode.workspace.onDidChangeWorkspaceFolders(event => {
            console.log('📁 Workspace folders changed:', event);
            this._onWorkspaceChanged(event);
        });
        this._disposables.push(workspaceListener);

        // Listen for configuration changes
        const configListener = vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration('aizen')) {
                console.log('🔧 Aizen configuration changed');
                this._onConfigurationChanged(event);
            }
        });
        this._disposables.push(configListener);

        console.log('✅ Event listeners setup');
    }

    private _initializeStatusBar(): void {
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.text = '$(robot) Aizen AI';
        statusBarItem.tooltip = 'Aizen AI - Click to open chat';
        statusBarItem.command = 'aizen.chatView.focus';
        statusBarItem.show();
        
        this._disposables.push(statusBarItem);
        console.log('✅ Status bar initialized');
    }

    private async _showWelcomeMessage(): Promise<void> {
        const settings = this._settingsService.getSettings();
        if (settings.features.notifications) {
            const isFirstTime = !this._context.globalState.get('aizen.hasShownWelcome', false);
            
            if (isFirstTime) {
                const action = await vscode.window.showInformationMessage(
                    'Welcome to Aizen AI! 🚀 Your revolutionary AI assistant is ready.',
                    'Open Settings',
                    'Start Chat',
                    'Don\'t show again'
                );

                switch (action) {
                    case 'Open Settings':
                        await vscode.commands.executeCommand('aizen.showSettings');
                        break;
                    case 'Start Chat':
                        await vscode.commands.executeCommand('aizen.chatView.focus');
                        break;
                }

                await this._context.globalState.update('aizen.hasShownWelcome', true);
            }
        }
    }

    // Command handlers
    private async _showSettings(): Promise<void> {
        AizenSettingsProvider.createOrShow(this._context.extensionUri, this._context, this._mcpHub);
    }

    private async _resetSettings(): Promise<void> {
        const confirm = await vscode.window.showWarningMessage(
            'Are you sure you want to reset all Aizen AI settings to defaults?',
            { modal: true },
            'Reset Settings'
        );

        if (confirm === 'Reset Settings') {
            await this._settingsService.resetSettings();
        }
    }

    private async _newChat(): Promise<void> {
        if (this._chatProvider) {
            // TODO: Implement newChat method
            // await this._chatProvider.newChat();
        }
        await vscode.commands.executeCommand('aizen.chatView.focus');
    }

    private async _clearChat(): Promise<void> {
        if (this._chatProvider) {
            // TODO: Implement clearChat method
            // await this._chatProvider.clearChat();
        }
    }

    private async _testConnection(): Promise<void> {
        try {
            // TODO: Implement testConnection method
            // const result = await this._integrationService.testConnection();
            const result = { success: true, error: '' };
            if (result.success) {
                vscode.window.showInformationMessage('✅ Connection test successful!');
            } else {
                vscode.window.showErrorMessage(`❌ Connection test failed: ${result.error}`);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`❌ Connection test error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    private async _refreshAgents(): Promise<void> {
        // TODO: Implement refreshAgents method
        // await this._integrationService.refreshAgents();
        vscode.window.showInformationMessage('🔄 Agents refreshed!');
        vscode.window.showInformationMessage('🔄 Agents refreshed');
    }

    private async _showLogs(): Promise<void> {
        // Implementation for showing logs
        vscode.window.showInformationMessage('📋 Logs feature coming soon!');
    }

    private async _exportSettings(): Promise<void> {
        // Implementation for exporting settings
        vscode.window.showInformationMessage('📤 Export settings feature coming soon!');
    }

    private async _importSettings(): Promise<void> {
        // Implementation for importing settings
        vscode.window.showInformationMessage('📥 Import settings feature coming soon!');
    }

    // Event handlers
    private _onSettingsChanged(settings: any): void {
        // Handle settings changes
        console.log('Settings changed, updating components...');
    }

    private _onWorkspaceChanged(event: vscode.WorkspaceFoldersChangeEvent): void {
        // Handle workspace changes
        console.log('Workspace changed, updating context...');
    }

    private _onConfigurationChanged(event: vscode.ConfigurationChangeEvent): void {
        // Handle configuration changes
        console.log('Configuration changed, reloading...');
    }
}
