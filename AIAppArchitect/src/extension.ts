/**
 * Aizen AI Extension - VS Code Entry Point
 * Connects React UI to Python AI backend with real AI frameworks
 */

import * as vscode from 'vscode';
import { AizenExtensionManager } from './services/AizenExtensionManager';
import { AizenIntegrationService } from './services/AizenIntegrationService';
import { AizenChatViewProvider } from './providers/AizenChatViewProvider';
import { MCPHub } from './mcp/hub';
import { MCPSecurityManager } from './mcp/security';
import { ExaMCPServerConfig } from './mcp/servers/exa';
import { FirecrawlMCPServerConfig } from './mcp/servers/firecrawl';
import { runMCPTests } from './mcp/test';
import { MCPConfigEditorProvider } from './mcp/MCPConfigEditorProvider';

// Extension manager instance
let extensionManager: AizenExtensionManager;
let mcpHub: MCPHub;
let mcpSecurity: MCPSecurityManager;
let mcpConfigEditorProvider: MCPConfigEditorProvider;


export async function activate(context: vscode.ExtensionContext) {
    console.log('🚀 Activating Aizen AI Extension - Revolutionary AI Assistant');

    try {
        console.log('🔑 Built-in MCP servers will be configured with embedded API keys');

        // Initialize MCP Security Manager
        mcpSecurity = new MCPSecurityManager(context);

        // Initialize Integration Service
        const integrationService = new AizenIntegrationService();

        // Register Chat View Provider
        const chatViewProvider = new AizenChatViewProvider(context.extensionUri, integrationService);
        context.subscriptions.push(
            vscode.window.registerWebviewViewProvider('aizen.chatView', chatViewProvider)
        );
        console.log('✅ Chat view provider registered');

        // Initialize MCP Hub with new architecture
        mcpHub = new MCPHub(context);
        mcpHub.initialize().then(async () => {
            console.log('✅ MCP Hub initialized successfully');

            // Auto-configure built-in servers
            await initializeBuiltInServers();
        }).catch((error) => {
            console.error('❌ MCP Hub initialization failed:', error);
        });

        // Initialize MCP Configuration Editor Provider
        mcpConfigEditorProvider = new MCPConfigEditorProvider(context);
        console.log('✅ MCP Configuration Editor Provider initialized');

        // Register MCP commands
        context.subscriptions.push(
            vscode.commands.registerCommand('aizen.test.basic', () => {
                console.log('🎯 Basic test command executed!');
                vscode.window.showInformationMessage('🎯 Basic test works! MCP commands should work too.');
            }),

            vscode.commands.registerCommand('aizen.mcp.status', async () => {
                try {
                    const servers = mcpHub.getServers();
                    const connectedServers = mcpHub.getConnectedServers();
                    const tools = mcpHub.getAllTools();

                    const message = `🔌 MCP Status:\n` +
                        `• Total Servers: ${servers.length}\n` +
                        `• Connected: ${connectedServers.length}\n` +
                        `• Available Tools: ${tools.length}\n\n` +
                        `Connected Servers:\n${connectedServers.map(s => `  • ${s.config.name} (${s.tools?.length || 0} tools)`).join('\n')}`;

                    vscode.window.showInformationMessage(message);
                } catch (error) {
                    vscode.window.showErrorMessage(`MCP Status check failed: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.addExaServer', async () => {
                try {
                    const apiKey = await vscode.window.showInputBox({
                        prompt: 'Enter your Exa API Key',
                        password: true,
                        placeHolder: 'exa_...'
                    });

                    if (apiKey) {
                        const config = {
                            ...ExaMCPServerConfig,
                            env: {
                                ...ExaMCPServerConfig.env,
                                'Authorization': `Bearer ${apiKey}`
                            }
                        };

                        const serverId = await mcpHub.addServer(config);
                        vscode.window.showInformationMessage(`✅ Exa server added successfully! Server ID: ${serverId}`);
                    }
                } catch (error) {
                    vscode.window.showErrorMessage(`Failed to add Exa server: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.addFirecrawlServer', async () => {
                try {
                    const apiKey = await vscode.window.showInputBox({
                        prompt: 'Enter your Firecrawl API Key',
                        password: true,
                        placeHolder: 'fc-...'
                    });

                    if (apiKey) {
                        const config = {
                            ...FirecrawlMCPServerConfig,
                            env: {
                                ...FirecrawlMCPServerConfig.env,
                                'Authorization': `Bearer ${apiKey}`
                            }
                        };

                        const serverId = await mcpHub.addServer(config);
                        vscode.window.showInformationMessage(`✅ Firecrawl server added successfully! Server ID: ${serverId}`);
                    }
                } catch (error) {
                    vscode.window.showErrorMessage(`Failed to add Firecrawl server: ${error}`);
                }
            }),
            vscode.commands.registerCommand('aizen.mcp.testExaSearch', async () => {
                try {
                    console.log('🧪 Testing Exa search...');
                    const servers = mcpHub.getServers().filter(s => s.config.name.includes('Exa'));

                    if (servers.length === 0) {
                        vscode.window.showWarningMessage('No Exa servers configured. Add one first.');
                        return;
                    }

                    const result = await mcpHub.executeTool(servers[0].id, 'web_search_exa', {
                        query: 'latest AI developments 2025',
                        numResults: 3
                    });

                    vscode.window.showInformationMessage(`✅ Exa search test successful! Found ${result.content?.length || 0} results.`);
                } catch (error) {
                    console.error('❌ Exa test failed:', error);
                    vscode.window.showErrorMessage(`❌ Exa test failed: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.testFirecrawlScrape', async () => {
                try {
                    console.log('🧪 Testing Firecrawl scrape...');
                    const servers = mcpHub.getServers().filter(s => s.config.name.includes('Firecrawl'));

                    if (servers.length === 0) {
                        vscode.window.showWarningMessage('No Firecrawl servers configured. Add one first.');
                        return;
                    }

                    const result = await mcpHub.executeTool(servers[0].id, 'firecrawl_scrape', {
                        url: 'https://example.com',
                        formats: ['markdown']
                    });

                    const content = result.content?.[0];
                    const contentLength = content?.type === 'text' ? content.text.length : 0;
                    vscode.window.showInformationMessage(`✅ Firecrawl scrape test successful! Content length: ${contentLength} chars.`);
                } catch (error) {
                    console.error('❌ Firecrawl test failed:', error);
                    vscode.window.showErrorMessage(`❌ Firecrawl test failed: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.listTools', async () => {
                try {
                    const tools = mcpHub.getAllTools();
                    const toolsList = tools.map(t => `• ${t.tool.name} (${t.serverId})`).join('\n');
                    const message = `� Available MCP Tools (${tools.length}):\n\n${toolsList || 'No tools available'}`;
                    vscode.window.showInformationMessage(message);
                } catch (error) {
                    vscode.window.showErrorMessage(`Failed to list tools: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.mcp.runTests', async () => {
                try {
                    console.log('🧪 Running comprehensive MCP tests...');
                    vscode.window.showInformationMessage('🧪 Running MCP tests... Check console for details.');
                    await runMCPTests(context);
                } catch (error) {
                    console.error('❌ MCP tests failed:', error);
                    vscode.window.showErrorMessage(`MCP tests failed: ${error}`);
                }
            }),

            vscode.commands.registerCommand('aizen.test.simple', () => {
                console.log('🎯 Simple test command executed!');
                vscode.window.showInformationMessage('🎯 Simple test command works!');
            }),

            vscode.commands.registerCommand('aizen.mcp.configEditor', async () => {
                try {
                    console.log('🔧 Opening MCP Configuration Editor...');
                    await mcpConfigEditorProvider.openConfigEditor();
                } catch (error) {
                    console.error('❌ Failed to open MCP Configuration Editor:', error);
                    vscode.window.showErrorMessage(`Failed to open MCP Configuration Editor: ${error}`);
                }
            })
        );

        // Initialize Extension Manager
        extensionManager = AizenExtensionManager.getInstance(context);
        extensionManager.setMCPHub(mcpHub);
        await extensionManager.activate();

        console.log('✅ Aizen AI Extension activated successfully');

    } catch (error) {
        console.error('❌ Failed to activate Aizen AI Extension:', error);
        vscode.window.showErrorMessage(`Failed to activate Aizen AI: ${error instanceof Error ? error.message : String(error)}`);
    }
}

// Initialize built-in MCP servers
async function initializeBuiltInServers() {
    try {
        console.log('🔧 Initializing built-in MCP servers...');

        // Add Exa server with direct API key
        const exaConfig = {
            ...ExaMCPServerConfig,
            enabled: true,
            autoStart: true,
            env: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer f01e507f-cdd2-454d-adcf-545d24035692`,
                'EXA_API_KEY': 'f01e507f-cdd2-454d-adcf-545d24035692'
            }
        };

        const exaServerId = await mcpHub.addServer(exaConfig);
        console.log('✅ Exa server initialized:', exaServerId);

        // Add Firecrawl server with direct API key
        const firecrawlConfig = {
            ...FirecrawlMCPServerConfig,
            enabled: true,
            autoStart: true,
            env: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer fc-73581888d5374a1a99893178925cc8bb`,
                'FIRECRAWL_API_KEY': 'fc-73581888d5374a1a99893178925cc8bb'
            }
        };

        const firecrawlServerId = await mcpHub.addServer(firecrawlConfig);
        console.log('✅ Firecrawl server initialized:', firecrawlServerId);

        vscode.window.showInformationMessage('🚀 Built-in MCP servers (Exa & Firecrawl) are ready!');

    } catch (error) {
        console.error('❌ Failed to initialize built-in servers:', error);
        vscode.window.showErrorMessage(`Failed to initialize MCP servers: ${error}`);
    }
}

export async function deactivate() {
    console.log('🔄 Deactivating Aizen AI Extension...');

    if (extensionManager) {
        await extensionManager.deactivate();
    }

    if (mcpHub) {
        await mcpHub.dispose();
    }

    if (mcpSecurity) {
        await mcpSecurity.dispose();
    }

    console.log('✅ Aizen AI Extension deactivated');
}