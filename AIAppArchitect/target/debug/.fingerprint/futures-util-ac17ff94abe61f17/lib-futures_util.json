{"rustc": 15597765236515928571, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 15664164965450599468, "path": 1528268494982096961, "deps": [[5103565458935487, "futures_io", false, 13834776574502142667], [1615478164327904835, "pin_utils", false, 12700230289537703848], [1811549171721445101, "futures_channel", false, 12962476199214507895], [1906322745568073236, "pin_project_lite", false, 5980172383742024024], [3129130049864710036, "memchr", false, 17916188484455641544], [6955678925937229351, "slab", false, 16711710020489308264], [7013762810557009322, "futures_sink", false, 4626131793991425911], [7620660491849607393, "futures_core", false, 12744854022782223202], [10565019901765856648, "futures_macro", false, 16482024696434666848], [16240732885093539806, "futures_task", false, 17929601246073952608]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-ac17ff94abe61f17/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}