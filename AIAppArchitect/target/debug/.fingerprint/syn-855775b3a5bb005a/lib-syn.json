{"rustc": 15597765236515928571, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 11876527447619405325, "path": 7634937744895078854, "deps": [[1988483478007900009, "unicode_ident", false, 8105898112184850355], [3060637413840920116, "proc_macro2", false, 3298921773751450005], [17990358020177143287, "quote", false, 5839799800268131780]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-855775b3a5bb005a/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}