{"rustc": 4920195589138853144, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 5226164553319255144, "path": 18191206003546278029, "deps": [[5103565458935487, "futures_io", false, 12260919695619466618], [1615478164327904835, "pin_utils", false, 13085848428547479963], [1811549171721445101, "futures_channel", false, 13334361147997209226], [1906322745568073236, "pin_project_lite", false, 4504485402618370691], [3129130049864710036, "memchr", false, 1642944573890712273], [6955678925937229351, "slab", false, 14812784718514000733], [7013762810557009322, "futures_sink", false, 7943397427381368556], [7620660491849607393, "futures_core", false, 9732770850438626906], [10565019901765856648, "futures_macro", false, 4504016681612978375], [16240732885093539806, "futures_task", false, 3845133248883031503]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-af0abced52145588\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}