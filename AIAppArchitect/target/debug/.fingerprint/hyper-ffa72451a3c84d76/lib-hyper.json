{"rustc": 15597765236515928571, "features": "[\"client\", \"h2\", \"http1\", \"http2\", \"runtime\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 10809724437792986082, "path": 17588115579690358319, "deps": [[784494742817713399, "tower_service", false, 3707634968429648613], [1569313478171189446, "want", false, 12526886873738958233], [1811549171721445101, "futures_channel", false, 12962476199214507895], [1906322745568073236, "pin_project_lite", false, 5980172383742024024], [4405182208873388884, "http", false, 7657167772215774522], [6163892036024256188, "httparse", false, 9332776119220171647], [6304235478050270880, "httpdate", false, 4861425073329834650], [7620660491849607393, "futures_core", false, 12744854022782223202], [7695812897323945497, "itoa", false, 6536226791438237493], [8606274917505247608, "tracing", false, 20989095090208648], [8915503303801890683, "http_body", false, 7369973612794184163], [9538054652646069845, "tokio", false, 12394086576767263998], [10629569228670356391, "futures_util", false, 15856140439400279777], [12614995553916589825, "socket2", false, 15219654922297176294], [13809605890706463735, "h2", false, 2887034219739338617], [16066129441945555748, "bytes", false, 14325424245145942293]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hyper-ffa72451a3c84d76/dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}