{"rustc": 15597765236515928571, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 5226164553319255144, "path": 12907717444123387044, "deps": [[5103565458935487, "futures_io", false, 3164859862449992588], [1811549171721445101, "futures_channel", false, 14529205780177840798], [7013762810557009322, "futures_sink", false, 4125538769169501075], [7620660491849607393, "futures_core", false, 6505681253592386983], [10629569228670356391, "futures_util", false, 7804840261015003195], [12779779637805422465, "futures_executor", false, 1221117527114263071], [16240732885093539806, "futures_task", false, 3362425666669728064]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-9bd54bd175e59936/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}