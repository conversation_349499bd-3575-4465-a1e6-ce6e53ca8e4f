{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 10809724437792986082, "path": 106355859371738648, "deps": [[10411997081178400487, "cfg_if", false, 3601889425575385129]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/encoding_rs-e98f4a70d4b1776b/dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}