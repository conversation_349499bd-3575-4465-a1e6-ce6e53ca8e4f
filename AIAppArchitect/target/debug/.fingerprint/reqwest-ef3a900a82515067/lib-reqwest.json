{"rustc": 15597765236515928571, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 11876527447619405325, "path": 13142604898180357124, "deps": [[40386456601120721, "percent_encoding", false, 13322049627125437806], [95042085696191081, "ipnet", false, 2582806099207778621], [264090853244900308, "sync_wrapper", false, 8556158231545619186], [784494742817713399, "tower_service", false, 2488572399342097396], [1288403060204016458, "tokio_util", false, 8344271524910361214], [1906322745568073236, "pin_project_lite", false, 2706982381983368045], [3150220818285335163, "url", false, 14268177396360611257], [3722963349756955755, "once_cell", false, 3324833106109614183], [4405182208873388884, "http", false, 11857689287279957576], [5986029879202738730, "log", false, 13780663036076696344], [7414427314941361239, "hyper", false, 5670314564428584250], [7620660491849607393, "futures_core", false, 6505681253592386983], [8915503303801890683, "http_body", false, 5727879142446699189], [9538054652646069845, "tokio", false, 12935088067770535089], [9689903380558560274, "serde", false, 14532101420022382631], [10229185211513642314, "mime", false, 12469393369911010561], [10629569228670356391, "futures_util", false, 7804840261015003195], [12186126227181294540, "tokio_native_tls", false, 11224722353012207960], [12367227501898450486, "hyper_tls", false, 4048318954177661793], [13809605890706463735, "h2", false, 10534717285630412045], [14564311161534545801, "encoding_rs", false, 4176429088581509986], [15367738274754116744, "serde_json", false, 16774102614742854356], [16066129441945555748, "bytes", false, 1943542103241172407], [16311359161338405624, "rustls_pemfile", false, 5176220279851400433], [16542808166767769916, "serde_urlencoded", false, 4656908064336306339], [16785601910559813697, "native_tls_crate", false, 5648089902914751514], [18066890886671768183, "base64", false, 18265207252375770543]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-ef3a900a82515067/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}