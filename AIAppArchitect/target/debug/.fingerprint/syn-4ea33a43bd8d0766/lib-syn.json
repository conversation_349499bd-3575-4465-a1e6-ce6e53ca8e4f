{"rustc": 15597765236515928571, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 7634937744895078854, "deps": [[1988483478007900009, "unicode_ident", false, 13357245772266255491], [3060637413840920116, "proc_macro2", false, 16665467933488650730], [17990358020177143287, "quote", false, 9447608864428910967]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-4ea33a43bd8d0766/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}