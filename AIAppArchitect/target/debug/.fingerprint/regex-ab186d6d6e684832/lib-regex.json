{"rustc": 15597765236515928571, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 11876527447619405325, "path": 2018800930230103155, "deps": [[555019317135488525, "regex_automata", false, 13078772505036450409], [2779309023524819297, "aho_corasick", false, 8548823507061371235], [3129130049864710036, "memchr", false, 4723945815080860588], [9408802513701742484, "regex_syntax", false, 4977089696629377831]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-ab186d6d6e684832/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}