{"rustc": 15597765236515928571, "features": "[\"default\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 11876527447619405325, "path": 9736768090327714799, "deps": [[2924422107542798392, "libc", false, 2319784687849789562], [3722963349756955755, "once_cell", false, 3324833106109614183], [6635237767502169825, "foreign_types", false, 4032717856601769248], [7896293946984509699, "bitflags", false, 5186732035925040739], [8607891082156236373, "build_script_build", false, 17699766159577876643], [9070360545695802481, "ffi", false, 284238142743932389], [10099563100786658307, "openssl_macros", false, 17414167139195929928], [10411997081178400487, "cfg_if", false, 10206829899388294296]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/openssl-f92e29745e5538e6/dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}