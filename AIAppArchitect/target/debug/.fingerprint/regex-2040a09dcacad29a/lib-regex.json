{"rustc": 15597765236515928571, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 10809724437792986082, "path": 2018800930230103155, "deps": [[555019317135488525, "regex_automata", false, 10613420036460037205], [2779309023524819297, "aho_corasick", false, 5366656599192138608], [3129130049864710036, "memchr", false, 17916188484455641544], [9408802513701742484, "regex_syntax", false, 3227531215982206569]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-2040a09dcacad29a/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}