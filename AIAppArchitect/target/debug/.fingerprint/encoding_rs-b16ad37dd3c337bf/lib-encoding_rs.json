{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 11876527447619405325, "path": 106355859371738648, "deps": [[10411997081178400487, "cfg_if", false, 10206829899388294296]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/encoding_rs-b16ad37dd3c337bf/dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}