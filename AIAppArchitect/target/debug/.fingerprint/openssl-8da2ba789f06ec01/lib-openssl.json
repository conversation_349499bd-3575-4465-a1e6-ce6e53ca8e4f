{"rustc": 15597765236515928571, "features": "[\"default\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 10809724437792986082, "path": 9736768090327714799, "deps": [[2924422107542798392, "libc", false, 796874582541399489], [3722963349756955755, "once_cell", false, 6427495708372001534], [6635237767502169825, "foreign_types", false, 8084417519766610630], [7896293946984509699, "bitflags", false, 16310486851812739865], [8607891082156236373, "build_script_build", false, 17699766159577876643], [9070360545695802481, "ffi", false, 17978670465425984929], [10099563100786658307, "openssl_macros", false, 17414167139195929928], [10411997081178400487, "cfg_if", false, 3601889425575385129]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/openssl-8da2ba789f06ec01/dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}