<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aizen AI Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .status {
            margin-top: 2rem;
            padding: 10px;
            background: rgba(0, 255, 0, 0.2);
            border-radius: 8px;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Aizen AI</h1>
        <p>Revolutionary AI Assistant</p>
        <button class="button" onclick="testFunction()">Test Connection</button>
        <div class="status" id="status">
            ✅ Webview loaded successfully!
        </div>
    </div>
    
    <script>
        function testFunction() {
            document.getElementById('status').innerHTML = '🔄 Testing VS Code integration...';
            
            // Test VS Code API
            if (typeof acquireVsCodeApi !== 'undefined') {
                const vscode = acquireVsCodeApi();
                vscode.postMessage({
                    command: 'test',
                    message: 'Hello from webview!'
                });
                document.getElementById('status').innerHTML = '✅ VS Code API working!';
            } else {
                document.getElementById('status').innerHTML = '❌ VS Code API not available';
            }
        }
        
        // Auto-test on load
        setTimeout(testFunction, 1000);
        
        console.log('🎉 Aizen AI webview loaded!');
    </script>
</body>
</html>
