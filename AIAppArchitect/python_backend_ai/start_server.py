#!/usr/bin/env python3
"""
Aizen AI Extension - Python Backend Startup Script
Production-ready startup script with proper configuration
"""

import os
import sys
import asyncio
import argparse
import logging
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

import uvicorn
import structlog
from api.main import create_app


def setup_logging(log_level: str = "INFO", log_format: str = "json"):
    """Setup structured logging"""
    
    if log_format == "json":
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ]
    else:
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.dev.ConsoleRenderer()
        ]
    
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Set log level
    logging.basicConfig(level=getattr(logging, log_level.upper()))


def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        "fastapi",
        "uvicorn",
        "pydantic",
        "structlog",
        "openai",
        "anthropic",
        "langchain",
        "numpy"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_environment():
    """Check environment configuration"""
    logger = structlog.get_logger(__name__)
    
    # Check for API keys
    api_keys = {
        "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY"),
        "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY"),
    }
    
    missing_keys = [key for key, value in api_keys.items() if not value]
    
    if missing_keys:
        logger.warning("Missing API keys", missing_keys=missing_keys)
        print("⚠️  Warning: Some API keys are not set:")
        for key in missing_keys:
            print(f"   - {key}")
        print("AI services may not work properly without these keys.")
    else:
        logger.info("All API keys configured")
    
    # Check optional dependencies
    optional_deps = {
        "E2B": "e2b_code_interpreter",
        "Docker": "docker",
        "Git": "git"
    }
    
    available_deps = []
    unavailable_deps = []
    
    for name, package in optional_deps.items():
        try:
            __import__(package)
            available_deps.append(name)
        except ImportError:
            unavailable_deps.append(name)
    
    if available_deps:
        logger.info("Optional dependencies available", deps=available_deps)
    
    if unavailable_deps:
        logger.warning("Optional dependencies unavailable", deps=unavailable_deps)
        print(f"ℹ️  Optional features unavailable: {', '.join(unavailable_deps)}")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Aizen AI Extension Backend Server")
    
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="Host to bind to (default: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind to (default: 8000)"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="Number of worker processes (default: 1)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Log level (default: INFO)"
    )
    
    parser.add_argument(
        "--log-format",
        choices=["json", "console"],
        default="console",
        help="Log format (default: console)"
    )
    
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="Check dependencies and exit"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level, args.log_format)
    logger = structlog.get_logger(__name__)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    if args.check_deps:
        print("✅ All required dependencies are installed")
        sys.exit(0)
    
    # Check environment
    check_environment()
    
    # Create FastAPI app
    app = create_app()
    
    # Configure uvicorn
    config = {
        "app": app,
        "host": args.host,
        "port": args.port,
        "log_level": args.log_level.lower(),
        "access_log": True,
    }
    
    if args.reload:
        config["reload"] = True
        config["reload_dirs"] = [str(backend_dir)]
    else:
        config["workers"] = args.workers
    
    # Print startup information
    print("🚀 Starting Aizen AI Extension Backend")
    print(f"   Host: {args.host}")
    print(f"   Port: {args.port}")
    print(f"   Log Level: {args.log_level}")
    print(f"   Workers: {args.workers if not args.reload else '1 (reload mode)'}")
    print(f"   Reload: {'Enabled' if args.reload else 'Disabled'}")
    print()
    print(f"📡 API Documentation: http://{args.host}:{args.port}/docs")
    print(f"🔍 Health Check: http://{args.host}:{args.port}/health")
    print()
    
    logger.info("Starting Aizen AI Backend server", 
                host=args.host, 
                port=args.port,
                workers=args.workers,
                reload=args.reload)
    
    try:
        # Start server
        uvicorn.run(**config)
        
    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
        print("\n👋 Shutting down Aizen AI Backend...")
        
    except Exception as e:
        logger.error("Server startup failed", error=str(e))
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
