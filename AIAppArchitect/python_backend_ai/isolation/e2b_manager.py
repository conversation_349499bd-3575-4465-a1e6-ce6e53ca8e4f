"""
E2B Execution Manager - Production Implementation
Secure code execution with E2B sandboxes and Git worktree isolation
"""

import asyncio
import uuid
import tempfile
import shutil
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import structlog
import git
from datetime import datetime, timedelta

# E2B imports
try:
    from e2b_code_interpreter import Sandbox
    E2B_AVAILABLE = True
except ImportError:
    E2B_AVAILABLE = False
    logger.warning("E2B not available, using fallback execution")

logger = structlog.get_logger(__name__)


class ExecutionEnvironment(str, Enum):
    """Types of execution environments"""
    E2B_SANDBOX = "e2b_sandbox"
    DOCKER_CONTAINER = "docker_container"
    LOCAL_PROCESS = "local_process"
    GIT_WORKTREE = "git_worktree"


@dataclass
class ExecutionRequest:
    """Request for code execution"""
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    code: str = ""
    language: str = "python"
    working_directory: str = ""
    environment_type: ExecutionEnvironment = ExecutionEnvironment.E2B_SANDBOX
    timeout: int = 300  # 5 minutes
    memory_limit: str = "512m"
    cpu_limit: str = "1"
    network_access: bool = False
    file_access: List[str] = field(default_factory=list)
    environment_variables: Dict[str, str] = field(default_factory=dict)


@dataclass
class ExecutionResult:
    """Result of code execution"""
    request_id: str
    success: bool
    output: str = ""
    error: Optional[str] = None
    exit_code: int = 0
    execution_time: float = 0.0
    memory_usage: float = 0.0
    files_created: List[str] = field(default_factory=list)
    files_modified: List[str] = field(default_factory=list)
    network_activity: List[str] = field(default_factory=list)
    sandbox_id: Optional[str] = None


@dataclass
class IsolationEnvironment:
    """Isolated execution environment"""
    environment_id: str
    agent_id: str
    environment_type: ExecutionEnvironment
    sandbox_id: Optional[str] = None
    worktree_path: Optional[str] = None
    container_id: Optional[str] = None
    status: str = "created"
    created_at: datetime = field(default_factory=datetime.now)
    last_used: datetime = field(default_factory=datetime.now)
    resource_usage: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class E2BSandboxManager:
    """Manager for E2B sandboxes"""
    
    def __init__(self):
        self.active_sandboxes: Dict[str, Any] = {}
        self.sandbox_pool: List[Any] = []
        self.max_pool_size = 5

    async def create_sandbox(self, environment_id: str) -> str:
        """Create a new E2B sandbox"""
        if not E2B_AVAILABLE:
            raise RuntimeError("E2B not available")
        
        try:
            # Create sandbox
            sandbox = await asyncio.to_thread(Sandbox.create)
            sandbox_id = str(uuid.uuid4())
            
            self.active_sandboxes[sandbox_id] = sandbox
            
            logger.info("E2B sandbox created", 
                       environment_id=environment_id, 
                       sandbox_id=sandbox_id)
            
            return sandbox_id
            
        except Exception as e:
            logger.error("Failed to create E2B sandbox", 
                        environment_id=environment_id, 
                        error=str(e))
            raise

    async def execute_code(
        self, 
        sandbox_id: str, 
        request: ExecutionRequest
    ) -> ExecutionResult:
        """Execute code in E2B sandbox"""
        if sandbox_id not in self.active_sandboxes:
            raise ValueError(f"Sandbox {sandbox_id} not found")
        
        sandbox = self.active_sandboxes[sandbox_id]
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Execute code
            execution = await asyncio.to_thread(
                sandbox.run_code,
                request.code,
                timeout=request.timeout
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # Parse results
            result = ExecutionResult(
                request_id=request.request_id,
                success=not execution.error,
                output=execution.text or "",
                error=execution.error.message if execution.error else None,
                execution_time=execution_time,
                sandbox_id=sandbox_id
            )
            
            # Get file changes if available
            if hasattr(execution, 'files'):
                result.files_created = execution.files.get('created', [])
                result.files_modified = execution.files.get('modified', [])
            
            logger.info("Code executed in E2B sandbox", 
                       request_id=request.request_id,
                       success=result.success,
                       execution_time=execution_time)
            
            return result
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            
            logger.error("E2B execution failed", 
                        request_id=request.request_id, 
                        error=str(e))
            
            return ExecutionResult(
                request_id=request.request_id,
                success=False,
                error=str(e),
                execution_time=execution_time,
                sandbox_id=sandbox_id
            )

    async def cleanup_sandbox(self, sandbox_id: str) -> None:
        """Clean up E2B sandbox"""
        if sandbox_id in self.active_sandboxes:
            try:
                sandbox = self.active_sandboxes[sandbox_id]
                await asyncio.to_thread(sandbox.close)
                del self.active_sandboxes[sandbox_id]
                
                logger.info("E2B sandbox cleaned up", sandbox_id=sandbox_id)
                
            except Exception as e:
                logger.error("Failed to cleanup E2B sandbox", 
                           sandbox_id=sandbox_id, 
                           error=str(e))


class GitWorktreeManager:
    """Manager for Git worktree isolation"""
    
    def __init__(self, base_repo_path: Optional[str] = None):
        self.base_repo_path = base_repo_path or "/tmp/aizen_base_repo"
        self.worktrees: Dict[str, str] = {}

    async def create_worktree(self, environment_id: str, agent_id: str) -> str:
        """Create isolated Git worktree for agent"""
        try:
            # Ensure base repository exists
            await self._ensure_base_repo()
            
            # Create worktree path
            worktree_path = f"/tmp/aizen_worktree_{agent_id}_{environment_id}"
            
            # Create worktree
            repo = git.Repo(self.base_repo_path)
            
            # Create new branch for this agent
            branch_name = f"agent_{agent_id}_{environment_id}"
            
            # Create worktree
            await asyncio.to_thread(
                repo.git.worktree,
                "add",
                worktree_path,
                "-b",
                branch_name
            )
            
            self.worktrees[environment_id] = worktree_path
            
            logger.info("Git worktree created", 
                       environment_id=environment_id,
                       agent_id=agent_id,
                       path=worktree_path)
            
            return worktree_path
            
        except Exception as e:
            logger.error("Failed to create Git worktree", 
                        environment_id=environment_id,
                        agent_id=agent_id,
                        error=str(e))
            raise

    async def _ensure_base_repo(self) -> None:
        """Ensure base repository exists"""
        if not Path(self.base_repo_path).exists():
            # Initialize base repository
            repo = git.Repo.init(self.base_repo_path)
            
            # Create initial commit
            readme_path = Path(self.base_repo_path) / "README.md"
            readme_path.write_text("# Aizen AI Base Repository\n\nBase repository for agent worktrees.")
            
            repo.index.add(["README.md"])
            repo.index.commit("Initial commit")
            
            logger.info("Base repository initialized", path=self.base_repo_path)

    async def cleanup_worktree(self, environment_id: str) -> None:
        """Clean up Git worktree"""
        if environment_id in self.worktrees:
            try:
                worktree_path = self.worktrees[environment_id]
                
                # Remove worktree
                repo = git.Repo(self.base_repo_path)
                await asyncio.to_thread(
                    repo.git.worktree,
                    "remove",
                    worktree_path,
                    "--force"
                )
                
                del self.worktrees[environment_id]
                
                logger.info("Git worktree cleaned up", 
                           environment_id=environment_id,
                           path=worktree_path)
                
            except Exception as e:
                logger.error("Failed to cleanup Git worktree", 
                           environment_id=environment_id, 
                           error=str(e))


class LocalProcessManager:
    """Manager for local process execution (fallback)"""
    
    def __init__(self):
        self.active_processes: Dict[str, asyncio.subprocess.Process] = {}

    async def execute_code(self, request: ExecutionRequest) -> ExecutionResult:
        """Execute code in local process"""
        import subprocess
        import tempfile
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Create temporary file for code
            with tempfile.NamedTemporaryFile(
                mode='w', 
                suffix=f'.{request.language}', 
                delete=False
            ) as f:
                f.write(request.code)
                code_file = f.name
            
            # Determine execution command
            if request.language == "python":
                cmd = ["python", code_file]
            elif request.language == "javascript":
                cmd = ["node", code_file]
            elif request.language == "typescript":
                cmd = ["ts-node", code_file]
            else:
                raise ValueError(f"Unsupported language: {request.language}")
            
            # Execute with timeout
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=request.working_directory or "/tmp"
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=request.timeout
                )
                
                execution_time = asyncio.get_event_loop().time() - start_time
                
                result = ExecutionResult(
                    request_id=request.request_id,
                    success=process.returncode == 0,
                    output=stdout.decode('utf-8') if stdout else "",
                    error=stderr.decode('utf-8') if stderr else None,
                    exit_code=process.returncode or 0,
                    execution_time=execution_time
                )
                
                logger.info("Local process execution completed", 
                           request_id=request.request_id,
                           success=result.success,
                           execution_time=execution_time)
                
                return result
                
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                
                execution_time = asyncio.get_event_loop().time() - start_time
                
                return ExecutionResult(
                    request_id=request.request_id,
                    success=False,
                    error="Execution timeout",
                    execution_time=execution_time
                )
                
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            
            logger.error("Local process execution failed", 
                        request_id=request.request_id, 
                        error=str(e))
            
            return ExecutionResult(
                request_id=request.request_id,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
        
        finally:
            # Clean up temporary file
            try:
                Path(code_file).unlink()
            except:
                pass


class E2BExecutionManager:
    """
    Main E2B Execution Manager
    Coordinates secure code execution across different isolation environments
    """
    
    def __init__(self):
        self.environments: Dict[str, IsolationEnvironment] = {}
        self.e2b_manager = E2BSandboxManager()
        self.git_manager = GitWorktreeManager()
        self.local_manager = LocalProcessManager()
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the E2B execution manager"""
        if self._initialized:
            return
        
        logger.info("Initializing E2B Execution Manager...")
        
        # Check E2B availability
        if not E2B_AVAILABLE:
            logger.warning("E2B not available, using fallback execution methods")
        
        self._initialized = True
        logger.info("E2B Execution Manager initialized successfully")

    async def create_environment(
        self, 
        agent_id: str,
        environment_type: ExecutionEnvironment = ExecutionEnvironment.E2B_SANDBOX
    ) -> str:
        """Create isolated execution environment for agent"""
        environment_id = str(uuid.uuid4())
        
        environment = IsolationEnvironment(
            environment_id=environment_id,
            agent_id=agent_id,
            environment_type=environment_type
        )
        
        try:
            if environment_type == ExecutionEnvironment.E2B_SANDBOX and E2B_AVAILABLE:
                sandbox_id = await self.e2b_manager.create_sandbox(environment_id)
                environment.sandbox_id = sandbox_id
                
            elif environment_type == ExecutionEnvironment.GIT_WORKTREE:
                worktree_path = await self.git_manager.create_worktree(environment_id, agent_id)
                environment.worktree_path = worktree_path
            
            environment.status = "ready"
            self.environments[environment_id] = environment
            
            logger.info("Execution environment created", 
                       environment_id=environment_id,
                       agent_id=agent_id,
                       type=environment_type)
            
            return environment_id
            
        except Exception as e:
            environment.status = "error"
            logger.error("Failed to create execution environment", 
                        environment_id=environment_id,
                        agent_id=agent_id,
                        error=str(e))
            raise

    async def execute_code(
        self, 
        environment_id: str, 
        request: ExecutionRequest
    ) -> ExecutionResult:
        """Execute code in specified environment"""
        if environment_id not in self.environments:
            raise ValueError(f"Environment {environment_id} not found")
        
        environment = self.environments[environment_id]
        environment.last_used = datetime.now()
        
        try:
            if environment.environment_type == ExecutionEnvironment.E2B_SANDBOX:
                if environment.sandbox_id:
                    return await self.e2b_manager.execute_code(environment.sandbox_id, request)
                else:
                    raise RuntimeError("E2B sandbox not available")
            
            elif environment.environment_type == ExecutionEnvironment.GIT_WORKTREE:
                # Execute in worktree directory
                if environment.worktree_path:
                    request.working_directory = environment.worktree_path
                return await self.local_manager.execute_code(request)
            
            else:
                # Fallback to local execution
                return await self.local_manager.execute_code(request)
                
        except Exception as e:
            logger.error("Code execution failed", 
                        environment_id=environment_id,
                        request_id=request.request_id,
                        error=str(e))
            
            return ExecutionResult(
                request_id=request.request_id,
                success=False,
                error=str(e)
            )

    async def cleanup_environment(self, environment_id: str) -> None:
        """Clean up execution environment"""
        if environment_id not in self.environments:
            return
        
        environment = self.environments[environment_id]
        
        try:
            if environment.sandbox_id:
                await self.e2b_manager.cleanup_sandbox(environment.sandbox_id)
            
            if environment.worktree_path:
                await self.git_manager.cleanup_worktree(environment_id)
            
            del self.environments[environment_id]
            
            logger.info("Execution environment cleaned up", environment_id=environment_id)
            
        except Exception as e:
            logger.error("Failed to cleanup execution environment", 
                        environment_id=environment_id, 
                        error=str(e))

    async def get_environment_status(self, environment_id: str) -> Optional[Dict[str, Any]]:
        """Get status of execution environment"""
        if environment_id not in self.environments:
            return None
        
        environment = self.environments[environment_id]
        
        return {
            "environment_id": environment_id,
            "agent_id": environment.agent_id,
            "type": environment.environment_type,
            "status": environment.status,
            "created_at": environment.created_at.isoformat(),
            "last_used": environment.last_used.isoformat(),
            "resource_usage": environment.resource_usage,
            "metadata": environment.metadata
        }

    async def shutdown(self) -> None:
        """Shutdown the E2B execution manager"""
        logger.info("Shutting down E2B Execution Manager...")
        
        # Clean up all environments
        for environment_id in list(self.environments.keys()):
            await self.cleanup_environment(environment_id)
        
        self._initialized = False
        logger.info("E2B Execution Manager shutdown complete")
