#!/usr/bin/env python3
"""
Test Runner for Magentic One Gemini Implementation
Validates core AI agent functionality with Google Gemini
"""

import asyncio
import os
import sys
import json
from datetime import datetime
import structlog

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from frameworks.magentic_one_gemini_test import MagenticOneGeminiTest, GeminiMagenticConfig

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class MagenticOneTestRunner:
    """Test runner for validating Magentic One Gemini implementation"""
    
    def __init__(self):
        self.framework = None
        self.test_results = []
        self.start_time = None
        self.end_time = None

    async def setup(self) -> bool:
        """Setup the test environment"""
        try:
            logger.info("Setting up Magentic One Gemini Test...")
            
            # Check for API key
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                logger.error("GEMINI_API_KEY environment variable not set")
                print("\n❌ ERROR: Please set GEMINI_API_KEY environment variable")
                print("   You can get an API key from: https://makersuite.google.com/app/apikey")
                return False
            
            # Create configuration
            config = GeminiMagenticConfig(
                model_name="gemini-pro",
                gemini_api_key=api_key,
                temperature=0.7,
                max_tokens=2048,
                max_rounds=3,
                enable_code_execution=True,
                enable_file_handling=True
            )
            
            # Initialize framework
            self.framework = MagenticOneGeminiTest(config)
            await self.framework.initialize()
            
            logger.info("Test environment setup complete")
            return True
            
        except Exception as e:
            logger.error("Failed to setup test environment", error=str(e))
            print(f"\n❌ Setup failed: {e}")
            return False

    async def run_basic_functionality_tests(self) -> None:
        """Run basic functionality tests"""
        print("\n🧪 Running Basic Functionality Tests...")
        
        test_cases = [
            {
                "name": "Simple Query Processing",
                "description": "What is the capital of France?",
                "expected_keywords": ["Paris", "France", "capital"]
            },
            {
                "name": "Mathematical Calculation",
                "description": "Calculate the area of a circle with radius 5",
                "expected_keywords": ["area", "circle", "78.5", "π", "pi"]
            },
            {
                "name": "Code Generation Request",
                "description": "Write a Python function to calculate factorial",
                "expected_keywords": ["def", "factorial", "function", "python"]
            },
            {
                "name": "Analysis Task",
                "description": "Analyze the pros and cons of renewable energy",
                "expected_keywords": ["renewable", "energy", "pros", "cons", "analysis"]
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n  Test {i}: {test_case['name']}")
            await self._run_single_test(test_case)

    async def run_multi_agent_coordination_tests(self) -> None:
        """Run multi-agent coordination tests"""
        print("\n🤝 Running Multi-Agent Coordination Tests...")
        
        coordination_tests = [
            {
                "name": "Complex Problem Solving",
                "description": "Design a simple web application for a todo list with both frontend and backend considerations",
                "expected_agents": ["orchestrator", "coder", "analyst"]
            },
            {
                "name": "Research and Development",
                "description": "Research machine learning algorithms and recommend the best one for image classification",
                "expected_agents": ["orchestrator", "analyst", "assistant"]
            }
        ]
        
        for i, test_case in enumerate(coordination_tests, 1):
            print(f"\n  Coordination Test {i}: {test_case['name']}")
            await self._run_coordination_test(test_case)

    async def run_performance_tests(self) -> None:
        """Run performance and reliability tests"""
        print("\n⚡ Running Performance Tests...")
        
        # Test response time
        start_time = datetime.now()
        result = await self.framework.execute_test_task("What is 2 + 2?")
        end_time = datetime.now()
        
        response_time = (end_time - start_time).total_seconds()
        
        test_result = {
            "name": "Response Time Test",
            "success": result["success"],
            "response_time": response_time,
            "acceptable": response_time < 30.0  # 30 seconds threshold
        }
        
        self.test_results.append(test_result)
        
        status = "✅ PASS" if test_result["acceptable"] else "❌ FAIL"
        print(f"  Response Time: {response_time:.2f}s {status}")

    async def _run_single_test(self, test_case: dict) -> None:
        """Run a single test case"""
        try:
            result = await self.framework.execute_test_task(test_case["description"])
            
            # Check if task succeeded
            success = result["success"]
            
            # Check for expected keywords in result
            result_text = result.get("result", "").lower()
            keywords_found = sum(1 for keyword in test_case["expected_keywords"] 
                               if keyword.lower() in result_text)
            keyword_score = keywords_found / len(test_case["expected_keywords"])
            
            test_result = {
                "name": test_case["name"],
                "success": success,
                "keyword_score": keyword_score,
                "execution_time": result.get("execution_time", 0),
                "agents_used": result.get("agents_used", []),
                "result_length": len(result.get("result", ""))
            }
            
            self.test_results.append(test_result)
            
            # Display result
            status = "✅ PASS" if success and keyword_score > 0.3 else "❌ FAIL"
            print(f"    Status: {status}")
            print(f"    Execution Time: {test_result['execution_time']:.2f}s")
            print(f"    Agents Used: {', '.join(test_result['agents_used'])}")
            print(f"    Keyword Match: {keyword_score:.1%}")
            
        except Exception as e:
            logger.error("Test case failed", test_name=test_case["name"], error=str(e))
            print(f"    Status: ❌ FAIL - {e}")
            
            self.test_results.append({
                "name": test_case["name"],
                "success": False,
                "error": str(e)
            })

    async def _run_coordination_test(self, test_case: dict) -> None:
        """Run a coordination test case"""
        try:
            result = await self.framework.execute_test_task(test_case["description"])
            
            success = result["success"]
            agents_used = result.get("agents_used", [])
            
            # Check if expected agents were used
            expected_agents = test_case["expected_agents"]
            agents_match = all(agent in agents_used for agent in expected_agents)
            
            test_result = {
                "name": test_case["name"],
                "success": success,
                "agents_coordination": agents_match,
                "agents_used": agents_used,
                "expected_agents": expected_agents,
                "execution_time": result.get("execution_time", 0)
            }
            
            self.test_results.append(test_result)
            
            status = "✅ PASS" if success and agents_match else "❌ FAIL"
            print(f"    Status: {status}")
            print(f"    Expected Agents: {', '.join(expected_agents)}")
            print(f"    Used Agents: {', '.join(agents_used)}")
            print(f"    Coordination: {'✅' if agents_match else '❌'}")
            
        except Exception as e:
            logger.error("Coordination test failed", test_name=test_case["name"], error=str(e))
            print(f"    Status: ❌ FAIL - {e}")

    async def display_framework_status(self) -> None:
        """Display framework status and statistics"""
        print("\n📊 Framework Status:")
        
        status = await self.framework.get_framework_status()
        agents = await self.framework.list_agents()
        
        print(f"  Initialized: {'✅' if status['initialized'] else '❌'}")
        print(f"  Model: {status['config']['model_name']}")
        print(f"  Agents: {status['agents']}")
        print(f"  Total Tasks: {sum(status['task_counts'].values())}")
        
        print("\n🤖 Available Agents:")
        for agent in agents:
            print(f"  - {agent['name']} ({agent['role']})")

    def generate_test_report(self) -> None:
        """Generate and display test report"""
        print("\n📋 Test Report Summary:")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for test in self.test_results if test.get("success", False))
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "N/A")
        
        if self.start_time and self.end_time:
            total_time = (self.end_time - self.start_time).total_seconds()
            print(f"Total Execution Time: {total_time:.2f}s")
        
        print("\n📝 Detailed Results:")
        for test in self.test_results:
            status = "✅ PASS" if test.get("success", False) else "❌ FAIL"
            print(f"  {test['name']}: {status}")
            if "execution_time" in test:
                print(f"    Execution Time: {test['execution_time']:.2f}s")
            if "error" in test:
                print(f"    Error: {test['error']}")

    async def cleanup(self) -> None:
        """Cleanup test environment"""
        if self.framework:
            await self.framework.shutdown()
        logger.info("Test environment cleaned up")

    async def run_all_tests(self) -> bool:
        """Run all tests and return overall success"""
        self.start_time = datetime.now()
        
        print("🚀 Starting Magentic One Gemini Test Suite")
        print("=" * 50)
        
        try:
            # Setup
            if not await self.setup():
                return False
            
            # Display initial status
            await self.display_framework_status()
            
            # Run test suites
            await self.run_basic_functionality_tests()
            await self.run_multi_agent_coordination_tests()
            await self.run_performance_tests()
            
            self.end_time = datetime.now()
            
            # Generate report
            self.generate_test_report()
            
            # Determine overall success
            total_tests = len(self.test_results)
            passed_tests = sum(1 for test in self.test_results if test.get("success", False))
            success_rate = (passed_tests / total_tests) if total_tests > 0 else 0
            
            overall_success = success_rate >= 0.8  # 80% success threshold
            
            print(f"\n🎯 Overall Result: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
            
            return overall_success
            
        except Exception as e:
            logger.error("Test suite failed", error=str(e))
            print(f"\n❌ Test suite failed: {e}")
            return False
            
        finally:
            await self.cleanup()


async def main():
    """Main test runner function"""
    runner = MagenticOneTestRunner()
    success = await runner.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
