#!/usr/bin/env python3
"""
Magentic One Backend Runner
Easy startup script for the Aizen AI Backend with Magentic One Gemini support
"""

import os
import sys
import subprocess
import argparse
import time
import requests
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "google-generativeai",
        "structlog",
        "aiohttp"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All dependencies satisfied")
    return True


def check_api_key():
    """Check if Gemini API key is set"""
    print("\n🔑 Checking API key...")
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY environment variable not set")
        print("\n📝 To set up your API key:")
        print("1. Get a free API key from: https://makersuite.google.com/app/apikey")
        print("2. Set the environment variable:")
        print("   export GEMINI_API_KEY='your-api-key-here'")
        print("   # or on Windows:")
        print("   set GEMINI_API_KEY=your-api-key-here")
        print("\n⚠️  Backend will start but Magentic One endpoints will be unavailable")
        return False
    
    print("✅ GEMINI_API_KEY is set")
    return True


def wait_for_server(host="localhost", port=8000, timeout=30):
    """Wait for the server to start"""
    print(f"\n⏳ Waiting for server to start at http://{host}:{port}...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"http://{host}:{port}/health", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(1)
        print(".", end="", flush=True)
    
    print(f"\n❌ Server failed to start within {timeout} seconds")
    return False


def run_backend(host="0.0.0.0", port=8000, reload=True, log_level="INFO"):
    """Run the backend server"""
    print(f"\n🚀 Starting Aizen AI Backend with Magentic One support...")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Reload: {reload}")
    print(f"   Log Level: {log_level}")
    
    # Get the directory of this script
    backend_dir = Path(__file__).parent
    
    # Change to backend directory
    os.chdir(backend_dir)
    
    # Prepare command
    cmd = [
        sys.executable, "-m", "uvicorn",
        "api.main:app",
        "--host", host,
        "--port", str(port),
        "--log-level", log_level.lower()
    ]
    
    if reload:
        cmd.extend(["--reload", "--reload-dir", str(backend_dir)])
    
    print(f"\n📡 API Documentation will be available at: http://{host}:{port}/docs")
    print(f"🔍 Health Check: http://{host}:{port}/health")
    print(f"🤖 Magentic One Health: http://{host}:{port}/magentic-one/health")
    print("\n" + "="*60)
    
    try:
        # Start the server
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n\n⏹️  Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Server failed to start: {e}")
        return False
    
    return True


def run_tests():
    """Run the backend integration tests"""
    print("\n🧪 Running Backend Integration Tests...")
    
    backend_dir = Path(__file__).parent
    test_script = backend_dir / "test_backend_integration.py"
    
    if not test_script.exists():
        print("❌ Test script not found")
        return False
    
    try:
        result = subprocess.run([sys.executable, str(test_script)], 
                              cwd=backend_dir, 
                              check=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Magentic One Backend Runner")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")
    parser.add_argument("--log-level", default="INFO", 
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Log level")
    parser.add_argument("--test", action="store_true", 
                       help="Run integration tests instead of starting server")
    parser.add_argument("--check-only", action="store_true",
                       help="Only check dependencies and configuration")
    
    args = parser.parse_args()
    
    print("🤖 Aizen AI Backend - Magentic One Gemini Edition")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check API key
    api_key_available = check_api_key()
    
    if args.check_only:
        print("\n✅ Configuration check complete")
        if api_key_available:
            print("🎉 Ready to run with full Magentic One support!")
        else:
            print("⚠️  Ready to run with limited functionality (no Magentic One)")
        sys.exit(0)
    
    if args.test:
        if not api_key_available:
            print("\n❌ Cannot run tests without GEMINI_API_KEY")
            sys.exit(1)
        
        print("\n🔧 Make sure the backend server is running in another terminal:")
        print(f"   python {__file__} --host localhost --port 8000")
        print("\nPress Enter to continue with tests, or Ctrl+C to cancel...")
        try:
            input()
        except KeyboardInterrupt:
            print("\n⏹️  Tests cancelled")
            sys.exit(0)
        
        success = run_tests()
        sys.exit(0 if success else 1)
    
    # Run the backend server
    reload = not args.no_reload
    success = run_backend(args.host, args.port, reload, args.log_level)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
