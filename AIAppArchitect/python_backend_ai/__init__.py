"""
Aizen AI Extension - Python Backend
Production-ready AI agent framework with self-evolving capabilities
"""

__version__ = "2.0.0"
__author__ = "Aizen AI Team"

from .core.agent_manager import AizenAgentManager
from .core.ai_service import AizenAIService
from .frameworks.evoagentx_engine import EvoAgentXEngine
from .frameworks.taskweaver_engine import TaskWeaverEngine
from .frameworks.swarm_intelligence import SwarmIntelligenceEngine
from .isolation.e2b_manager import E2BExecutionManager
from .api.main import create_app

__all__ = [
    "AizenAgentManager",
    "AizenAIService", 
    "EvoAgentXEngine",
    "TaskWeaverEngine",
    "SwarmIntelligenceEngine",
    "E2BExecutionManager",
    "create_app"
]
