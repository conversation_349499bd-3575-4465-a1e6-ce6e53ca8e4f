#!/usr/bin/env python3
"""
Gemini API Test Script
Tests the provided Gemini API key and validates functionality
"""

import asyncio
import os
import sys
from datetime import datetime

# Set the API key for testing
os.environ["GEMINI_API_KEY"] = "AIzaSyAAjJ4VEsjdo5ejc2_qGYTB0ZmmJOETNPs"

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.ai_service import AizenAIService, AIConfig
from frameworks.magentic_one_gemini_test import MagenticOneGeminiTest, GeminiMagenticConfig


async def test_gemini_api_direct():
    """Test Gemini API directly"""
    print("🔑 Testing Gemini API Key...")
    print("-" * 40)
    
    try:
        # Test with our AI service
        ai_service = AizenAIService(
            provider="gemini",
            model="gemini-pro",
            api_key="AIzaSyAAjJ4VEsjdo5ejc2_qGYTB0ZmmJOETNPs",
            temperature=0.7,
            max_tokens=1024
        )
        
        await ai_service.initialize()
        print("✅ Gemini API connection successful")
        
        # Test simple query
        response = await ai_service.generate_response(
            prompt="What is the capital of France?",
            system_prompt="You are a helpful assistant. Provide concise answers."
        )
        
        print(f"✅ Test Query Response:")
        print(f"   Content: {response.content}")
        print(f"   Model: {response.model}")
        print(f"   Tokens: {response.tokens_used}")
        print(f"   Time: {response.response_time:.2f}s")
        
        await ai_service.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Gemini API test failed: {e}")
        return False


async def test_magentic_one_with_api():
    """Test Magentic One framework with the API key"""
    print("\n🤖 Testing Magentic One Framework...")
    print("-" * 40)
    
    try:
        config = GeminiMagenticConfig(
            model_name="gemini-pro",
            gemini_api_key="AIzaSyAAjJ4VEsjdo5ejc2_qGYTB0ZmmJOETNPs",
            temperature=0.7,
            max_tokens=1024,
            max_rounds=2
        )
        
        framework = MagenticOneGeminiTest(config)
        await framework.initialize()
        
        print("✅ Magentic One framework initialized")
        
        # Test simple task
        result = await framework.execute_test_task("What is 2 + 2? Explain your answer.")
        
        if result["success"]:
            print(f"✅ Task execution successful:")
            print(f"   Task ID: {result['task_id']}")
            print(f"   Agents Used: {', '.join(result['agents_used'])}")
            print(f"   Execution Time: {result['execution_time']:.2f}s")
            print(f"   Result: {result['result'][:200]}...")
        else:
            print(f"❌ Task execution failed: {result.get('error')}")
            return False
        
        # Test framework status
        status = await framework.get_framework_status()
        print(f"\n📊 Framework Status:")
        print(f"   Initialized: {status['initialized']}")
        print(f"   Agents: {status['agents']}")
        print(f"   Completed Tasks: {status['task_counts']['completed']}")
        
        await framework.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Magentic One test failed: {e}")
        return False


async def test_multiple_requests():
    """Test multiple concurrent requests"""
    print("\n⚡ Testing Multiple Requests...")
    print("-" * 40)
    
    try:
        config = GeminiMagenticConfig(
            model_name="gemini-pro",
            gemini_api_key="AIzaSyAAjJ4VEsjdo5ejc2_qGYTB0ZmmJOETNPs",
            temperature=0.5,
            max_tokens=256,
            max_rounds=1
        )
        
        framework = MagenticOneGeminiTest(config)
        await framework.initialize()
        
        tasks = [
            "What is 5 + 3?",
            "Name a color",
            "What is Python?",
            "Define AI"
        ]
        
        start_time = datetime.now()
        results = []
        
        for i, task in enumerate(tasks, 1):
            print(f"   Task {i}: {task}")
            result = await framework.execute_test_task(task)
            results.append(result)
            
            if result["success"]:
                print(f"     ✅ {result['execution_time']:.2f}s")
            else:
                print(f"     ❌ Failed")
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        successful = sum(1 for r in results if r["success"])
        success_rate = (successful / len(tasks)) * 100
        
        print(f"\n📊 Performance Summary:")
        print(f"   Total Time: {total_time:.2f}s")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Average Time: {total_time/len(tasks):.2f}s")
        
        await framework.shutdown()
        return success_rate >= 75
        
    except Exception as e:
        print(f"❌ Multiple requests test failed: {e}")
        return False


async def main():
    """Main test function"""
    print("🧪 Gemini API Key Validation Test")
    print("=" * 50)
    print("Testing API Key: AIzaSyAAjJ4VEsjdo5ejc2_qGYTB0ZmmJOETNPs")
    print("=" * 50)
    
    tests = [
        ("Direct API Test", test_gemini_api_direct),
        ("Magentic One Test", test_magentic_one_with_api),
        ("Multiple Requests Test", test_multiple_requests)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            success = await test_func()
            if success:
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n💥 {test_name}: CRASHED - {e}")
    
    print(f"\n{'='*50}")
    print(f"📋 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - API key is working perfectly!")
        print("\n✅ The API key is valid and functional")
        print("✅ Magentic One framework works with this key")
        print("✅ Performance is within acceptable limits")
    elif passed >= total * 0.7:
        print("⚠️  MOST TESTS PASSED - API key works with minor issues")
    else:
        print("❌ TESTS FAILED - API key may have issues")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
