#!/usr/bin/env python3
"""
Magentic One Gemini Demo
Simple demonstration of the Magentic One framework with Google Gemini
"""

import asyncio
import os
import sys
import json
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from frameworks.magentic_one_gemini_test import MagenticOneGeminiTest, GeminiMagenticConfig


async def demo_basic_functionality():
    """Demonstrate basic AI agent functionality"""
    print("🤖 Demo: Basic AI Agent Functionality")
    print("-" * 40)
    
    # Check for API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ Error: GEMINI_API_KEY environment variable not set")
        print("   Get your API key from: https://makersuite.google.com/app/apikey")
        return
    
    # Configure the framework
    config = GeminiMagenticConfig(
        model_name="gemini-pro",
        gemini_api_key=api_key,
        temperature=0.7,
        max_tokens=1024,
        max_rounds=3
    )
    
    # Initialize framework
    framework = MagenticOneGeminiTest(config)
    
    try:
        print("🔧 Initializing Magentic One framework...")
        await framework.initialize()
        
        # Display framework status
        status = await framework.get_framework_status()
        print(f"✅ Framework initialized with {status['agents']} agents")
        
        # List available agents
        agents = await framework.list_agents()
        print("\n🤖 Available Agents:")
        for agent in agents:
            print(f"  - {agent['name']} ({agent['role']})")
        
        # Demo tasks
        demo_tasks = [
            "What is the capital of Japan?",
            "Calculate the area of a rectangle with width 5 and height 8",
            "Write a simple Python function to check if a number is prime",
            "Explain the benefits of renewable energy in 3 bullet points"
        ]
        
        print(f"\n🎯 Running {len(demo_tasks)} demo tasks...")
        
        for i, task in enumerate(demo_tasks, 1):
            print(f"\n📝 Task {i}: {task}")
            
            start_time = datetime.now()
            result = await framework.execute_test_task(task)
            end_time = datetime.now()
            
            execution_time = (end_time - start_time).total_seconds()
            
            if result["success"]:
                print(f"✅ Completed in {execution_time:.2f}s")
                print(f"🤖 Agents used: {', '.join(result['agents_used'])}")
                print(f"📄 Result preview: {result['result'][:200]}...")
                if len(result['result']) > 200:
                    print("   (truncated)")
            else:
                print(f"❌ Failed: {result.get('error', 'Unknown error')}")
        
        # Display final statistics
        print(f"\n📊 Final Statistics:")
        final_status = await framework.get_framework_status()
        task_counts = final_status['task_counts']
        total_tasks = sum(task_counts.values())
        
        print(f"  Total tasks executed: {total_tasks}")
        print(f"  Successful: {task_counts['completed']}")
        print(f"  Failed: {task_counts['failed']}")
        
        if total_tasks > 0:
            success_rate = (task_counts['completed'] / total_tasks) * 100
            print(f"  Success rate: {success_rate:.1f}%")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        
    finally:
        print("\n🔧 Shutting down framework...")
        await framework.shutdown()
        print("✅ Demo complete!")


async def demo_multi_agent_coordination():
    """Demonstrate multi-agent coordination"""
    print("\n\n🤝 Demo: Multi-Agent Coordination")
    print("-" * 40)
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ Error: GEMINI_API_KEY environment variable not set")
        return
    
    config = GeminiMagenticConfig(
        model_name="gemini-pro",
        gemini_api_key=api_key,
        temperature=0.8,
        max_tokens=2048,
        max_rounds=4
    )
    
    framework = MagenticOneGeminiTest(config)
    
    try:
        await framework.initialize()
        
        # Complex task requiring multiple agents
        complex_task = """
        Design a simple web application for a personal budget tracker. 
        Consider both the technical implementation and user experience aspects.
        Provide code examples and explain the architecture.
        """
        
        print(f"📝 Complex Task: {complex_task.strip()}")
        print("\n🔄 Executing with multi-agent coordination...")
        
        start_time = datetime.now()
        result = await framework.execute_test_task(complex_task)
        end_time = datetime.now()
        
        execution_time = (end_time - start_time).total_seconds()
        
        if result["success"]:
            print(f"\n✅ Task completed successfully in {execution_time:.2f}s")
            print(f"🤖 Agents involved: {', '.join(result['agents_used'])}")
            print(f"🔄 Agent interactions: {result['agent_interactions']}")
            
            # Show detailed result
            print(f"\n📄 Detailed Result:")
            print("=" * 60)
            print(result['result'])
            print("=" * 60)
            
            # Get task details
            task_status = await framework.get_task_status(result['task_id'])
            print(f"\n📊 Task Details:")
            print(f"  Task ID: {task_status['task_id']}")
            print(f"  Status: {task_status['status']}")
            print(f"  Execution time: {task_status.get('execution_time', 0):.2f}s")
            print(f"  Agent interactions: {task_status['interactions']}")
            
        else:
            print(f"❌ Task failed: {result.get('error', 'Unknown error')}")
        
    except Exception as e:
        print(f"❌ Multi-agent demo failed: {e}")
        
    finally:
        await framework.shutdown()


async def demo_performance_testing():
    """Demonstrate performance characteristics"""
    print("\n\n⚡ Demo: Performance Testing")
    print("-" * 40)
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ Error: GEMINI_API_KEY environment variable not set")
        return
    
    config = GeminiMagenticConfig(
        model_name="gemini-pro",
        gemini_api_key=api_key,
        temperature=0.5,
        max_tokens=512,  # Smaller for faster responses
        max_rounds=2
    )
    
    framework = MagenticOneGeminiTest(config)
    
    try:
        await framework.initialize()
        
        # Performance test tasks
        perf_tasks = [
            "What is 15 * 23?",
            "Name 3 programming languages",
            "What is the current year?",
            "Define artificial intelligence in one sentence",
            "List 2 benefits of exercise"
        ]
        
        print(f"🏃 Running {len(perf_tasks)} performance tests...")
        
        total_time = 0
        successful_tasks = 0
        
        for i, task in enumerate(perf_tasks, 1):
            print(f"\n⏱️  Performance Test {i}: {task}")
            
            start_time = datetime.now()
            result = await framework.execute_test_task(task)
            end_time = datetime.now()
            
            execution_time = (end_time - start_time).total_seconds()
            total_time += execution_time
            
            if result["success"]:
                successful_tasks += 1
                print(f"  ✅ {execution_time:.2f}s - {result['result'][:100]}...")
            else:
                print(f"  ❌ {execution_time:.2f}s - Failed")
        
        # Performance summary
        avg_time = total_time / len(perf_tasks)
        success_rate = (successful_tasks / len(perf_tasks)) * 100
        
        print(f"\n📊 Performance Summary:")
        print(f"  Total execution time: {total_time:.2f}s")
        print(f"  Average time per task: {avg_time:.2f}s")
        print(f"  Success rate: {success_rate:.1f}%")
        print(f"  Tasks per minute: {(len(perf_tasks) / total_time) * 60:.1f}")
        
        # Performance rating
        if avg_time < 3.0 and success_rate > 90:
            print("  🏆 Performance: Excellent")
        elif avg_time < 5.0 and success_rate > 80:
            print("  🥈 Performance: Good")
        elif avg_time < 10.0 and success_rate > 70:
            print("  🥉 Performance: Acceptable")
        else:
            print("  ⚠️  Performance: Needs improvement")
        
    except Exception as e:
        print(f"❌ Performance demo failed: {e}")
        
    finally:
        await framework.shutdown()


async def main():
    """Main demo function"""
    print("🚀 Magentic One Gemini Framework Demo")
    print("=" * 50)
    print("This demo showcases the core capabilities of the")
    print("Magentic One framework with Google Gemini integration.")
    print("=" * 50)
    
    # Check prerequisites
    if not os.getenv("GEMINI_API_KEY"):
        print("\n⚠️  Setup Required:")
        print("1. Get a Gemini API key from: https://makersuite.google.com/app/apikey")
        print("2. Set environment variable: export GEMINI_API_KEY='your-key-here'")
        print("3. Run this demo again")
        return
    
    try:
        # Run all demos
        await demo_basic_functionality()
        await demo_multi_agent_coordination()
        await demo_performance_testing()
        
        print("\n🎉 All demos completed successfully!")
        print("\nNext steps:")
        print("- Review the test results above")
        print("- Check the documentation in MAGENTIC_ONE_GEMINI_README.md")
        print("- Run the full test suite with: python test_gemini_magentic_one.py")
        print("- Explore the code in frameworks/magentic_one_gemini_test.py")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo suite failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
