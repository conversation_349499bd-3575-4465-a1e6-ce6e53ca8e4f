"""
Unit Tests for Magentic One Gemini Test Implementation
"""

import pytest
import asyncio
import os
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

# Import the test framework
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from frameworks.magentic_one_gemini_test import (
    MagenticOneGeminiTest,
    GeminiMagenticConfig,
    GeminiModelClient,
    SimplifiedAgent,
    TestTask
)
from core.ai_service import AIResponse


class TestGeminiModelClient:
    """Test cases for GeminiModelClient"""
    
    @pytest.fixture
    def config(self):
        return GeminiMagenticConfig(
            model_name="gemini-pro",
            gemini_api_key="test-api-key"
        )
    
    @pytest.fixture
    def mock_ai_service(self):
        mock_service = AsyncMock()
        mock_service.initialize = AsyncMock()
        mock_service.generate_response = AsyncMock()
        mock_service.shutdown = AsyncMock()
        return mock_service
    
    @pytest.mark.asyncio
    async def test_client_initialization(self, config):
        """Test client initialization"""
        with patch('frameworks.magentic_one_gemini_test.AizenAIService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            client = GeminiModelClient(config)
            await client.initialize()
            
            assert client._initialized
            mock_service.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_completion(self, config):
        """Test completion creation"""
        with patch('frameworks.magentic_one_gemini_test.AizenAIService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Mock response
            mock_response = AIResponse(
                content="Test response",
                model="gemini-pro",
                provider="gemini",
                tokens_used=100,
                response_time=1.0,
                finish_reason="stop"
            )
            mock_service.generate_response.return_value = mock_response
            
            client = GeminiModelClient(config)
            await client.initialize()
            
            messages = [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Hello"}
            ]
            
            result = await client.create_completion(messages)
            
            assert result["choices"][0]["message"]["content"] == "Test response"
            assert result["model"] == "gemini-pro"
            assert result["usage"]["total_tokens"] == 100


class TestSimplifiedAgent:
    """Test cases for SimplifiedAgent"""
    
    @pytest.fixture
    def mock_model_client(self):
        client = AsyncMock()
        client.create_completion = AsyncMock()
        return client
    
    @pytest.fixture
    def agent(self, mock_model_client):
        return SimplifiedAgent(
            name="test_agent",
            role="assistant",
            system_message="You are a test agent",
            model_client=mock_model_client
        )
    
    @pytest.mark.asyncio
    async def test_process_message(self, agent, mock_model_client):
        """Test message processing"""
        # Mock response
        mock_model_client.create_completion.return_value = {
            "choices": [{
                "message": {
                    "role": "assistant",
                    "content": "Test response from agent"
                },
                "finish_reason": "stop"
            }]
        }
        
        response = await agent.process_message("Hello, agent!")
        
        assert response == "Test response from agent"
        assert len(agent.conversation_history) == 2  # User message + agent response
        mock_model_client.create_completion.assert_called_once()


class TestMagenticOneGeminiTest:
    """Test cases for MagenticOneGeminiTest framework"""
    
    @pytest.fixture
    def config(self):
        return GeminiMagenticConfig(
            model_name="gemini-pro",
            gemini_api_key="test-api-key",
            max_rounds=3
        )
    
    @pytest.fixture
    def framework(self, config):
        return MagenticOneGeminiTest(config)
    
    @pytest.mark.asyncio
    async def test_framework_initialization(self, framework):
        """Test framework initialization"""
        with patch.object(framework, '_create_test_agents', new_callable=AsyncMock):
            with patch('frameworks.magentic_one_gemini_test.GeminiModelClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_client_class.return_value = mock_client
                
                await framework.initialize()
                
                assert framework._initialized
                assert framework.model_client is not None
                mock_client.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_agent_creation(self, framework):
        """Test agent creation"""
        # Mock model client
        mock_client = AsyncMock()
        framework.model_client = mock_client
        
        await framework._create_test_agents()
        
        # Check that all expected agents are created
        expected_agents = ["orchestrator", "coder", "analyst", "assistant"]
        for agent_name in expected_agents:
            assert agent_name in framework.agents
            assert framework.agents[agent_name].name == agent_name
    
    @pytest.mark.asyncio
    async def test_task_execution(self, framework):
        """Test task execution"""
        # Setup mock agents
        mock_orchestrator = AsyncMock()
        mock_orchestrator.process_message.return_value = "I'll coordinate this task using the coder and analyst agents."
        
        mock_coder = AsyncMock()
        mock_coder.process_message.return_value = "Here's the code solution: print('Hello World')"
        
        mock_analyst = AsyncMock()
        mock_analyst.process_message.return_value = "Analysis shows this is a simple greeting program."
        
        mock_assistant = AsyncMock()
        mock_assistant.process_message.return_value = "This task has been completed successfully."
        
        framework.agents = {
            "orchestrator": mock_orchestrator,
            "coder": mock_coder,
            "analyst": mock_analyst,
            "assistant": mock_assistant
        }
        framework._initialized = True
        
        result = await framework.execute_test_task("Write a hello world program")
        
        assert result["success"] is True
        assert "task_id" in result
        assert "result" in result
        assert "execution_time" in result
        assert len(result["agents_used"]) > 0
    
    @pytest.mark.asyncio
    async def test_task_failure_handling(self, framework):
        """Test task failure handling"""
        # Setup mock agent that raises an exception
        mock_orchestrator = AsyncMock()
        mock_orchestrator.process_message.side_effect = Exception("Test error")
        
        framework.agents = {"orchestrator": mock_orchestrator}
        framework._initialized = True
        
        result = await framework.execute_test_task("Test task")
        
        assert result["success"] is False
        assert "error" in result
        assert "Test error" in result["error"]
    
    def test_agent_determination(self, framework):
        """Test agent determination logic"""
        # Test coding task
        agents = framework._determine_agents_for_task(
            "Write a Python function",
            "I'll use the coder agent for this programming task"
        )
        assert "coder" in agents
        assert "orchestrator" in agents
        
        # Test analysis task
        agents = framework._determine_agents_for_task(
            "Analyze this data pattern",
            "The analyst agent should handle this analysis task"
        )
        assert "analyst" in agents
        assert "orchestrator" in agents
    
    @pytest.mark.asyncio
    async def test_get_task_status(self, framework):
        """Test task status retrieval"""
        # Create a test task
        task = TestTask(
            id="test-123",
            description="Test task",
            status="completed",
            result="Test result"
        )
        framework.tasks["test-123"] = task
        
        status = await framework.get_task_status("test-123")
        
        assert status["task_id"] == "test-123"
        assert status["status"] == "completed"
        assert status["result"] == "Test result"
        
        # Test non-existent task
        status = await framework.get_task_status("non-existent")
        assert "error" in status
    
    @pytest.mark.asyncio
    async def test_list_agents(self, framework):
        """Test agent listing"""
        # Setup mock agents
        framework.agents = {
            "test_agent": SimplifiedAgent(
                name="test_agent",
                role="test",
                system_message="Test system message",
                model_client=AsyncMock()
            )
        }
        
        agents = await framework.list_agents()
        
        assert len(agents) == 1
        assert agents[0]["name"] == "test_agent"
        assert agents[0]["role"] == "test"
    
    @pytest.mark.asyncio
    async def test_framework_status(self, framework):
        """Test framework status"""
        # Add some test tasks
        framework.tasks = {
            "task1": TestTask(id="task1", description="Task 1", status="completed"),
            "task2": TestTask(id="task2", description="Task 2", status="pending"),
            "task3": TestTask(id="task3", description="Task 3", status="failed")
        }
        framework._initialized = True
        framework.agents = {"agent1": Mock(), "agent2": Mock()}
        
        status = await framework.get_framework_status()
        
        assert status["initialized"] is True
        assert status["agents"] == 2
        assert status["task_counts"]["completed"] == 1
        assert status["task_counts"]["pending"] == 1
        assert status["task_counts"]["failed"] == 1
    
    @pytest.mark.asyncio
    async def test_shutdown(self, framework):
        """Test framework shutdown"""
        mock_client = AsyncMock()
        framework.model_client = mock_client
        framework.agents = {"agent1": Mock()}
        framework.tasks = {"task1": Mock()}
        framework._initialized = True
        
        await framework.shutdown()
        
        assert not framework._initialized
        assert len(framework.agents) == 0
        assert len(framework.tasks) == 0
        assert framework.model_client is None
        mock_client.shutdown.assert_called_once()


class TestIntegration:
    """Integration tests for the complete system"""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow (requires real API key)"""
        # Skip if no API key available
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            pytest.skip("GEMINI_API_KEY not available for integration test")
        
        config = GeminiMagenticConfig(
            model_name="gemini-pro",
            gemini_api_key=api_key,
            max_rounds=2
        )
        
        framework = MagenticOneGeminiTest(config)
        
        try:
            await framework.initialize()
            
            # Test simple task
            result = await framework.execute_test_task("What is 2 + 2?")
            
            assert result["success"] is True
            assert "result" in result
            assert len(result["agents_used"]) > 0
            
            # Check task status
            task_status = await framework.get_task_status(result["task_id"])
            assert task_status["status"] == "completed"
            
        finally:
            await framework.shutdown()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
