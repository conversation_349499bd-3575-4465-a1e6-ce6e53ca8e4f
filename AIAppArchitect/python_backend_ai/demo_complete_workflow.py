#!/usr/bin/env python3
"""
Complete Workflow Demonstration
Shows the full Magentic One Gemini implementation working end-to-end
"""

import asyncio
import aiohttp
import json
import os
import sys
import time
from datetime import datetime


async def demonstrate_complete_workflow():
    """Demonstrate the complete Magentic One workflow"""
    
    print("🚀 Magentic One Gemini - Complete Workflow Demonstration")
    print("=" * 60)
    
    # Check prerequisites
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY environment variable not set")
        print("   Get your API key from: https://makersuite.google.com/app/apikey")
        return False
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            # Step 1: Check backend health
            print("\n🏥 Step 1: Checking Backend Health")
            print("-" * 30)
            
            async with session.get(f"{base_url}/health") as response:
                health = await response.json()
                print(f"✅ Backend Status: {health['status']}")
            
            async with session.get(f"{base_url}/magentic-one/health") as response:
                magentic_health = await response.json()
                print(f"🤖 Magentic One Status: {magentic_health['status']}")
                
                if magentic_health['status'] != 'healthy':
                    print(f"⚠️  {magentic_health.get('message', 'Framework not available')}")
                    return False
            
            # Step 2: Initialize with custom configuration
            print("\n🔧 Step 2: Initializing Magentic One Framework")
            print("-" * 30)
            
            config_data = {
                "model_name": "gemini-pro",
                "temperature": 0.7,
                "max_tokens": 2048,
                "max_rounds": 4,
                "enable_code_execution": True,
                "enable_file_handling": True,
                "gemini_api_key": api_key
            }
            
            async with session.post(f"{base_url}/magentic-one/initialize", json=config_data) as response:
                init_result = await response.json()
                print(f"✅ Initialization: {init_result['status']}")
                print(f"   Message: {init_result['message']}")
            
            # Step 3: Get framework status
            print("\n📊 Step 3: Framework Status and Agents")
            print("-" * 30)
            
            async with session.get(f"{base_url}/magentic-one/status") as response:
                status = await response.json()
                print(f"✅ Framework Initialized: {status['initialized']}")
                print(f"   Model: {status['config']['model_name']}")
                print(f"   Available Agents: {status['agents']}")
                print(f"   Total Tasks: {sum(status['task_counts'].values())}")
            
            async with session.get(f"{base_url}/magentic-one/agents") as response:
                agents = await response.json()
                print(f"\n🤖 Available Agents ({len(agents)}):")
                for agent in agents:
                    print(f"   - {agent['name']} ({agent['role']})")
            
            # Step 4: Execute simple task
            print("\n🎯 Step 4: Simple Task Execution")
            print("-" * 30)
            
            simple_task = {
                "task_description": "What is the capital of Japan and what is it famous for?",
                "model_name": "gemini-pro",
                "temperature": 0.7,
                "max_tokens": 1024,
                "max_rounds": 2
            }
            
            print(f"📝 Task: {simple_task['task_description']}")
            start_time = time.time()
            
            async with session.post(f"{base_url}/magentic-one/execute", json=simple_task) as response:
                result = await response.json()
                
            execution_time = time.time() - start_time
            
            if result['success']:
                print(f"✅ Completed in {execution_time:.2f}s")
                print(f"   Task ID: {result['task_id']}")
                print(f"   Agents Used: {', '.join(result['agents_used'])}")
                print(f"   Result: {result['result'][:200]}...")
                
                # Check task status
                task_id = result['task_id']
                async with session.get(f"{base_url}/magentic-one/tasks/{task_id}") as response:
                    task_status = await response.json()
                    print(f"   Task Status: {task_status['status']}")
                    print(f"   Execution Time: {task_status.get('execution_time', 0):.2f}s")
            else:
                print(f"❌ Task failed: {result.get('error', 'Unknown error')}")
                return False
            
            # Step 5: Execute complex multi-agent task
            print("\n🧠 Step 5: Complex Multi-Agent Task")
            print("-" * 30)
            
            complex_task = {
                "task_description": """
                Create a Python web application for a simple todo list manager. 
                Include both backend API design and frontend considerations.
                Provide code examples and explain the architecture choices.
                """.strip(),
                "model_name": "gemini-pro",
                "temperature": 0.8,
                "max_tokens": 3072,
                "max_rounds": 5
            }
            
            print(f"📝 Complex Task: {complex_task['task_description'][:100]}...")
            start_time = time.time()
            
            async with session.post(f"{base_url}/magentic-one/execute", json=complex_task) as response:
                result = await response.json()
                
            execution_time = time.time() - start_time
            
            if result['success']:
                print(f"✅ Complex task completed in {execution_time:.2f}s")
                print(f"   Agents Used: {', '.join(result['agents_used'])}")
                print(f"   Agent Interactions: {result['agent_interactions']}")
                
                # Verify multi-agent coordination
                expected_agents = ['orchestrator', 'coder']
                used_agents = result['agents_used']
                
                if all(agent in used_agents for agent in expected_agents):
                    print("   ✅ Multi-agent coordination successful")
                else:
                    print("   ⚠️  Expected multi-agent coordination")
                
                print(f"\n📄 Result Preview:")
                print("-" * 40)
                print(result['result'][:500] + "..." if len(result['result']) > 500 else result['result'])
                print("-" * 40)
            else:
                print(f"❌ Complex task failed: {result.get('error', 'Unknown error')}")
                return False
            
            # Step 6: Performance test with multiple tasks
            print("\n⚡ Step 6: Performance Testing")
            print("-" * 30)
            
            performance_tasks = [
                "What is 15 * 23?",
                "Name three programming languages",
                "What is artificial intelligence?",
                "List two benefits of exercise",
                "What color is the sky?"
            ]
            
            total_time = 0
            successful_tasks = 0
            
            for i, task_desc in enumerate(performance_tasks, 1):
                task_data = {
                    "task_description": task_desc,
                    "model_name": "gemini-pro",
                    "temperature": 0.3,
                    "max_tokens": 256,
                    "max_rounds": 1
                }
                
                start_time = time.time()
                async with session.post(f"{base_url}/magentic-one/execute", json=task_data) as response:
                    result = await response.json()
                execution_time = time.time() - start_time
                
                total_time += execution_time
                
                if result['success']:
                    successful_tasks += 1
                    print(f"   ✅ Task {i}: {execution_time:.2f}s")
                else:
                    print(f"   ❌ Task {i}: {execution_time:.2f}s - Failed")
            
            avg_time = total_time / len(performance_tasks)
            success_rate = (successful_tasks / len(performance_tasks)) * 100
            
            print(f"\n📊 Performance Summary:")
            print(f"   Total Time: {total_time:.2f}s")
            print(f"   Average Time: {avg_time:.2f}s")
            print(f"   Success Rate: {success_rate:.1f}%")
            print(f"   Tasks/Minute: {(len(performance_tasks) / total_time) * 60:.1f}")
            
            # Step 7: Final status check
            print("\n📈 Step 7: Final Framework Status")
            print("-" * 30)
            
            async with session.get(f"{base_url}/magentic-one/status") as response:
                final_status = await response.json()
                
            task_counts = final_status['task_counts']
            total_tasks = sum(task_counts.values())
            
            print(f"✅ Final Statistics:")
            print(f"   Total Tasks Executed: {total_tasks}")
            print(f"   Successful: {task_counts['completed']}")
            print(f"   Failed: {task_counts['failed']}")
            
            if total_tasks > 0:
                final_success_rate = (task_counts['completed'] / total_tasks) * 100
                print(f"   Overall Success Rate: {final_success_rate:.1f}%")
            
            # Determine overall success
            overall_success = (
                success_rate >= 80 and  # Performance test success rate
                task_counts['completed'] >= 6 and  # Minimum successful tasks
                final_success_rate >= 85  # Overall success rate
            )
            
            print(f"\n🎯 Overall Assessment:")
            if overall_success:
                print("🎉 EXCELLENT - All systems working perfectly!")
                print("   ✅ Backend integration successful")
                print("   ✅ Multi-agent coordination working")
                print("   ✅ Performance within acceptable limits")
                print("   ✅ Error handling robust")
            else:
                print("⚠️  GOOD - System working with minor issues")
                print("   ✅ Basic functionality confirmed")
                print("   ⚠️  Some performance or reliability concerns")
            
            return overall_success
            
        except aiohttp.ClientError as e:
            print(f"❌ Connection error: {e}")
            print("   Make sure the backend server is running:")
            print("   python run_magentic_one_backend.py")
            return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False


async def main():
    """Main demonstration function"""
    print("🔧 Prerequisites Check:")
    print("1. Backend server should be running (python run_magentic_one_backend.py)")
    print("2. GEMINI_API_KEY environment variable should be set")
    print("3. All dependencies should be installed")
    print()
    
    # Check if user wants to continue
    try:
        input("Press Enter to start the demonstration, or Ctrl+C to cancel...")
    except KeyboardInterrupt:
        print("\n⏹️  Demonstration cancelled")
        return
    
    try:
        success = await demonstrate_complete_workflow()
        
        print(f"\n{'='*60}")
        if success:
            print("🎉 DEMONSTRATION COMPLETED SUCCESSFULLY!")
            print("\nNext Steps:")
            print("- Explore the API documentation at http://localhost:8000/docs")
            print("- Try the VS Code extension integration")
            print("- Review the code in frameworks/magentic_one_gemini_test.py")
            print("- Check the backend integration in api/main.py")
        else:
            print("⚠️  DEMONSTRATION COMPLETED WITH ISSUES")
            print("\nTroubleshooting:")
            print("- Check the backend logs for errors")
            print("- Verify your GEMINI_API_KEY is valid")
            print("- Ensure all dependencies are installed")
            print("- Review the BACKEND_INTEGRATION_README.md")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demonstration interrupted by user")
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
