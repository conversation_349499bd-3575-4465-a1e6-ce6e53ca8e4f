# Aizen AI Extension - Python Backend Requirements
# Core AI Frameworks
openai>=1.12.0
anthropic>=0.18.0
google-generativeai>=0.8.0  # Google Gemini API
langchain>=0.1.17
langchain-openai>=0.0.8
langchain-anthropic>=0.1.0
langchain-google-genai>=2.0.0  # LangChain Google Gemini integration
langgraph>=0.2.34
pydantic>=2.6.0
pydantic-ai>=0.0.13

# Latest 2025 AI Agent Frameworks - REAL IMPLEMENTATIONS
ag2>=0.2.34  # Microsoft AutoGen successor
crewai>=0.86.0  # Latest CrewAI
taskweaver>=0.2.17  # Microsoft TaskWeaver
llama-index>=0.12.0  # LlamaIndex agents
autogen>=0.2.34  # Microsoft AutoGen

# Real Framework Dependencies
git+https://github.com/EvoAgentX/EvoAgentX.git  # EvoAgentX framework
git+https://github.com/microsoft/TinyTroupe.git  # Microsoft TinyTroupe framework
git+https://github.com/microsoft/autogen.git  # Microsoft AutoGen with Magentic-One

# Custom Framework Dependencies for BabyAGI, SuperAGI, etc.
requests>=2.31.0
aiohttp>=3.9.0
asyncio-mqtt>=0.16.0
redis>=5.0.0
celery>=5.3.0
sqlalchemy>=2.0.0
alembic>=1.13.0
numpy>=1.24.0
scipy>=1.11.0

# Code Execution & Isolation
e2b-code-interpreter>=0.0.10
docker>=7.0.0
jupyter>=1.0.0
ipykernel>=6.29.0

# Vector Databases & RAG
qdrant-client>=1.7.0
chromadb>=0.4.0
faiss-cpu>=1.7.4
sentence-transformers>=2.2.0

# Code Analysis & Understanding
tree-sitter>=0.20.0
tree-sitter-python>=0.20.0
tree-sitter-javascript>=0.20.0
tree-sitter-typescript>=0.20.0
tree-sitter-rust>=0.20.0
ast-tools>=0.1.0

# Git & Version Control
gitpython>=3.1.0
dulwich>=0.21.0

# Web Framework & API
fastapi>=0.109.0
uvicorn>=0.27.0
websockets>=12.0
aiohttp>=3.9.0

# Data Processing & ML
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.4.0
torch>=2.1.0
transformers>=4.37.0

# Async & Concurrency
asyncio>=3.4.3
aiofiles>=23.2.0
celery>=5.3.0
redis>=5.0.0

# Monitoring & Logging
structlog>=23.2.0
prometheus-client>=0.19.0
opentelemetry-api>=1.22.0

# Security & Validation
cryptography>=42.0.0
pyjwt>=2.8.0
bcrypt>=4.1.0

# Development & Testing
pytest>=8.0.0
pytest-asyncio>=0.23.0
black>=24.0.0
isort>=5.13.0
mypy>=1.8.0
pre-commit>=3.6.0

# Optional: Advanced ML/AI Libraries
# huggingface-hub>=0.20.0
# accelerate>=0.26.0
# bitsandbytes>=0.42.0
