"""
Code Generation Agent - Production Implementation
Specialized agent for code generation, implementation, and refactoring
"""

import asyncio
import json
import re
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import structlog
from datetime import datetime

from ..core.base_agent import BaseAgent, Task, TaskResult, AgentConfig
from ..core.ai_service import AizenAIService

logger = structlog.get_logger(__name__)


class CodeGenerationAgent(BaseAgent):
    """
    Specialized agent for code generation tasks
    """

    def __init__(self, agent_id: str, config: AgentConfig, manager=None):
        super().__init__(agent_id, config, manager)

        # Code generation specific prompts
        self.system_prompts = {
            "code_generation": """You are an expert software engineer specializing in code generation.
Your task is to write clean, efficient, and well-documented code based on requirements.

Guidelines:
1. Write production-ready code with proper error handling
2. Include comprehensive comments and documentation
3. Follow best practices and design patterns
4. Consider security, performance, and maintainability
5. Provide clear explanations of your implementation choices
6. Include unit tests when appropriate

Always structure your response as:
1. Analysis of requirements
2. Implementation approach
3. Generated code with comments
4. Testing recommendations
5. Potential improvements""",

            "refactoring": """You are an expert at code refactoring and optimization.
Your task is to improve existing code while maintaining functionality.

Guidelines:
1. Identify code smells and anti-patterns
2. Improve readability and maintainability
3. Optimize performance where appropriate
4. Ensure backward compatibility
5. Add missing documentation and tests
6. Follow SOLID principles and clean code practices

Always provide:
1. Analysis of current code issues
2. Refactoring strategy
3. Improved code implementation
4. Migration guide if needed
5. Performance impact assessment""",

            "bug_fixing": """You are an expert debugger and problem solver.
Your task is to identify and fix bugs in code.

Guidelines:
1. Analyze the bug report and symptoms
2. Identify root causes
3. Provide targeted fixes
4. Ensure fixes don't introduce new issues
5. Add tests to prevent regression
6. Document the fix and reasoning

Always provide:
1. Bug analysis and root cause
2. Fix implementation
3. Test cases to verify the fix
4. Prevention strategies
5. Code review recommendations"""
        }

        # Supported programming languages
        self.supported_languages = [
            "python", "javascript", "typescript", "rust", "java",
            "go", "cpp", "csharp", "php", "ruby", "swift", "kotlin"
        ]

        # Code templates and patterns
        self.code_templates = {}
        self._load_code_templates()

    def get_capabilities(self) -> List[str]:
        """Get agent capabilities"""
        return [
            "code_generation",
            "refactoring",
            "bug_fixing",
            "feature_implementation",
            "api_development",
            "database_integration",
            "testing_code_generation",
            "documentation_generation",
            "performance_optimization",
            "security_implementation"
        ]

    def get_specialization(self) -> str:
        """Get agent specialization"""
        return "code_generation"

    async def execute_task(self, task: Task) -> TaskResult:
        """Execute code generation task"""
        logger.info("Executing code generation task",
                   task_id=task.id,
                   task_type=task.type)

        try:
            # Determine task type and route to appropriate handler
            if task.type in ["code-generation", "implement-feature", "create-component"]:
                return await self._handle_code_generation(task)
            elif task.type in ["refactor", "refactor-code", "optimize"]:
                return await self._handle_refactoring(task)
            elif task.type in ["fix-bug", "debug", "troubleshoot"]:
                return await self._handle_bug_fixing(task)
            elif task.type in ["create-api", "api-development"]:
                return await self._handle_api_development(task)
            elif task.type in ["database-integration", "data-layer"]:
                return await self._handle_database_integration(task)
            else:
                return await self._handle_generic_code_task(task)

        except Exception as e:
            logger.error("Code generation task failed",
                        task_id=task.id,
                        error=str(e))

            return TaskResult(
                task_id=task.id,
                success=False,
                error=str(e)
            )

    async def _handle_code_generation(self, task: Task) -> TaskResult:
        """Handle code generation tasks"""
        # Analyze requirements
        requirements = await self._analyze_requirements(task)

        # Determine programming language
        language = await self._determine_language(task, requirements)

        # Generate code
        code_result = await self._generate_code(task, requirements, language)

        # Create files if working directory is specified
        files_created = []
        if task.working_directory and code_result.get("files"):
            files_created = await self._create_code_files(
                task.working_directory,
                code_result["files"]
            )

        return TaskResult(
            task_id=task.id,
            success=True,
            output=code_result.get("explanation", "Code generated successfully"),
            files_created=files_created,
            metadata={
                "language": language,
                "requirements": requirements,
                "code_structure": code_result.get("structure", {}),
                "recommendations": code_result.get("recommendations", [])
            }
        )

    async def _handle_refactoring(self, task: Task) -> TaskResult:
        """Handle code refactoring tasks"""
        # Read existing code
        existing_code = await self._read_existing_code(task)

        # Analyze code issues
        analysis = await self._analyze_code_issues(existing_code)

        # Generate refactored code
        refactored_result = await self._refactor_code(task, existing_code, analysis)

        # Update files
        files_modified = []
        if task.working_directory and refactored_result.get("files"):
            files_modified = await self._update_code_files(
                task.working_directory,
                refactored_result["files"]
            )

        return TaskResult(
            task_id=task.id,
            success=True,
            output=refactored_result.get("summary", "Code refactored successfully"),
            files_modified=files_modified,
            metadata={
                "issues_found": analysis.get("issues", []),
                "improvements": refactored_result.get("improvements", []),
                "performance_impact": refactored_result.get("performance_impact", "neutral")
            }
        )

    async def _handle_bug_fixing(self, task: Task) -> TaskResult:
        """Handle bug fixing tasks"""
        # Read code with bug
        buggy_code = await self._read_existing_code(task)

        # Analyze bug
        bug_analysis = await self._analyze_bug(task, buggy_code)

        # Generate fix
        fix_result = await self._generate_bug_fix(task, buggy_code, bug_analysis)

        # Apply fix
        files_modified = []
        if task.working_directory and fix_result.get("files"):
            files_modified = await self._update_code_files(
                task.working_directory,
                fix_result["files"]
            )

        return TaskResult(
            task_id=task.id,
            success=True,
            output=fix_result.get("summary", "Bug fixed successfully"),
            files_modified=files_modified,
            metadata={
                "bug_analysis": bug_analysis,
                "fix_strategy": fix_result.get("strategy", ""),
                "test_cases": fix_result.get("test_cases", [])
            }
        )

    async def _analyze_requirements(self, task: Task) -> Dict[str, Any]:
        """Analyze task requirements"""
        prompt = f"""
Analyze the following software development requirements:

Task: {task.description}
Context: {json.dumps(task.context, indent=2)}

Provide a detailed analysis including:
1. Functional requirements
2. Non-functional requirements
3. Technical constraints
4. Dependencies
5. Success criteria

Return as JSON structure.
"""

        response = await self.ai_service.generate_response(
            prompt=prompt,
            system_prompt="You are a requirements analyst. Always respond with valid JSON."
        )

        try:
            return json.loads(response.content)
        except json.JSONDecodeError:
            # Fallback analysis
            return {
                "functional_requirements": [task.description],
                "non_functional_requirements": ["maintainable", "testable"],
                "technical_constraints": [],
                "dependencies": task.dependencies,
                "success_criteria": ["code compiles", "tests pass"]
            }

    async def _determine_language(self, task: Task, requirements: Dict[str, Any]) -> str:
        """Determine the best programming language for the task"""
        # Check if language is specified in task context
        if "language" in task.context:
            return task.context["language"]

        # Check file extensions in task files
        if task.files:
            for file_path in task.files:
                ext = Path(file_path).suffix.lower()
                if ext == ".py":
                    return "python"
                elif ext in [".js", ".jsx"]:
                    return "javascript"
                elif ext in [".ts", ".tsx"]:
                    return "typescript"
                elif ext == ".rs":
                    return "rust"
                elif ext == ".java":
                    return "java"
                elif ext == ".go":
                    return "go"

        # Analyze requirements to suggest language
        description = task.description.lower()
        if any(keyword in description for keyword in ["python", "django", "flask", "fastapi"]):
            return "python"
        elif any(keyword in description for keyword in ["javascript", "node", "react", "vue"]):
            return "javascript"
        elif any(keyword in description for keyword in ["typescript", "angular"]):
            return "typescript"
        elif any(keyword in description for keyword in ["rust", "performance", "systems"]):
            return "rust"

        # Default to Python
        return "python"

    async def _generate_code(
        self,
        task: Task,
        requirements: Dict[str, Any],
        language: str
    ) -> Dict[str, Any]:
        """Generate code based on requirements"""
        prompt = f"""
Generate {language} code for the following requirements:

Task: {task.description}
Requirements: {json.dumps(requirements, indent=2)}
Language: {language}

Generate complete, production-ready code including:
1. Main implementation
2. Error handling
3. Documentation/comments
4. Unit tests (if appropriate)
5. Configuration files (if needed)

Structure your response as JSON with:
{{
    "explanation": "Brief explanation of the implementation",
    "files": {{
        "filename.ext": "file content",
        ...
    }},
    "structure": {{
        "main_components": [...],
        "dependencies": [...],
        "architecture": "..."
    }},
    "recommendations": [...]
}}
"""

        response = await self.ai_service.generate_response(
            prompt=prompt,
            system_prompt=self.system_prompts["code_generation"]
        )

        try:
            return json.loads(response.content)
        except json.JSONDecodeError:
            # Fallback: extract code from response
            return {
                "explanation": "Code generated based on requirements",
                "files": self._extract_code_from_response(response.content, language),
                "structure": {"main_components": ["main module"]},
                "recommendations": ["Review and test the generated code"]
            }

    def _extract_code_from_response(self, response: str, language: str) -> Dict[str, str]:
        """Extract code blocks from AI response"""
        files = {}

        # Look for code blocks
        code_pattern = rf"```{language}(.*?)```"
        matches = re.findall(code_pattern, response, re.DOTALL)

        if matches:
            # Use first code block as main file
            main_ext = self._get_file_extension(language)
            files[f"main{main_ext}"] = matches[0].strip()
        else:
            # Look for any code blocks
            general_pattern = r"```(.*?)```"
            matches = re.findall(general_pattern, response, re.DOTALL)
            if matches:
                main_ext = self._get_file_extension(language)
                files[f"main{main_ext}"] = matches[0].strip()

        return files

    def _get_file_extension(self, language: str) -> str:
        """Get file extension for programming language"""
        extensions = {
            "python": ".py",
            "javascript": ".js",
            "typescript": ".ts",
            "rust": ".rs",
            "java": ".java",
            "go": ".go",
            "cpp": ".cpp",
            "csharp": ".cs",
            "php": ".php",
            "ruby": ".rb",
            "swift": ".swift",
            "kotlin": ".kt"
        }
        return extensions.get(language, ".txt")

    async def _create_code_files(
        self,
        working_directory: str,
        files: Dict[str, str]
    ) -> List[str]:
        """Create code files in working directory"""
        created_files = []
        base_path = Path(working_directory)

        try:
            base_path.mkdir(parents=True, exist_ok=True)

            for filename, content in files.items():
                file_path = base_path / filename
                file_path.parent.mkdir(parents=True, exist_ok=True)

                # Write file
                file_path.write_text(content, encoding='utf-8')
                created_files.append(str(file_path))

                logger.info("Code file created", file_path=str(file_path))

        except Exception as e:
            logger.error("Failed to create code files", error=str(e))
            raise

        return created_files

    def _load_code_templates(self) -> None:
        """Load code templates for common patterns"""
        self.code_templates = {
            "python": {
                "class": '''class {class_name}:
    """
    {description}
    """

    def __init__(self):
        pass
''',
                "function": '''def {function_name}({parameters}):
    """
    {description}

    Args:
        {args_doc}

    Returns:
        {return_doc}
    """
    pass
''',
                "api_endpoint": '''@app.route('/{endpoint}', methods=['{method}'])
def {function_name}():
    """
    {description}
    """
    try:
        # Implementation here
        return jsonify({{"status": "success"}})
    except Exception as e:
        return jsonify({{"error": str(e)}}), 500
'''
            },
            "javascript": {
                "class": '''class {class_name} {{
    /**
     * {description}
     */
    constructor() {{
        // Constructor implementation
    }}
}}''',
                "function": '''/**
 * {description}
 * @param {{*}} {parameters}
 * @returns {{*}} {return_doc}
 */
function {function_name}({parameters}) {{
    // Implementation here
}}''',
                "api_endpoint": '''app.{method}('/{endpoint}', async (req, res) => {{
    try {{
        // {description}
        res.json({{ status: 'success' }});
    }} catch (error) {{
        res.status(500).json({{ error: error.message }});
    }}
}});'''
            }
        }