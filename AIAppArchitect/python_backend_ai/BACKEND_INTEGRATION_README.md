# Aizen AI Backend - Magentic One Gemini Integration

## Overview

This is a production-ready FastAPI backend that integrates the Magentic One framework with Google Gemini, providing a complete AI agent system accessible via REST API endpoints.

## Features

### ✅ Core Backend Features
- **FastAPI Server**: Production-ready async web server
- **Agent Management**: Create and manage multiple AI agents
- **Task Execution**: Execute tasks with automatic agent assignment
- **Real-time Monitoring**: Health checks and performance metrics
- **CORS Support**: Cross-origin requests for web frontends

### ✅ Magentic One Integration
- **Google Gemini Support**: Full integration with Gemini Pro models
- **Multi-Agent Coordination**: Orchestrator delegates to specialized agents
- **Task Management**: Complete task lifecycle with status tracking
- **Agent Types**: Orchestra<PERSON>, Coder, Analyst, and Assistant agents
- **Configurable Settings**: Flexible model and behavior configuration

### ✅ API Endpoints

#### Main Backend Endpoints
- `GET /health` - Backend health check
- `POST /agents` - Create new AI agents
- `GET /agents` - List all agents
- `POST /tasks` - Execute tasks
- `GET /metrics` - System metrics

#### Magentic One Endpoints
- `POST /magentic-one/execute` - Execute tasks with Magentic One
- `GET /magentic-one/status` - Framework status
- `GET /magentic-one/agents` - List available agents
- `GET /magentic-one/tasks/{task_id}` - Get task status
- `POST /magentic-one/initialize` - Initialize with custom config
- `GET /magentic-one/health` - Magentic One health check

## Quick Start

### 1. Prerequisites

```bash
# Python 3.8+
python --version

# Install dependencies
cd AIAppArchitect/python_backend_ai
pip install -r requirements.txt
```

### 2. Get Gemini API Key

1. Visit: https://makersuite.google.com/app/apikey
2. Create a free API key
3. Set environment variable:

```bash
# Linux/Mac
export GEMINI_API_KEY="your-api-key-here"

# Windows
set GEMINI_API_KEY=your-api-key-here
```

### 3. Start the Backend

#### Option A: Using the Runner Script (Recommended)
```bash
cd AIAppArchitect/python_backend_ai
python run_magentic_one_backend.py
```

#### Option B: Direct FastAPI
```bash
cd AIAppArchitect/python_backend_ai
python api/main.py
```

#### Option C: Using Uvicorn
```bash
cd AIAppArchitect/python_backend_ai
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. Verify Installation

```bash
# Check backend health
curl http://localhost:8000/health

# Check Magentic One health
curl http://localhost:8000/magentic-one/health

# View API documentation
open http://localhost:8000/docs
```

## Usage Examples

### Basic Task Execution

```bash
curl -X POST "http://localhost:8000/magentic-one/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "task_description": "What is the capital of France?",
    "model_name": "gemini-pro",
    "temperature": 0.7,
    "max_tokens": 1024
  }'
```

### Complex Coding Task

```bash
curl -X POST "http://localhost:8000/magentic-one/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "task_description": "Write a Python function to calculate fibonacci numbers and explain how it works",
    "model_name": "gemini-pro",
    "temperature": 0.8,
    "max_tokens": 2048,
    "max_rounds": 4
  }'
```

### Python Client Example

```python
import aiohttp
import asyncio

async def test_magentic_one():
    async with aiohttp.ClientSession() as session:
        # Execute a task
        task_data = {
            "task_description": "Explain machine learning in simple terms",
            "model_name": "gemini-pro",
            "temperature": 0.7
        }
        
        async with session.post(
            "http://localhost:8000/magentic-one/execute",
            json=task_data
        ) as response:
            result = await response.json()
            
            if result["success"]:
                print(f"Task completed: {result['result']}")
                print(f"Agents used: {result['agents_used']}")
            else:
                print(f"Task failed: {result['error']}")

asyncio.run(test_magentic_one())
```

## Testing

### Run Integration Tests

```bash
# Start the backend in one terminal
cd AIAppArchitect/python_backend_ai
python run_magentic_one_backend.py

# Run tests in another terminal
cd AIAppArchitect/python_backend_ai
python test_backend_integration.py
```

### Run Unit Tests

```bash
cd AIAppArchitect/python_backend_ai
python -m pytest tests/test_magentic_one_gemini.py -v
```

### Check Configuration

```bash
cd AIAppArchitect/python_backend_ai
python run_magentic_one_backend.py --check-only
```

## Configuration

### Environment Variables

```bash
# Required for Magentic One
GEMINI_API_KEY=your-gemini-api-key

# Optional backend configuration
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
LOG_LEVEL=INFO
```

### Runtime Configuration

You can configure Magentic One at runtime:

```bash
curl -X POST "http://localhost:8000/magentic-one/initialize" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "gemini-pro",
    "temperature": 0.8,
    "max_tokens": 4096,
    "max_rounds": 5,
    "enable_code_execution": true,
    "enable_file_handling": true,
    "gemini_api_key": "your-key-here"
  }'
```

## API Documentation

### Interactive Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Response Format

All Magentic One endpoints return JSON responses:

```json
{
  "success": true,
  "task_id": "uuid-string",
  "result": "AI agent response",
  "agents_used": ["orchestrator", "coder", "assistant"],
  "execution_time": 3.45,
  "agent_interactions": 4
}
```

### Error Handling

Error responses include detailed information:

```json
{
  "detail": "Error description",
  "status_code": 500,
  "error_type": "MagenticOneError"
}
```

## Performance

### Expected Performance
- **Simple queries**: 1-3 seconds
- **Complex tasks**: 5-15 seconds
- **Multi-agent coordination**: 10-30 seconds
- **Concurrent requests**: 10+ simultaneous tasks

### Optimization Tips
1. Use smaller `max_tokens` for faster responses
2. Reduce `max_rounds` for simpler tasks
3. Set lower `temperature` for consistent results
4. Monitor token usage to optimize costs

## Troubleshooting

### Common Issues

1. **Server won't start**
   ```bash
   # Check if port is in use
   lsof -i :8000
   
   # Use different port
   python run_magentic_one_backend.py --port 8001
   ```

2. **Magentic One unavailable**
   ```bash
   # Check API key
   echo $GEMINI_API_KEY
   
   # Test API key
   curl -H "Authorization: Bearer $GEMINI_API_KEY" \
        "https://generativelanguage.googleapis.com/v1/models"
   ```

3. **Import errors**
   ```bash
   # Reinstall dependencies
   pip install -r requirements.txt --force-reinstall
   ```

4. **Slow responses**
   - Check internet connection
   - Reduce `max_tokens` and `max_rounds`
   - Monitor Gemini API quotas

### Debug Mode

Enable debug logging:

```bash
python run_magentic_one_backend.py --log-level DEBUG
```

### Health Checks

Monitor system health:

```bash
# Backend health
curl http://localhost:8000/health

# Magentic One health
curl http://localhost:8000/magentic-one/health

# Framework status
curl http://localhost:8000/magentic-one/status
```

## Production Deployment

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "run_magentic_one_backend.py", "--host", "0.0.0.0"]
```

### Environment Setup

```bash
# Production environment variables
export GEMINI_API_KEY=your-production-key
export BACKEND_HOST=0.0.0.0
export BACKEND_PORT=8000
export LOG_LEVEL=INFO
export WORKERS=4
```

### Load Balancing

For high-traffic deployments, use multiple workers:

```bash
uvicorn api.main:app --host 0.0.0.0 --port 8000 --workers 4
```

## Integration with VS Code Extension

The backend is designed to work with the Aizen AI VS Code extension:

1. **Automatic Discovery**: Extension detects running backend
2. **Agent Integration**: VS Code commands use backend agents
3. **Real-time Updates**: WebSocket support for live updates
4. **Error Handling**: Graceful fallbacks when backend unavailable

## Contributing

### Development Setup

1. Clone the repository
2. Install development dependencies
3. Set up pre-commit hooks
4. Run tests before submitting changes

### Adding New Endpoints

1. Add Pydantic models for request/response
2. Implement endpoint function
3. Add route to FastAPI app
4. Update tests and documentation

## License

This backend implementation is part of the Aizen AI Extension project and follows the same licensing terms.
