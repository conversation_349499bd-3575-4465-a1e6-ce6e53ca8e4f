"""
Pydantic AI Engine - Production Implementation
Type-safe AI agents using Pydantic AI framework
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Union, Type
from dataclasses import dataclass, field
from enum import Enum
import structlog
from pydantic import BaseModel, Field, ValidationError

# Pydantic AI imports
try:
    from pydantic_ai import Agent, RunContext
    from pydantic_ai.models import OpenAIModel, AnthropicModel
    from pydantic_ai.tools import Tool
    PYDANTIC_AI_AVAILABLE = True
except ImportError:
    PYDANTIC_AI_AVAILABLE = False
    logger.warning("Pydantic AI not available, using fallback implementation")

logger = structlog.get_logger(__name__)


# Pydantic models for type safety
class TaskInput(BaseModel):
    """Input model for tasks"""
    description: str = Field(..., description="Task description")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")
    requirements: List[str] = Field(default_factory=list, description="Specific requirements")
    files: List[str] = Field(default_factory=list, description="Related files")


class CodeOutput(BaseModel):
    """Output model for code generation"""
    code: str = Field(..., description="Generated code")
    language: str = Field(..., description="Programming language")
    explanation: str = Field(..., description="Code explanation")
    tests: Optional[str] = Field(None, description="Generated tests")
    dependencies: List[str] = Field(default_factory=list, description="Required dependencies")


class AnalysisOutput(BaseModel):
    """Output model for code analysis"""
    issues: List[str] = Field(default_factory=list, description="Found issues")
    suggestions: List[str] = Field(default_factory=list, description="Improvement suggestions")
    complexity_score: float = Field(..., description="Code complexity score (0-10)")
    maintainability: str = Field(..., description="Maintainability assessment")


class DocumentationOutput(BaseModel):
    """Output model for documentation generation"""
    content: str = Field(..., description="Generated documentation")
    format: str = Field(..., description="Documentation format (markdown, rst, etc.)")
    sections: List[str] = Field(default_factory=list, description="Documentation sections")


@dataclass
class PydanticAIConfig:
    """Configuration for Pydantic AI engine"""
    model_name: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4096
    timeout: int = 30
    retries: int = 3


class PydanticAIEngine:
    """
    Production Pydantic AI Engine for type-safe agent interactions
    """
    
    def __init__(self, config: Optional[PydanticAIConfig] = None):
        self.config = config or PydanticAIConfig()
        self.agents: Dict[str, Any] = {}
        self.model = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the Pydantic AI engine"""
        if self._initialized:
            return
        
        if not PYDANTIC_AI_AVAILABLE:
            logger.warning("Pydantic AI not available, using mock implementation")
            self._initialized = True
            return
        
        logger.info("Initializing Pydantic AI Engine...")
        
        try:
            # Initialize model
            if "gpt" in self.config.model_name:
                self.model = OpenAIModel(self.config.model_name)
            elif "claude" in self.config.model_name:
                self.model = AnthropicModel(self.config.model_name)
            else:
                self.model = OpenAIModel("gpt-4-turbo-preview")
            
            # Create specialized agents
            await self._create_specialized_agents()
            
            self._initialized = True
            logger.info("Pydantic AI Engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Pydantic AI Engine", error=str(e))
            raise

    async def _create_specialized_agents(self) -> None:
        """Create specialized Pydantic AI agents"""
        if not PYDANTIC_AI_AVAILABLE:
            return
        
        # Code Generation Agent
        code_agent = Agent(
            model=self.model,
            result_type=CodeOutput,
            system_prompt="""
            You are an expert software engineer specializing in code generation.
            Generate clean, efficient, and well-documented code based on requirements.
            Always include proper error handling and follow best practices.
            Provide explanations for your implementation choices.
            """
        )
        
        @code_agent.tool
        async def analyze_requirements(ctx: RunContext[TaskInput], requirements: str) -> str:
            """Analyze requirements and suggest implementation approach"""
            return f"Requirements analysis: {requirements}"
        
        @code_agent.tool
        async def generate_tests(ctx: RunContext[TaskInput], code: str) -> str:
            """Generate unit tests for the provided code"""
            return f"# Unit tests for the code\nimport unittest\n\nclass TestCode(unittest.TestCase):\n    def test_functionality(self):\n        pass"
        
        self.agents["code_generation"] = code_agent
        
        # Code Analysis Agent
        analysis_agent = Agent(
            model=self.model,
            result_type=AnalysisOutput,
            system_prompt="""
            You are a senior code reviewer and static analysis expert.
            Analyze code for issues, security vulnerabilities, and improvement opportunities.
            Provide actionable feedback and complexity assessments.
            """
        )
        
        @analysis_agent.tool
        async def check_security(ctx: RunContext[TaskInput], code: str) -> str:
            """Check code for security vulnerabilities"""
            security_issues = []
            if "eval(" in code:
                security_issues.append("Use of eval() function - potential code injection")
            if "exec(" in code:
                security_issues.append("Use of exec() function - potential code injection")
            if "input(" in code and "password" in code.lower():
                security_issues.append("Potential password handling issue")
            
            return f"Security analysis: {', '.join(security_issues) if security_issues else 'No obvious security issues found'}"
        
        @analysis_agent.tool
        async def calculate_complexity(ctx: RunContext[TaskInput], code: str) -> float:
            """Calculate cyclomatic complexity of code"""
            # Simple complexity calculation
            complexity = 1  # Base complexity
            complexity += code.count("if ")
            complexity += code.count("elif ")
            complexity += code.count("for ")
            complexity += code.count("while ")
            complexity += code.count("except ")
            complexity += code.count("and ")
            complexity += code.count("or ")
            
            return min(complexity / 10.0, 10.0)  # Normalize to 0-10 scale
        
        self.agents["code_analysis"] = analysis_agent
        
        # Documentation Agent
        docs_agent = Agent(
            model=self.model,
            result_type=DocumentationOutput,
            system_prompt="""
            You are a technical writer specializing in software documentation.
            Create clear, comprehensive documentation for code and APIs.
            Use appropriate formatting and include examples where helpful.
            """
        )
        
        @docs_agent.tool
        async def extract_api_endpoints(ctx: RunContext[TaskInput], code: str) -> List[str]:
            """Extract API endpoints from code"""
            endpoints = []
            lines = code.split('\n')
            for line in lines:
                if '@app.route(' in line or '@router.' in line:
                    endpoints.append(line.strip())
            return endpoints
        
        @docs_agent.tool
        async def generate_examples(ctx: RunContext[TaskInput], function_name: str) -> str:
            """Generate usage examples for a function"""
            return f"""
# Example usage of {function_name}
result = {function_name}(param1="value1", param2="value2")
print(result)
"""
        
        self.agents["documentation"] = docs_agent
        
        logger.info("Specialized Pydantic AI agents created")

    async def execute_task(
        self, 
        agent_type: str, 
        task_input: TaskInput,
        context: Optional[Dict[str, Any]] = None
    ) -> Union[CodeOutput, AnalysisOutput, DocumentationOutput]:
        """Execute a task using the specified agent type"""
        if not self._initialized:
            await self.initialize()
        
        if not PYDANTIC_AI_AVAILABLE:
            return await self._mock_execution(agent_type, task_input)
        
        if agent_type not in self.agents:
            raise ValueError(f"Agent type '{agent_type}' not available")
        
        agent = self.agents[agent_type]
        
        try:
            logger.info("Executing Pydantic AI task", 
                       agent_type=agent_type, 
                       task=task_input.description)
            
            # Execute with type safety
            result = await agent.arun(
                task_input.description,
                deps=task_input,
                message_history=[]
            )
            
            logger.info("Pydantic AI task completed successfully",
                       agent_type=agent_type,
                       result_type=type(result).__name__)
            
            return result
            
        except ValidationError as e:
            logger.error("Pydantic validation error", agent_type=agent_type, error=str(e))
            raise
        except Exception as e:
            logger.error("Pydantic AI task execution failed", 
                        agent_type=agent_type, 
                        error=str(e))
            raise

    async def _mock_execution(
        self, 
        agent_type: str, 
        task_input: TaskInput
    ) -> Union[CodeOutput, AnalysisOutput, DocumentationOutput]:
        """Mock execution when Pydantic AI is not available"""
        logger.info("Using mock Pydantic AI execution", agent_type=agent_type)
        
        if agent_type == "code_generation":
            return CodeOutput(
                code=f"# Generated code for: {task_input.description}\ndef solution():\n    pass",
                language="python",
                explanation="Mock code generation - Pydantic AI not available",
                dependencies=["typing"]
            )
        elif agent_type == "code_analysis":
            return AnalysisOutput(
                issues=["Mock analysis - Pydantic AI not available"],
                suggestions=["Install Pydantic AI for real analysis"],
                complexity_score=5.0,
                maintainability="Unknown - mock analysis"
            )
        elif agent_type == "documentation":
            return DocumentationOutput(
                content=f"# Documentation for {task_input.description}\n\nMock documentation - Pydantic AI not available",
                format="markdown",
                sections=["Overview", "Usage"]
            )
        else:
            raise ValueError(f"Unknown agent type: {agent_type}")

    async def generate_code(self, task_input: TaskInput) -> CodeOutput:
        """Generate code using the code generation agent"""
        return await self.execute_task("code_generation", task_input)

    async def analyze_code(self, code: str, context: Optional[Dict[str, Any]] = None) -> AnalysisOutput:
        """Analyze code using the analysis agent"""
        task_input = TaskInput(
            description=f"Analyze this code: {code[:200]}...",
            context={"code": code, **(context or {})}
        )
        return await self.execute_task("code_analysis", task_input)

    async def generate_documentation(self, task_input: TaskInput) -> DocumentationOutput:
        """Generate documentation using the documentation agent"""
        return await self.execute_task("documentation", task_input)

    async def create_custom_agent(
        self, 
        name: str, 
        result_type: Type[BaseModel],
        system_prompt: str,
        tools: Optional[List[Any]] = None
    ) -> str:
        """Create a custom Pydantic AI agent"""
        if not PYDANTIC_AI_AVAILABLE:
            logger.warning("Cannot create custom agent - Pydantic AI not available")
            return f"mock_agent_{name}"
        
        try:
            agent = Agent(
                model=self.model,
                result_type=result_type,
                system_prompt=system_prompt
            )
            
            # Add custom tools if provided
            if tools:
                for tool in tools:
                    agent.tool(tool)
            
            self.agents[name] = agent
            
            logger.info("Custom Pydantic AI agent created", name=name)
            return name
            
        except Exception as e:
            logger.error("Failed to create custom agent", name=name, error=str(e))
            raise

    async def list_agents(self) -> List[str]:
        """List available agents"""
        return list(self.agents.keys())

    async def get_agent_info(self, agent_name: str) -> Dict[str, Any]:
        """Get information about a specific agent"""
        if agent_name not in self.agents:
            raise ValueError(f"Agent '{agent_name}' not found")
        
        agent = self.agents[agent_name]
        
        if not PYDANTIC_AI_AVAILABLE:
            return {
                "name": agent_name,
                "type": "mock",
                "model": "mock",
                "tools": []
            }
        
        return {
            "name": agent_name,
            "type": "pydantic_ai",
            "model": str(agent.model),
            "result_type": str(agent.result_type),
            "tools": [tool.__name__ for tool in getattr(agent, '_tools', [])]
        }

    async def shutdown(self) -> None:
        """Shutdown the Pydantic AI engine"""
        logger.info("Shutting down Pydantic AI Engine...")
        
        self.agents.clear()
        self.model = None
        self._initialized = False
        
        logger.info("Pydantic AI Engine shutdown complete")
