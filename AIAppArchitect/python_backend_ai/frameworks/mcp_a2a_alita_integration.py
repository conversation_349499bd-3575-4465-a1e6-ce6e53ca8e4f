"""
MCP × A2A × ALITA Integration Layer
Orchestrates the complete framework combining:
- MCP × A2A protocol layer for agent interoperability
- ALITA's self-evolving MCP creation capabilities
- Environment management for isolated execution
- Web agent integration for open source discovery
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict

from .mcp_a2a_core import CoreProtocolLayer, A2AMessage, MessageType, AgentCard, MCPTool
from .alita_mcp_evolution import ALITAMCPEvolution
from .environment_manager import EnvironmentManager

logger = logging.getLogger(__name__)

@dataclass
class IntegrationConfig:
    """Configuration for the integrated framework"""
    agent_id: str
    agent_name: str
    enable_self_evolution: bool = True
    enable_web_discovery: bool = True
    enable_environment_isolation: bool = True
    max_concurrent_evolutions: int = 3
    environment_cleanup_hours: int = 24
    mcp_box_max_size: int = 100

class MCPxA2AxALITAFramework:
    """
    Main integration framework combining MCP × A2A with ALITA capabilities
    """
    
    def __init__(self, config: IntegrationConfig):
        self.config = config
        
        # Core components
        self.core_protocol = CoreProtocolLayer(config.agent_id, config.agent_name)
        self.alita_evolution = None
        self.environment_manager = None
        
        # State tracking
        self.active_evolutions: Dict[str, Dict[str, Any]] = {}
        self.discovered_resources: Dict[str, Dict[str, Any]] = {}
        self.performance_metrics: Dict[str, Any] = {
            "total_tasks_processed": 0,
            "successful_evolutions": 0,
            "failed_evolutions": 0,
            "tools_generated": 0,
            "environments_created": 0
        }
        
        # Integration status
        self.initialized = False
        self.running = False
    
    async def initialize(self):
        """Initialize the integrated framework"""
        logger.info("Initializing MCP × A2A × ALITA framework")
        
        try:
            # Initialize core protocol
            await self.core_protocol.initialize()
            
            # Initialize ALITA evolution if enabled
            if self.config.enable_self_evolution:
                self.alita_evolution = ALITAMCPEvolution(self.core_protocol)
                logger.info("ALITA evolution system initialized")
            
            # Initialize environment manager if enabled
            if self.config.enable_environment_isolation:
                self.environment_manager = EnvironmentManager()
                logger.info("Environment manager initialized")
            
            # Setup periodic tasks
            asyncio.create_task(self._periodic_maintenance())
            
            self.initialized = True
            self.running = True
            
            logger.info("Framework initialization complete")
            
        except Exception as e:
            logger.error(f"Framework initialization failed: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the framework gracefully"""
        logger.info("Shutting down MCP × A2A × ALITA framework")
        
        self.running = False
        
        # Shutdown core protocol
        await self.core_protocol.shutdown()
        
        # Cleanup environments
        if self.environment_manager:
            for env_id in list(self.environment_manager.environments.keys()):
                await self.environment_manager.destroy_environment(env_id)
        
        logger.info("Framework shutdown complete")
    
    async def process_task(self, task_description: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process a task using the integrated framework
        This is the main entry point for task processing
        """
        if not self.initialized:
            raise RuntimeError("Framework not initialized")
        
        logger.info(f"Processing task: {task_description[:100]}...")
        
        task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.performance_metrics)}"
        context = context or {}
        
        try:
            # Update metrics
            self.performance_metrics["total_tasks_processed"] += 1
            
            # Step 1: Analyze task requirements and check existing capabilities
            existing_tools = await self._analyze_existing_capabilities(task_description)
            
            # Step 2: If capabilities are insufficient, trigger evolution
            if self.config.enable_self_evolution and not existing_tools:
                evolution_result = await self._trigger_capability_evolution(task_description, context)
                if evolution_result["success"]:
                    existing_tools = evolution_result["new_tools"]
            
            # Step 3: Execute task with available tools
            execution_result = await self._execute_task_with_tools(
                task_description, existing_tools, context
            )
            
            # Step 4: Learn from execution for future improvements
            await self._learn_from_execution(task_description, execution_result)
            
            return {
                "task_id": task_id,
                "success": execution_result["success"],
                "result": execution_result,
                "tools_used": [tool.name for tool in existing_tools],
                "evolution_triggered": "evolution_result" in locals(),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Task processing failed: {e}")
            return {
                "task_id": task_id,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _analyze_existing_capabilities(self, task_description: str) -> List[MCPTool]:
        """Analyze if existing tools can handle the task"""
        available_tools = []
        
        # Check registered MCP tools
        for tool_name, tool in self.core_protocol.mcp_tools.items():
            if self._tool_matches_task(tool, task_description):
                available_tools.append(tool)
        
        # Check MCP box for reusable tools
        for tool_name, tool in self.core_protocol.mcp_box.items():
            if self._tool_matches_task(tool, task_description) and tool not in available_tools:
                available_tools.append(tool)
        
        logger.info(f"Found {len(available_tools)} existing tools for task")
        return available_tools
    
    def _tool_matches_task(self, tool: MCPTool, task_description: str) -> bool:
        """Check if a tool is relevant for the task"""
        task_lower = task_description.lower()
        tool_desc_lower = tool.description.lower()
        
        # Simple keyword matching (would be enhanced with semantic similarity)
        common_words = set(task_lower.split()) & set(tool_desc_lower.split())
        return len(common_words) > 0
    
    async def _trigger_capability_evolution(self, task_description: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Trigger ALITA capability evolution"""
        if not self.alita_evolution:
            return {"success": False, "error": "Evolution not enabled"}
        
        evolution_id = f"evolution_{len(self.active_evolutions)}"
        
        try:
            logger.info(f"Triggering capability evolution: {evolution_id}")
            
            # Track active evolution
            self.active_evolutions[evolution_id] = {
                "task_description": task_description,
                "context": context,
                "started_at": datetime.now().isoformat(),
                "status": "in_progress"
            }
            
            # Evolve capabilities
            new_tools = await self.alita_evolution.evolve_capabilities(task_description, context)
            
            # Update tracking
            self.active_evolutions[evolution_id]["status"] = "completed"
            self.active_evolutions[evolution_id]["completed_at"] = datetime.now().isoformat()
            self.active_evolutions[evolution_id]["tools_generated"] = len(new_tools)
            
            # Update metrics
            self.performance_metrics["successful_evolutions"] += 1
            self.performance_metrics["tools_generated"] += len(new_tools)
            
            logger.info(f"Evolution completed: {len(new_tools)} new tools generated")
            
            return {
                "success": True,
                "evolution_id": evolution_id,
                "new_tools": new_tools,
                "tools_count": len(new_tools)
            }
            
        except Exception as e:
            logger.error(f"Evolution failed: {e}")
            
            # Update tracking
            if evolution_id in self.active_evolutions:
                self.active_evolutions[evolution_id]["status"] = "failed"
                self.active_evolutions[evolution_id]["error"] = str(e)
            
            # Update metrics
            self.performance_metrics["failed_evolutions"] += 1
            
            return {
                "success": False,
                "evolution_id": evolution_id,
                "error": str(e)
            }
    
    async def _execute_task_with_tools(self, 
                                     task_description: str, 
                                     tools: List[MCPTool], 
                                     context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task using available tools"""
        if not tools:
            return {
                "success": False,
                "error": "No suitable tools available",
                "suggestion": "Consider enabling capability evolution"
            }
        
        logger.info(f"Executing task with {len(tools)} tools")
        
        try:
            # For demonstration, we'll simulate task execution
            # In a real implementation, this would orchestrate tool usage
            
            execution_results = []
            
            for tool in tools:
                # Create isolated environment if needed
                env_id = None
                if self.config.enable_environment_isolation and tool.metadata.get("generated"):
                    env_id = await self._create_execution_environment(tool)
                
                # Simulate tool execution
                tool_result = await self._execute_tool(tool, task_description, context, env_id)
                execution_results.append(tool_result)
            
            # Aggregate results
            success = any(result["success"] for result in execution_results)
            
            return {
                "success": success,
                "tool_results": execution_results,
                "execution_summary": self._summarize_execution(execution_results)
            }
            
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _create_execution_environment(self, tool: MCPTool) -> Optional[str]:
        """Create isolated environment for tool execution"""
        if not self.environment_manager:
            return None
        
        try:
            dependencies = tool.metadata.get("dependencies", [])
            environment = await self.environment_manager.create_environment(
                name=f"env_{tool.name}",
                dependencies=dependencies
            )
            
            self.performance_metrics["environments_created"] += 1
            logger.info(f"Created environment for tool: {tool.name}")
            
            return environment.id
            
        except Exception as e:
            logger.error(f"Failed to create environment for {tool.name}: {e}")
            return None
    
    async def _execute_tool(self, 
                          tool: MCPTool, 
                          task_description: str, 
                          context: Dict[str, Any],
                          env_id: Optional[str]) -> Dict[str, Any]:
        """Execute a specific tool"""
        logger.info(f"Executing tool: {tool.name}")
        
        try:
            # Simulate tool execution
            # In a real implementation, this would call the actual tool
            
            result = {
                "tool_name": tool.name,
                "success": True,
                "output": f"Simulated execution of {tool.name} for task: {task_description[:50]}...",
                "environment_id": env_id,
                "execution_time": datetime.now().isoformat()
            }
            
            logger.info(f"Tool execution completed: {tool.name}")
            return result
            
        except Exception as e:
            logger.error(f"Tool execution failed {tool.name}: {e}")
            return {
                "tool_name": tool.name,
                "success": False,
                "error": str(e),
                "environment_id": env_id,
                "execution_time": datetime.now().isoformat()
            }
    
    def _summarize_execution(self, execution_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize execution results"""
        total_tools = len(execution_results)
        successful_tools = len([r for r in execution_results if r["success"]])
        
        return {
            "total_tools_executed": total_tools,
            "successful_executions": successful_tools,
            "success_rate": successful_tools / total_tools if total_tools > 0 else 0,
            "tools_used": [r["tool_name"] for r in execution_results]
        }
    
    async def _learn_from_execution(self, task_description: str, execution_result: Dict[str, Any]):
        """Learn from execution for future improvements"""
        # This would implement learning mechanisms
        # For now, we'll just log insights
        
        if execution_result["success"]:
            logger.info("Task execution successful - no immediate learning needed")
        else:
            logger.info("Task execution failed - analyzing for improvement opportunities")
            
            # Could trigger additional capability evolution here
            if self.config.enable_self_evolution:
                logger.info("Considering additional capability evolution")
    
    async def _periodic_maintenance(self):
        """Periodic maintenance tasks"""
        while self.running:
            try:
                # Cleanup old environments
                if self.environment_manager:
                    await self.environment_manager.cleanup_inactive_environments(
                        self.config.environment_cleanup_hours
                    )
                
                # Cleanup old evolution tracking
                current_time = datetime.now()
                old_evolutions = []
                
                for evo_id, evo_data in self.active_evolutions.items():
                    started_at = datetime.fromisoformat(evo_data["started_at"])
                    age_hours = (current_time - started_at).total_seconds() / 3600
                    
                    if age_hours > 24:  # Remove evolutions older than 24 hours
                        old_evolutions.append(evo_id)
                
                for evo_id in old_evolutions:
                    del self.active_evolutions[evo_id]
                
                # Wait before next maintenance cycle
                await asyncio.sleep(3600)  # 1 hour
                
            except Exception as e:
                logger.error(f"Maintenance error: {e}")
                await asyncio.sleep(300)  # 5 minutes on error
    
    def get_framework_status(self) -> Dict[str, Any]:
        """Get comprehensive framework status"""
        status = {
            "framework": "MCP × A2A × ALITA",
            "initialized": self.initialized,
            "running": self.running,
            "config": asdict(self.config),
            "performance_metrics": self.performance_metrics,
            "active_evolutions": len(self.active_evolutions),
            "core_protocol": self.core_protocol.get_protocol_status() if self.core_protocol else None
        }
        
        if self.alita_evolution:
            status["alita_evolution"] = self.alita_evolution.get_evolution_status()
        
        if self.environment_manager:
            status["environment_manager"] = self.environment_manager.get_manager_status()
        
        return status
    
    async def discover_and_integrate_resources(self, query: str) -> Dict[str, Any]:
        """Discover and integrate external resources (Web Agent functionality)"""
        # This would implement web discovery capabilities
        # For now, return a placeholder
        
        logger.info(f"Discovering resources for: {query}")
        
        return {
            "query": query,
            "resources_found": 0,
            "integrated_tools": 0,
            "status": "discovery_not_implemented",
            "timestamp": datetime.now().isoformat()
        }
    
    async def create_agent_collaboration(self, target_agent_id: str, task: str) -> Dict[str, Any]:
        """Create collaboration with another agent using A2A protocol"""
        logger.info(f"Creating collaboration with agent: {target_agent_id}")
        
        # Create A2A message
        message = A2AMessage(
            id=f"collab_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            type=MessageType.TASK_REQUEST,
            sender_id=self.config.agent_id,
            receiver_id=target_agent_id,
            timestamp=datetime.now().isoformat(),
            content={
                "collaboration_request": True,
                "task": task,
                "capabilities_offered": list(self.core_protocol.mcp_tools.keys()),
                "framework": "MCP × A2A × ALITA"
            }
        )
        
        # In a real implementation, this would send the message
        logger.info(f"Collaboration request created: {message.id}")
        
        return {
            "collaboration_id": message.id,
            "target_agent": target_agent_id,
            "task": task,
            "status": "request_created",
            "timestamp": datetime.now().isoformat()
        }
