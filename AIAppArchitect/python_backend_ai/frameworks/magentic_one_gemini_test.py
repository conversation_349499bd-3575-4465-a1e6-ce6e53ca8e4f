"""
Magentic One Test Implementation with Google Gemini
A simplified test version for validating core AI agent functionality
"""

import asyncio
import json
import uuid
import os
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
import structlog

# Core imports
from ..core.ai_service import AizenAIService, AIConfig, AIResponse

logger = structlog.get_logger(__name__)


@dataclass
class GeminiMagenticConfig:
    """Configuration for Gemini-powered Magentic One test"""
    model_name: str = "gemini-pro"
    temperature: float = 0.7
    max_tokens: int = 4096
    max_rounds: int = 5  # Reduced for testing
    gemini_api_key: Optional[str] = None
    enable_web_browsing: bool = False  # Simplified for testing
    enable_code_execution: bool = True
    enable_file_handling: bool = True


@dataclass
class TestTask:
    """Simplified task definition for testing"""
    id: str
    description: str
    status: str = "pending"
    result: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    agent_responses: List[Dict[str, Any]] = field(default_factory=list)
    execution_time: float = 0.0


class GeminiModelClient:
    """
    Gemini model client compatible with AutoGen interface
    Wraps our AizenAIService for use with Magentic One
    """
    
    def __init__(self, config: GeminiMagenticConfig):
        self.config = config
        self.ai_service = AizenAIService(
            provider="gemini",
            model=config.model_name,
            api_key=config.gemini_api_key or os.getenv("GEMINI_API_KEY"),
            temperature=config.temperature,
            max_tokens=config.max_tokens
        )
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the Gemini client"""
        if not self._initialized:
            await self.ai_service.initialize()
            self._initialized = True
            logger.info("Gemini model client initialized")

    async def create_completion(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> Dict[str, Any]:
        """Create completion compatible with AutoGen interface"""
        if not self._initialized:
            await self.initialize()
        
        # Convert messages to prompt format
        system_prompt = None
        user_prompt = ""
        
        for message in messages:
            role = message.get("role", "")
            content = message.get("content", "")
            
            if role == "system":
                system_prompt = content
            elif role == "user":
                user_prompt += f"User: {content}\n"
            elif role == "assistant":
                user_prompt += f"Assistant: {content}\n"
        
        # Generate response
        response = await self.ai_service.generate_response(
            prompt=user_prompt.strip(),
            system_prompt=system_prompt
        )
        
        # Return in AutoGen-compatible format
        return {
            "choices": [{
                "message": {
                    "role": "assistant",
                    "content": response.content
                },
                "finish_reason": response.finish_reason
            }],
            "usage": {
                "total_tokens": response.tokens_used,
                "prompt_tokens": response.tokens_used // 2,  # Estimate
                "completion_tokens": response.tokens_used // 2
            },
            "model": response.model
        }

    async def shutdown(self) -> None:
        """Shutdown the client"""
        await self.ai_service.shutdown()
        self._initialized = False


class SimplifiedAgent:
    """
    Simplified agent for testing without full AutoGen dependencies
    """
    
    def __init__(self, name: str, role: str, system_message: str, model_client: GeminiModelClient):
        self.name = name
        self.role = role
        self.system_message = system_message
        self.model_client = model_client
        self.conversation_history: List[Dict[str, str]] = []

    async def process_message(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Process a message and generate response"""
        # Add to conversation history
        self.conversation_history.append({"role": "user", "content": message})
        
        # Prepare messages for the model
        messages = [{"role": "system", "content": self.system_message}]
        messages.extend(self.conversation_history[-5:])  # Keep last 5 messages for context
        
        # Generate response
        response = await self.model_client.create_completion(messages)
        agent_response = response["choices"][0]["message"]["content"]
        
        # Add response to history
        self.conversation_history.append({"role": "assistant", "content": agent_response})
        
        logger.info("Agent response generated", 
                   agent=self.name, 
                   role=self.role,
                   response_length=len(agent_response))
        
        return agent_response


class MagenticOneGeminiTest:
    """
    Simplified Magentic One test implementation using Google Gemini
    Focus on core functionality validation
    """
    
    def __init__(self, config: Optional[GeminiMagenticConfig] = None):
        self.config = config or GeminiMagenticConfig()
        self.model_client: Optional[GeminiModelClient] = None
        self.agents: Dict[str, SimplifiedAgent] = {}
        self.tasks: Dict[str, TestTask] = {}
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the test framework"""
        if self._initialized:
            return
        
        logger.info("Initializing Magentic One Gemini Test...")
        
        try:
            # Initialize model client
            self.model_client = GeminiModelClient(self.config)
            await self.model_client.initialize()
            
            # Create simplified agents
            await self._create_test_agents()
            
            self._initialized = True
            logger.info("Magentic One Gemini Test initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Magentic One Gemini Test", error=str(e))
            raise

    async def _create_test_agents(self) -> None:
        """Create simplified test agents"""
        
        # Orchestrator Agent
        self.agents["orchestrator"] = SimplifiedAgent(
            name="orchestrator",
            role="orchestrator",
            system_message="""You are the Orchestrator agent in a multi-agent system.
            Your role is to coordinate tasks, delegate to other agents, and synthesize results.
            You work with: coder (for programming tasks), analyst (for analysis), and assistant (for general tasks).
            Always provide clear, structured responses and coordinate effectively.""",
            model_client=self.model_client
        )
        
        # Coder Agent
        if self.config.enable_code_execution:
            self.agents["coder"] = SimplifiedAgent(
                name="coder",
                role="coder",
                system_message="""You are a Coder agent specialized in programming and software development.
                You can write code in multiple languages, debug issues, and provide technical solutions.
                Always write clean, well-documented code with explanations.""",
                model_client=self.model_client
            )
        
        # Analyst Agent
        self.agents["analyst"] = SimplifiedAgent(
            name="analyst",
            role="analyst",
            system_message="""You are an Analyst agent specialized in data analysis and problem-solving.
            You can analyze information, identify patterns, and provide insights.
            Always provide thorough analysis with clear reasoning.""",
            model_client=self.model_client
        )
        
        # General Assistant Agent
        self.agents["assistant"] = SimplifiedAgent(
            name="assistant",
            role="assistant",
            system_message="""You are a General Assistant agent that can help with various tasks.
            You provide helpful, accurate, and detailed responses to user queries.
            You work collaboratively with other agents when needed.""",
            model_client=self.model_client
        )

    async def execute_test_task(self, task_description: str) -> Dict[str, Any]:
        """Execute a test task using the simplified agent system"""
        if not self._initialized:
            await self.initialize()
        
        task_id = str(uuid.uuid4())
        task = TestTask(
            id=task_id,
            description=task_description,
            status="in_progress"
        )
        
        self.tasks[task_id] = task
        start_time = datetime.now()
        
        try:
            logger.info("Executing test task", task_id=task_id, description=task_description[:100])
            
            # Step 1: Orchestrator analyzes the task
            orchestrator_response = await self.agents["orchestrator"].process_message(
                f"Analyze this task and determine which agents should handle it: {task_description}"
            )
            
            task.agent_responses.append({
                "agent": "orchestrator",
                "response": orchestrator_response,
                "timestamp": datetime.now().isoformat()
            })
            
            # Step 2: Determine which agents to use based on task content
            agents_to_use = self._determine_agents_for_task(task_description, orchestrator_response)
            
            # Step 3: Execute with selected agents
            final_result = await self._execute_with_agents(task_description, agents_to_use, task)
            
            # Complete the task
            task.status = "completed"
            task.result = final_result
            task.completed_at = datetime.now()
            task.execution_time = (task.completed_at - start_time).total_seconds()
            
            return {
                "success": True,
                "task_id": task_id,
                "result": final_result,
                "agents_used": agents_to_use,
                "execution_time": task.execution_time,
                "agent_interactions": len(task.agent_responses)
            }
            
        except Exception as e:
            task.status = "failed"
            task.result = f"Error: {str(e)}"
            task.completed_at = datetime.now()
            task.execution_time = (task.completed_at - start_time).total_seconds()
            
            logger.error("Test task failed", task_id=task_id, error=str(e))
            
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e),
                "execution_time": task.execution_time
            }

    def _determine_agents_for_task(self, task_description: str, orchestrator_response: str) -> List[str]:
        """Determine which agents should handle the task"""
        agents_to_use = ["orchestrator"]  # Always include orchestrator
        
        task_lower = task_description.lower()
        orchestrator_lower = orchestrator_response.lower()
        
        # Check for coding tasks
        if any(word in task_lower for word in ["code", "program", "script", "develop", "function"]) or \
           any(word in orchestrator_lower for word in ["coder", "programming", "code"]):
            if "coder" in self.agents:
                agents_to_use.append("coder")
        
        # Check for analysis tasks
        if any(word in task_lower for word in ["analyze", "analysis", "data", "pattern", "insight"]) or \
           any(word in orchestrator_lower for word in ["analyst", "analysis", "analyze"]):
            agents_to_use.append("analyst")
        
        # Always include assistant for general support
        agents_to_use.append("assistant")
        
        return list(set(agents_to_use))  # Remove duplicates

    async def _execute_with_agents(self, task_description: str, agents_to_use: List[str], task: TestTask) -> str:
        """Execute task with selected agents"""
        responses = []
        
        for agent_name in agents_to_use:
            if agent_name in self.agents:
                agent = self.agents[agent_name]
                
                # Customize prompt based on agent role
                if agent_name == "orchestrator":
                    prompt = f"Coordinate the following task: {task_description}"
                else:
                    prompt = f"Help with this task from your expertise area: {task_description}"
                
                response = await agent.process_message(prompt)
                
                task.agent_responses.append({
                    "agent": agent_name,
                    "response": response,
                    "timestamp": datetime.now().isoformat()
                })
                
                responses.append(f"{agent_name.title()}: {response}")
        
        # Synthesize final result
        final_result = "\n\n".join(responses)
        return final_result

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get status of a test task"""
        if task_id not in self.tasks:
            return {"error": f"Task '{task_id}' not found"}
        
        task = self.tasks[task_id]
        
        return {
            "task_id": task_id,
            "description": task.description,
            "status": task.status,
            "result": task.result,
            "created_at": task.created_at.isoformat(),
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "execution_time": task.execution_time,
            "agent_interactions": len(task.agent_responses)
        }

    async def list_agents(self) -> List[Dict[str, Any]]:
        """List available test agents"""
        return [
            {
                "name": agent.name,
                "role": agent.role,
                "system_message": agent.system_message[:100] + "..." if len(agent.system_message) > 100 else agent.system_message
            }
            for agent in self.agents.values()
        ]

    async def get_framework_status(self) -> Dict[str, Any]:
        """Get framework status"""
        task_counts = {
            "pending": len([t for t in self.tasks.values() if t.status == "pending"]),
            "in_progress": len([t for t in self.tasks.values() if t.status == "in_progress"]),
            "completed": len([t for t in self.tasks.values() if t.status == "completed"]),
            "failed": len([t for t in self.tasks.values() if t.status == "failed"])
        }
        
        return {
            "initialized": self._initialized,
            "model": self.config.model_name,
            "agents": len(self.agents),
            "task_counts": task_counts,
            "config": {
                "model_name": self.config.model_name,
                "max_rounds": self.config.max_rounds,
                "code_execution": self.config.enable_code_execution,
                "file_handling": self.config.enable_file_handling
            }
        }

    async def shutdown(self) -> None:
        """Shutdown the test framework"""
        logger.info("Shutting down Magentic One Gemini Test...")
        
        if self.model_client:
            await self.model_client.shutdown()
        
        self.agents.clear()
        self.tasks.clear()
        self.model_client = None
        self._initialized = False
        
        logger.info("Magentic One Gemini Test shutdown complete")
