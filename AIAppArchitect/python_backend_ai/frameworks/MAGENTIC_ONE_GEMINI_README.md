# Magentic One Gemini Test Implementation

## Overview

This is a simplified test implementation of Microsoft's Magentic One framework using Google Gemini as the underlying language model. The implementation focuses on validating core AI agent functionality rather than providing a full production system.

## Architecture

### Core Components

1. **GeminiModelClient**: Wraps the Google Gemini API to be compatible with AutoGen-style interfaces
2. **SimplifiedAgent**: Lightweight agent implementation for testing multi-agent coordination
3. **MagenticOneGeminiTest**: Main framework orchestrating multiple agents for task execution
4. **Test Suite**: Comprehensive tests validating functionality and performance

### Agent Types

- **Orchestrator**: Coordinates tasks and delegates to specialized agents
- **Coder**: Handles programming and software development tasks
- **Analyst**: Performs data analysis and problem-solving
- **Assistant**: General-purpose agent for various tasks

## Features

### ✅ Implemented Features

- **Google Gemini Integration**: Full support for Gemini Pro models
- **Multi-Agent Coordination**: Orchestrator delegates tasks to specialized agents
- **Task Management**: Complete task lifecycle with status tracking
- **Error Handling**: Robust error handling and recovery
- **Comprehensive Testing**: Unit tests and integration tests
- **Performance Monitoring**: Execution time and token usage tracking
- **Configurable Settings**: Flexible configuration for different use cases

### 🔄 Simplified for Testing

- **Web Browsing**: Disabled by default (can be enabled)
- **File Operations**: Simplified file handling
- **Code Execution**: Simulated rather than actual execution
- **Agent Count**: Reduced to 4 core agents for testing

## Installation

### Prerequisites

1. Python 3.8+
2. Google Gemini API key

### Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variable:
```bash
export GEMINI_API_KEY="your-gemini-api-key-here"
```

3. Get API key from: https://makersuite.google.com/app/apikey

## Usage

### Basic Usage

```python
import asyncio
from frameworks.magentic_one_gemini_test import MagenticOneGeminiTest, GeminiMagenticConfig

async def main():
    # Configure the framework
    config = GeminiMagenticConfig(
        model_name="gemini-pro",
        gemini_api_key="your-api-key",
        temperature=0.7,
        max_tokens=2048
    )
    
    # Initialize framework
    framework = MagenticOneGeminiTest(config)
    await framework.initialize()
    
    try:
        # Execute a task
        result = await framework.execute_test_task(
            "Write a Python function to calculate the Fibonacci sequence"
        )
        
        print(f"Task completed: {result['success']}")
        print(f"Result: {result['result']}")
        print(f"Agents used: {result['agents_used']}")
        
    finally:
        await framework.shutdown()

asyncio.run(main())
```

### Running Tests

#### Quick Test
```bash
cd AIAppArchitect/python_backend_ai
python test_gemini_magentic_one.py
```

#### Unit Tests
```bash
cd AIAppArchitect/python_backend_ai
python -m pytest tests/test_magentic_one_gemini.py -v
```

#### Integration Tests (requires API key)
```bash
cd AIAppArchitect/python_backend_ai
python -m pytest tests/test_magentic_one_gemini.py::TestIntegration -v
```

## Configuration

### GeminiMagenticConfig Options

```python
config = GeminiMagenticConfig(
    model_name="gemini-pro",           # Gemini model to use
    temperature=0.7,                   # Response creativity (0.0-1.0)
    max_tokens=4096,                   # Maximum response length
    max_rounds=5,                      # Maximum agent interaction rounds
    gemini_api_key=None,               # API key (or use env var)
    enable_web_browsing=False,         # Enable web browsing (simplified)
    enable_code_execution=True,        # Enable code generation
    enable_file_handling=True          # Enable file operations
)
```

## Test Results Validation

The test suite validates the following capabilities:

### ✅ Core Functionality
- **Query Processing**: Agents can process and respond to user queries
- **Response Generation**: Gemini integration produces coherent responses
- **Task Completion**: Tasks are executed from start to finish
- **Error Handling**: Graceful handling of failures and exceptions

### ✅ Multi-Agent Coordination
- **Task Delegation**: Orchestrator properly delegates to specialized agents
- **Agent Selection**: Appropriate agents are chosen based on task content
- **Result Synthesis**: Multiple agent responses are combined effectively
- **Communication**: Agents can build on each other's responses

### ✅ Performance Metrics
- **Response Time**: Tasks complete within reasonable time limits
- **Token Usage**: Efficient use of API tokens
- **Success Rate**: High percentage of successful task completions
- **Reliability**: Consistent performance across multiple runs

## Expected Test Results

### Successful Test Run Example

```
🚀 Starting Magentic One Gemini Test Suite
==================================================

📊 Framework Status:
  Initialized: ✅
  Model: gemini-pro
  Agents: 4
  Total Tasks: 0

🤖 Available Agents:
  - orchestrator (orchestrator)
  - coder (coder)
  - analyst (analyst)
  - assistant (assistant)

🧪 Running Basic Functionality Tests...

  Test 1: Simple Query Processing
    Status: ✅ PASS
    Execution Time: 2.34s
    Agents Used: orchestrator, assistant
    Keyword Match: 100.0%

  Test 2: Mathematical Calculation
    Status: ✅ PASS
    Execution Time: 3.12s
    Agents Used: orchestrator, analyst, assistant
    Keyword Match: 80.0%

🤝 Running Multi-Agent Coordination Tests...

  Coordination Test 1: Complex Problem Solving
    Status: ✅ PASS
    Expected Agents: orchestrator, coder, analyst
    Used Agents: orchestrator, coder, analyst, assistant
    Coordination: ✅

⚡ Running Performance Tests...
  Response Time: 1.87s ✅ PASS

📋 Test Report Summary:
==================================================
Total Tests: 6
Passed: 6
Failed: 0
Success Rate: 100.0%
Total Execution Time: 45.23s

🎯 Overall Result: ✅ SUCCESS
```

## Troubleshooting

### Common Issues

1. **API Key Error**
   ```
   Error: GEMINI_API_KEY environment variable not set
   ```
   Solution: Set the environment variable or pass it in config

2. **Import Errors**
   ```
   ImportError: No module named 'google.generativeai'
   ```
   Solution: Install dependencies with `pip install google-generativeai`

3. **Rate Limiting**
   ```
   Error: Rate limit exceeded
   ```
   Solution: Reduce request frequency or upgrade API plan

4. **Timeout Errors**
   ```
   Error: Request timeout
   ```
   Solution: Increase timeout in configuration or check network

### Debug Mode

Enable debug logging:
```python
import structlog
structlog.configure(level="DEBUG")
```

## Limitations

### Current Limitations

1. **Simplified Agents**: Not full AutoGen agents, simplified for testing
2. **No Real Code Execution**: Code generation only, no actual execution
3. **Limited Web Browsing**: Simulated web browsing capabilities
4. **Basic File Handling**: Simplified file operations
5. **No Persistent Memory**: Agents don't retain memory between sessions

### Future Enhancements

1. **Full AutoGen Integration**: Use real AutoGen agents with Gemini
2. **Code Execution Environment**: Integrate with E2B or similar
3. **Advanced Web Browsing**: Real web scraping and interaction
4. **Persistent Memory**: Agent memory across sessions
5. **Tool Integration**: Support for external tools and APIs

## Performance Benchmarks

### Expected Performance

- **Simple Queries**: 1-3 seconds
- **Complex Tasks**: 5-15 seconds
- **Multi-Agent Coordination**: 10-30 seconds
- **Success Rate**: >90% for basic tasks
- **Token Efficiency**: <2000 tokens per task average

### Optimization Tips

1. **Reduce max_tokens** for faster responses
2. **Lower temperature** for more consistent results
3. **Limit max_rounds** to prevent long conversations
4. **Use specific prompts** to guide agent behavior
5. **Monitor token usage** to optimize costs

## Contributing

### Development Setup

1. Clone the repository
2. Install development dependencies
3. Set up pre-commit hooks
4. Run tests before submitting changes

### Testing Guidelines

1. Add unit tests for new features
2. Include integration tests for API changes
3. Update documentation for new capabilities
4. Ensure all tests pass before committing

## License

This implementation is part of the Aizen AI Extension project and follows the same licensing terms.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review test output for specific errors
3. Enable debug logging for detailed information
4. Consult the main project documentation
