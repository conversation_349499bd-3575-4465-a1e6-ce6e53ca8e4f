"""
DGM Evolution Utilities
Supporting utilities for the Darwin Gödel Machine evolution process
Based on the real DGM repository implementation
"""

import os
import json
import logging
import glob
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime


def load_dgm_metadata(metadata_path: str, last_only: bool = False) -> Dict[str, Any]:
    """Load DGM metadata from JSONL file"""
    try:
        if not os.path.exists(metadata_path):
            return {} if last_only else []
        
        with open(metadata_path, 'r') as f:
            lines = f.readlines()
            
        if not lines:
            return {} if last_only else []
        
        if last_only:
            return json.loads(lines[-1].strip())
        else:
            return [json.loads(line.strip()) for line in lines if line.strip()]
            
    except Exception as e:
        logging.error(f"Failed to load DGM metadata: {e}")
        return {} if last_only else []


def save_dgm_metadata(metadata: Dict[str, Any], metadata_path: str) -> bool:
    """Save DGM metadata to JSONL file"""
    try:
        os.makedirs(os.path.dirname(metadata_path), exist_ok=True)
        
        with open(metadata_path, 'a') as f:
            f.write(json.dumps(metadata) + '\n')
        
        return True
        
    except Exception as e:
        logging.error(f"Failed to save DGM metadata: {e}")
        return False


def is_compiled_self_improve(metadata: Dict[str, Any], num_swe_issues: List[int] = None, logger=None) -> bool:
    """
    Check if a self-improvement run compiled successfully
    Based on the real DGM repository logic
    """
    try:
        # Check for errors
        if 'error' in metadata:
            if logger:
                logger.info(f"Run has error: {metadata.get('error')}")
            return False
        
        # Check if overall performance exists
        overall_perf = metadata.get('overall_performance', {})
        if not overall_perf:
            if logger:
                logger.info("No overall performance data")
            return False
        
        # Check if minimum number of instances were submitted
        submitted = overall_perf.get('total_submitted_instances', 0)
        if num_swe_issues and submitted < min(num_swe_issues) * 0.5:
            if logger:
                logger.info(f"Too few instances submitted: {submitted}")
            return False
        
        # Check if accuracy score exists and is reasonable
        accuracy = overall_perf.get('accuracy_score', 0.0)
        if accuracy < 0.0 or accuracy > 1.0:
            if logger:
                logger.info(f"Invalid accuracy score: {accuracy}")
            return False
        
        # Check if there are resolved instances (indicating the agent worked)
        resolved_ids = overall_perf.get('total_resolved_ids', [])
        if not resolved_ids and accuracy == 0.0:
            if logger:
                logger.info("No resolved instances and zero accuracy")
            return False
        
        return True
        
    except Exception as e:
        if logger:
            logger.error(f"Error checking compilation: {e}")
        return False


def get_model_patch_paths(output_dir: str, run_id: str) -> Tuple[str, str]:
    """Get the paths to model patch files for a run"""
    run_dir = os.path.join(output_dir, run_id)
    model_patch_file = os.path.join(run_dir, "model_patch.diff")
    model_name_or_path = os.path.join(run_dir, "coding_agent.py")
    return model_patch_file, model_name_or_path


def get_all_performance(model_name_or_path: str, results_dir: str) -> Tuple[List[Dict], Dict[str, Any]]:
    """
    Get all performance data from evaluation results
    Based on the real DGM repository implementation
    """
    performances = []
    overall_performance = {
        'accuracy_score': 0.0,
        'total_resolved_ids': [],
        'total_unresolved_ids': [],
        'total_emptypatch_ids': [],
        'total_submitted_instances': 0
    }
    
    try:
        # Look for evaluation result files
        eval_pattern = os.path.join(results_dir, "**/evaluation_results.json")
        eval_files = glob.glob(eval_pattern, recursive=True)
        
        total_resolved = 0
        total_instances = 0
        all_resolved_ids = []
        all_unresolved_ids = []
        all_emptypatch_ids = []
        
        for eval_file in eval_files:
            try:
                with open(eval_file, 'r') as f:
                    eval_data = json.load(f)
                
                performances.append(eval_data)
                
                # Aggregate performance metrics
                if 'resolved_ids' in eval_data:
                    resolved_ids = eval_data['resolved_ids']
                    all_resolved_ids.extend(resolved_ids)
                    total_resolved += len(resolved_ids)
                
                if 'unresolved_ids' in eval_data:
                    unresolved_ids = eval_data['unresolved_ids']
                    all_unresolved_ids.extend(unresolved_ids)
                
                if 'emptypatch_ids' in eval_data:
                    emptypatch_ids = eval_data['emptypatch_ids']
                    all_emptypatch_ids.extend(emptypatch_ids)
                
                if 'total_instances' in eval_data:
                    total_instances += eval_data['total_instances']
                
            except Exception as e:
                logging.warning(f"Failed to load evaluation file {eval_file}: {e}")
                continue
        
        # Calculate overall accuracy
        if total_instances > 0:
            overall_performance['accuracy_score'] = total_resolved / total_instances
        
        overall_performance['total_resolved_ids'] = list(set(all_resolved_ids))
        overall_performance['total_unresolved_ids'] = list(set(all_unresolved_ids))
        overall_performance['total_emptypatch_ids'] = list(set(all_emptypatch_ids))
        overall_performance['total_submitted_instances'] = total_instances
        
    except Exception as e:
        logging.error(f"Failed to get performance data: {e}")
    
    return performances, overall_performance


def calculate_performance_improvement(parent_performance: Dict, child_performance: Dict) -> float:
    """Calculate the performance improvement between parent and child"""
    try:
        parent_score = parent_performance.get('accuracy_score', 0.0)
        child_score = child_performance.get('accuracy_score', 0.0)
        return child_score - parent_score
    except Exception:
        return 0.0


def get_best_performing_commit(output_dir: str, archive: List[str]) -> str:
    """Get the commit with the best performance from the archive"""
    best_commit = 'initial'
    best_score = 0.0
    
    for commit in archive:
        try:
            metadata_path = os.path.join(output_dir, commit, "metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                score = metadata.get("overall_performance", {}).get("accuracy_score", 0.0)
                if score > best_score:
                    best_score = score
                    best_commit = commit
                    
        except Exception as e:
            logging.warning(f"Failed to get performance for commit {commit}: {e}")
            continue
    
    return best_commit


def filter_archive_by_performance(output_dir: str, archive: List[str], min_score: float = 0.0) -> List[str]:
    """Filter archive to only include commits above a minimum performance threshold"""
    filtered_archive = []
    
    for commit in archive:
        try:
            metadata_path = os.path.join(output_dir, commit, "metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                score = metadata.get("overall_performance", {}).get("accuracy_score", 0.0)
                if score >= min_score:
                    filtered_archive.append(commit)
                    
        except Exception as e:
            logging.warning(f"Failed to check performance for commit {commit}: {e}")
            continue
    
    return filtered_archive


def get_evolution_statistics(output_dir: str) -> Dict[str, Any]:
    """Get statistics about the evolution process"""
    stats = {
        'total_generations': 0,
        'total_attempts': 0,
        'total_successes': 0,
        'success_rate': 0.0,
        'best_score': 0.0,
        'improvement_trend': []
    }
    
    try:
        metadata_path = os.path.join(output_dir, "dgm_metadata.jsonl")
        if os.path.exists(metadata_path):
            metadata_list = load_dgm_metadata(metadata_path, last_only=False)
            
            stats['total_generations'] = len(metadata_list)
            
            for metadata in metadata_list:
                attempts = len(metadata.get('children', []))
                successes = len(metadata.get('children_compiled', []))
                
                stats['total_attempts'] += attempts
                stats['total_successes'] += successes
                
                # Track improvement trend
                archive = metadata.get('archive', [])
                if archive:
                    best_in_gen = get_best_performing_commit(output_dir, archive)
                    best_metadata_path = os.path.join(output_dir, best_in_gen, "metadata.json")
                    if os.path.exists(best_metadata_path):
                        with open(best_metadata_path, 'r') as f:
                            best_metadata = json.load(f)
                        score = best_metadata.get("overall_performance", {}).get("accuracy_score", 0.0)
                        stats['improvement_trend'].append(score)
                        stats['best_score'] = max(stats['best_score'], score)
            
            if stats['total_attempts'] > 0:
                stats['success_rate'] = stats['total_successes'] / stats['total_attempts']
                
    except Exception as e:
        logging.error(f"Failed to get evolution statistics: {e}")
    
    return stats


def cleanup_old_runs(output_dir: str, keep_best_n: int = 10) -> None:
    """Clean up old runs, keeping only the best performing ones"""
    try:
        # Get all run directories
        run_dirs = [d for d in os.listdir(output_dir) 
                   if os.path.isdir(os.path.join(output_dir, d)) and d != 'initial']
        
        # Get performance for each run
        run_performances = []
        for run_id in run_dirs:
            try:
                metadata_path = os.path.join(output_dir, run_id, "metadata.json")
                if os.path.exists(metadata_path):
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                    score = metadata.get("overall_performance", {}).get("accuracy_score", 0.0)
                    run_performances.append((run_id, score))
            except Exception:
                continue
        
        # Sort by performance and keep only the best
        run_performances.sort(key=lambda x: x[1], reverse=True)
        runs_to_keep = [run_id for run_id, _ in run_performances[:keep_best_n]]
        
        # Remove the rest
        for run_id in run_dirs:
            if run_id not in runs_to_keep:
                import shutil
                run_path = os.path.join(output_dir, run_id)
                shutil.rmtree(run_path, ignore_errors=True)
                logging.info(f"Cleaned up old run: {run_id}")
                
    except Exception as e:
        logging.error(f"Failed to cleanup old runs: {e}")
