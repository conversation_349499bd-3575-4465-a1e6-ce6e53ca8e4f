"""
MCP × A2A Core Protocol Layer Implementation
Integrating ALITA's self-evolving approach with MCP and A2A protocols

This module implements the foundational protocol specifications for:
- Model Context Protocol (MCP) for tool integration
- Agent-to-Agent (A2A) protocol for agent communication
- ALITA's minimal predefinition and maximal self-evolution principles
"""

import json
import uuid
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import aiohttp
from abc import ABC, abstractmethod

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MessageType(Enum):
    """Message types for A2A protocol"""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    CAPABILITY_DISCOVERY = "capability_discovery"
    AGENT_CARD = "agent_card"
    ARTIFACT = "artifact"
    STATUS_UPDATE = "status_update"
    ERROR = "error"

class TaskStatus(Enum):
    """Task lifecycle states"""
    CREATED = "created"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class MCPToolType(Enum):
    """MCP tool types"""
    FUNCTION = "function"
    RESOURCE = "resource"
    PROMPT = "prompt"

@dataclass
class A2AMessage:
    """A2A protocol message structure"""
    id: str
    type: MessageType
    sender_id: str
    receiver_id: Optional[str]
    timestamp: str
    content: Dict[str, Any]
    parts: List[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None

    def to_json(self) -> str:
        """Serialize message to JSON"""
        data = asdict(self)
        data['type'] = self.type.value
        return json.dumps(data, default=str)

    @classmethod
    def from_json(cls, json_str: str) -> 'A2AMessage':
        """Deserialize message from JSON"""
        data = json.loads(json_str)
        data['type'] = MessageType(data['type'])
        return cls(**data)

@dataclass
class AgentCard:
    """Agent capability description for A2A discovery"""
    agent_id: str
    name: str
    description: str
    version: str
    capabilities: List[str]
    supported_functions: List[Dict[str, Any]]
    authentication: Dict[str, Any]
    endpoints: Dict[str, str]
    metadata: Dict[str, Any] = None

    def to_json(self) -> str:
        return json.dumps(asdict(self), default=str)

@dataclass
class MCPTool:
    """MCP tool description"""
    name: str
    description: str
    tool_type: MCPToolType
    schema: Dict[str, Any]
    parameters: Dict[str, Any]
    returns: Dict[str, Any]
    examples: List[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None

@dataclass
class Task:
    """Task object for A2A communication"""
    id: str
    title: str
    description: str
    status: TaskStatus
    created_at: str
    updated_at: str
    requester_id: str
    assignee_id: Optional[str] = None
    artifacts: List[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None

class ProtocolError(Exception):
    """Base exception for protocol errors"""
    pass

class A2AProtocolError(ProtocolError):
    """A2A protocol specific errors"""
    pass

class MCPProtocolError(ProtocolError):
    """MCP protocol specific errors"""
    pass

class CoreProtocolLayer:
    """
    Core protocol layer implementing MCP and A2A specifications
    with ALITA's self-evolution capabilities
    """
    
    def __init__(self, agent_id: str, agent_name: str):
        self.agent_id = agent_id
        self.agent_name = agent_name
        self.agent_card = None
        self.message_handlers: Dict[MessageType, Callable] = {}
        self.mcp_tools: Dict[str, MCPTool] = {}
        self.active_tasks: Dict[str, Task] = {}
        self.session = None
        
        # ALITA integration
        self.mcp_box: Dict[str, MCPTool] = {}  # Reusable MCP storage
        self.capability_gaps: List[str] = []
        self.self_evolution_enabled = True
        
        self._setup_default_handlers()
    
    def _setup_default_handlers(self):
        """Setup default message handlers"""
        self.message_handlers[MessageType.CAPABILITY_DISCOVERY] = self._handle_capability_discovery
        self.message_handlers[MessageType.TASK_REQUEST] = self._handle_task_request
        self.message_handlers[MessageType.TASK_RESPONSE] = self._handle_task_response
        self.message_handlers[MessageType.AGENT_CARD] = self._handle_agent_card
        self.message_handlers[MessageType.STATUS_UPDATE] = self._handle_status_update
        self.message_handlers[MessageType.ERROR] = self._handle_error
    
    async def initialize(self):
        """Initialize the protocol layer"""
        self.session = aiohttp.ClientSession()
        await self._create_agent_card()
        logger.info(f"Core protocol layer initialized for agent {self.agent_id}")
    
    async def shutdown(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
        logger.info(f"Core protocol layer shutdown for agent {self.agent_id}")
    
    async def _create_agent_card(self):
        """Create agent card for A2A discovery"""
        self.agent_card = AgentCard(
            agent_id=self.agent_id,
            name=self.agent_name,
            description="Self-evolving agent with MCP × A2A capabilities",
            version="1.0.0",
            capabilities=list(self.mcp_tools.keys()),
            supported_functions=[
                {
                    "name": "mcp_brainstorming",
                    "description": "Assess capability gaps and plan tool generation",
                    "parameters": {"task": "string", "context": "object"}
                },
                {
                    "name": "dynamic_mcp_creation",
                    "description": "Generate new MCPs based on task requirements",
                    "parameters": {"specification": "object", "resources": "array"}
                }
            ],
            authentication={"type": "bearer", "required": False},
            endpoints={
                "message": f"/agents/{self.agent_id}/messages",
                "capabilities": f"/agents/{self.agent_id}/capabilities",
                "tasks": f"/agents/{self.agent_id}/tasks"
            },
            metadata={
                "framework": "MCP × A2A with ALITA",
                "self_evolution": self.self_evolution_enabled,
                "mcp_box_size": len(self.mcp_box)
            }
        )
    
    def register_mcp_tool(self, tool: MCPTool):
        """Register an MCP tool"""
        self.mcp_tools[tool.name] = tool
        logger.info(f"Registered MCP tool: {tool.name}")
    
    def add_to_mcp_box(self, tool: MCPTool):
        """Add tool to reusable MCP box (ALITA feature)"""
        self.mcp_box[tool.name] = tool
        logger.info(f"Added tool to MCP box: {tool.name}")
    
    async def send_message(self, message: A2AMessage, endpoint: str) -> Dict[str, Any]:
        """Send A2A message to another agent"""
        try:
            async with self.session.post(
                endpoint,
                data=message.to_json(),
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise A2AProtocolError(f"Message send failed: {response.status}")
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise A2AProtocolError(f"Message transmission error: {e}")
    
    async def handle_message(self, message: A2AMessage) -> Dict[str, Any]:
        """Handle incoming A2A message"""
        try:
            handler = self.message_handlers.get(message.type)
            if handler:
                return await handler(message)
            else:
                raise A2AProtocolError(f"No handler for message type: {message.type}")
        except Exception as e:
            logger.error(f"Message handling error: {e}")
            return self._create_error_response(message, str(e))
    
    async def _handle_capability_discovery(self, message: A2AMessage) -> Dict[str, Any]:
        """Handle capability discovery request"""
        return {
            "agent_card": asdict(self.agent_card),
            "mcp_tools": {name: asdict(tool) for name, tool in self.mcp_tools.items()},
            "mcp_box_tools": {name: asdict(tool) for name, tool in self.mcp_box.items()}
        }
    
    async def _handle_task_request(self, message: A2AMessage) -> Dict[str, Any]:
        """Handle task request with ALITA self-evolution"""
        task_data = message.content
        task = Task(
            id=str(uuid.uuid4()),
            title=task_data.get("title", "Untitled Task"),
            description=task_data.get("description", ""),
            status=TaskStatus.CREATED,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat(),
            requester_id=message.sender_id
        )
        
        self.active_tasks[task.id] = task
        
        # ALITA: Check if we need to evolve capabilities
        if self.self_evolution_enabled:
            await self._assess_capability_gaps(task)
        
        return {"task_id": task.id, "status": "accepted"}
    
    async def _assess_capability_gaps(self, task: Task):
        """ALITA: Assess if new capabilities are needed for the task"""
        # This would integrate with MCP Brainstorming component
        # For now, we'll implement a basic gap detection
        required_capabilities = self._extract_required_capabilities(task.description)
        
        for capability in required_capabilities:
            if capability not in self.mcp_tools and capability not in self.mcp_box:
                self.capability_gaps.append(capability)
                logger.info(f"Capability gap identified: {capability}")
    
    def _extract_required_capabilities(self, description: str) -> List[str]:
        """Extract required capabilities from task description"""
        # Simple keyword-based extraction (would be enhanced with LLM)
        keywords = {
            "web": ["web", "browser", "scraping", "url", "website"],
            "file": ["file", "document", "pdf", "excel", "csv"],
            "image": ["image", "photo", "picture", "visual", "screenshot"],
            "video": ["video", "youtube", "mp4", "streaming"],
            "data": ["data", "database", "sql", "query", "analysis"],
            "api": ["api", "rest", "http", "request", "endpoint"]
        }
        
        required = []
        description_lower = description.lower()
        
        for capability, terms in keywords.items():
            if any(term in description_lower for term in terms):
                required.append(capability)
        
        return required
    
    async def _handle_task_response(self, message: A2AMessage) -> Dict[str, Any]:
        """Handle task response"""
        return {"status": "received"}
    
    async def _handle_agent_card(self, message: A2AMessage) -> Dict[str, Any]:
        """Handle agent card exchange"""
        return {"status": "received"}
    
    async def _handle_status_update(self, message: A2AMessage) -> Dict[str, Any]:
        """Handle status update"""
        return {"status": "received"}
    
    async def _handle_error(self, message: A2AMessage) -> Dict[str, Any]:
        """Handle error message"""
        logger.error(f"Received error from {message.sender_id}: {message.content}")
        return {"status": "error_acknowledged"}
    
    def _create_error_response(self, original_message: A2AMessage, error_msg: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            "error": True,
            "message": error_msg,
            "original_message_id": original_message.id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def create_task(self, title: str, description: str, target_agent_id: str) -> str:
        """Create and send a task to another agent"""
        task_id = str(uuid.uuid4())
        message = A2AMessage(
            id=str(uuid.uuid4()),
            type=MessageType.TASK_REQUEST,
            sender_id=self.agent_id,
            receiver_id=target_agent_id,
            timestamp=datetime.now().isoformat(),
            content={
                "task_id": task_id,
                "title": title,
                "description": description
            }
        )
        
        # This would send to the target agent's endpoint
        # For now, we'll just log the task creation
        logger.info(f"Task created: {task_id} for agent {target_agent_id}")
        return task_id
    
    def get_protocol_status(self) -> Dict[str, Any]:
        """Get current protocol layer status"""
        return {
            "agent_id": self.agent_id,
            "agent_name": self.agent_name,
            "mcp_tools_count": len(self.mcp_tools),
            "mcp_box_size": len(self.mcp_box),
            "active_tasks": len(self.active_tasks),
            "capability_gaps": len(self.capability_gaps),
            "self_evolution_enabled": self.self_evolution_enabled,
            "status": "active"
        }
