"""
DGM Docker Utilities
Docker integration utilities for the Darwin Gödel Machine implementation
Based on the real DGM repository implementation
"""

import os
import logging
import docker
import tempfile
import shutil
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime


def setup_logger(log_file: str = './dgm_docker.log', level: int = logging.INFO) -> logging.Logger:
    """Set up a logger for Docker operations"""
    logger = logging.getLogger(f'DGMDocker-{os.getpid()}')
    logger.setLevel(level)
    
    # Remove existing handlers to avoid duplicates
    logger.handlers = []
    
    # Create formatters
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Create and set up file handler
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    
    return logger


def build_dgm_container(dockerfile_path: str = None, image_name: str = "dgm", logger=None) -> bool:
    """
    Build the DGM Docker container
    Based on the real DGM repository implementation
    """
    try:
        client = docker.from_env()
        
        # Use default Dockerfile if not provided
        if dockerfile_path is None:
            dockerfile_content = """
FROM python:3.10-slim

# Install system-level dependencies, including git
RUN apt-get update && apt-get install -y \\
    build-essential \\
    git \\
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Set the working directory inside the container
WORKDIR /dgm

# Copy the entire repository into the container
COPY . .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Keep the container running by default
CMD ["tail", "-f", "/dev/null"]
"""
            # Create temporary Dockerfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.dockerfile', delete=False) as f:
                f.write(dockerfile_content)
                dockerfile_path = f.name
        
        # Build the image
        if logger:
            logger.info(f"Building Docker image: {image_name}")
        
        # Get the directory containing the Dockerfile
        build_context = os.path.dirname(dockerfile_path) if dockerfile_path else "."
        
        image, build_logs = client.images.build(
            path=build_context,
            dockerfile=dockerfile_path,
            tag=image_name,
            rm=True,
            forcerm=True
        )
        
        if logger:
            logger.info(f"Successfully built Docker image: {image_name}")
            for log in build_logs:
                if 'stream' in log:
                    logger.debug(log['stream'].strip())
        
        return True
        
    except Exception as e:
        if logger:
            logger.error(f"Failed to build Docker image: {e}")
        return False
    finally:
        # Clean up temporary Dockerfile if created
        if dockerfile_path and dockerfile_path.endswith('.dockerfile'):
            try:
                os.unlink(dockerfile_path)
            except Exception:
                pass


def create_dgm_container(image_name: str = "dgm", container_name: str = None, 
                        volumes: Dict[str, str] = None, environment: Dict[str, str] = None,
                        logger=None) -> Optional[docker.models.containers.Container]:
    """Create and start a DGM container"""
    try:
        client = docker.from_env()
        
        if container_name is None:
            container_name = f"dgm-container-{int(time.time())}"
        
        # Default volumes
        if volumes is None:
            volumes = {}
        
        # Default environment variables
        if environment is None:
            environment = {}
        
        if logger:
            logger.info(f"Creating container: {container_name}")
        
        container = client.containers.run(
            image_name,
            name=container_name,
            volumes=volumes,
            environment=environment,
            detach=True,
            remove=False,
            working_dir="/dgm"
        )
        
        if logger:
            logger.info(f"Successfully created container: {container_name}")
        
        return container
        
    except Exception as e:
        if logger:
            logger.error(f"Failed to create container: {e}")
        return None


def copy_to_container(container, src_path: str, dst_path: str, logger=None) -> bool:
    """Copy files/directories to a container"""
    try:
        if logger:
            logger.info(f"Copying {src_path} to container:{dst_path}")
        
        # Create a tar archive of the source
        import tarfile
        import io
        
        tar_stream = io.BytesIO()
        with tarfile.open(fileobj=tar_stream, mode='w') as tar:
            if os.path.isfile(src_path):
                tar.add(src_path, arcname=os.path.basename(src_path))
            else:
                tar.add(src_path, arcname='.')
        
        tar_stream.seek(0)
        
        # Copy to container
        container.put_archive(dst_path, tar_stream)
        
        if logger:
            logger.info(f"Successfully copied to container")
        
        return True
        
    except Exception as e:
        if logger:
            logger.error(f"Failed to copy to container: {e}")
        return False


def copy_from_container(container, src_path: str, dst_path: str, logger=None) -> bool:
    """Copy files/directories from a container"""
    try:
        if logger:
            logger.info(f"Copying container:{src_path} to {dst_path}")
        
        # Get tar archive from container
        tar_stream, _ = container.get_archive(src_path)
        
        # Extract to destination
        import tarfile
        import io
        
        tar_data = b''.join(tar_stream)
        tar_file = tarfile.open(fileobj=io.BytesIO(tar_data))
        
        os.makedirs(os.path.dirname(dst_path), exist_ok=True)
        tar_file.extractall(path=os.path.dirname(dst_path))
        tar_file.close()
        
        if logger:
            logger.info(f"Successfully copied from container")
        
        return True
        
    except Exception as e:
        if logger:
            logger.error(f"Failed to copy from container: {e}")
        return False


def execute_in_container(container, command: List[str], workdir: str = "/dgm", 
                        environment: Dict[str, str] = None, logger=None) -> Tuple[int, str]:
    """Execute a command in a container and return exit code and output"""
    try:
        if logger:
            logger.info(f"Executing in container: {' '.join(command)}")
        
        exec_result = container.exec_run(
            command,
            workdir=workdir,
            environment=environment or {},
            stdout=True,
            stderr=True
        )
        
        exit_code = exec_result.exit_code
        output = exec_result.output.decode('utf-8') if exec_result.output else ""
        
        if logger:
            logger.info(f"Command exit code: {exit_code}")
            if output:
                logger.debug(f"Command output: {output}")
        
        return exit_code, output
        
    except Exception as e:
        if logger:
            logger.error(f"Failed to execute command in container: {e}")
        return -1, str(e)


def log_container_output(exec_result, logger=None) -> None:
    """Log the output from a container execution result"""
    try:
        if hasattr(exec_result, 'output') and exec_result.output:
            output = exec_result.output.decode('utf-8') if isinstance(exec_result.output, bytes) else str(exec_result.output)
            if logger:
                logger.info(f"Container output: {output}")
            else:
                print(f"Container output: {output}")
        
        if hasattr(exec_result, 'exit_code'):
            if logger:
                logger.info(f"Container exit code: {exec_result.exit_code}")
            else:
                print(f"Container exit code: {exec_result.exit_code}")
                
    except Exception as e:
        if logger:
            logger.error(f"Failed to log container output: {e}")


def cleanup_container(container, logger=None) -> bool:
    """Stop and remove a container"""
    try:
        if logger:
            logger.info(f"Cleaning up container: {container.name}")
        
        # Stop the container
        container.stop(timeout=10)
        
        # Remove the container
        container.remove()
        
        if logger:
            logger.info(f"Successfully cleaned up container")
        
        return True
        
    except Exception as e:
        if logger:
            logger.error(f"Failed to cleanup container: {e}")
        return False


def setup_dgm_workspace(container, workspace_path: str = "/dgm", logger=None) -> bool:
    """Set up the DGM workspace in a container"""
    try:
        if logger:
            logger.info(f"Setting up DGM workspace in container")
        
        # Create workspace directory
        exit_code, output = execute_in_container(
            container, 
            ["mkdir", "-p", workspace_path],
            logger=logger
        )
        
        if exit_code != 0:
            if logger:
                logger.error(f"Failed to create workspace directory: {output}")
            return False
        
        # Initialize git repository
        exit_code, output = execute_in_container(
            container,
            ["git", "init"],
            workdir=workspace_path,
            logger=logger
        )
        
        if exit_code != 0:
            if logger:
                logger.error(f"Failed to initialize git repository: {output}")
            return False
        
        # Configure git
        execute_in_container(
            container,
            ["git", "config", "user.name", "DGM Agent"],
            workdir=workspace_path,
            logger=logger
        )
        
        execute_in_container(
            container,
            ["git", "config", "user.email", "<EMAIL>"],
            workdir=workspace_path,
            logger=logger
        )
        
        if logger:
            logger.info(f"Successfully set up DGM workspace")
        
        return True
        
    except Exception as e:
        if logger:
            logger.error(f"Failed to setup DGM workspace: {e}")
        return False


def get_container_logs(container, logger=None) -> str:
    """Get logs from a container"""
    try:
        logs = container.logs(stdout=True, stderr=True, timestamps=True)
        return logs.decode('utf-8') if isinstance(logs, bytes) else str(logs)
        
    except Exception as e:
        if logger:
            logger.error(f"Failed to get container logs: {e}")
        return ""


def is_container_running(container_name: str, logger=None) -> bool:
    """Check if a container is running"""
    try:
        client = docker.from_env()
        container = client.containers.get(container_name)
        return container.status == 'running'
        
    except docker.errors.NotFound:
        return False
    except Exception as e:
        if logger:
            logger.error(f"Failed to check container status: {e}")
        return False


def wait_for_container_ready(container, timeout: int = 60, logger=None) -> bool:
    """Wait for a container to be ready"""
    try:
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Check if container is running
            container.reload()
            if container.status == 'running':
                # Test if we can execute commands
                exit_code, _ = execute_in_container(
                    container,
                    ["echo", "ready"],
                    logger=logger
                )
                if exit_code == 0:
                    if logger:
                        logger.info(f"Container is ready")
                    return True
            
            time.sleep(1)
        
        if logger:
            logger.error(f"Container not ready after {timeout} seconds")
        return False
        
    except Exception as e:
        if logger:
            logger.error(f"Failed to wait for container: {e}")
        return False
