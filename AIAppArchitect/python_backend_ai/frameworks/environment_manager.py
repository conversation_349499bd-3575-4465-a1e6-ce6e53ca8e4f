"""
Environment Management System for ALITA MCP Evolution
Provides isolated execution environments for generated MCPs with:
- Conda environment creation and management
- Dependency isolation
- Automated recovery procedures
- Security sandboxing
"""

import os
import json
import uuid
import asyncio
import logging
import subprocess
import tempfile
import shutil
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import yaml

logger = logging.getLogger(__name__)

@dataclass
class Environment:
    """Represents an isolated execution environment"""
    id: str
    name: str
    python_version: str
    dependencies: List[str]
    status: str  # "creating", "active", "error", "destroyed"
    created_at: str
    last_used: str
    path: str
    conda_env_name: str
    metadata: Dict[str, Any] = None

@dataclass
class ExecutionProfile:
    """Execution profile for MCP tools"""
    id: str
    name: str
    environment_id: str
    resource_limits: Dict[str, Any]
    security_settings: Dict[str, Any]
    allowed_operations: List[str]
    created_at: str

class EnvironmentManager:
    """
    Manages isolated execution environments for ALITA MCP tools
    """
    
    def __init__(self, base_path: Optional[str] = None):
        self.base_path = Path(base_path) if base_path else Path(tempfile.gettempdir()) / "alita_environments"
        self.base_path.mkdir(exist_ok=True)
        
        self.environments: Dict[str, Environment] = {}
        self.execution_profiles: Dict[str, ExecutionProfile] = {}
        self.conda_available = self._check_conda_availability()
        
        # Default resource limits
        self.default_limits = {
            "max_memory_mb": 512,
            "max_cpu_percent": 50,
            "max_execution_time": 300,  # 5 minutes
            "max_file_size_mb": 100,
            "max_network_requests": 50
        }
        
        # Security settings
        self.security_settings = {
            "allow_file_system_access": True,
            "allow_network_access": True,
            "allow_subprocess": False,
            "allow_import_restrictions": True,
            "sandbox_mode": True
        }
    
    def _check_conda_availability(self) -> bool:
        """Check if conda is available"""
        try:
            result = subprocess.run(
                ["conda", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            available = result.returncode == 0
            logger.info(f"Conda availability: {available}")
            return available
        except Exception as e:
            logger.warning(f"Conda not available: {e}")
            return False
    
    async def create_environment(self, 
                                name: str, 
                                python_version: str = "3.9",
                                dependencies: List[str] = None) -> Environment:
        """Create a new isolated environment"""
        logger.info(f"Creating environment: {name}")
        
        env_id = str(uuid.uuid4())
        conda_env_name = f"alita_{name}_{env_id[:8]}"
        env_path = self.base_path / env_id
        env_path.mkdir(exist_ok=True)
        
        environment = Environment(
            id=env_id,
            name=name,
            python_version=python_version,
            dependencies=dependencies or [],
            status="creating",
            created_at=datetime.now().isoformat(),
            last_used=datetime.now().isoformat(),
            path=str(env_path),
            conda_env_name=conda_env_name,
            metadata={}
        )
        
        self.environments[env_id] = environment
        
        try:
            if self.conda_available:
                await self._create_conda_environment(environment)
            else:
                await self._create_venv_environment(environment)
            
            environment.status = "active"
            logger.info(f"Environment created successfully: {name}")
            
        except Exception as e:
            environment.status = "error"
            environment.metadata = {"error": str(e)}
            logger.error(f"Failed to create environment {name}: {e}")
            raise
        
        return environment
    
    async def _create_conda_environment(self, environment: Environment):
        """Create conda environment"""
        logger.info(f"Creating conda environment: {environment.conda_env_name}")
        
        # Create conda environment
        create_cmd = [
            "conda", "create", "-n", environment.conda_env_name,
            f"python={environment.python_version}", "-y"
        ]
        
        result = await self._run_command(create_cmd, timeout=300)
        if result.returncode != 0:
            raise Exception(f"Conda environment creation failed: {result.stderr}")
        
        # Install dependencies
        if environment.dependencies:
            await self._install_conda_dependencies(environment)
        
        # Create environment.yml for reproducibility
        await self._export_conda_environment(environment)
    
    async def _create_venv_environment(self, environment: Environment):
        """Create virtual environment using venv"""
        logger.info(f"Creating venv environment: {environment.name}")
        
        venv_path = Path(environment.path) / "venv"
        
        # Create virtual environment
        create_cmd = [
            "python", "-m", "venv", str(venv_path)
        ]
        
        result = await self._run_command(create_cmd, timeout=120)
        if result.returncode != 0:
            raise Exception(f"Venv creation failed: {result.stderr}")
        
        # Install dependencies
        if environment.dependencies:
            await self._install_pip_dependencies(environment, venv_path)
        
        environment.metadata["venv_path"] = str(venv_path)
    
    async def _install_conda_dependencies(self, environment: Environment):
        """Install dependencies in conda environment"""
        if not environment.dependencies:
            return
        
        logger.info(f"Installing conda dependencies: {environment.dependencies}")
        
        # Try conda install first, fallback to pip
        for dep in environment.dependencies:
            conda_cmd = [
                "conda", "install", "-n", environment.conda_env_name,
                "-c", "conda-forge", dep, "-y"
            ]
            
            result = await self._run_command(conda_cmd, timeout=180)
            
            if result.returncode != 0:
                # Fallback to pip
                pip_cmd = [
                    "conda", "run", "-n", environment.conda_env_name,
                    "pip", "install", dep
                ]
                
                pip_result = await self._run_command(pip_cmd, timeout=180)
                if pip_result.returncode != 0:
                    logger.warning(f"Failed to install {dep}: {pip_result.stderr}")
    
    async def _install_pip_dependencies(self, environment: Environment, venv_path: Path):
        """Install dependencies using pip in virtual environment"""
        if not environment.dependencies:
            return
        
        logger.info(f"Installing pip dependencies: {environment.dependencies}")
        
        # Determine pip executable path
        if os.name == 'nt':  # Windows
            pip_exe = venv_path / "Scripts" / "pip.exe"
        else:  # Unix-like
            pip_exe = venv_path / "bin" / "pip"
        
        for dep in environment.dependencies:
            pip_cmd = [str(pip_exe), "install", dep]
            
            result = await self._run_command(pip_cmd, timeout=180)
            if result.returncode != 0:
                logger.warning(f"Failed to install {dep}: {result.stderr}")
    
    async def _export_conda_environment(self, environment: Environment):
        """Export conda environment to environment.yml"""
        export_cmd = [
            "conda", "env", "export", "-n", environment.conda_env_name
        ]
        
        result = await self._run_command(export_cmd, timeout=60)
        if result.returncode == 0:
            env_file = Path(environment.path) / "environment.yml"
            with open(env_file, 'w') as f:
                f.write(result.stdout)
    
    async def _run_command(self, cmd: List[str], timeout: int = 60) -> subprocess.CompletedProcess:
        """Run command asynchronously"""
        logger.debug(f"Running command: {' '.join(cmd)}")
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        try:
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=timeout
            )
            
            return subprocess.CompletedProcess(
                args=cmd,
                returncode=process.returncode,
                stdout=stdout.decode(),
                stderr=stderr.decode()
            )
        except asyncio.TimeoutError:
            process.kill()
            await process.wait()
            raise Exception(f"Command timeout: {' '.join(cmd)}")
    
    async def execute_in_environment(self, 
                                   environment_id: str, 
                                   script_path: str,
                                   profile_id: Optional[str] = None) -> Dict[str, Any]:
        """Execute script in isolated environment"""
        environment = self.environments.get(environment_id)
        if not environment:
            raise ValueError(f"Environment not found: {environment_id}")
        
        if environment.status != "active":
            raise ValueError(f"Environment not active: {environment.status}")
        
        profile = None
        if profile_id:
            profile = self.execution_profiles.get(profile_id)
        
        logger.info(f"Executing script in environment: {environment.name}")
        
        try:
            # Update last used
            environment.last_used = datetime.now().isoformat()
            
            # Execute based on environment type
            if self.conda_available and not environment.metadata.get("venv_path"):
                result = await self._execute_conda(environment, script_path, profile)
            else:
                result = await self._execute_venv(environment, script_path, profile)
            
            return result
            
        except Exception as e:
            logger.error(f"Execution failed in environment {environment_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "environment_id": environment_id
            }
    
    async def _execute_conda(self, 
                           environment: Environment, 
                           script_path: str,
                           profile: Optional[ExecutionProfile]) -> Dict[str, Any]:
        """Execute script in conda environment"""
        cmd = [
            "conda", "run", "-n", environment.conda_env_name,
            "python", script_path
        ]
        
        # Apply resource limits if profile exists
        timeout = self.default_limits["max_execution_time"]
        if profile:
            timeout = profile.resource_limits.get("max_execution_time", timeout)
        
        result = await self._run_command(cmd, timeout=timeout)
        
        return {
            "success": result.returncode == 0,
            "returncode": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "environment_id": environment.id,
            "execution_time": datetime.now().isoformat()
        }
    
    async def _execute_venv(self, 
                          environment: Environment, 
                          script_path: str,
                          profile: Optional[ExecutionProfile]) -> Dict[str, Any]:
        """Execute script in virtual environment"""
        venv_path = Path(environment.metadata["venv_path"])
        
        # Determine python executable
        if os.name == 'nt':  # Windows
            python_exe = venv_path / "Scripts" / "python.exe"
        else:  # Unix-like
            python_exe = venv_path / "bin" / "python"
        
        cmd = [str(python_exe), script_path]
        
        # Apply resource limits if profile exists
        timeout = self.default_limits["max_execution_time"]
        if profile:
            timeout = profile.resource_limits.get("max_execution_time", timeout)
        
        result = await self._run_command(cmd, timeout=timeout)
        
        return {
            "success": result.returncode == 0,
            "returncode": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "environment_id": environment.id,
            "execution_time": datetime.now().isoformat()
        }
    
    def create_execution_profile(self, 
                               name: str,
                               environment_id: str,
                               resource_limits: Dict[str, Any] = None,
                               security_settings: Dict[str, Any] = None,
                               allowed_operations: List[str] = None) -> ExecutionProfile:
        """Create execution profile for controlled execution"""
        profile_id = str(uuid.uuid4())
        
        profile = ExecutionProfile(
            id=profile_id,
            name=name,
            environment_id=environment_id,
            resource_limits=resource_limits or self.default_limits.copy(),
            security_settings=security_settings or self.security_settings.copy(),
            allowed_operations=allowed_operations or ["read", "write", "execute"],
            created_at=datetime.now().isoformat()
        )
        
        self.execution_profiles[profile_id] = profile
        logger.info(f"Created execution profile: {name}")
        
        return profile
    
    async def destroy_environment(self, environment_id: str) -> bool:
        """Destroy environment and cleanup resources"""
        environment = self.environments.get(environment_id)
        if not environment:
            return False
        
        logger.info(f"Destroying environment: {environment.name}")
        
        try:
            # Remove conda environment
            if self.conda_available and not environment.metadata.get("venv_path"):
                remove_cmd = [
                    "conda", "env", "remove", "-n", environment.conda_env_name, "-y"
                ]
                await self._run_command(remove_cmd, timeout=120)
            
            # Remove directory
            env_path = Path(environment.path)
            if env_path.exists():
                shutil.rmtree(env_path)
            
            # Update status
            environment.status = "destroyed"
            
            # Remove from active environments
            del self.environments[environment_id]
            
            logger.info(f"Environment destroyed: {environment.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to destroy environment {environment_id}: {e}")
            environment.status = "error"
            environment.metadata = environment.metadata or {}
            environment.metadata["destroy_error"] = str(e)
            return False
    
    async def cleanup_inactive_environments(self, max_age_hours: int = 24):
        """Cleanup environments that haven't been used recently"""
        current_time = datetime.now()
        
        for env_id, environment in list(self.environments.items()):
            last_used = datetime.fromisoformat(environment.last_used)
            age_hours = (current_time - last_used).total_seconds() / 3600
            
            if age_hours > max_age_hours:
                logger.info(f"Cleaning up inactive environment: {environment.name}")
                await self.destroy_environment(env_id)
    
    def get_environment_status(self, environment_id: str) -> Dict[str, Any]:
        """Get environment status and metrics"""
        environment = self.environments.get(environment_id)
        if not environment:
            return {"error": "Environment not found"}
        
        return {
            "id": environment.id,
            "name": environment.name,
            "status": environment.status,
            "python_version": environment.python_version,
            "dependencies": environment.dependencies,
            "created_at": environment.created_at,
            "last_used": environment.last_used,
            "path": environment.path,
            "conda_env_name": environment.conda_env_name,
            "metadata": environment.metadata
        }
    
    def list_environments(self) -> List[Dict[str, Any]]:
        """List all environments"""
        return [
            {
                "id": env.id,
                "name": env.name,
                "status": env.status,
                "created_at": env.created_at,
                "last_used": env.last_used
            }
            for env in self.environments.values()
        ]
    
    def get_manager_status(self) -> Dict[str, Any]:
        """Get environment manager status"""
        return {
            "total_environments": len(self.environments),
            "active_environments": len([e for e in self.environments.values() if e.status == "active"]),
            "execution_profiles": len(self.execution_profiles),
            "conda_available": self.conda_available,
            "base_path": str(self.base_path),
            "default_limits": self.default_limits,
            "security_settings": self.security_settings
        }
