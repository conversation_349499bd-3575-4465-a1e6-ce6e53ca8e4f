"""
BabyAGI Engine - Production Implementation
Self-building autonomous agent based on the original BabyAGI framework
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import structlog
from datetime import datetime
import sqlite3
import inspect
import importlib.util

logger = structlog.get_logger(__name__)


@dataclass
class FunctionMetadata:
    """Metadata for BabyAGI functions"""
    name: str
    description: str
    imports: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    key_dependencies: List[str] = field(default_factory=list)
    code: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    version: int = 1


@dataclass
class ExecutionLog:
    """Log entry for function execution"""
    function_name: str
    execution_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    inputs: Dict[str, Any] = field(default_factory=dict)
    outputs: Any = None
    error: Optional[str] = None
    execution_time: Optional[float] = None


class BabyAGIEngine:
    """
    Production BabyAGI Engine - Self-building autonomous agent framework
    Based on the original BabyAGI by <PERSON><PERSON><PERSON>
    """
    
    def __init__(self, db_path: str = ":memory:"):
        self.db_path = db_path
        self.functions: Dict[str, FunctionMetadata] = {}
        self.key_store: Dict[str, str] = {}
        self.execution_logs: List[ExecutionLog] = []
        self.triggers: Dict[str, List[Callable]] = {}
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the BabyAGI engine"""
        if self._initialized:
            return
        
        logger.info("Initializing BabyAGI Engine...")
        
        try:
            # Initialize database
            await self._init_database()
            
            # Load default functions
            await self._load_default_functions()
            
            self._initialized = True
            logger.info("BabyAGI Engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize BabyAGI Engine", error=str(e))
            raise

    async def _init_database(self) -> None:
        """Initialize SQLite database for function storage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create functions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS functions (
                name TEXT PRIMARY KEY,
                description TEXT,
                imports TEXT,
                dependencies TEXT,
                key_dependencies TEXT,
                code TEXT,
                created_at TEXT,
                version INTEGER
            )
        """)
        
        # Create execution logs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS execution_logs (
                id TEXT PRIMARY KEY,
                function_name TEXT,
                start_time TEXT,
                end_time TEXT,
                inputs TEXT,
                outputs TEXT,
                error TEXT,
                execution_time REAL
            )
        """)
        
        # Create key store table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS key_store (
                key_name TEXT PRIMARY KEY,
                key_value TEXT
            )
        """)
        
        conn.commit()
        conn.close()

    async def _load_default_functions(self) -> None:
        """Load default BabyAGI functions"""
        
        # Function execution function
        await self.register_function(
            name="run_function",
            description="Execute a registered function with given arguments",
            code="""
async def run_function(function_name: str, *args, **kwargs):
    if function_name not in engine.functions:
        raise ValueError(f"Function '{function_name}' not found")
    
    func_meta = engine.functions[function_name]
    
    # Load dependencies
    for dep in func_meta.dependencies:
        if dep not in globals():
            await run_function(dep)
    
    # Execute function
    exec(func_meta.code, globals())
    func = globals()[function_name]
    
    return await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
""",
            dependencies=[]
        )
        
        # Function registration function
        await self.register_function(
            name="add_function",
            description="Add a new function to the BabyAGI system",
            code="""
async def add_function(name: str, description: str, code: str, dependencies: List[str] = None, imports: List[str] = None):
    await engine.register_function(
        name=name,
        description=description,
        code=code,
        dependencies=dependencies or [],
        imports=imports or []
    )
    return f"Function '{name}' added successfully"
""",
            dependencies=[]
        )
        
        # Key management function
        await self.register_function(
            name="add_key_wrapper",
            description="Add a secret key to the key store",
            code="""
def add_key_wrapper(key_name: str, key_value: str):
    engine.key_store[key_name] = key_value
    return f"Key '{key_name}' added successfully"
""",
            dependencies=[]
        )
        
        # AI description generator
        await self.register_function(
            name="generate_function_description",
            description="Generate description for a function using AI",
            code="""
async def generate_function_description(function_name: str):
    if 'openai_api_key' not in engine.key_store:
        return "OpenAI API key not found"
    
    import openai
    openai.api_key = engine.key_store['openai_api_key']
    
    func_meta = engine.functions.get(function_name)
    if not func_meta:
        return f"Function '{function_name}' not found"
    
    prompt = f"Generate a clear description for this Python function:\\n\\n{func_meta.code}"
    
    try:
        response = await openai.ChatCompletion.acreate(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=150
        )
        
        description = response.choices[0].message.content.strip()
        func_meta.description = description
        
        return description
    except Exception as e:
        return f"Error generating description: {str(e)}"
""",
            dependencies=["add_key_wrapper"],
            key_dependencies=["openai_api_key"]
        )
        
        # Task processing function
        await self.register_function(
            name="process_user_input",
            description="Process user input and determine whether to use existing functions or create new ones",
            code="""
async def process_user_input(user_input: str):
    if 'openai_api_key' not in engine.key_store:
        return "OpenAI API key not found"
    
    import openai
    openai.api_key = engine.key_store['openai_api_key']
    
    # Analyze if existing functions can handle the request
    existing_functions = list(engine.functions.keys())
    
    analysis_prompt = f'''
    User request: {user_input}
    
    Available functions: {existing_functions}
    
    Can this request be handled by existing functions? If not, what new functions need to be created?
    Respond with JSON: {{"can_handle": true/false, "existing_functions": [...], "new_functions": [...]}}
    '''
    
    try:
        response = await openai.ChatCompletion.acreate(
            model="gpt-4",
            messages=[{"role": "user", "content": analysis_prompt}],
            max_tokens=500
        )
        
        analysis = json.loads(response.choices[0].message.content.strip())
        
        if analysis["can_handle"]:
            # Use existing functions
            result = "Using existing functions: "
            for func_name in analysis["existing_functions"]:
                result += await run_function(func_name)
            return result
        else:
            # Create new functions
            for func_spec in analysis["new_functions"]:
                await create_function_from_spec(func_spec, user_input)
            return f"Created {len(analysis['new_functions'])} new functions"
            
    except Exception as e:
        return f"Error processing input: {str(e)}"
""",
            dependencies=["run_function", "add_function"],
            key_dependencies=["openai_api_key"]
        )

    async def register_function(
        self, 
        name: str, 
        description: str, 
        code: str,
        imports: List[str] = None,
        dependencies: List[str] = None,
        key_dependencies: List[str] = None
    ) -> None:
        """Register a new function in the BabyAGI system"""
        
        func_meta = FunctionMetadata(
            name=name,
            description=description,
            code=code,
            imports=imports or [],
            dependencies=dependencies or [],
            key_dependencies=key_dependencies or []
        )
        
        self.functions[name] = func_meta
        
        # Store in database
        await self._store_function_in_db(func_meta)
        
        # Execute any triggers
        await self._execute_triggers("function_added", func_meta)
        
        logger.info("Function registered", name=name)

    async def _store_function_in_db(self, func_meta: FunctionMetadata) -> None:
        """Store function metadata in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO functions 
            (name, description, imports, dependencies, key_dependencies, code, created_at, version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            func_meta.name,
            func_meta.description,
            json.dumps(func_meta.imports),
            json.dumps(func_meta.dependencies),
            json.dumps(func_meta.key_dependencies),
            func_meta.code,
            func_meta.created_at.isoformat(),
            func_meta.version
        ))
        
        conn.commit()
        conn.close()

    async def execute_function(self, function_name: str, *args, **kwargs) -> Any:
        """Execute a registered function with logging"""
        if function_name not in self.functions:
            raise ValueError(f"Function '{function_name}' not found")
        
        execution_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        log_entry = ExecutionLog(
            function_name=function_name,
            execution_id=execution_id,
            start_time=start_time,
            inputs={"args": args, "kwargs": kwargs}
        )
        
        try:
            func_meta = self.functions[function_name]
            
            # Load dependencies
            for dep in func_meta.dependencies:
                if dep in self.functions:
                    exec(self.functions[dep].code, globals())
            
            # Load imports
            for imp in func_meta.imports:
                exec(f"import {imp}", globals())
            
            # Execute function
            globals()['engine'] = self  # Make engine available to functions
            exec(func_meta.code, globals())
            func = globals()[function_name]
            
            # Execute with proper async handling
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Log successful execution
            end_time = datetime.now()
            log_entry.end_time = end_time
            log_entry.outputs = result
            log_entry.execution_time = (end_time - start_time).total_seconds()
            
            self.execution_logs.append(log_entry)
            
            logger.info("Function executed successfully", 
                       function=function_name, 
                       execution_time=log_entry.execution_time)
            
            return result
            
        except Exception as e:
            # Log failed execution
            end_time = datetime.now()
            log_entry.end_time = end_time
            log_entry.error = str(e)
            log_entry.execution_time = (end_time - start_time).total_seconds()
            
            self.execution_logs.append(log_entry)
            
            logger.error("Function execution failed", 
                        function=function_name, 
                        error=str(e))
            
            raise

    async def self_build(self, persona: str, num_tasks: int = 3) -> Dict[str, Any]:
        """Self-building functionality - generate tasks and create functions"""
        if 'openai_api_key' not in self.key_store:
            return {"error": "OpenAI API key not found"}
        
        try:
            import openai
            openai.api_key = self.key_store['openai_api_key']
            
            # Generate tasks for the persona
            prompt = f"""
            Generate {num_tasks} distinct tasks that a {persona} might ask an AI assistant to help with.
            Return as JSON array: ["task1", "task2", "task3"]
            """
            
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=300
            )
            
            tasks = json.loads(response.choices[0].message.content.strip())
            
            results = []
            for task in tasks:
                result = await self.execute_function("process_user_input", task)
                results.append({"task": task, "result": result})
            
            return {
                "persona": persona,
                "tasks_generated": len(tasks),
                "results": results
            }
            
        except Exception as e:
            logger.error("Self-build failed", error=str(e))
            return {"error": str(e)}

    async def add_trigger(self, event: str, function_name: str) -> None:
        """Add a trigger to execute a function on specific events"""
        if event not in self.triggers:
            self.triggers[event] = []
        
        self.triggers[event].append(function_name)
        logger.info("Trigger added", event=event, function=function_name)

    async def _execute_triggers(self, event: str, context: Any = None) -> None:
        """Execute all triggers for a specific event"""
        if event in self.triggers:
            for function_name in self.triggers[event]:
                try:
                    await self.execute_function(function_name, context)
                except Exception as e:
                    logger.error("Trigger execution failed", 
                                event=event, 
                                function=function_name, 
                                error=str(e))

    async def get_execution_logs(self, function_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get execution logs, optionally filtered by function name"""
        logs = self.execution_logs
        
        if function_name:
            logs = [log for log in logs if log.function_name == function_name]
        
        return [
            {
                "function_name": log.function_name,
                "execution_id": log.execution_id,
                "start_time": log.start_time.isoformat(),
                "end_time": log.end_time.isoformat() if log.end_time else None,
                "execution_time": log.execution_time,
                "inputs": log.inputs,
                "outputs": str(log.outputs)[:200] if log.outputs else None,
                "error": log.error
            }
            for log in logs
        ]

    async def list_functions(self) -> List[Dict[str, Any]]:
        """List all registered functions"""
        return [
            {
                "name": func.name,
                "description": func.description,
                "dependencies": func.dependencies,
                "imports": func.imports,
                "key_dependencies": func.key_dependencies,
                "created_at": func.created_at.isoformat(),
                "version": func.version
            }
            for func in self.functions.values()
        ]

    async def shutdown(self) -> None:
        """Shutdown the BabyAGI engine"""
        logger.info("Shutting down BabyAGI Engine...")
        
        # Save any pending data
        for func_meta in self.functions.values():
            await self._store_function_in_db(func_meta)
        
        self.functions.clear()
        self.key_store.clear()
        self.execution_logs.clear()
        self.triggers.clear()
        self._initialized = False
        
        logger.info("BabyAGI Engine shutdown complete")
