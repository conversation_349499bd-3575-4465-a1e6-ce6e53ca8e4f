"""
DGM Evaluation Utilities
Evaluation utilities for the Darwin Gödel Machine implementation
Based on the real DGM repository implementation
"""

import os
import json
import logging
import re
import subprocess
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime


def get_report_score(report: str) -> float:
    """
    Extract score from a test report
    Based on the real DGM repository implementation
    """
    try:
        # Simple heuristic - look for percentage or score patterns
        
        # Look for patterns like "85%" or "0.85" or "85/100"
        percentage_match = re.search(r'(\d+(?:\.\d+)?)%', report)
        if percentage_match:
            return float(percentage_match.group(1)) / 100.0
        
        decimal_match = re.search(r'score[:\s]+(\d+(?:\.\d+)?)', report, re.IGNORECASE)
        if decimal_match:
            score = float(decimal_match.group(1))
            return score if score <= 1.0 else score / 100.0
        
        fraction_match = re.search(r'(\d+)/(\d+)', report)
        if fraction_match:
            numerator = float(fraction_match.group(1))
            denominator = float(fraction_match.group(2))
            return numerator / denominator if denominator > 0 else 0.0
        
        # Look for "passed X out of Y" patterns
        passed_match = re.search(r'passed\s+(\d+)\s+out\s+of\s+(\d+)', report, re.IGNORECASE)
        if passed_match:
            passed = float(passed_match.group(1))
            total = float(passed_match.group(2))
            return passed / total if total > 0 else 0.0
        
        # Look for "X/Y tests passed" patterns
        tests_match = re.search(r'(\d+)/(\d+)\s+tests?\s+passed', report, re.IGNORECASE)
        if tests_match:
            passed = float(tests_match.group(1))
            total = float(tests_match.group(2))
            return passed / total if total > 0 else 0.0
        
        # Default score if no pattern found
        return 0.5
        
    except Exception as e:
        logging.error(f"Failed to extract score from report: {e}")
        return 0.0


def msg_history_to_report(instance_id: str, msg_history: List[Dict], model: str = "claude") -> str:
    """
    Convert message history to a test report
    Based on the real DGM repository implementation
    """
    try:
        # Extract relevant information from message history
        report_parts = [
            f"Test Report for Instance: {instance_id}",
            f"Model: {model}",
            f"Timestamp: {datetime.now().isoformat()}",
            "=" * 50
        ]
        
        for i, msg in enumerate(msg_history):
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            
            if isinstance(content, list):
                # Handle structured content
                text_content = []
                for item in content:
                    if isinstance(item, dict) and 'text' in item:
                        text_content.append(item['text'])
                content = '\n'.join(text_content)
            
            report_parts.append(f"\n--- Message {i+1} ({role}) ---")
            report_parts.append(str(content)[:500] + "..." if len(str(content)) > 500 else str(content))
        
        return '\n'.join(report_parts)
        
    except Exception as e:
        logging.error(f"Failed to convert message history to report: {e}")
        return f"Error generating report for {instance_id}: {e}"


def score_tie_breaker(scores: List[float]) -> float:
    """Break ties in scores by returning the average"""
    if not scores:
        return 0.0
    return sum(scores) / len(scores)


def parse_test_results(test_output: str) -> Dict[str, Any]:
    """Parse test output to extract structured results"""
    results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'skipped_tests': 0,
        'error_tests': 0,
        'success_rate': 0.0,
        'details': []
    }
    
    try:
        # Parse pytest output
        if 'pytest' in test_output.lower() or '::' in test_output:
            results.update(parse_pytest_output(test_output))
        
        # Parse unittest output
        elif 'unittest' in test_output.lower() or 'Ran ' in test_output:
            results.update(parse_unittest_output(test_output))
        
        # Parse generic test output
        else:
            results.update(parse_generic_test_output(test_output))
        
        # Calculate success rate
        if results['total_tests'] > 0:
            results['success_rate'] = results['passed_tests'] / results['total_tests']
        
    except Exception as e:
        logging.error(f"Failed to parse test results: {e}")
    
    return results


def parse_pytest_output(output: str) -> Dict[str, Any]:
    """Parse pytest specific output"""
    results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'skipped_tests': 0,
        'error_tests': 0,
        'details': []
    }
    
    try:
        # Look for summary line like "5 passed, 2 failed, 1 skipped"
        summary_match = re.search(
            r'(\d+)\s+passed(?:,\s+(\d+)\s+failed)?(?:,\s+(\d+)\s+skipped)?(?:,\s+(\d+)\s+error)?',
            output
        )
        
        if summary_match:
            results['passed_tests'] = int(summary_match.group(1) or 0)
            results['failed_tests'] = int(summary_match.group(2) or 0)
            results['skipped_tests'] = int(summary_match.group(3) or 0)
            results['error_tests'] = int(summary_match.group(4) or 0)
            
            results['total_tests'] = (
                results['passed_tests'] + results['failed_tests'] + 
                results['skipped_tests'] + results['error_tests']
            )
        
        # Extract individual test results
        test_lines = re.findall(r'(.*?)::(.*?)\s+(PASSED|FAILED|SKIPPED|ERROR)', output)
        for file_path, test_name, status in test_lines:
            results['details'].append({
                'file': file_path.strip(),
                'test': test_name.strip(),
                'status': status.strip()
            })
    
    except Exception as e:
        logging.error(f"Failed to parse pytest output: {e}")
    
    return results


def parse_unittest_output(output: str) -> Dict[str, Any]:
    """Parse unittest specific output"""
    results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'skipped_tests': 0,
        'error_tests': 0,
        'details': []
    }
    
    try:
        # Look for "Ran X tests" line
        ran_match = re.search(r'Ran\s+(\d+)\s+tests?', output)
        if ran_match:
            results['total_tests'] = int(ran_match.group(1))
        
        # Look for result summary
        if 'OK' in output and 'FAILED' not in output:
            results['passed_tests'] = results['total_tests']
        else:
            # Look for failure/error counts
            failure_match = re.search(r'failures=(\d+)', output)
            error_match = re.search(r'errors=(\d+)', output)
            skip_match = re.search(r'skipped=(\d+)', output)
            
            results['failed_tests'] = int(failure_match.group(1)) if failure_match else 0
            results['error_tests'] = int(error_match.group(1)) if error_match else 0
            results['skipped_tests'] = int(skip_match.group(1)) if skip_match else 0
            
            results['passed_tests'] = (
                results['total_tests'] - results['failed_tests'] - 
                results['error_tests'] - results['skipped_tests']
            )
    
    except Exception as e:
        logging.error(f"Failed to parse unittest output: {e}")
    
    return results


def parse_generic_test_output(output: str) -> Dict[str, Any]:
    """Parse generic test output"""
    results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'skipped_tests': 0,
        'error_tests': 0,
        'details': []
    }
    
    try:
        # Count occurrences of common test result indicators
        passed_count = len(re.findall(r'\bpass(?:ed)?\b', output, re.IGNORECASE))
        failed_count = len(re.findall(r'\bfail(?:ed)?\b', output, re.IGNORECASE))
        error_count = len(re.findall(r'\berror\b', output, re.IGNORECASE))
        skip_count = len(re.findall(r'\bskip(?:ped)?\b', output, re.IGNORECASE))
        
        results['passed_tests'] = passed_count
        results['failed_tests'] = failed_count
        results['error_tests'] = error_count
        results['skipped_tests'] = skip_count
        results['total_tests'] = passed_count + failed_count + error_count + skip_count
        
        # If no clear indicators, try to extract from score patterns
        if results['total_tests'] == 0:
            score = get_report_score(output)
            if score > 0:
                # Assume 10 tests for scoring purposes
                results['total_tests'] = 10
                results['passed_tests'] = int(score * 10)
                results['failed_tests'] = 10 - results['passed_tests']
    
    except Exception as e:
        logging.error(f"Failed to parse generic test output: {e}")
    
    return results


def run_evaluation_benchmark(agent_path: str, benchmark_type: str = "swe", 
                           test_cases: List[str] = None, timeout: int = 300) -> Dict[str, Any]:
    """Run evaluation benchmark on an agent"""
    results = {
        'benchmark_type': benchmark_type,
        'total_cases': 0,
        'resolved_cases': 0,
        'unresolved_cases': 0,
        'empty_patch_cases': 0,
        'accuracy_score': 0.0,
        'execution_time': 0.0,
        'details': []
    }
    
    try:
        start_time = datetime.now()
        
        if test_cases is None:
            test_cases = get_default_test_cases(benchmark_type)
        
        results['total_cases'] = len(test_cases)
        
        for case in test_cases:
            case_result = run_single_test_case(agent_path, case, benchmark_type, timeout)
            results['details'].append(case_result)
            
            if case_result['status'] == 'resolved':
                results['resolved_cases'] += 1
            elif case_result['status'] == 'empty_patch':
                results['empty_patch_cases'] += 1
            else:
                results['unresolved_cases'] += 1
        
        # Calculate accuracy
        if results['total_cases'] > 0:
            results['accuracy_score'] = results['resolved_cases'] / results['total_cases']
        
        end_time = datetime.now()
        results['execution_time'] = (end_time - start_time).total_seconds()
        
    except Exception as e:
        logging.error(f"Failed to run evaluation benchmark: {e}")
        results['error'] = str(e)
    
    return results


def run_single_test_case(agent_path: str, test_case: str, benchmark_type: str, timeout: int) -> Dict[str, Any]:
    """Run a single test case"""
    result = {
        'test_case': test_case,
        'status': 'unresolved',
        'score': 0.0,
        'output': '',
        'error': None
    }
    
    try:
        # This would be implemented based on the specific benchmark
        # For now, return a mock result
        result['status'] = 'resolved'
        result['score'] = 0.8
        result['output'] = f"Mock evaluation result for {test_case}"
        
    except Exception as e:
        result['error'] = str(e)
        result['status'] = 'error'
    
    return result


def get_default_test_cases(benchmark_type: str) -> List[str]:
    """Get default test cases for a benchmark type"""
    if benchmark_type == "swe":
        return [
            "django__django-10999",
            "django__django-11001",
            "requests__requests-2317",
            "scikit-learn__scikit-learn-10297",
            "matplotlib__matplotlib-18869"
        ]
    elif benchmark_type == "polyglot":
        return [
            "python_basic_1",
            "python_basic_2", 
            "javascript_basic_1",
            "rust_basic_1",
            "go_basic_1"
        ]
    else:
        return ["mock_test_1", "mock_test_2", "mock_test_3"]


def save_evaluation_results(results: Dict[str, Any], output_path: str) -> bool:
    """Save evaluation results to file"""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        return True
        
    except Exception as e:
        logging.error(f"Failed to save evaluation results: {e}")
        return False


def load_evaluation_results(results_path: str) -> Dict[str, Any]:
    """Load evaluation results from file"""
    try:
        with open(results_path, 'r') as f:
            return json.load(f)
            
    except Exception as e:
        logging.error(f"Failed to load evaluation results: {e}")
        return {}
