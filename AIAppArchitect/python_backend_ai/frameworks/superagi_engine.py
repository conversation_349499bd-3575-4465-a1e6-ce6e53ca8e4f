"""
SuperAGI Engine - Production Implementation
Dev-first autonomous AI agent framework based on SuperAGI
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import structlog
from datetime import datetime
import sqlite3

logger = structlog.get_logger(__name__)


class AgentStatus(str, Enum):
    """SuperAGI agent status"""
    CREATED = "CREATED"
    RUNNING = "RUNNING"
    PAUSED = "PAUSED"
    COMPLETED = "COMPLETED"
    TERMINATED = "TERMINATED"
    ERROR = "ERROR"


@dataclass
class SuperAGIConfig:
    """Configuration for SuperAGI engine"""
    model_name: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4096
    max_iterations: int = 25
    agent_memory_window: int = 10
    enable_tools: bool = True


@dataclass
class AgentGoal:
    """Goal definition for SuperAGI agents"""
    id: str
    description: str
    priority: int = 1
    completed: bool = False
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class AgentRun:
    """Agent execution run"""
    id: str
    agent_id: str
    name: str
    goals: List[AgentGoal]
    status: AgentStatus = AgentStatus.CREATED
    current_step: int = 0
    max_iterations: int = 25
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    output: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class SuperAGITool:
    """Tool definition for SuperAGI"""
    name: str
    description: str
    function: callable
    parameters: Dict[str, Any] = field(default_factory=dict)


class SuperAGIEngine:
    """
    Production SuperAGI Engine - Dev-first autonomous AI agent framework
    Based on the SuperAGI project by TransformerOptimus
    """
    
    def __init__(self, config: Optional[SuperAGIConfig] = None):
        self.config = config or SuperAGIConfig()
        self.agents: Dict[str, Dict[str, Any]] = {}
        self.agent_runs: Dict[str, AgentRun] = {}
        self.tools: Dict[str, SuperAGITool] = {}
        self.db_path = ":memory:"
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the SuperAGI engine"""
        if self._initialized:
            return
        
        logger.info("Initializing SuperAGI Engine...")
        
        try:
            # Initialize database
            await self._init_database()
            
            # Initialize default tools
            await self._init_default_tools()
            
            self._initialized = True
            logger.info("SuperAGI Engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize SuperAGI Engine", error=str(e))
            raise

    async def _init_database(self) -> None:
        """Initialize SQLite database for SuperAGI data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create agents table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS agents (
                id TEXT PRIMARY KEY,
                name TEXT,
                description TEXT,
                goals TEXT,
                config TEXT,
                created_at TEXT
            )
        """)
        
        # Create agent runs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS agent_runs (
                id TEXT PRIMARY KEY,
                agent_id TEXT,
                name TEXT,
                status TEXT,
                current_step INTEGER,
                max_iterations INTEGER,
                output TEXT,
                created_at TEXT,
                completed_at TEXT
            )
        """)
        
        # Create tools table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tools (
                name TEXT PRIMARY KEY,
                description TEXT,
                parameters TEXT
            )
        """)
        
        conn.commit()
        conn.close()

    async def _init_default_tools(self) -> None:
        """Initialize default SuperAGI tools"""
        
        # File management tool
        async def write_file(filename: str, content: str) -> str:
            """Write content to a file"""
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                return f"Successfully wrote to {filename}"
            except Exception as e:
                return f"Error writing to {filename}: {str(e)}"
        
        # Code analysis tool
        async def analyze_code(code: str) -> str:
            """Analyze code for issues and improvements"""
            issues = []
            
            # Basic code analysis
            if "eval(" in code:
                issues.append("Security: Use of eval() function")
            if "exec(" in code:
                issues.append("Security: Use of exec() function")
            if len(code.split('\n')) > 100:
                issues.append("Maintainability: Function too long")
            if code.count("if ") > 10:
                issues.append("Complexity: Too many conditional statements")
            if "TODO" in code:
                issues.append("Incomplete: Contains TODO comments")
            
            return f"Code analysis: {'; '.join(issues) if issues else 'No major issues found'}"
        
        # Web search tool
        async def web_search(query: str) -> str:
            """Search the web for information"""
            # Mock implementation - in real SuperAGI this would use actual search APIs
            return f"Web search results for '{query}': [Mock results - implement with real search API]"
        
        # Task planning tool
        async def create_task_plan(goal: str) -> List[str]:
            """Create a step-by-step plan for achieving a goal"""
            # Basic task decomposition
            steps = [
                f"1. Analyze the goal: {goal}",
                "2. Break down into smaller subtasks",
                "3. Identify required resources and tools",
                "4. Execute tasks in logical order",
                "5. Validate results and iterate if needed"
            ]
            return steps
        
        # Register tools
        self.tools = {
            "write_file": SuperAGITool(
                name="write_file",
                description="Write content to a file",
                function=write_file,
                parameters={"filename": "string", "content": "string"}
            ),
            "analyze_code": SuperAGITool(
                name="analyze_code",
                description="Analyze code for issues and improvements",
                function=analyze_code,
                parameters={"code": "string"}
            ),
            "web_search": SuperAGITool(
                name="web_search",
                description="Search the web for information",
                function=web_search,
                parameters={"query": "string"}
            ),
            "create_task_plan": SuperAGITool(
                name="create_task_plan",
                description="Create a step-by-step plan for achieving a goal",
                function=create_task_plan,
                parameters={"goal": "string"}
            )
        }

    async def create_agent(
        self, 
        name: str, 
        description: str,
        goals: List[str],
        tools: Optional[List[str]] = None
    ) -> str:
        """Create a new SuperAGI agent"""
        agent_id = str(uuid.uuid4())
        
        # Convert goals to AgentGoal objects
        agent_goals = [
            AgentGoal(id=str(uuid.uuid4()), description=goal, priority=i+1)
            for i, goal in enumerate(goals)
        ]
        
        agent_data = {
            "id": agent_id,
            "name": name,
            "description": description,
            "goals": agent_goals,
            "tools": tools or list(self.tools.keys()),
            "config": self.config,
            "created_at": datetime.now()
        }
        
        self.agents[agent_id] = agent_data
        
        # Store in database
        await self._store_agent_in_db(agent_data)
        
        logger.info("SuperAGI agent created", agent_id=agent_id, name=name)
        return agent_id

    async def _store_agent_in_db(self, agent_data: Dict[str, Any]) -> None:
        """Store agent data in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO agents 
            (id, name, description, goals, config, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            agent_data["id"],
            agent_data["name"],
            agent_data["description"],
            json.dumps([goal.__dict__ for goal in agent_data["goals"]], default=str),
            json.dumps(agent_data["config"].__dict__),
            agent_data["created_at"].isoformat()
        ))
        
        conn.commit()
        conn.close()

    async def run_agent(self, agent_id: str, run_name: Optional[str] = None) -> str:
        """Start running a SuperAGI agent"""
        if agent_id not in self.agents:
            raise ValueError(f"Agent '{agent_id}' not found")
        
        agent = self.agents[agent_id]
        run_id = str(uuid.uuid4())
        
        agent_run = AgentRun(
            id=run_id,
            agent_id=agent_id,
            name=run_name or f"{agent['name']}_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            goals=agent["goals"],
            max_iterations=self.config.max_iterations
        )
        
        self.agent_runs[run_id] = agent_run
        
        # Start agent execution in background
        asyncio.create_task(self._execute_agent_run(agent_run))
        
        logger.info("SuperAGI agent run started", agent_id=agent_id, run_id=run_id)
        return run_id

    async def _execute_agent_run(self, agent_run: AgentRun) -> None:
        """Execute a SuperAGI agent run"""
        try:
            agent_run.status = AgentStatus.RUNNING
            agent = self.agents[agent_run.agent_id]
            
            logger.info("Starting agent execution", 
                       agent_id=agent_run.agent_id, 
                       run_id=agent_run.id)
            
            for iteration in range(agent_run.max_iterations):
                agent_run.current_step = iteration + 1
                
                # Check if all goals are completed
                if all(goal.completed for goal in agent_run.goals):
                    agent_run.status = AgentStatus.COMPLETED
                    break
                
                # Execute one iteration
                iteration_result = await self._execute_iteration(agent_run, agent)
                agent_run.output.append(iteration_result)
                
                # Add delay between iterations
                await asyncio.sleep(1)
            
            if agent_run.status != AgentStatus.COMPLETED:
                agent_run.status = AgentStatus.COMPLETED
            
            agent_run.completed_at = datetime.now()
            
            logger.info("Agent execution completed", 
                       agent_id=agent_run.agent_id, 
                       run_id=agent_run.id,
                       iterations=agent_run.current_step)
            
        except Exception as e:
            agent_run.status = AgentStatus.ERROR
            agent_run.output.append({
                "iteration": agent_run.current_step,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
            logger.error("Agent execution failed", 
                        agent_id=agent_run.agent_id, 
                        run_id=agent_run.id,
                        error=str(e))

    async def _execute_iteration(self, agent_run: AgentRun, agent: Dict[str, Any]) -> Dict[str, Any]:
        """Execute one iteration of agent thinking and action"""
        
        # Find next incomplete goal
        current_goal = None
        for goal in agent_run.goals:
            if not goal.completed:
                current_goal = goal
                break
        
        if not current_goal:
            return {
                "iteration": agent_run.current_step,
                "action": "no_goals_remaining",
                "timestamp": datetime.now().isoformat()
            }
        
        # Simulate agent thinking and tool usage
        thought = f"Working on goal: {current_goal.description}"
        
        # Select and use a tool
        available_tools = agent.get("tools", [])
        if available_tools and "create_task_plan" in available_tools:
            tool = self.tools["create_task_plan"]
            tool_result = await tool.function(current_goal.description)
            
            action = f"Created task plan for goal: {current_goal.description}"
            
            # Mark goal as completed (simplified logic)
            current_goal.completed = True
            
        else:
            action = f"No suitable tools available for goal: {current_goal.description}"
            tool_result = "No action taken"
        
        return {
            "iteration": agent_run.current_step,
            "goal": current_goal.description,
            "thought": thought,
            "action": action,
            "tool_result": tool_result,
            "timestamp": datetime.now().isoformat()
        }

    async def get_agent_run_status(self, run_id: str) -> Dict[str, Any]:
        """Get the status of an agent run"""
        if run_id not in self.agent_runs:
            raise ValueError(f"Agent run '{run_id}' not found")
        
        agent_run = self.agent_runs[run_id]
        
        return {
            "run_id": run_id,
            "agent_id": agent_run.agent_id,
            "name": agent_run.name,
            "status": agent_run.status.value,
            "current_step": agent_run.current_step,
            "max_iterations": agent_run.max_iterations,
            "goals_completed": sum(1 for goal in agent_run.goals if goal.completed),
            "total_goals": len(agent_run.goals),
            "created_at": agent_run.created_at.isoformat(),
            "completed_at": agent_run.completed_at.isoformat() if agent_run.completed_at else None,
            "output_length": len(agent_run.output)
        }

    async def get_agent_run_output(self, run_id: str) -> List[Dict[str, Any]]:
        """Get the output of an agent run"""
        if run_id not in self.agent_runs:
            raise ValueError(f"Agent run '{run_id}' not found")
        
        return self.agent_runs[run_id].output

    async def pause_agent_run(self, run_id: str) -> None:
        """Pause an agent run"""
        if run_id not in self.agent_runs:
            raise ValueError(f"Agent run '{run_id}' not found")
        
        agent_run = self.agent_runs[run_id]
        if agent_run.status == AgentStatus.RUNNING:
            agent_run.status = AgentStatus.PAUSED
            logger.info("Agent run paused", run_id=run_id)

    async def resume_agent_run(self, run_id: str) -> None:
        """Resume a paused agent run"""
        if run_id not in self.agent_runs:
            raise ValueError(f"Agent run '{run_id}' not found")
        
        agent_run = self.agent_runs[run_id]
        if agent_run.status == AgentStatus.PAUSED:
            agent_run.status = AgentStatus.RUNNING
            # Continue execution
            asyncio.create_task(self._execute_agent_run(agent_run))
            logger.info("Agent run resumed", run_id=run_id)

    async def terminate_agent_run(self, run_id: str) -> None:
        """Terminate an agent run"""
        if run_id not in self.agent_runs:
            raise ValueError(f"Agent run '{run_id}' not found")
        
        agent_run = self.agent_runs[run_id]
        agent_run.status = AgentStatus.TERMINATED
        agent_run.completed_at = datetime.now()
        logger.info("Agent run terminated", run_id=run_id)

    async def list_agents(self) -> List[Dict[str, Any]]:
        """List all agents"""
        return [
            {
                "id": agent["id"],
                "name": agent["name"],
                "description": agent["description"],
                "goals_count": len(agent["goals"]),
                "tools_count": len(agent.get("tools", [])),
                "created_at": agent["created_at"].isoformat()
            }
            for agent in self.agents.values()
        ]

    async def list_agent_runs(self, agent_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """List agent runs, optionally filtered by agent ID"""
        runs = list(self.agent_runs.values())
        
        if agent_id:
            runs = [run for run in runs if run.agent_id == agent_id]
        
        return [
            {
                "run_id": run.id,
                "agent_id": run.agent_id,
                "name": run.name,
                "status": run.status.value,
                "current_step": run.current_step,
                "created_at": run.created_at.isoformat()
            }
            for run in runs
        ]

    async def add_tool(self, tool: SuperAGITool) -> None:
        """Add a custom tool to SuperAGI"""
        self.tools[tool.name] = tool
        logger.info("Tool added", name=tool.name)

    async def list_tools(self) -> List[Dict[str, Any]]:
        """List all available tools"""
        return [
            {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.parameters
            }
            for tool in self.tools.values()
        ]

    async def shutdown(self) -> None:
        """Shutdown the SuperAGI engine"""
        logger.info("Shutting down SuperAGI Engine...")
        
        # Terminate all running agent runs
        for run in self.agent_runs.values():
            if run.status == AgentStatus.RUNNING:
                await self.terminate_agent_run(run.id)
        
        self.agents.clear()
        self.agent_runs.clear()
        self.tools.clear()
        self._initialized = False
        
        logger.info("SuperAGI Engine shutdown complete")
