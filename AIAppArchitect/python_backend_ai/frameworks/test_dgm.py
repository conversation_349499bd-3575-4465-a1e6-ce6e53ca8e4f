"""
Test script for the Darwin Gödel Machine (DGM) implementation
Demonstrates the real DGM functionality based on the actual repository
"""

import asyncio
import os
import tempfile
import shutil
from datetime import datetime

from dgm_engine import DGMEngine, AgenticSystem
from dgm_utils import setup_workspace
from dgm_llm import chat_with_agent, CLAUDE_MODEL


async def test_basic_dgm_functionality():
    """Test basic DGM functionality"""
    print("🧬 Testing Darwin Gödel Machine - Basic Functionality")
    print("=" * 60)
    
    # Create DGM engine
    output_dir = os.path.join(tempfile.gettempdir(), f"dgm_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    dgm = DGMEngine(output_dir=output_dir, max_generation=3, selfimprove_size=2)
    
    try:
        # Initialize DGM
        print("🔧 Initializing DGM Engine...")
        await dgm.initialize()
        
        # Get initial status
        status = await dgm.get_agent_status()
        print(f"✅ DGM initialized successfully!")
        print(f"   Output directory: {status['output_dir']}")
        print(f"   Archive size: {status['archive_size']}")
        print(f"   Current generation: {status['current_generation']}")
        
        # Test agent creation
        print("\n🤖 Testing Agent Creation...")
        problem = "Implement a function to calculate the factorial of a number"
        agent_id = await dgm.create_agent(problem)
        print(f"✅ Agent created: {agent_id}")
        
        # Test problem solving
        print("\n🧠 Testing Problem Solving...")
        solution = await dgm.solve_problem(problem)
        print(f"✅ Problem solved: {solution['success']}")
        if solution['success']:
            print(f"   Agent used: {solution['best_agent_used']}")
            print(f"   Solution preview: {solution['solution'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    finally:
        await dgm.shutdown()
        # Clean up test directory
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)


async def test_self_improvement_cycle():
    """Test self-improvement cycle"""
    print("\n🔄 Testing Self-Improvement Cycle")
    print("=" * 60)
    
    # Create DGM engine with smaller parameters for testing
    output_dir = os.path.join(tempfile.gettempdir(), f"dgm_improve_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    dgm = DGMEngine(output_dir=output_dir, max_generation=2, selfimprove_size=1)
    
    try:
        # Initialize DGM
        print("🔧 Initializing DGM for self-improvement...")
        await dgm.initialize()
        
        # Create initial agent performance data
        print("📊 Setting up initial performance baseline...")
        
        # Test self-improvement selection
        print("\n🎯 Testing self-improvement selection...")
        selfimprove_entries = dgm.choose_selfimproves('random')
        print(f"✅ Selected {len(selfimprove_entries)} self-improvement entries:")
        for i, (parent, entry) in enumerate(selfimprove_entries):
            print(f"   {i+1}. Parent: {parent}, Entry: {entry}")
        
        # Test single self-improvement
        if selfimprove_entries:
            print("\n🧬 Testing single self-improvement...")
            parent_commit, entry = selfimprove_entries[0]
            metadata = await dgm.self_improve(parent_commit, entry)
            print(f"✅ Self-improvement completed:")
            print(f"   Run ID: {metadata.get('run_id', 'unknown')}")
            print(f"   Parent: {metadata.get('parent_commit', 'unknown')}")
            print(f"   Entry: {metadata.get('entry', 'unknown')}")
        
        # Test performance metrics
        print("\n📈 Testing performance metrics...")
        metrics = await dgm.get_performance_metrics()
        print(f"✅ Performance metrics retrieved:")
        print(f"   Total generations: {metrics['total_generations']}")
        print(f"   Total attempts: {metrics['total_attempts']}")
        print(f"   Total successes: {metrics['total_successes']}")
        print(f"   Best score: {metrics['best_score']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Self-improvement test failed: {e}")
        return False
    
    finally:
        await dgm.shutdown()
        # Clean up test directory
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)


async def test_evolution_loop():
    """Test the full evolution loop"""
    print("\n🌟 Testing Full Evolution Loop")
    print("=" * 60)
    
    # Create DGM engine with minimal parameters for testing
    output_dir = os.path.join(tempfile.gettempdir(), f"dgm_evolution_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    dgm = DGMEngine(output_dir=output_dir, max_generation=2, selfimprove_size=1)
    
    try:
        # Initialize DGM
        print("🔧 Initializing DGM for evolution...")
        await dgm.initialize()
        
        # Run evolution
        print("\n🧬 Running evolution loop...")
        print("   This may take a few minutes...")
        
        evolution_results = await dgm.run_evolution()
        
        print(f"✅ Evolution completed!")
        print(f"   Generations run: {len(evolution_results['generations'])}")
        print(f"   Total improvements: {evolution_results['total_improvements']}")
        print(f"   Best performance: {evolution_results['best_performance']:.3f}")
        print(f"   Final archive size: {len(evolution_results['final_archive'])}")
        
        # Show generation details
        print("\n📊 Generation Details:")
        for gen in evolution_results['generations']:
            print(f"   Gen {gen['generation']}: {gen['improvements_compiled']}/{gen['improvements_attempted']} successful, "
                  f"score: {gen['best_score']:.3f}")
        
        # Test evolution history
        print("\n📜 Testing evolution history...")
        history = await dgm.get_evolution_history()
        print(f"✅ Evolution history retrieved: {len(history)} generations")
        
        return True
        
    except Exception as e:
        print(f"❌ Evolution test failed: {e}")
        return False
    
    finally:
        await dgm.shutdown()
        # Clean up test directory
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)


async def test_agentic_system():
    """Test the AgenticSystem directly"""
    print("\n🤖 Testing AgenticSystem")
    print("=" * 60)
    
    # Create temporary workspace
    workspace_dir = tempfile.mkdtemp(prefix="dgm_agent_test_")
    
    try:
        # Set up workspace
        print("🔧 Setting up workspace...")
        setup_workspace(workspace_dir)
        
        # Create AgenticSystem
        print("🤖 Creating AgenticSystem...")
        agent = AgenticSystem(
            problem_statement="Create a simple calculator function",
            git_tempdir=workspace_dir,
            base_commit="HEAD",
            chat_history_file=os.path.join(workspace_dir, "chat_history.md"),
            test_description="Test basic arithmetic operations",
            instance_id="test_agent_001"
        )
        
        # Test forward pass
        print("🧠 Running agent forward pass...")
        agent.forward()
        
        # Test regression tests
        print("🧪 Testing regression test identification...")
        regression_summary = agent.get_regression_tests()
        print(f"✅ Regression tests identified: {len(regression_summary)} characters")
        
        # Test running regression tests
        print("🏃 Running regression tests...")
        test_report = agent.run_regression_tests(regression_summary)
        print(f"✅ Test report generated: {len(test_report)} characters")
        
        # Test getting current edits
        print("📝 Getting current edits...")
        edits = agent.get_current_edits()
        print(f"✅ Current edits: {len(edits)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ AgenticSystem test failed: {e}")
        return False
    
    finally:
        # Clean up workspace
        if os.path.exists(workspace_dir):
            shutil.rmtree(workspace_dir)


async def test_llm_interface():
    """Test the LLM interface"""
    print("\n💬 Testing LLM Interface")
    print("=" * 60)
    
    try:
        # Test basic chat
        print("🗣️ Testing basic chat...")
        msg_history = chat_with_agent(
            "Analyze this simple Python function: def add(a, b): return a + b",
            model=CLAUDE_MODEL,
            logging=lambda msg: print(f"   LOG: {msg}")
        )
        
        print(f"✅ Chat completed with {len(msg_history)} messages")
        
        # Test different types of requests
        test_requests = [
            "Test this function with various inputs",
            "Improve the performance of this algorithm",
            "Solve the problem of implementing a binary search"
        ]
        
        for i, request in enumerate(test_requests):
            print(f"🧪 Testing request {i+1}: {request[:30]}...")
            response = chat_with_agent(request, model=CLAUDE_MODEL)
            print(f"✅ Response received: {len(response)} messages")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM interface test failed: {e}")
        return False


async def run_all_tests():
    """Run all DGM tests"""
    print("🧬 Darwin Gödel Machine (DGM) - Comprehensive Test Suite")
    print("=" * 80)
    print("Based on the real DGM repository: https://github.com/jennyzzt/dgm")
    print("Research paper: 'Darwin Gödel Machine: Open-Ended Evolution of Self-Improving Agents'")
    print("=" * 80)
    
    tests = [
        ("LLM Interface", test_llm_interface),
        ("AgenticSystem", test_agentic_system),
        ("Basic DGM Functionality", test_basic_dgm_functionality),
        ("Self-Improvement Cycle", test_self_improvement_cycle),
        ("Evolution Loop", test_evolution_loop),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("🏁 TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! DGM implementation is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    # Run the comprehensive test suite
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
