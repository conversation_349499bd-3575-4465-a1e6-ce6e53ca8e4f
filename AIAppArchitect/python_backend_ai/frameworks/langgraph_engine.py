"""
LangGraph Engine - Production Implementation
State-based agent orchestration using LangGraph framework
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, TypedDict, Annotated
from dataclasses import dataclass, field
from enum import Enum
import structlog

# LangGraph imports
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import Too<PERSON><PERSON>xecutor
from langgraph.checkpoint.sqlite import SqliteSaver
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic

logger = structlog.get_logger(__name__)


class AgentState(TypedDict):
    """State for LangGraph agents"""
    messages: Annotated[List[BaseMessage], "The messages in the conversation"]
    task_description: str
    current_step: str
    completed_steps: List[str]
    agent_outputs: Dict[str, Any]
    error: Optional[str]
    metadata: Dict[str, Any]


@dataclass
class LangGraphConfig:
    """Configuration for LangGraph engine"""
    model_name: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_iterations: int = 10
    checkpoint_enabled: bool = True
    tools_enabled: bool = True


@dataclass
class WorkflowStep:
    """Individual step in a LangGraph workflow"""
    name: str
    description: str
    agent_type: str
    dependencies: List[str] = field(default_factory=list)
    tools: List[str] = field(default_factory=list)
    prompt_template: str = ""


class LangGraphEngine:
    """
    Production LangGraph Engine for state-based agent orchestration
    """
    
    def __init__(self, config: Optional[LangGraphConfig] = None):
        self.config = config or LangGraphConfig()
        self.workflows: Dict[str, StateGraph] = {}
        self.checkpointer = None
        self.llm = None
        self.tools = {}
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the LangGraph engine"""
        if self._initialized:
            return
        
        logger.info("Initializing LangGraph Engine...")
        
        try:
            # Initialize LLM
            if "gpt" in self.config.model_name:
                self.llm = ChatOpenAI(
                    model=self.config.model_name,
                    temperature=self.config.temperature
                )
            elif "claude" in self.config.model_name:
                self.llm = ChatAnthropic(
                    model=self.config.model_name,
                    temperature=self.config.temperature
                )
            else:
                self.llm = ChatOpenAI(model="gpt-4-turbo-preview")
            
            # Initialize checkpointer for state persistence
            if self.config.checkpoint_enabled:
                self.checkpointer = SqliteSaver.from_conn_string(":memory:")
            
            # Initialize built-in tools
            await self._initialize_tools()
            
            # Create default workflows
            await self._create_default_workflows()
            
            self._initialized = True
            logger.info("LangGraph Engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize LangGraph Engine", error=str(e))
            raise

    async def _initialize_tools(self) -> None:
        """Initialize built-in tools for agents"""
        
        @tool
        def code_analyzer(code: str) -> str:
            """Analyze code for issues and improvements"""
            # Simple code analysis
            issues = []
            if "TODO" in code:
                issues.append("Contains TODO comments")
            if "print(" in code and "python" in code.lower():
                issues.append("Contains debug print statements")
            if len(code.split('\n')) > 100:
                issues.append("Function is too long (>100 lines)")
            
            return f"Code analysis: {', '.join(issues) if issues else 'No issues found'}"
        
        @tool
        def file_writer(filename: str, content: str) -> str:
            """Write content to a file"""
            try:
                with open(filename, 'w') as f:
                    f.write(content)
                return f"Successfully wrote to {filename}"
            except Exception as e:
                return f"Error writing to {filename}: {str(e)}"
        
        @tool
        def test_generator(function_code: str) -> str:
            """Generate unit tests for given function code"""
            # Simple test generation
            function_name = "test_function"  # Extract from code in real implementation
            test_code = f"""
import unittest

class Test{function_name.title()}(unittest.TestCase):
    def test_{function_name}(self):
        # TODO: Implement test cases
        self.assertTrue(True)
        
    def test_{function_name}_edge_cases(self):
        # TODO: Test edge cases
        self.assertTrue(True)

if __name__ == '__main__':
    unittest.main()
"""
            return test_code
        
        self.tools = {
            "code_analyzer": code_analyzer,
            "file_writer": file_writer,
            "test_generator": test_generator
        }

    async def _create_default_workflows(self) -> None:
        """Create default LangGraph workflows"""
        
        # Code Generation Workflow
        code_workflow = StateGraph(AgentState)
        
        # Define workflow nodes
        async def planning_node(state: AgentState) -> AgentState:
            """Planning phase of code generation"""
            messages = state["messages"]
            task = state["task_description"]
            
            planning_prompt = f"""
            You are a senior software architect. Analyze this task and create a detailed plan:
            
            Task: {task}
            
            Create a step-by-step plan for implementation. Consider:
            1. Requirements analysis
            2. Architecture design
            3. Implementation approach
            4. Testing strategy
            
            Respond with a structured plan.
            """
            
            messages.append(SystemMessage(content=planning_prompt))
            response = await self.llm.ainvoke(messages)
            messages.append(response)
            
            return {
                **state,
                "messages": messages,
                "current_step": "planning",
                "completed_steps": state["completed_steps"] + ["planning"],
                "agent_outputs": {**state["agent_outputs"], "plan": response.content}
            }
        
        async def implementation_node(state: AgentState) -> AgentState:
            """Implementation phase"""
            messages = state["messages"]
            plan = state["agent_outputs"].get("plan", "")
            
            impl_prompt = f"""
            Based on this plan, implement the solution:
            
            Plan: {plan}
            
            Write clean, production-ready code with:
            1. Proper error handling
            2. Documentation
            3. Type hints (if Python)
            4. Following best practices
            
            Provide the complete implementation.
            """
            
            messages.append(HumanMessage(content=impl_prompt))
            response = await self.llm.ainvoke(messages)
            messages.append(response)
            
            return {
                **state,
                "messages": messages,
                "current_step": "implementation",
                "completed_steps": state["completed_steps"] + ["implementation"],
                "agent_outputs": {**state["agent_outputs"], "code": response.content}
            }
        
        async def review_node(state: AgentState) -> AgentState:
            """Code review phase"""
            messages = state["messages"]
            code = state["agent_outputs"].get("code", "")
            
            # Use code analyzer tool
            if "code_analyzer" in self.tools:
                analysis = self.tools["code_analyzer"].invoke({"code": code})
            else:
                analysis = "Code analysis not available"
            
            review_prompt = f"""
            Review this implementation:
            
            Code: {code}
            Analysis: {analysis}
            
            Provide feedback on:
            1. Code quality
            2. Best practices
            3. Potential improvements
            4. Security considerations
            
            Suggest any necessary changes.
            """
            
            messages.append(HumanMessage(content=review_prompt))
            response = await self.llm.ainvoke(messages)
            messages.append(response)
            
            return {
                **state,
                "messages": messages,
                "current_step": "review",
                "completed_steps": state["completed_steps"] + ["review"],
                "agent_outputs": {**state["agent_outputs"], "review": response.content}
            }
        
        def should_continue(state: AgentState) -> str:
            """Determine next step in workflow"""
            current_step = state["current_step"]
            
            if current_step == "planning":
                return "implementation"
            elif current_step == "implementation":
                return "review"
            else:
                return END
        
        # Add nodes to workflow
        code_workflow.add_node("planning", planning_node)
        code_workflow.add_node("implementation", implementation_node)
        code_workflow.add_node("review", review_node)
        
        # Add edges
        code_workflow.set_entry_point("planning")
        code_workflow.add_conditional_edges(
            "planning",
            should_continue,
            {"implementation": "implementation", END: END}
        )
        code_workflow.add_conditional_edges(
            "implementation", 
            should_continue,
            {"review": "review", END: END}
        )
        code_workflow.add_conditional_edges(
            "review",
            should_continue,
            {END: END}
        )
        
        # Compile workflow
        if self.checkpointer:
            compiled_workflow = code_workflow.compile(checkpointer=self.checkpointer)
        else:
            compiled_workflow = code_workflow.compile()
        
        self.workflows["code_generation"] = compiled_workflow
        
        logger.info("Default LangGraph workflows created")

    async def execute_workflow(
        self, 
        workflow_name: str, 
        task_description: str,
        initial_state: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute a LangGraph workflow"""
        if not self._initialized:
            await self.initialize()
        
        if workflow_name not in self.workflows:
            raise ValueError(f"Workflow '{workflow_name}' not found")
        
        workflow = self.workflows[workflow_name]
        
        # Initialize state
        state = AgentState(
            messages=[HumanMessage(content=task_description)],
            task_description=task_description,
            current_step="start",
            completed_steps=[],
            agent_outputs={},
            error=None,
            metadata=initial_state or {}
        )
        
        try:
            logger.info("Executing LangGraph workflow", 
                       workflow=workflow_name, 
                       task=task_description)
            
            # Execute workflow
            config = {"configurable": {"thread_id": "default"}}
            result = await workflow.ainvoke(state, config=config)
            
            logger.info("LangGraph workflow completed successfully",
                       workflow=workflow_name,
                       steps_completed=len(result["completed_steps"]))
            
            return {
                "success": True,
                "result": result,
                "steps_completed": result["completed_steps"],
                "outputs": result["agent_outputs"],
                "messages": [msg.content for msg in result["messages"]]
            }
            
        except Exception as e:
            logger.error("LangGraph workflow execution failed",
                        workflow=workflow_name,
                        error=str(e))
            
            return {
                "success": False,
                "error": str(e),
                "steps_completed": state["completed_steps"],
                "outputs": state["agent_outputs"]
            }

    async def create_custom_workflow(
        self, 
        workflow_name: str, 
        steps: List[WorkflowStep]
    ) -> None:
        """Create a custom LangGraph workflow"""
        if not self._initialized:
            await self.initialize()
        
        logger.info("Creating custom LangGraph workflow", 
                   workflow=workflow_name, 
                   steps=len(steps))
        
        workflow = StateGraph(AgentState)
        
        # Create nodes for each step
        for step in steps:
            async def step_node(state: AgentState, step_config=step) -> AgentState:
                messages = state["messages"]
                
                # Build prompt from template
                prompt = step_config.prompt_template.format(
                    task=state["task_description"],
                    previous_outputs=state["agent_outputs"]
                )
                
                messages.append(HumanMessage(content=prompt))
                response = await self.llm.ainvoke(messages)
                messages.append(response)
                
                return {
                    **state,
                    "messages": messages,
                    "current_step": step_config.name,
                    "completed_steps": state["completed_steps"] + [step_config.name],
                    "agent_outputs": {
                        **state["agent_outputs"], 
                        step_config.name: response.content
                    }
                }
            
            workflow.add_node(step.name, step_node)
        
        # Add edges based on dependencies
        entry_steps = [step for step in steps if not step.dependencies]
        if entry_steps:
            workflow.set_entry_point(entry_steps[0].name)
        
        # Add sequential edges (simplified)
        for i, step in enumerate(steps[:-1]):
            workflow.add_edge(step.name, steps[i + 1].name)
        
        # Last step goes to END
        if steps:
            workflow.add_edge(steps[-1].name, END)
        
        # Compile and store
        if self.checkpointer:
            compiled_workflow = workflow.compile(checkpointer=self.checkpointer)
        else:
            compiled_workflow = workflow.compile()
        
        self.workflows[workflow_name] = compiled_workflow
        
        logger.info("Custom LangGraph workflow created", workflow=workflow_name)

    async def get_workflow_state(
        self, 
        workflow_name: str, 
        thread_id: str = "default"
    ) -> Optional[Dict[str, Any]]:
        """Get current state of a workflow"""
        if not self.checkpointer:
            return None
        
        try:
            config = {"configurable": {"thread_id": thread_id}}
            state = await self.workflows[workflow_name].aget_state(config)
            return state.values if state else None
        except Exception as e:
            logger.error("Failed to get workflow state", error=str(e))
            return None

    async def list_workflows(self) -> List[str]:
        """List available workflows"""
        return list(self.workflows.keys())

    async def shutdown(self) -> None:
        """Shutdown the LangGraph engine"""
        logger.info("Shutting down LangGraph Engine...")
        
        # Close checkpointer if needed
        if self.checkpointer:
            # SqliteSaver doesn't need explicit closing for in-memory
            pass
        
        self.workflows.clear()
        self._initialized = False
        
        logger.info("LangGraph Engine shutdown complete")


# Alias for backward compatibility
EvoAgentXEngine = LangGraphEngine
