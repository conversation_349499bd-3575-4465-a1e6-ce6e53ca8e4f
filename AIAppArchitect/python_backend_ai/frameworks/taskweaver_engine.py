"""
TaskWeaver Engine - Production Implementation
Microsoft TaskWeaver integration for code-first agent orchestration
"""

import asyncio
import json
import uuid
import tempfile
import subprocess
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import structlog
from pathlib import Path
import docker
import yaml

logger = structlog.get_logger(__name__)


class TaskType(str, Enum):
    """Task types supported by TaskWeaver"""
    CODE_GENERATION = "code_generation"
    DATA_ANALYSIS = "data_analysis"
    FILE_PROCESSING = "file_processing"
    API_INTEGRATION = "api_integration"
    TESTING = "testing"
    DEPLOYMENT = "deployment"


@dataclass
class SubTask:
    """Individual subtask in a TaskWeaver plan"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    description: str = ""
    task_type: TaskType = TaskType.CODE_GENERATION
    dependencies: List[str] = field(default_factory=list)
    required_plugins: List[str] = field(default_factory=list)
    estimated_duration: float = 0.0
    priority: int = 1
    code: Optional[str] = None
    result: Optional[Any] = None
    status: str = "pending"


@dataclass
class ExecutionPlan:
    """TaskWeaver execution plan"""
    plan_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    subtasks: List[SubTask] = field(default_factory=list)
    execution_order: List[str] = field(default_factory=list)
    parallel_groups: List[List[str]] = field(default_factory=list)
    estimated_total_time: float = 0.0
    resource_requirements: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Plugin:
    """TaskWeaver plugin definition"""
    name: str
    description: str
    version: str = "1.0.0"
    dependencies: List[str] = field(default_factory=list)
    schema: Dict[str, Any] = field(default_factory=dict)
    code: str = ""
    enabled: bool = True


@dataclass
class ExecutionResult:
    """Result of task execution"""
    plan_id: str
    success: bool
    output: str = ""
    artifacts: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    memory_usage: float = 0.0
    errors: List[str] = field(default_factory=list)
    subtask_results: Dict[str, Any] = field(default_factory=dict)


class TaskWeaverPlanner:
    """TaskWeaver task planning component"""
    
    def __init__(self, ai_service):
        self.ai_service = ai_service
        self.planning_prompt_template = """
You are a TaskWeaver planner. Break down the following user request into executable subtasks.

User Request: {user_query}
Context: {context}

For each subtask, provide:
1. Clear description
2. Task type (code_generation, data_analysis, file_processing, api_integration, testing, deployment)
3. Dependencies on other subtasks
4. Required plugins
5. Estimated duration in minutes

Return the plan as a JSON structure with subtasks array.
"""

    async def decompose_task(self, user_query: str, context: Dict[str, Any]) -> List[SubTask]:
        """Decompose user query into subtasks"""
        logger.info("Decomposing task", query=user_query)
        
        try:
            # Generate planning prompt
            prompt = self.planning_prompt_template.format(
                user_query=user_query,
                context=json.dumps(context, indent=2)
            )
            
            # Get AI response
            response = await self.ai_service.generate_response(
                prompt=prompt,
                system_prompt="You are an expert task planner. Always respond with valid JSON."
            )
            
            # Parse response
            plan_data = json.loads(response.content)
            
            # Create SubTask objects
            subtasks = []
            for task_data in plan_data.get("subtasks", []):
                subtask = SubTask(
                    description=task_data.get("description", ""),
                    task_type=TaskType(task_data.get("task_type", "code_generation")),
                    dependencies=task_data.get("dependencies", []),
                    required_plugins=task_data.get("required_plugins", []),
                    estimated_duration=task_data.get("estimated_duration", 5.0),
                    priority=task_data.get("priority", 1)
                )
                subtasks.append(subtask)
            
            logger.info("Task decomposition completed", subtask_count=len(subtasks))
            return subtasks
            
        except Exception as e:
            logger.error("Task decomposition failed", error=str(e))
            # Fallback: create a single subtask
            return [SubTask(
                description=user_query,
                task_type=TaskType.CODE_GENERATION,
                estimated_duration=10.0
            )]

    async def generate_execution_plan(self, subtasks: List[SubTask]) -> ExecutionPlan:
        """Generate optimized execution plan"""
        logger.info("Generating execution plan", subtask_count=len(subtasks))
        
        # Analyze dependencies and create execution order
        execution_order = self._resolve_dependencies(subtasks)
        
        # Identify parallel execution opportunities
        parallel_groups = self._identify_parallel_groups(subtasks, execution_order)
        
        # Calculate resource requirements
        resource_requirements = self._calculate_resource_requirements(subtasks)
        
        # Estimate total execution time
        estimated_time = self._estimate_execution_time(subtasks, parallel_groups)
        
        plan = ExecutionPlan(
            subtasks=subtasks,
            execution_order=execution_order,
            parallel_groups=parallel_groups,
            estimated_total_time=estimated_time,
            resource_requirements=resource_requirements
        )
        
        logger.info("Execution plan generated", 
                   total_time=estimated_time,
                   parallel_groups=len(parallel_groups))
        
        return plan

    def _resolve_dependencies(self, subtasks: List[SubTask]) -> List[str]:
        """Resolve task dependencies and create execution order"""
        task_map = {task.id: task for task in subtasks}
        visited = set()
        order = []
        
        def visit(task_id: str):
            if task_id in visited:
                return
            
            task = task_map.get(task_id)
            if not task:
                return
            
            # Visit dependencies first
            for dep_id in task.dependencies:
                visit(dep_id)
            
            visited.add(task_id)
            order.append(task_id)
        
        # Visit all tasks
        for task in subtasks:
            visit(task.id)
        
        return order

    def _identify_parallel_groups(
        self, 
        subtasks: List[SubTask], 
        execution_order: List[str]
    ) -> List[List[str]]:
        """Identify tasks that can be executed in parallel"""
        task_map = {task.id: task for task in subtasks}
        parallel_groups = []
        processed = set()
        
        for task_id in execution_order:
            if task_id in processed:
                continue
            
            # Find tasks that can run in parallel with this one
            parallel_group = [task_id]
            task = task_map[task_id]
            
            for other_id in execution_order:
                if other_id in processed or other_id == task_id:
                    continue
                
                other_task = task_map[other_id]
                
                # Check if tasks can run in parallel
                if self._can_run_parallel(task, other_task, task_map):
                    parallel_group.append(other_id)
            
            parallel_groups.append(parallel_group)
            processed.update(parallel_group)
        
        return parallel_groups

    def _can_run_parallel(
        self, 
        task1: SubTask, 
        task2: SubTask, 
        task_map: Dict[str, SubTask]
    ) -> bool:
        """Check if two tasks can run in parallel"""
        # Tasks can't run in parallel if one depends on the other
        if task1.id in task2.dependencies or task2.id in task1.dependencies:
            return False
        
        # Check for transitive dependencies
        def has_dependency_path(from_task: SubTask, to_task_id: str, visited: set) -> bool:
            if from_task.id in visited:
                return False
            visited.add(from_task.id)
            
            if to_task_id in from_task.dependencies:
                return True
            
            for dep_id in from_task.dependencies:
                dep_task = task_map.get(dep_id)
                if dep_task and has_dependency_path(dep_task, to_task_id, visited.copy()):
                    return True
            
            return False
        
        return not (has_dependency_path(task1, task2.id, set()) or 
                   has_dependency_path(task2, task1.id, set()))

    def _calculate_resource_requirements(self, subtasks: List[SubTask]) -> Dict[str, Any]:
        """Calculate resource requirements for the plan"""
        return {
            "cpu_cores": min(4, len(subtasks)),  # Max 4 cores
            "memory_gb": max(2, len(subtasks) * 0.5),  # 0.5GB per task, min 2GB
            "disk_gb": max(1, len(subtasks) * 0.1),  # 0.1GB per task, min 1GB
            "network_bandwidth": "1Mbps"
        }

    def _estimate_execution_time(
        self, 
        subtasks: List[SubTask], 
        parallel_groups: List[List[str]]
    ) -> float:
        """Estimate total execution time considering parallelization"""
        task_map = {task.id: task for task in subtasks}
        total_time = 0.0
        
        for group in parallel_groups:
            # For parallel group, time is the maximum of all tasks in the group
            group_time = max(
                task_map[task_id].estimated_duration 
                for task_id in group 
                if task_id in task_map
            )
            total_time += group_time
        
        return total_time


class TaskWeaverCodeGenerator:
    """TaskWeaver code generation component"""
    
    def __init__(self, ai_service):
        self.ai_service = ai_service
        self.code_generation_template = """
Generate Python code for the following task:

Task: {task_description}
Task Type: {task_type}
Required Plugins: {plugins}
Context: {context}

Requirements:
1. Write clean, well-documented Python code
2. Include error handling
3. Use the specified plugins if available
4. Return results in a structured format
5. Include any necessary imports

Code:
"""

    async def generate_code(self, subtask: SubTask, plugins: List[Plugin]) -> str:
        """Generate Python code for a subtask"""
        logger.info("Generating code", task_id=subtask.id, task_type=subtask.task_type)
        
        try:
            # Prepare plugin information
            plugin_info = []
            for plugin_name in subtask.required_plugins:
                plugin = next((p for p in plugins if p.name == plugin_name), None)
                if plugin:
                    plugin_info.append(f"- {plugin.name}: {plugin.description}")
            
            # Generate code prompt
            prompt = self.code_generation_template.format(
                task_description=subtask.description,
                task_type=subtask.task_type,
                plugins="\n".join(plugin_info) if plugin_info else "None",
                context=json.dumps({"task_id": subtask.id}, indent=2)
            )
            
            # Get AI response
            response = await self.ai_service.generate_response(
                prompt=prompt,
                system_prompt="You are an expert Python developer. Generate clean, executable code."
            )
            
            # Extract code from response
            code = self._extract_code_from_response(response.content)
            subtask.code = code
            
            logger.info("Code generation completed", task_id=subtask.id, code_length=len(code))
            return code
            
        except Exception as e:
            logger.error("Code generation failed", task_id=subtask.id, error=str(e))
            raise

    def _extract_code_from_response(self, response: str) -> str:
        """Extract Python code from AI response"""
        # Look for code blocks
        if "```python" in response:
            start = response.find("```python") + 9
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        if "```" in response:
            start = response.find("```") + 3
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        # If no code blocks found, return the whole response
        return response.strip()


class TaskWeaverExecutor:
    """TaskWeaver code execution component"""
    
    def __init__(self):
        self.docker_client = None
        self.execution_timeout = 300  # 5 minutes

    async def initialize(self) -> None:
        """Initialize the executor"""
        try:
            self.docker_client = docker.from_env()
            logger.info("TaskWeaver executor initialized")
        except Exception as e:
            logger.warning("Docker not available, using local execution", error=str(e))

    async def execute_code(
        self, 
        code: str, 
        subtask: SubTask,
        working_directory: str = "/tmp"
    ) -> Dict[str, Any]:
        """Execute generated code safely"""
        logger.info("Executing code", task_id=subtask.id)
        
        try:
            if self.docker_client:
                return await self._execute_in_docker(code, subtask, working_directory)
            else:
                return await self._execute_locally(code, subtask, working_directory)
                
        except Exception as e:
            logger.error("Code execution failed", task_id=subtask.id, error=str(e))
            return {
                "success": False,
                "output": "",
                "error": str(e),
                "execution_time": 0.0
            }

    async def _execute_in_docker(
        self, 
        code: str, 
        subtask: SubTask,
        working_directory: str
    ) -> Dict[str, Any]:
        """Execute code in Docker container"""
        import time
        start_time = time.time()
        
        try:
            # Create temporary file with code
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                code_file = f.name
            
            # Run in Docker container
            container = self.docker_client.containers.run(
                "python:3.11-slim",
                f"python {Path(code_file).name}",
                volumes={
                    Path(code_file).parent: {'bind': '/app', 'mode': 'rw'},
                    working_directory: {'bind': '/workspace', 'mode': 'rw'}
                },
                working_dir='/app',
                detach=True,
                remove=True,
                mem_limit='512m',
                cpu_period=100000,
                cpu_quota=50000  # 50% CPU
            )
            
            # Wait for completion
            result = container.wait(timeout=self.execution_timeout)
            logs = container.logs().decode('utf-8')
            
            execution_time = time.time() - start_time
            
            return {
                "success": result['StatusCode'] == 0,
                "output": logs,
                "error": logs if result['StatusCode'] != 0 else None,
                "execution_time": execution_time
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            return {
                "success": False,
                "output": "",
                "error": str(e),
                "execution_time": execution_time
            }
        finally:
            # Clean up
            try:
                Path(code_file).unlink()
            except:
                pass

    async def _execute_locally(
        self, 
        code: str, 
        subtask: SubTask,
        working_directory: str
    ) -> Dict[str, Any]:
        """Execute code locally (less secure)"""
        import time
        start_time = time.time()
        
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                code_file = f.name
            
            # Execute with subprocess
            result = subprocess.run(
                ["python", code_file],
                cwd=working_directory,
                capture_output=True,
                text=True,
                timeout=self.execution_timeout
            )
            
            execution_time = time.time() - start_time
            
            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr if result.returncode != 0 else None,
                "execution_time": execution_time
            }
            
        except subprocess.TimeoutExpired:
            execution_time = time.time() - start_time
            return {
                "success": False,
                "output": "",
                "error": "Execution timeout",
                "execution_time": execution_time
            }
        except Exception as e:
            execution_time = time.time() - start_time
            return {
                "success": False,
                "output": "",
                "error": str(e),
                "execution_time": execution_time
            }
        finally:
            # Clean up
            try:
                Path(code_file).unlink()
            except:
                pass


class TaskWeaverEngine:
    """
    Main TaskWeaver engine for code-first agent orchestration
    """
    
    def __init__(self):
        self.planner = None
        self.code_generator = None
        self.executor = None
        self.plugins: List[Plugin] = []
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize TaskWeaver engine"""
        if self._initialized:
            return
        
        logger.info("Initializing TaskWeaver engine...")
        
        # Initialize components (AI service will be injected)
        self.executor = TaskWeaverExecutor()
        await self.executor.initialize()
        
        # Load built-in plugins
        await self._load_builtin_plugins()
        
        self._initialized = True
        logger.info("TaskWeaver engine initialized successfully")

    async def set_ai_service(self, ai_service) -> None:
        """Set AI service for planning and code generation"""
        self.planner = TaskWeaverPlanner(ai_service)
        self.code_generator = TaskWeaverCodeGenerator(ai_service)

    async def execute_task(
        self, 
        user_query: str, 
        context: Dict[str, Any] = None
    ) -> ExecutionResult:
        """Execute a task using TaskWeaver workflow"""
        if not self._initialized:
            await self.initialize()
        
        context = context or {}
        logger.info("Starting TaskWeaver execution", query=user_query)
        
        try:
            # Step 1: Decompose task
            subtasks = await self.planner.decompose_task(user_query, context)
            
            # Step 2: Generate execution plan
            plan = await self.planner.generate_execution_plan(subtasks)
            
            # Step 3: Execute plan
            result = await self._execute_plan(plan)
            
            logger.info("TaskWeaver execution completed", 
                       plan_id=plan.plan_id,
                       success=result.success,
                       execution_time=result.execution_time)
            
            return result
            
        except Exception as e:
            logger.error("TaskWeaver execution failed", error=str(e))
            return ExecutionResult(
                plan_id="failed",
                success=False,
                output="",
                errors=[str(e)]
            )

    async def _execute_plan(self, plan: ExecutionPlan) -> ExecutionResult:
        """Execute the generated plan"""
        import time
        start_time = time.time()
        
        result = ExecutionResult(plan_id=plan.plan_id, success=True)
        task_map = {task.id: task for task in plan.subtasks}
        
        try:
            # Execute parallel groups sequentially
            for group in plan.parallel_groups:
                # Execute tasks in group in parallel
                group_tasks = []
                for task_id in group:
                    if task_id in task_map:
                        task = task_map[task_id]
                        group_tasks.append(self._execute_subtask(task))
                
                # Wait for all tasks in group to complete
                group_results = await asyncio.gather(*group_tasks, return_exceptions=True)
                
                # Process results
                for i, task_result in enumerate(group_results):
                    task_id = group[i]
                    if isinstance(task_result, Exception):
                        result.errors.append(f"Task {task_id} failed: {task_result}")
                        result.success = False
                    else:
                        result.subtask_results[task_id] = task_result
                        if not task_result.get("success", False):
                            result.success = False
            
            result.execution_time = time.time() - start_time
            
            # Combine outputs
            outputs = []
            for task_id, task_result in result.subtask_results.items():
                if task_result.get("output"):
                    outputs.append(f"Task {task_id}: {task_result['output']}")
            
            result.output = "\n\n".join(outputs)
            
            return result
            
        except Exception as e:
            result.success = False
            result.errors.append(str(e))
            result.execution_time = time.time() - start_time
            return result

    async def _execute_subtask(self, subtask: SubTask) -> Dict[str, Any]:
        """Execute a single subtask"""
        try:
            # Generate code for subtask
            code = await self.code_generator.generate_code(subtask, self.plugins)
            
            # Execute the code
            execution_result = await self.executor.execute_code(code, subtask)
            
            # Update subtask status
            subtask.status = "completed" if execution_result["success"] else "failed"
            subtask.result = execution_result["output"]
            
            return execution_result
            
        except Exception as e:
            subtask.status = "failed"
            return {
                "success": False,
                "output": "",
                "error": str(e),
                "execution_time": 0.0
            }

    async def _load_builtin_plugins(self) -> None:
        """Load built-in plugins"""
        builtin_plugins = [
            Plugin(
                name="file_operations",
                description="File reading, writing, and manipulation operations",
                code="""
def read_file(filepath):
    with open(filepath, 'r') as f:
        return f.read()

def write_file(filepath, content):
    with open(filepath, 'w') as f:
        f.write(content)
    return f"File written to {filepath}"
"""
            ),
            Plugin(
                name="data_analysis",
                description="Basic data analysis operations using pandas",
                dependencies=["pandas", "numpy"],
                code="""
import pandas as pd
import numpy as np

def analyze_csv(filepath):
    df = pd.read_csv(filepath)
    return {
        'shape': df.shape,
        'columns': df.columns.tolist(),
        'summary': df.describe().to_dict()
    }
"""
            )
        ]
        
        self.plugins.extend(builtin_plugins)
        logger.info("Built-in plugins loaded", count=len(builtin_plugins))

    async def shutdown(self) -> None:
        """Shutdown TaskWeaver engine"""
        logger.info("Shutting down TaskWeaver engine...")
        
        if self.executor and self.executor.docker_client:
            self.executor.docker_client.close()
        
        self._initialized = False
        logger.info("TaskWeaver engine shutdown complete")
