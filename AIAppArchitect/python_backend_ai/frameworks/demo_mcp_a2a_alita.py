"""
Demonstration of MCP × A2A × ALITA Framework Integration
Shows how to use the integrated framework for self-evolving AI agents

This demo implements the use cases from both research papers:
1. Stock Information System (MCP × A2A paper)
2. Dynamic capability evolution (ALITA paper)
"""

import asyncio
import logging
import json
from datetime import datetime

from mcp_a2a_alita_integration import MCPxA2AxALITAFramework, IntegrationConfig
from mcp_a2a_core import MCPTool, MCPToolType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FrameworkDemo:
    """Demonstration of the integrated framework"""
    
    def __init__(self):
        self.framework = None
    
    async def run_demo(self):
        """Run the complete demonstration"""
        logger.info("Starting MCP × A2A × ALITA Framework Demo")
        
        try:
            # Initialize framework
            await self._initialize_framework()
            
            # Demo 1: Basic task processing without evolution
            await self._demo_basic_task_processing()
            
            # Demo 2: Task requiring capability evolution
            await self._demo_capability_evolution()
            
            # Demo 3: Agent collaboration using A2A
            await self._demo_agent_collaboration()
            
            # Demo 4: Environment isolation
            await self._demo_environment_isolation()
            
            # Demo 5: MCP Box reusability
            await self._demo_mcp_box_reusability()
            
            # Show final status
            await self._show_framework_status()
            
        except Exception as e:
            logger.error(f"Demo failed: {e}")
        finally:
            if self.framework:
                await self.framework.shutdown()
    
    async def _initialize_framework(self):
        """Initialize the framework with demo configuration"""
        logger.info("Initializing framework...")
        
        config = IntegrationConfig(
            agent_id="demo_agent_001",
            agent_name="Demo AI Agent",
            enable_self_evolution=True,
            enable_web_discovery=True,
            enable_environment_isolation=True,
            max_concurrent_evolutions=2,
            environment_cleanup_hours=1,  # Short for demo
            mcp_box_max_size=50
        )
        
        self.framework = MCPxA2AxALITAFramework(config)
        await self.framework.initialize()
        
        # Register some initial tools for demonstration
        await self._register_demo_tools()
        
        logger.info("Framework initialized successfully")
    
    async def _register_demo_tools(self):
        """Register some initial MCP tools for demonstration"""
        
        # Basic calculator tool
        calculator_tool = MCPTool(
            name="CalculatorTool",
            description="Basic mathematical calculations",
            tool_type=MCPToolType.FUNCTION,
            schema={
                "type": "object",
                "properties": {
                    "operation": {"type": "string", "enum": ["add", "subtract", "multiply", "divide"]},
                    "a": {"type": "number"},
                    "b": {"type": "number"}
                },
                "required": ["operation", "a", "b"]
            },
            parameters={"operation": "string", "a": "number", "b": "number"},
            returns={"type": "number"},
            examples=[
                {"operation": "add", "a": 5, "b": 3, "result": 8}
            ],
            metadata={"category": "math", "complexity": "low"}
        )
        
        # Text processing tool
        text_tool = MCPTool(
            name="TextProcessorTool",
            description="Text processing and analysis",
            tool_type=MCPToolType.FUNCTION,
            schema={
                "type": "object",
                "properties": {
                    "text": {"type": "string"},
                    "operation": {"type": "string", "enum": ["count_words", "uppercase", "lowercase", "reverse"]}
                },
                "required": ["text", "operation"]
            },
            parameters={"text": "string", "operation": "string"},
            returns={"type": "string"},
            examples=[
                {"text": "hello world", "operation": "count_words", "result": "2"}
            ],
            metadata={"category": "text", "complexity": "low"}
        )
        
        # Register tools
        self.framework.core_protocol.register_mcp_tool(calculator_tool)
        self.framework.core_protocol.register_mcp_tool(text_tool)
        
        logger.info("Demo tools registered")
    
    async def _demo_basic_task_processing(self):
        """Demo 1: Basic task processing with existing tools"""
        logger.info("\n=== Demo 1: Basic Task Processing ===")
        
        task = "Calculate the sum of 15 and 27, then count the words in the result"
        
        result = await self.framework.process_task(task)
        
        logger.info(f"Task: {task}")
        logger.info(f"Result: {json.dumps(result, indent=2)}")
        
        return result
    
    async def _demo_capability_evolution(self):
        """Demo 2: Task requiring capability evolution"""
        logger.info("\n=== Demo 2: Capability Evolution ===")
        
        # Task that requires new capabilities
        task = "Scrape the latest stock prices from a financial website and create a visualization chart"
        
        result = await self.framework.process_task(task)
        
        logger.info(f"Task: {task}")
        logger.info(f"Result: {json.dumps(result, indent=2)}")
        
        # Show evolution status
        if self.framework.alita_evolution:
            evolution_status = self.framework.alita_evolution.get_evolution_status()
            logger.info(f"Evolution Status: {json.dumps(evolution_status, indent=2)}")
        
        return result
    
    async def _demo_agent_collaboration(self):
        """Demo 3: Agent collaboration using A2A protocol"""
        logger.info("\n=== Demo 3: Agent Collaboration ===")
        
        # Simulate collaboration with another agent
        collaboration_result = await self.framework.create_agent_collaboration(
            target_agent_id="financial_agent_002",
            task="Provide real-time stock market data for AAPL, GOOGL, MSFT"
        )
        
        logger.info(f"Collaboration Result: {json.dumps(collaboration_result, indent=2)}")
        
        # Show agent card
        if self.framework.core_protocol.agent_card:
            agent_card = self.framework.core_protocol.agent_card
            logger.info(f"Agent Card: {json.dumps(agent_card.to_json(), indent=2)}")
        
        return collaboration_result
    
    async def _demo_environment_isolation(self):
        """Demo 4: Environment isolation for generated tools"""
        logger.info("\n=== Demo 4: Environment Isolation ===")
        
        if not self.framework.environment_manager:
            logger.info("Environment isolation not enabled")
            return
        
        # Create a test environment
        environment = await self.framework.environment_manager.create_environment(
            name="demo_env",
            python_version="3.9",
            dependencies=["requests", "beautifulsoup4"]
        )
        
        logger.info(f"Created environment: {environment.name}")
        
        # Show environment status
        env_status = self.framework.environment_manager.get_environment_status(environment.id)
        logger.info(f"Environment Status: {json.dumps(env_status, indent=2)}")
        
        return environment
    
    async def _demo_mcp_box_reusability(self):
        """Demo 5: MCP Box reusability system"""
        logger.info("\n=== Demo 5: MCP Box Reusability ===")
        
        # Show current MCP box contents
        mcp_box_tools = list(self.framework.core_protocol.mcp_box.keys())
        logger.info(f"MCP Box Tools: {mcp_box_tools}")
        
        # Simulate adding a tool to MCP box
        reusable_tool = MCPTool(
            name="DataAnalysisTool",
            description="Advanced data analysis and statistics",
            tool_type=MCPToolType.FUNCTION,
            schema={"type": "object"},
            parameters={"data": "array", "analysis_type": "string"},
            returns={"type": "object"},
            metadata={"generated": True, "reusable": True}
        )
        
        self.framework.core_protocol.add_to_mcp_box(reusable_tool)
        
        logger.info(f"Added tool to MCP box: {reusable_tool.name}")
        logger.info(f"MCP Box size: {len(self.framework.core_protocol.mcp_box)}")
        
        return reusable_tool
    
    async def _show_framework_status(self):
        """Show comprehensive framework status"""
        logger.info("\n=== Framework Status ===")
        
        status = self.framework.get_framework_status()
        logger.info(f"Framework Status: {json.dumps(status, indent=2, default=str)}")

class StockInformationSystemDemo:
    """
    Implementation of the Stock Information System from the MCP × A2A paper
    Demonstrates multi-agent collaboration for financial data processing
    """
    
    def __init__(self):
        self.data_agent = None
        self.analysis_agent = None
        self.visualization_agent = None
    
    async def run_stock_demo(self):
        """Run the stock information system demo"""
        logger.info("\n=== Stock Information System Demo ===")
        
        try:
            # Initialize specialized agents
            await self._initialize_agents()
            
            # Simulate stock data workflow
            await self._simulate_stock_workflow()
            
        except Exception as e:
            logger.error(f"Stock demo failed: {e}")
        finally:
            await self._cleanup_agents()
    
    async def _initialize_agents(self):
        """Initialize specialized agents for stock system"""
        
        # Data Collection Agent
        data_config = IntegrationConfig(
            agent_id="stock_data_agent",
            agent_name="Stock Data Collector",
            enable_self_evolution=True
        )
        self.data_agent = MCPxA2AxALITAFramework(data_config)
        await self.data_agent.initialize()
        
        # Analysis Agent
        analysis_config = IntegrationConfig(
            agent_id="stock_analysis_agent",
            agent_name="Stock Data Analyzer",
            enable_self_evolution=True
        )
        self.analysis_agent = MCPxA2AxALITAFramework(analysis_config)
        await self.analysis_agent.initialize()
        
        # Visualization Agent
        viz_config = IntegrationConfig(
            agent_id="stock_viz_agent",
            agent_name="Stock Data Visualizer",
            enable_self_evolution=True
        )
        self.visualization_agent = MCPxA2AxALITAFramework(viz_config)
        await self.visualization_agent.initialize()
        
        logger.info("Stock system agents initialized")
    
    async def _simulate_stock_workflow(self):
        """Simulate the complete stock information workflow"""
        
        # Step 1: Data collection
        data_task = "Collect real-time stock prices for AAPL, GOOGL, MSFT from financial APIs"
        data_result = await self.data_agent.process_task(data_task)
        logger.info(f"Data Collection Result: {data_result['success']}")
        
        # Step 2: Data analysis
        analysis_task = "Analyze stock price trends and calculate technical indicators"
        analysis_result = await self.analysis_agent.process_task(analysis_task)
        logger.info(f"Analysis Result: {analysis_result['success']}")
        
        # Step 3: Visualization
        viz_task = "Create interactive charts and dashboards for stock data"
        viz_result = await self.visualization_agent.process_task(viz_task)
        logger.info(f"Visualization Result: {viz_result['success']}")
        
        # Step 4: Agent collaboration
        collaboration = await self.data_agent.create_agent_collaboration(
            target_agent_id="stock_analysis_agent",
            task="Share collected stock data for analysis"
        )
        logger.info(f"Collaboration established: {collaboration['collaboration_id']}")
    
    async def _cleanup_agents(self):
        """Cleanup all agents"""
        for agent in [self.data_agent, self.analysis_agent, self.visualization_agent]:
            if agent:
                await agent.shutdown()

async def main():
    """Main demo function"""
    logger.info("Starting MCP × A2A × ALITA Framework Demonstrations")
    
    # Run basic framework demo
    demo = FrameworkDemo()
    await demo.run_demo()
    
    # Run stock information system demo
    stock_demo = StockInformationSystemDemo()
    await stock_demo.run_stock_demo()
    
    logger.info("All demonstrations completed")

if __name__ == "__main__":
    asyncio.run(main())
