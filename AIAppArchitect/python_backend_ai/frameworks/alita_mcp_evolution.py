"""
ALITA MCP Evolution Module
Implements ALITA's approach to minimal predefinition and maximal self-evolution
for dynamic MCP creation and capability expansion

Key Components:
- MCP Brainstorming: Assess capability gaps and plan tool generation
- ScriptGeneratingTool: Generate Python scripts for new capabilities
- CodeRunningTool: Execute and validate generated code
- Environment Management: Isolated execution environments
"""

import json
import uuid
import asyncio
import logging
import subprocess
import tempfile
import os
import sys
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import ast
import importlib.util

from .mcp_a2a_core import MCPTool, MCPToolType, CoreProtocolLayer

logger = logging.getLogger(__name__)

@dataclass
class CapabilityGap:
    """Represents an identified capability gap"""
    id: str
    description: str
    priority: int  # 1-10, 10 being highest
    required_resources: List[str]
    estimated_complexity: str  # "low", "medium", "high"
    suggested_approach: str
    created_at: str

@dataclass
class GeneratedMCP:
    """Represents a dynamically generated MCP"""
    id: str
    name: str
    description: str
    source_code: str
    dependencies: List[str]
    test_results: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    created_at: str
    validated: bool = False

class MCPBrainstorming:
    """
    ALITA's MCP Brainstorming component
    Analyzes tasks and identifies capability gaps for tool generation
    """
    
    def __init__(self, core_protocol: CoreProtocolLayer):
        self.core_protocol = core_protocol
        self.capability_database = {}
        self.gap_history: List[CapabilityGap] = []
        
    async def analyze_task_requirements(self, task_description: str, context: Dict[str, Any]) -> List[CapabilityGap]:
        """Analyze task and identify capability gaps"""
        logger.info(f"Analyzing task requirements: {task_description[:100]}...")
        
        # Extract requirements using LLM-like analysis
        requirements = await self._extract_requirements(task_description, context)
        
        # Check against existing capabilities
        gaps = []
        for requirement in requirements:
            if not self._capability_exists(requirement):
                gap = CapabilityGap(
                    id=str(uuid.uuid4()),
                    description=requirement["description"],
                    priority=requirement.get("priority", 5),
                    required_resources=requirement.get("resources", []),
                    estimated_complexity=requirement.get("complexity", "medium"),
                    suggested_approach=requirement.get("approach", ""),
                    created_at=datetime.now().isoformat()
                )
                gaps.append(gap)
                self.gap_history.append(gap)
        
        logger.info(f"Identified {len(gaps)} capability gaps")
        return gaps
    
    async def _extract_requirements(self, task_description: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract detailed requirements from task description"""
        # This would use an LLM for sophisticated analysis
        # For now, implementing rule-based extraction
        
        requirements = []
        description_lower = task_description.lower()
        
        # Web-related requirements
        if any(term in description_lower for term in ["web", "website", "url", "scraping", "browser"]):
            requirements.append({
                "description": "Web browsing and scraping capability",
                "priority": 8,
                "resources": ["requests", "beautifulsoup4", "selenium"],
                "complexity": "medium",
                "approach": "HTTP client with HTML parsing"
            })
        
        # File processing requirements
        if any(term in description_lower for term in ["file", "document", "pdf", "excel", "csv"]):
            requirements.append({
                "description": "Document processing capability",
                "priority": 7,
                "resources": ["pandas", "openpyxl", "PyPDF2"],
                "complexity": "low",
                "approach": "File format specific libraries"
            })
        
        # Image processing requirements
        if any(term in description_lower for term in ["image", "photo", "picture", "visual"]):
            requirements.append({
                "description": "Image processing capability",
                "priority": 6,
                "resources": ["Pillow", "opencv-python", "matplotlib"],
                "complexity": "medium",
                "approach": "Computer vision libraries"
            })
        
        # API integration requirements
        if any(term in description_lower for term in ["api", "rest", "http", "endpoint"]):
            requirements.append({
                "description": "API integration capability",
                "priority": 9,
                "resources": ["requests", "aiohttp"],
                "complexity": "low",
                "approach": "HTTP client with authentication"
            })
        
        # Data analysis requirements
        if any(term in description_lower for term in ["analyze", "data", "statistics", "chart", "graph"]):
            requirements.append({
                "description": "Data analysis capability",
                "priority": 8,
                "resources": ["pandas", "numpy", "matplotlib", "seaborn"],
                "complexity": "medium",
                "approach": "Statistical analysis and visualization"
            })
        
        return requirements
    
    def _capability_exists(self, requirement: Dict[str, Any]) -> bool:
        """Check if capability already exists"""
        description = requirement["description"].lower()
        
        # Check existing MCP tools
        for tool_name, tool in self.core_protocol.mcp_tools.items():
            if any(keyword in tool.description.lower() for keyword in description.split()):
                return True
        
        # Check MCP box
        for tool_name, tool in self.core_protocol.mcp_box.items():
            if any(keyword in tool.description.lower() for keyword in description.split()):
                return True
        
        return False
    
    async def prioritize_gaps(self, gaps: List[CapabilityGap]) -> List[CapabilityGap]:
        """Prioritize capability gaps based on urgency and impact"""
        # Sort by priority (descending) and complexity (ascending for quick wins)
        complexity_weights = {"low": 1, "medium": 2, "high": 3}
        
        def priority_score(gap):
            complexity_penalty = complexity_weights.get(gap.estimated_complexity, 2)
            return gap.priority - (complexity_penalty * 0.5)
        
        return sorted(gaps, key=priority_score, reverse=True)

class ScriptGeneratingTool:
    """
    ALITA's Script Generating Tool
    Generates Python scripts for new MCP capabilities
    """
    
    def __init__(self):
        self.templates = self._load_templates()
        self.generated_scripts: Dict[str, GeneratedMCP] = {}
    
    def _load_templates(self) -> Dict[str, str]:
        """Load code templates for different capability types"""
        return {
            "web_scraping": '''
import requests
from bs4 import BeautifulSoup
from typing import Dict, Any, List
import json

class WebScrapingTool:
    """Generated web scraping MCP tool"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    async def scrape_url(self, url: str, selector: str = None) -> Dict[str, Any]:
        """Scrape content from URL"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            if selector:
                elements = soup.select(selector)
                content = [elem.get_text(strip=True) for elem in elements]
            else:
                content = soup.get_text(strip=True)
            
            return {
                "url": url,
                "content": content,
                "status_code": response.status_code,
                "success": True
            }
        except Exception as e:
            return {
                "url": url,
                "error": str(e),
                "success": False
            }
''',
            "file_processing": '''
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List
import json

class FileProcessingTool:
    """Generated file processing MCP tool"""
    
    def __init__(self):
        self.supported_formats = ['.csv', '.xlsx', '.json', '.txt']
    
    async def process_file(self, file_path: str, operation: str = "read") -> Dict[str, Any]:
        """Process file based on format and operation"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                return {"error": "File not found", "success": False}
            
            if path.suffix == '.csv':
                df = pd.read_csv(file_path)
                return {
                    "data": df.to_dict('records'),
                    "shape": df.shape,
                    "columns": df.columns.tolist(),
                    "success": True
                }
            elif path.suffix == '.xlsx':
                df = pd.read_excel(file_path)
                return {
                    "data": df.to_dict('records'),
                    "shape": df.shape,
                    "columns": df.columns.tolist(),
                    "success": True
                }
            elif path.suffix == '.json':
                with open(file_path, 'r') as f:
                    data = json.load(f)
                return {
                    "data": data,
                    "type": type(data).__name__,
                    "success": True
                }
            else:
                with open(file_path, 'r') as f:
                    content = f.read()
                return {
                    "content": content,
                    "size": len(content),
                    "success": True
                }
        except Exception as e:
            return {
                "error": str(e),
                "success": False
            }
''',
            "api_integration": '''
import aiohttp
import asyncio
from typing import Dict, Any, Optional
import json

class APIIntegrationTool:
    """Generated API integration MCP tool"""
    
    def __init__(self):
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def make_request(self, 
                          url: str, 
                          method: str = "GET", 
                          headers: Optional[Dict[str, str]] = None,
                          data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make HTTP request to API endpoint"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            kwargs = {
                "url": url,
                "headers": headers or {}
            }
            
            if data and method.upper() in ["POST", "PUT", "PATCH"]:
                kwargs["json"] = data
            
            async with self.session.request(method.upper(), **kwargs) as response:
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    response_data = await response.json()
                else:
                    response_data = await response.text()
                
                return {
                    "status_code": response.status,
                    "data": response_data,
                    "headers": dict(response.headers),
                    "success": response.status < 400
                }
        except Exception as e:
            return {
                "error": str(e),
                "success": False
            }
'''
        }
    
    async def generate_script(self, gap: CapabilityGap) -> GeneratedMCP:
        """Generate script for capability gap"""
        logger.info(f"Generating script for capability: {gap.description}")
        
        # Determine template based on gap description
        template_key = self._select_template(gap.description)
        template = self.templates.get(template_key, self.templates["api_integration"])
        
        # Customize template based on specific requirements
        customized_code = await self._customize_template(template, gap)
        
        generated_mcp = GeneratedMCP(
            id=str(uuid.uuid4()),
            name=self._generate_tool_name(gap.description),
            description=gap.description,
            source_code=customized_code,
            dependencies=gap.required_resources,
            test_results={},
            performance_metrics={},
            created_at=datetime.now().isoformat(),
            validated=False
        )
        
        self.generated_scripts[generated_mcp.id] = generated_mcp
        logger.info(f"Generated script: {generated_mcp.name}")
        
        return generated_mcp
    
    def _select_template(self, description: str) -> str:
        """Select appropriate template based on description"""
        description_lower = description.lower()
        
        if any(term in description_lower for term in ["web", "scraping", "browser"]):
            return "web_scraping"
        elif any(term in description_lower for term in ["file", "document", "csv", "excel"]):
            return "file_processing"
        else:
            return "api_integration"
    
    async def _customize_template(self, template: str, gap: CapabilityGap) -> str:
        """Customize template based on specific gap requirements"""
        # This would use LLM for sophisticated customization
        # For now, basic string replacement
        
        customizations = {
            "{{TOOL_NAME}}": self._generate_tool_name(gap.description),
            "{{DESCRIPTION}}": gap.description,
            "{{DEPENDENCIES}}": ", ".join(gap.required_resources)
        }
        
        customized = template
        for placeholder, value in customizations.items():
            customized = customized.replace(placeholder, value)
        
        return customized
    
    def _generate_tool_name(self, description: str) -> str:
        """Generate tool name from description"""
        # Simple name generation
        words = description.lower().split()
        name_words = [word for word in words if word not in ["capability", "tool", "the", "a", "an"]]
        return "".join(word.capitalize() for word in name_words[:3]) + "Tool"

class CodeRunningTool:
    """
    ALITA's Code Running Tool
    Executes and validates generated scripts in isolated environments
    """
    
    def __init__(self):
        self.execution_results: Dict[str, Dict[str, Any]] = {}
        self.temp_dir = tempfile.mkdtemp(prefix="alita_mcp_")
    
    async def execute_script(self, generated_mcp: GeneratedMCP) -> Dict[str, Any]:
        """Execute generated script in isolated environment"""
        logger.info(f"Executing script: {generated_mcp.name}")
        
        try:
            # Create temporary file for script
            script_path = Path(self.temp_dir) / f"{generated_mcp.name}.py"
            
            with open(script_path, 'w') as f:
                f.write(generated_mcp.source_code)
            
            # Validate syntax
            syntax_valid = await self._validate_syntax(script_path)
            if not syntax_valid:
                return {"success": False, "error": "Syntax validation failed"}
            
            # Install dependencies
            deps_installed = await self._install_dependencies(generated_mcp.dependencies)
            if not deps_installed:
                return {"success": False, "error": "Dependency installation failed"}
            
            # Run basic tests
            test_results = await self._run_tests(script_path, generated_mcp)
            
            execution_result = {
                "success": True,
                "syntax_valid": syntax_valid,
                "dependencies_installed": deps_installed,
                "test_results": test_results,
                "execution_time": datetime.now().isoformat()
            }
            
            self.execution_results[generated_mcp.id] = execution_result
            generated_mcp.test_results = test_results
            generated_mcp.validated = test_results.get("passed", False)
            
            logger.info(f"Script execution completed: {generated_mcp.name}")
            return execution_result
            
        except Exception as e:
            error_result = {
                "success": False,
                "error": str(e),
                "execution_time": datetime.now().isoformat()
            }
            self.execution_results[generated_mcp.id] = error_result
            return error_result
    
    async def _validate_syntax(self, script_path: Path) -> bool:
        """Validate Python syntax"""
        try:
            with open(script_path, 'r') as f:
                source = f.read()
            ast.parse(source)
            return True
        except SyntaxError as e:
            logger.error(f"Syntax error in {script_path}: {e}")
            return False
    
    async def _install_dependencies(self, dependencies: List[str]) -> bool:
        """Install required dependencies"""
        if not dependencies:
            return True
        
        try:
            for dep in dependencies:
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", dep],
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                if result.returncode != 0:
                    logger.error(f"Failed to install {dep}: {result.stderr}")
                    return False
            return True
        except Exception as e:
            logger.error(f"Dependency installation error: {e}")
            return False
    
    async def _run_tests(self, script_path: Path, generated_mcp: GeneratedMCP) -> Dict[str, Any]:
        """Run basic tests on generated script"""
        try:
            # Import the module
            spec = importlib.util.spec_from_file_location("test_module", script_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Basic instantiation test
            class_name = generated_mcp.name.replace("Tool", "") + "Tool"
            if hasattr(module, class_name):
                tool_class = getattr(module, class_name)
                tool_instance = tool_class()
                
                return {
                    "passed": True,
                    "instantiation": True,
                    "class_found": True,
                    "methods": [method for method in dir(tool_instance) if not method.startswith('_')]
                }
            else:
                return {
                    "passed": False,
                    "instantiation": False,
                    "class_found": False,
                    "error": f"Class {class_name} not found"
                }
        except Exception as e:
            return {
                "passed": False,
                "error": str(e)
            }
    
    def cleanup(self):
        """Cleanup temporary files"""
        import shutil
        try:
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

class ALITAMCPEvolution:
    """
    Main ALITA MCP Evolution orchestrator
    Coordinates brainstorming, generation, and validation
    """
    
    def __init__(self, core_protocol: CoreProtocolLayer):
        self.core_protocol = core_protocol
        self.brainstorming = MCPBrainstorming(core_protocol)
        self.script_generator = ScriptGeneratingTool()
        self.code_runner = CodeRunningTool()
        self.evolution_history: List[Dict[str, Any]] = []
    
    async def evolve_capabilities(self, task_description: str, context: Dict[str, Any] = None) -> List[MCPTool]:
        """Main evolution process"""
        logger.info("Starting capability evolution process")
        
        context = context or {}
        
        # Step 1: Brainstorming - identify gaps
        gaps = await self.brainstorming.analyze_task_requirements(task_description, context)
        
        if not gaps:
            logger.info("No capability gaps identified")
            return []
        
        # Step 2: Prioritize gaps
        prioritized_gaps = await self.brainstorming.prioritize_gaps(gaps)
        
        # Step 3: Generate and validate tools
        new_tools = []
        for gap in prioritized_gaps[:3]:  # Limit to top 3 gaps
            try:
                # Generate script
                generated_mcp = await self.script_generator.generate_script(gap)
                
                # Execute and validate
                execution_result = await self.code_runner.execute_script(generated_mcp)
                
                if execution_result["success"] and generated_mcp.validated:
                    # Convert to MCPTool
                    mcp_tool = MCPTool(
                        name=generated_mcp.name,
                        description=generated_mcp.description,
                        tool_type=MCPToolType.FUNCTION,
                        schema={"type": "object"},  # Would be generated from code analysis
                        parameters={"task": {"type": "string"}},
                        returns={"type": "object"},
                        metadata={
                            "generated": True,
                            "alita_id": generated_mcp.id,
                            "created_at": generated_mcp.created_at,
                            "dependencies": generated_mcp.dependencies
                        }
                    )
                    
                    # Register with core protocol
                    self.core_protocol.register_mcp_tool(mcp_tool)
                    self.core_protocol.add_to_mcp_box(mcp_tool)
                    
                    new_tools.append(mcp_tool)
                    
                    # Record evolution
                    self.evolution_history.append({
                        "gap_id": gap.id,
                        "tool_name": mcp_tool.name,
                        "success": True,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    logger.info(f"Successfully evolved capability: {mcp_tool.name}")
                
            except Exception as e:
                logger.error(f"Evolution failed for gap {gap.id}: {e}")
                self.evolution_history.append({
                    "gap_id": gap.id,
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        logger.info(f"Evolution complete. Generated {len(new_tools)} new tools")
        return new_tools
    
    def get_evolution_status(self) -> Dict[str, Any]:
        """Get evolution system status"""
        return {
            "total_evolutions": len(self.evolution_history),
            "successful_evolutions": len([h for h in self.evolution_history if h["success"]]),
            "generated_scripts": len(self.script_generator.generated_scripts),
            "capability_gaps": len(self.brainstorming.gap_history),
            "mcp_box_size": len(self.core_protocol.mcp_box)
        }
