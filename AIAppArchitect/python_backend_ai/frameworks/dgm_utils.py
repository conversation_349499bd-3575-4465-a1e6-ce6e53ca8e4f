"""
DGM Utility Functions
Supporting utilities for the Darwin Gödel Machine implementation
"""

import os
import json
import subprocess
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime


def load_json_file(file_path: str) -> Dict[str, Any]:
    """Load JSON file safely"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Failed to load JSON file {file_path}: {e}")
        return {}


def save_json_file(data: Dict[str, Any], file_path: str) -> bool:
    """Save data to JSON file safely"""
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        logging.error(f"Failed to save JSON file {file_path}: {e}")
        return False


def diff_versus_commit(git_dir: str, commit: str) -> str:
    """Get git diff versus a specific commit"""
    try:
        result = subprocess.run(
            ['git', 'diff', commit],
            cwd=git_dir,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        logging.error(f"Git diff failed: {e}")
        return ""


def reset_to_commit(git_dir: str, commit: str) -> bool:
    """Reset git repository to a specific commit"""
    try:
        subprocess.run(
            ['git', 'reset', '--hard', commit],
            cwd=git_dir,
            check=True,
            capture_output=True
        )
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"Git reset failed: {e}")
        return False


def apply_patch(git_dir: str, patch_str: str) -> bool:
    """Apply a patch to the git repository"""
    try:
        # Write patch to temporary file
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.patch', delete=False) as f:
            f.write(patch_str)
            patch_file = f.name
        
        # Apply patch
        subprocess.run(
            ['git', 'apply', patch_file],
            cwd=git_dir,
            check=True,
            capture_output=True
        )
        
        # Clean up
        os.unlink(patch_file)
        return True
        
    except subprocess.CalledProcessError as e:
        logging.error(f"Patch apply failed: {e}")
        return False
    except Exception as e:
        logging.error(f"Patch operation failed: {e}")
        return False


def get_git_commit_hash(git_dir: str) -> str:
    """Get current git commit hash"""
    try:
        result = subprocess.run(
            ['git', 'rev-parse', 'HEAD'],
            cwd=git_dir,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logging.error(f"Failed to get git commit hash: {e}")
        return "unknown"


def create_git_commit(git_dir: str, message: str) -> str:
    """Create a git commit with the given message"""
    try:
        # Add all changes
        subprocess.run(['git', 'add', '.'], cwd=git_dir, check=True)
        
        # Commit changes
        subprocess.run(
            ['git', 'commit', '-m', message],
            cwd=git_dir,
            check=True,
            capture_output=True
        )
        
        # Return new commit hash
        return get_git_commit_hash(git_dir)
        
    except subprocess.CalledProcessError as e:
        logging.error(f"Git commit failed: {e}")
        return ""


def is_compiled_self_improve(metadata: Dict[str, Any], num_swe_issues: List[int] = None, logger=None) -> bool:
    """Check if a self-improvement run compiled successfully"""
    try:
        # Check for errors
        if 'error' in metadata:
            if logger:
                logger.info(f"Run has error: {metadata.get('error')}")
            return False
        
        # Check if overall performance exists
        overall_perf = metadata.get('overall_performance', {})
        if not overall_perf:
            if logger:
                logger.info("No overall performance data")
            return False
        
        # Check if minimum number of instances were submitted
        submitted = overall_perf.get('total_submitted_instances', 0)
        if num_swe_issues and submitted < min(num_swe_issues) * 0.5:
            if logger:
                logger.info(f"Too few instances submitted: {submitted}")
            return False
        
        # Check if accuracy score exists and is reasonable
        accuracy = overall_perf.get('accuracy_score', 0.0)
        if accuracy < 0.0 or accuracy > 1.0:
            if logger:
                logger.info(f"Invalid accuracy score: {accuracy}")
            return False
        
        return True
        
    except Exception as e:
        if logger:
            logger.error(f"Error checking compilation: {e}")
        return False


def load_dgm_metadata(metadata_path: str, last_only: bool = False) -> Dict[str, Any]:
    """Load DGM metadata from JSONL file"""
    try:
        if not os.path.exists(metadata_path):
            return {} if last_only else []
        
        with open(metadata_path, 'r') as f:
            lines = f.readlines()
            
        if not lines:
            return {} if last_only else []
        
        if last_only:
            return json.loads(lines[-1].strip())
        else:
            return [json.loads(line.strip()) for line in lines if line.strip()]
            
    except Exception as e:
        logging.error(f"Failed to load DGM metadata: {e}")
        return {} if last_only else []


def save_dgm_metadata(metadata: Dict[str, Any], metadata_path: str) -> bool:
    """Save DGM metadata to JSONL file"""
    try:
        os.makedirs(os.path.dirname(metadata_path), exist_ok=True)
        
        with open(metadata_path, 'a') as f:
            f.write(json.dumps(metadata) + '\n')
        
        return True
        
    except Exception as e:
        logging.error(f"Failed to save DGM metadata: {e}")
        return False


def get_report_score(report: str) -> float:
    """Extract score from a test report"""
    try:
        # Simple heuristic - look for percentage or score patterns
        import re
        
        # Look for patterns like "85%" or "0.85" or "85/100"
        percentage_match = re.search(r'(\d+(?:\.\d+)?)%', report)
        if percentage_match:
            return float(percentage_match.group(1)) / 100.0
        
        decimal_match = re.search(r'score[:\s]+(\d+(?:\.\d+)?)', report, re.IGNORECASE)
        if decimal_match:
            score = float(decimal_match.group(1))
            return score if score <= 1.0 else score / 100.0
        
        fraction_match = re.search(r'(\d+)/(\d+)', report)
        if fraction_match:
            numerator = float(fraction_match.group(1))
            denominator = float(fraction_match.group(2))
            return numerator / denominator if denominator > 0 else 0.0
        
        # Default score if no pattern found
        return 0.5
        
    except Exception as e:
        logging.error(f"Failed to extract score from report: {e}")
        return 0.0


def msg_history_to_report(instance_id: str, msg_history: List[Dict], model: str = "claude") -> str:
    """Convert message history to a test report"""
    try:
        # Extract relevant information from message history
        report_parts = [
            f"Test Report for Instance: {instance_id}",
            f"Model: {model}",
            f"Timestamp: {datetime.now().isoformat()}",
            "=" * 50
        ]
        
        for i, msg in enumerate(msg_history):
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            
            if isinstance(content, list):
                # Handle structured content
                text_content = []
                for item in content:
                    if isinstance(item, dict) and 'text' in item:
                        text_content.append(item['text'])
                content = '\n'.join(text_content)
            
            report_parts.append(f"\n--- Message {i+1} ({role}) ---")
            report_parts.append(str(content)[:500] + "..." if len(str(content)) > 500 else str(content))
        
        return '\n'.join(report_parts)
        
    except Exception as e:
        logging.error(f"Failed to convert message history to report: {e}")
        return f"Error generating report for {instance_id}: {e}"


def score_tie_breaker(scores: List[float]) -> float:
    """Break ties in scores by returning the average"""
    if not scores:
        return 0.0
    return sum(scores) / len(scores)


def setup_workspace(workspace_dir: str, template_dir: str = None) -> bool:
    """Set up a workspace directory for DGM operations"""
    try:
        os.makedirs(workspace_dir, exist_ok=True)
        
        # Copy template if provided
        if template_dir and os.path.exists(template_dir):
            import shutil
            for item in os.listdir(template_dir):
                src = os.path.join(template_dir, item)
                dst = os.path.join(workspace_dir, item)
                if os.path.isdir(src):
                    shutil.copytree(src, dst, dirs_exist_ok=True)
                else:
                    shutil.copy2(src, dst)
        
        # Initialize git if not already initialized
        if not os.path.exists(os.path.join(workspace_dir, '.git')):
            subprocess.run(['git', 'init'], cwd=workspace_dir, check=True, capture_output=True)
            subprocess.run(['git', 'config', 'user.name', 'DGM Agent'], cwd=workspace_dir, check=True)
            subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], cwd=workspace_dir, check=True)
        
        return True
        
    except Exception as e:
        logging.error(f"Failed to setup workspace: {e}")
        return False
