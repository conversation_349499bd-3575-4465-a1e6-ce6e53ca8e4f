"""
CrewAI Engine - Production Implementation
Multi-agent collaboration using CrewAI framework
"""

import asyncio
import uuid
import random
import math
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import structlog
import numpy as np
from datetime import datetime, timedelta

logger = structlog.get_logger(__name__)


class SwarmRole(str, Enum):
    """Roles within a swarm"""
    LEADER = "leader"
    SPECIALIST = "specialist"
    COORDINATOR = "coordinator"
    EXECUTOR = "executor"
    SCOUT = "scout"
    VALIDATOR = "validator"


class SwarmBehavior(str, Enum):
    """Types of swarm behaviors"""
    COLLABORATIVE = "collaborative"
    COMPETITIVE = "competitive"
    HIERARCHICAL = "hierarchical"
    EMERGENT = "emergent"
    ADAPTIVE = "adaptive"


@dataclass
class SwarmAgent:
    """Agent within a swarm"""
    agent_id: str
    role: SwarmRole
    specialization: str
    capabilities: List[str] = field(default_factory=list)
    position: Tuple[float, float] = (0.0, 0.0)  # Virtual position in swarm space
    velocity: Tuple[float, float] = (0.0, 0.0)  # Movement vector
    fitness: float = 0.0
    collaboration_score: float = 0.0
    task_history: List[str] = field(default_factory=list)
    connections: Set[str] = field(default_factory=set)  # Connected agent IDs
    last_activity: datetime = field(default_factory=datetime.now)


@dataclass
class SwarmTask:
    """Task assigned to a swarm"""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    description: str = ""
    complexity: float = 1.0
    required_specializations: List[str] = field(default_factory=list)
    deadline: Optional[datetime] = None
    priority: int = 1
    decomposed_subtasks: List[str] = field(default_factory=list)
    assigned_agents: List[str] = field(default_factory=list)
    status: str = "pending"
    result: Optional[Any] = None


@dataclass
class EmergentBehavior:
    """Detected emergent behavior in swarm"""
    behavior_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    behavior_type: str = ""
    description: str = ""
    participating_agents: List[str] = field(default_factory=list)
    strength: float = 0.0
    duration: timedelta = field(default_factory=lambda: timedelta(seconds=0))
    detected_at: datetime = field(default_factory=datetime.now)
    patterns: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SwarmMetrics:
    """Swarm performance metrics"""
    total_agents: int = 0
    active_agents: int = 0
    collaboration_efficiency: float = 0.0
    task_completion_rate: float = 0.0
    emergent_behaviors_detected: int = 0
    average_fitness: float = 0.0
    network_density: float = 0.0
    adaptation_rate: float = 0.0


class ParticleSwarmOptimizer:
    """Particle Swarm Optimization for agent coordination"""
    
    def __init__(self):
        self.w = 0.7  # Inertia weight
        self.c1 = 1.5  # Cognitive parameter
        self.c2 = 1.5  # Social parameter
        self.max_velocity = 2.0
        self.search_space_bounds = (-10.0, 10.0)

    async def optimize_swarm_positions(
        self, 
        agents: List[SwarmAgent], 
        objective_function: callable
    ) -> List[SwarmAgent]:
        """Optimize agent positions using PSO"""
        logger.info("Starting PSO optimization", agent_count=len(agents))
        
        # Find global best position
        global_best_agent = max(agents, key=lambda a: a.fitness)
        global_best_position = global_best_agent.position
        
        optimized_agents = []
        
        for agent in agents:
            # Update velocity
            r1, r2 = random.random(), random.random()
            
            # Personal best (agent's current position if it's the best it has seen)
            personal_best = agent.position
            
            # Calculate new velocity
            new_velocity = (
                self.w * agent.velocity[0] + 
                self.c1 * r1 * (personal_best[0] - agent.position[0]) +
                self.c2 * r2 * (global_best_position[0] - agent.position[0]),
                self.w * agent.velocity[1] + 
                self.c1 * r1 * (personal_best[1] - agent.position[1]) +
                self.c2 * r2 * (global_best_position[1] - agent.position[1])
            )
            
            # Limit velocity
            new_velocity = (
                max(-self.max_velocity, min(self.max_velocity, new_velocity[0])),
                max(-self.max_velocity, min(self.max_velocity, new_velocity[1]))
            )
            
            # Update position
            new_position = (
                agent.position[0] + new_velocity[0],
                agent.position[1] + new_velocity[1]
            )
            
            # Keep within bounds
            new_position = (
                max(self.search_space_bounds[0], min(self.search_space_bounds[1], new_position[0])),
                max(self.search_space_bounds[0], min(self.search_space_bounds[1], new_position[1]))
            )
            
            # Update agent
            optimized_agent = SwarmAgent(
                agent_id=agent.agent_id,
                role=agent.role,
                specialization=agent.specialization,
                capabilities=agent.capabilities.copy(),
                position=new_position,
                velocity=new_velocity,
                fitness=await objective_function(new_position),
                collaboration_score=agent.collaboration_score,
                task_history=agent.task_history.copy(),
                connections=agent.connections.copy(),
                last_activity=datetime.now()
            )
            
            optimized_agents.append(optimized_agent)
        
        logger.info("PSO optimization completed")
        return optimized_agents


class EmergentBehaviorDetector:
    """Detects emergent behaviors in swarm"""
    
    def __init__(self):
        self.behavior_patterns = {
            "clustering": self._detect_clustering,
            "leadership_emergence": self._detect_leadership,
            "specialization_drift": self._detect_specialization_drift,
            "collective_intelligence": self._detect_collective_intelligence,
            "adaptive_coordination": self._detect_adaptive_coordination
        }

    async def detect_emergent_behaviors(
        self, 
        agents: List[SwarmAgent], 
        task_history: List[SwarmTask]
    ) -> List[EmergentBehavior]:
        """Detect emergent behaviors in the swarm"""
        logger.info("Detecting emergent behaviors", agent_count=len(agents))
        
        detected_behaviors = []
        
        for behavior_type, detector in self.behavior_patterns.items():
            try:
                behavior = await detector(agents, task_history)
                if behavior:
                    detected_behaviors.append(behavior)
            except Exception as e:
                logger.warning("Behavior detection failed", 
                             behavior_type=behavior_type, error=str(e))
        
        logger.info("Emergent behavior detection completed", 
                   behaviors_detected=len(detected_behaviors))
        
        return detected_behaviors

    async def _detect_clustering(
        self, 
        agents: List[SwarmAgent], 
        task_history: List[SwarmTask]
    ) -> Optional[EmergentBehavior]:
        """Detect clustering behavior"""
        if len(agents) < 3:
            return None
        
        # Calculate average distances between agents
        positions = [agent.position for agent in agents]
        distances = []
        
        for i in range(len(positions)):
            for j in range(i + 1, len(positions)):
                dist = math.sqrt(
                    (positions[i][0] - positions[j][0]) ** 2 + 
                    (positions[i][1] - positions[j][1]) ** 2
                )
                distances.append(dist)
        
        avg_distance = sum(distances) / len(distances)
        
        # Detect clusters (agents closer than average)
        clusters = []
        for i, agent in enumerate(agents):
            cluster = [agent.agent_id]
            for j, other_agent in enumerate(agents):
                if i != j:
                    dist = math.sqrt(
                        (agent.position[0] - other_agent.position[0]) ** 2 + 
                        (agent.position[1] - other_agent.position[1]) ** 2
                    )
                    if dist < avg_distance * 0.5:  # Threshold for clustering
                        cluster.append(other_agent.agent_id)
            
            if len(cluster) > 2:  # Significant cluster
                clusters.append(cluster)
        
        if clusters:
            return EmergentBehavior(
                behavior_type="clustering",
                description=f"Detected {len(clusters)} agent clusters",
                participating_agents=[agent_id for cluster in clusters for agent_id in cluster],
                strength=len(clusters) / len(agents),
                patterns={"clusters": clusters, "avg_distance": avg_distance}
            )
        
        return None

    async def _detect_leadership(
        self, 
        agents: List[SwarmAgent], 
        task_history: List[SwarmTask]
    ) -> Optional[EmergentBehavior]:
        """Detect leadership emergence"""
        # Analyze connection patterns and influence
        influence_scores = {}
        
        for agent in agents:
            # Calculate influence based on connections and task participation
            connection_score = len(agent.connections) / max(1, len(agents) - 1)
            task_participation = len(agent.task_history) / max(1, len(task_history))
            fitness_score = agent.fitness
            
            influence_scores[agent.agent_id] = (
                connection_score * 0.4 + 
                task_participation * 0.4 + 
                fitness_score * 0.2
            )
        
        # Find potential leaders (top 20% by influence)
        sorted_agents = sorted(influence_scores.items(), key=lambda x: x[1], reverse=True)
        leader_threshold = len(agents) * 0.2
        potential_leaders = [agent_id for agent_id, score in sorted_agents[:int(leader_threshold)]]
        
        if potential_leaders and sorted_agents[0][1] > 0.7:  # Strong leadership
            return EmergentBehavior(
                behavior_type="leadership_emergence",
                description=f"Leadership emerged with {len(potential_leaders)} leaders",
                participating_agents=potential_leaders,
                strength=sorted_agents[0][1],
                patterns={"influence_scores": influence_scores}
            )
        
        return None

    async def _detect_specialization_drift(
        self, 
        agents: List[SwarmAgent], 
        task_history: List[SwarmTask]
    ) -> Optional[EmergentBehavior]:
        """Detect specialization drift"""
        # Analyze if agents are developing new specializations
        specialization_changes = []
        
        for agent in agents:
            # Check if agent is taking on tasks outside their specialization
            non_specialized_tasks = 0
            for task_id in agent.task_history[-10:]:  # Last 10 tasks
                task = next((t for t in task_history if t.task_id == task_id), None)
                if task and agent.specialization not in task.required_specializations:
                    non_specialized_tasks += 1
            
            if non_specialized_tasks > 3:  # Significant drift
                specialization_changes.append(agent.agent_id)
        
        if specialization_changes:
            return EmergentBehavior(
                behavior_type="specialization_drift",
                description=f"{len(specialization_changes)} agents showing specialization drift",
                participating_agents=specialization_changes,
                strength=len(specialization_changes) / len(agents),
                patterns={"drifting_agents": specialization_changes}
            )
        
        return None

    async def _detect_collective_intelligence(
        self, 
        agents: List[SwarmAgent], 
        task_history: List[SwarmTask]
    ) -> Optional[EmergentBehavior]:
        """Detect collective intelligence emergence"""
        # Analyze collaborative problem-solving patterns
        collaborative_tasks = [
            task for task in task_history 
            if len(task.assigned_agents) > 1 and task.status == "completed"
        ]
        
        if not collaborative_tasks:
            return None
        
        # Calculate collective performance vs individual performance
        collective_success_rate = len([t for t in collaborative_tasks if t.result]) / len(collaborative_tasks)
        
        # Compare with individual task performance
        individual_tasks = [
            task for task in task_history 
            if len(task.assigned_agents) == 1 and task.status == "completed"
        ]
        
        if individual_tasks:
            individual_success_rate = len([t for t in individual_tasks if t.result]) / len(individual_tasks)
            
            if collective_success_rate > individual_success_rate * 1.2:  # 20% improvement
                participating_agents = list(set(
                    agent_id for task in collaborative_tasks 
                    for agent_id in task.assigned_agents
                ))
                
                return EmergentBehavior(
                    behavior_type="collective_intelligence",
                    description="Collective intelligence emerged - group performance exceeds individual",
                    participating_agents=participating_agents,
                    strength=collective_success_rate - individual_success_rate,
                    patterns={
                        "collective_success_rate": collective_success_rate,
                        "individual_success_rate": individual_success_rate
                    }
                )
        
        return None

    async def _detect_adaptive_coordination(
        self, 
        agents: List[SwarmAgent], 
        task_history: List[SwarmTask]
    ) -> Optional[EmergentBehavior]:
        """Detect adaptive coordination patterns"""
        # Analyze how agents adapt their coordination based on task complexity
        if len(task_history) < 10:
            return None
        
        recent_tasks = task_history[-10:]
        coordination_adaptations = 0
        
        for task in recent_tasks:
            if len(task.assigned_agents) > 1:
                # Check if coordination pattern changed based on task complexity
                expected_agents = min(3, int(task.complexity * 2))  # Simple heuristic
                actual_agents = len(task.assigned_agents)
                
                if abs(actual_agents - expected_agents) <= 1:  # Good adaptation
                    coordination_adaptations += 1
        
        adaptation_rate = coordination_adaptations / len(recent_tasks)
        
        if adaptation_rate > 0.7:  # Good adaptive coordination
            participating_agents = list(set(
                agent_id for task in recent_tasks 
                for agent_id in task.assigned_agents
            ))
            
            return EmergentBehavior(
                behavior_type="adaptive_coordination",
                description="Adaptive coordination patterns detected",
                participating_agents=participating_agents,
                strength=adaptation_rate,
                patterns={"adaptation_rate": adaptation_rate}
            )
        
        return None


class SwarmIntelligenceEngine:
    """
    Main Swarm Intelligence Engine
    """
    
    def __init__(self):
        self.swarms: Dict[str, List[SwarmAgent]] = {}
        self.swarm_tasks: Dict[str, List[SwarmTask]] = {}
        self.pso_optimizer = ParticleSwarmOptimizer()
        self.behavior_detector = EmergentBehaviorDetector()
        self.detected_behaviors: List[EmergentBehavior] = []
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the swarm intelligence engine"""
        if self._initialized:
            return
        
        logger.info("Initializing Swarm Intelligence Engine...")
        
        # Initialize components
        # PSO and behavior detector are already initialized
        
        self._initialized = True
        logger.info("Swarm Intelligence Engine initialized successfully")

    async def create_swarm_for_task(self, task: Any) -> str:
        """Create a specialized swarm for a specific task"""
        swarm_id = str(uuid.uuid4())
        
        # Analyze task to determine required specializations
        required_specializations = await self._analyze_task_requirements(task)
        
        # Create swarm agents
        swarm_agents = []
        for i, specialization in enumerate(required_specializations):
            agent = SwarmAgent(
                agent_id=f"swarm_{swarm_id}_agent_{i}",
                role=self._assign_role(i, len(required_specializations)),
                specialization=specialization,
                capabilities=await self._get_capabilities_for_specialization(specialization),
                position=(random.uniform(-5, 5), random.uniform(-5, 5)),
                velocity=(0.0, 0.0),
                fitness=0.5  # Initial fitness
            )
            swarm_agents.append(agent)
        
        # Establish initial connections
        await self._establish_connections(swarm_agents)
        
        # Store swarm
        self.swarms[swarm_id] = swarm_agents
        self.swarm_tasks[swarm_id] = []
        
        logger.info("Swarm created", 
                   swarm_id=swarm_id, 
                   agent_count=len(swarm_agents),
                   specializations=required_specializations)
        
        return swarm_id

    async def _analyze_task_requirements(self, task: Any) -> List[str]:
        """Analyze task to determine required specializations"""
        # Simple heuristic - in production, this would use AI analysis
        task_description = getattr(task, 'description', str(task)).lower()
        
        specializations = []
        
        if any(keyword in task_description for keyword in ['code', 'implement', 'develop', 'program']):
            specializations.append('code_generation')
        
        if any(keyword in task_description for keyword in ['test', 'verify', 'validate']):
            specializations.append('testing')
        
        if any(keyword in task_description for keyword in ['document', 'readme', 'docs']):
            specializations.append('documentation')
        
        if any(keyword in task_description for keyword in ['review', 'analyze', 'check']):
            specializations.append('review')
        
        if any(keyword in task_description for keyword in ['architecture', 'design', 'structure']):
            specializations.append('architecture')
        
        if any(keyword in task_description for keyword in ['security', 'secure', 'vulnerability']):
            specializations.append('security')
        
        if any(keyword in task_description for keyword in ['performance', 'optimize', 'speed']):
            specializations.append('performance')
        
        # Ensure at least one specialization
        if not specializations:
            specializations = ['code_generation']
        
        return specializations

    def _assign_role(self, index: int, total_agents: int) -> SwarmRole:
        """Assign role based on position in swarm"""
        if index == 0:
            return SwarmRole.LEADER
        elif index == total_agents - 1:
            return SwarmRole.VALIDATOR
        elif index % 3 == 0:
            return SwarmRole.COORDINATOR
        elif index % 3 == 1:
            return SwarmRole.SPECIALIST
        else:
            return SwarmRole.EXECUTOR

    async def _get_capabilities_for_specialization(self, specialization: str) -> List[str]:
        """Get capabilities for a specialization"""
        capability_map = {
            'code_generation': ['python', 'javascript', 'typescript', 'rust', 'code_analysis'],
            'testing': ['unit_testing', 'integration_testing', 'test_automation', 'quality_assurance'],
            'documentation': ['technical_writing', 'api_documentation', 'user_guides', 'markdown'],
            'review': ['code_review', 'security_analysis', 'best_practices', 'quality_assessment'],
            'architecture': ['system_design', 'patterns', 'scalability', 'microservices'],
            'security': ['vulnerability_assessment', 'secure_coding', 'penetration_testing', 'compliance'],
            'performance': ['optimization', 'profiling', 'benchmarking', 'scalability_analysis']
        }
        
        return capability_map.get(specialization, ['general_purpose'])

    async def _establish_connections(self, agents: List[SwarmAgent]) -> None:
        """Establish initial connections between agents"""
        for i, agent in enumerate(agents):
            # Connect to adjacent agents and leader
            connections = set()
            
            # Connect to leader (first agent)
            if i != 0:
                connections.add(agents[0].agent_id)
            
            # Connect to adjacent agents
            if i > 0:
                connections.add(agents[i - 1].agent_id)
            if i < len(agents) - 1:
                connections.add(agents[i + 1].agent_id)
            
            agent.connections = connections

    async def optimize_swarm(self, swarm_id: str) -> None:
        """Optimize swarm using PSO"""
        if swarm_id not in self.swarms:
            return
        
        agents = self.swarms[swarm_id]
        
        # Define objective function (maximize collaboration and task completion)
        async def objective_function(position: Tuple[float, float]) -> float:
            # Simple fitness function based on position
            # In production, this would consider task performance, collaboration, etc.
            x, y = position
            return 1.0 / (1.0 + x*x + y*y)  # Higher fitness closer to origin
        
        # Optimize swarm positions
        optimized_agents = await self.pso_optimizer.optimize_swarm_positions(
            agents, objective_function
        )
        
        self.swarms[swarm_id] = optimized_agents
        logger.info("Swarm optimized", swarm_id=swarm_id)

    async def detect_emergent_behaviors(self, swarm_id: str) -> List[EmergentBehavior]:
        """Detect emergent behaviors in a swarm"""
        if swarm_id not in self.swarms:
            return []
        
        agents = self.swarms[swarm_id]
        task_history = self.swarm_tasks.get(swarm_id, [])
        
        behaviors = await self.behavior_detector.detect_emergent_behaviors(
            agents, task_history
        )
        
        self.detected_behaviors.extend(behaviors)
        
        logger.info("Emergent behaviors detected", 
                   swarm_id=swarm_id, 
                   behavior_count=len(behaviors))
        
        return behaviors

    async def get_swarm_metrics(self, swarm_id: str) -> SwarmMetrics:
        """Get metrics for a specific swarm"""
        if swarm_id not in self.swarms:
            return SwarmMetrics()
        
        agents = self.swarms[swarm_id]
        tasks = self.swarm_tasks.get(swarm_id, [])
        
        # Calculate metrics
        active_agents = len([a for a in agents if a.last_activity > datetime.now() - timedelta(hours=1)])
        avg_fitness = sum(a.fitness for a in agents) / len(agents) if agents else 0.0
        
        # Calculate collaboration efficiency
        total_connections = sum(len(a.connections) for a in agents)
        max_connections = len(agents) * (len(agents) - 1)
        network_density = total_connections / max_connections if max_connections > 0 else 0.0
        
        # Task completion rate
        completed_tasks = len([t for t in tasks if t.status == "completed"])
        completion_rate = completed_tasks / len(tasks) if tasks else 0.0
        
        return SwarmMetrics(
            total_agents=len(agents),
            active_agents=active_agents,
            collaboration_efficiency=network_density,
            task_completion_rate=completion_rate,
            emergent_behaviors_detected=len(self.detected_behaviors),
            average_fitness=avg_fitness,
            network_density=network_density,
            adaptation_rate=0.8  # Placeholder
        )

    async def enable(self) -> None:
        """Enable swarm intelligence"""
        await self.initialize()
        logger.info("Swarm Intelligence enabled")

    async def shutdown(self) -> None:
        """Shutdown the swarm intelligence engine"""
        logger.info("Shutting down Swarm Intelligence Engine...")
        
        # Clean up swarms
        self.swarms.clear()
        self.swarm_tasks.clear()
        self.detected_behaviors.clear()
        
        self._initialized = False
        logger.info("Swarm Intelligence Engine shutdown complete")
