"""
AG2 Engine - Production Implementation
Microsoft's AutoGen successor for multi-agent conversations
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import structlog

# AG2 imports
try:
    import ag2
    from ag2 import ConversableAgent, GroupChat, GroupChatManager
    from ag2.coding import LocalCommandLineCodeExecutor
    AG2_AVAILABLE = True
except ImportError:
    AG2_AVAILABLE = False
    logger.warning("AG2 not available, using fallback implementation")

logger = structlog.get_logger(__name__)


@dataclass
class AG2Config:
    """Configuration for AG2 engine"""
    model_name: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4096
    timeout: int = 60
    max_rounds: int = 10
    code_execution_enabled: bool = True


@dataclass
class AgentRole:
    """Definition of an agent role in AG2"""
    name: str
    description: str
    system_message: str
    capabilities: List[str] = field(default_factory=list)
    tools: List[str] = field(default_factory=list)


class ConversationType(str, Enum):
    """Types of conversations in AG2"""
    TWO_AGENT = "two_agent"
    GROUP_CHAT = "group_chat"
    SEQUENTIAL = "sequential"
    HIERARCHICAL = "hierarchical"


class AG2Engine:
    """
    Production AG2 Engine for multi-agent conversations
    """
    
    def __init__(self, config: Optional[AG2Config] = None):
        self.config = config or AG2Config()
        self.agents: Dict[str, Any] = {}
        self.group_chats: Dict[str, Any] = {}
        self.code_executor = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the AG2 engine"""
        if self._initialized:
            return
        
        if not AG2_AVAILABLE:
            logger.warning("AG2 not available, using mock implementation")
            self._initialized = True
            return
        
        logger.info("Initializing AG2 Engine...")
        
        try:
            # Initialize code executor if enabled
            if self.config.code_execution_enabled:
                self.code_executor = LocalCommandLineCodeExecutor(
                    timeout=self.config.timeout,
                    work_dir="./ag2_workspace"
                )
            
            # Create default agent roles
            await self._create_default_agents()
            
            self._initialized = True
            logger.info("AG2 Engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize AG2 Engine", error=str(e))
            raise

    async def _create_default_agents(self) -> None:
        """Create default AG2 agents"""
        if not AG2_AVAILABLE:
            return
        
        # LLM configuration
        llm_config = {
            "model": self.config.model_name,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens,
        }
        
        # Software Engineer Agent
        engineer_agent = ConversableAgent(
            name="SoftwareEngineer",
            system_message="""
            You are a senior software engineer with expertise in multiple programming languages.
            Your role is to:
            1. Analyze requirements and design solutions
            2. Write clean, efficient, and well-documented code
            3. Follow best practices and design patterns
            4. Ensure code quality and maintainability
            
            When writing code, always include:
            - Proper error handling
            - Type hints (for Python)
            - Comprehensive comments
            - Unit tests when appropriate
            """,
            llm_config=llm_config,
            code_execution_config={"executor": self.code_executor} if self.code_executor else False,
            human_input_mode="NEVER"
        )
        
        # Code Reviewer Agent
        reviewer_agent = ConversableAgent(
            name="CodeReviewer",
            system_message="""
            You are an expert code reviewer with deep knowledge of software engineering best practices.
            Your role is to:
            1. Review code for quality, security, and performance
            2. Identify potential bugs and vulnerabilities
            3. Suggest improvements and optimizations
            4. Ensure adherence to coding standards
            
            Provide constructive feedback with specific suggestions for improvement.
            Focus on maintainability, readability, and robustness.
            """,
            llm_config=llm_config,
            human_input_mode="NEVER"
        )
        
        # Product Manager Agent
        pm_agent = ConversableAgent(
            name="ProductManager",
            system_message="""
            You are a product manager responsible for defining requirements and ensuring solutions meet business needs.
            Your role is to:
            1. Clarify requirements and acceptance criteria
            2. Prioritize features and functionality
            3. Ensure solutions align with business objectives
            4. Validate that implementations meet user needs
            
            Ask clarifying questions and provide detailed requirements.
            Focus on user experience and business value.
            """,
            llm_config=llm_config,
            human_input_mode="NEVER"
        )
        
        # QA Engineer Agent
        qa_agent = ConversableAgent(
            name="QAEngineer",
            system_message="""
            You are a QA engineer responsible for testing and quality assurance.
            Your role is to:
            1. Design comprehensive test strategies
            2. Create test cases and scenarios
            3. Identify edge cases and potential issues
            4. Ensure thorough test coverage
            
            Focus on both functional and non-functional testing.
            Consider security, performance, and usability aspects.
            """,
            llm_config=llm_config,
            human_input_mode="NEVER"
        )
        
        # DevOps Engineer Agent
        devops_agent = ConversableAgent(
            name="DevOpsEngineer",
            system_message="""
            You are a DevOps engineer responsible for deployment, infrastructure, and operations.
            Your role is to:
            1. Design deployment strategies
            2. Set up CI/CD pipelines
            3. Ensure scalability and reliability
            4. Monitor and maintain systems
            
            Focus on automation, security, and best practices.
            Consider containerization, orchestration, and cloud services.
            """,
            llm_config=llm_config,
            human_input_mode="NEVER"
        )
        
        # Store agents
        self.agents = {
            "software_engineer": engineer_agent,
            "code_reviewer": reviewer_agent,
            "product_manager": pm_agent,
            "qa_engineer": qa_agent,
            "devops_engineer": devops_agent
        }
        
        logger.info("Default AG2 agents created", agent_count=len(self.agents))

    async def create_two_agent_conversation(
        self, 
        agent1_name: str, 
        agent2_name: str,
        initial_message: str,
        max_turns: Optional[int] = None
    ) -> Dict[str, Any]:
        """Create a two-agent conversation"""
        if not self._initialized:
            await self.initialize()
        
        if not AG2_AVAILABLE:
            return await self._mock_conversation(agent1_name, agent2_name, initial_message)
        
        if agent1_name not in self.agents or agent2_name not in self.agents:
            raise ValueError("One or both agents not found")
        
        agent1 = self.agents[agent1_name]
        agent2 = self.agents[agent2_name]
        
        try:
            logger.info("Starting AG2 two-agent conversation",
                       agent1=agent1_name,
                       agent2=agent2_name,
                       message=initial_message[:100])
            
            # Start conversation
            chat_result = await asyncio.to_thread(
                agent1.initiate_chat,
                agent2,
                message=initial_message,
                max_turns=max_turns or self.config.max_rounds
            )
            
            logger.info("AG2 two-agent conversation completed",
                       agent1=agent1_name,
                       agent2=agent2_name,
                       turns=len(chat_result.chat_history))
            
            return {
                "success": True,
                "conversation_type": "two_agent",
                "participants": [agent1_name, agent2_name],
                "chat_history": chat_result.chat_history,
                "summary": chat_result.summary,
                "cost": getattr(chat_result, 'cost', None)
            }
            
        except Exception as e:
            logger.error("AG2 two-agent conversation failed",
                        agent1=agent1_name,
                        agent2=agent2_name,
                        error=str(e))
            
            return {
                "success": False,
                "error": str(e),
                "conversation_type": "two_agent",
                "participants": [agent1_name, agent2_name]
            }

    async def create_group_chat(
        self, 
        agent_names: List[str],
        initial_message: str,
        max_rounds: Optional[int] = None
    ) -> Dict[str, Any]:
        """Create a group chat with multiple agents"""
        if not self._initialized:
            await self.initialize()
        
        if not AG2_AVAILABLE:
            return await self._mock_group_chat(agent_names, initial_message)
        
        # Validate agents
        missing_agents = [name for name in agent_names if name not in self.agents]
        if missing_agents:
            raise ValueError(f"Agents not found: {missing_agents}")
        
        agents = [self.agents[name] for name in agent_names]
        
        try:
            logger.info("Starting AG2 group chat",
                       agents=agent_names,
                       message=initial_message[:100])
            
            # Create group chat
            group_chat = GroupChat(
                agents=agents,
                messages=[],
                max_round=max_rounds or self.config.max_rounds
            )
            
            # Create group chat manager
            manager = GroupChatManager(
                groupchat=group_chat,
                llm_config={
                    "model": self.config.model_name,
                    "temperature": self.config.temperature,
                }
            )
            
            # Start group conversation
            chat_result = await asyncio.to_thread(
                agents[0].initiate_chat,
                manager,
                message=initial_message
            )
            
            logger.info("AG2 group chat completed",
                       agents=agent_names,
                       rounds=len(group_chat.messages))
            
            return {
                "success": True,
                "conversation_type": "group_chat",
                "participants": agent_names,
                "chat_history": group_chat.messages,
                "summary": chat_result.summary,
                "rounds": len(group_chat.messages),
                "cost": getattr(chat_result, 'cost', None)
            }
            
        except Exception as e:
            logger.error("AG2 group chat failed",
                        agents=agent_names,
                        error=str(e))
            
            return {
                "success": False,
                "error": str(e),
                "conversation_type": "group_chat",
                "participants": agent_names
            }

    async def execute_software_development_workflow(
        self, 
        requirements: str,
        include_review: bool = True,
        include_testing: bool = True
    ) -> Dict[str, Any]:
        """Execute a complete software development workflow"""
        if not self._initialized:
            await self.initialize()
        
        logger.info("Starting AG2 software development workflow",
                   requirements=requirements[:100])
        
        try:
            # Phase 1: Requirements Analysis (PM + Engineer)
            requirements_result = await self.create_two_agent_conversation(
                "product_manager",
                "software_engineer",
                f"Analyze these requirements and create a detailed implementation plan: {requirements}",
                max_turns=4
            )
            
            if not requirements_result["success"]:
                return requirements_result
            
            # Phase 2: Code Implementation (Engineer)
            implementation_message = f"""
            Based on the requirements analysis, implement the solution:
            
            Requirements: {requirements}
            Analysis: {requirements_result.get('summary', 'See conversation history')}
            
            Provide complete, production-ready code with proper documentation.
            """
            
            # Phase 3: Code Review (if enabled)
            if include_review:
                review_result = await self.create_two_agent_conversation(
                    "software_engineer",
                    "code_reviewer",
                    implementation_message,
                    max_turns=6
                )
                
                if not review_result["success"]:
                    return review_result
            
            # Phase 4: Testing Strategy (if enabled)
            if include_testing:
                testing_result = await self.create_two_agent_conversation(
                    "software_engineer",
                    "qa_engineer",
                    f"Create comprehensive tests for this implementation: {implementation_message}",
                    max_turns=4
                )
                
                if not testing_result["success"]:
                    return testing_result
            
            # Compile results
            workflow_result = {
                "success": True,
                "workflow_type": "software_development",
                "phases": {
                    "requirements": requirements_result,
                }
            }
            
            if include_review:
                workflow_result["phases"]["review"] = review_result
            
            if include_testing:
                workflow_result["phases"]["testing"] = testing_result
            
            logger.info("AG2 software development workflow completed successfully")
            
            return workflow_result
            
        except Exception as e:
            logger.error("AG2 software development workflow failed", error=str(e))
            
            return {
                "success": False,
                "error": str(e),
                "workflow_type": "software_development"
            }

    async def _mock_conversation(
        self, 
        agent1_name: str, 
        agent2_name: str, 
        initial_message: str
    ) -> Dict[str, Any]:
        """Mock conversation when AG2 is not available"""
        logger.info("Using mock AG2 conversation", 
                   agent1=agent1_name, 
                   agent2=agent2_name)
        
        return {
            "success": True,
            "conversation_type": "two_agent",
            "participants": [agent1_name, agent2_name],
            "chat_history": [
                {"role": agent1_name, "content": initial_message},
                {"role": agent2_name, "content": f"Mock response from {agent2_name} - AG2 not available"}
            ],
            "summary": "Mock conversation - AG2 framework not available",
            "mock": True
        }

    async def _mock_group_chat(
        self, 
        agent_names: List[str], 
        initial_message: str
    ) -> Dict[str, Any]:
        """Mock group chat when AG2 is not available"""
        logger.info("Using mock AG2 group chat", agents=agent_names)
        
        chat_history = [{"role": "user", "content": initial_message}]
        
        for agent_name in agent_names:
            chat_history.append({
                "role": agent_name,
                "content": f"Mock response from {agent_name} - AG2 not available"
            })
        
        return {
            "success": True,
            "conversation_type": "group_chat",
            "participants": agent_names,
            "chat_history": chat_history,
            "summary": "Mock group chat - AG2 framework not available",
            "rounds": len(agent_names),
            "mock": True
        }

    async def create_custom_agent(
        self, 
        name: str, 
        system_message: str,
        capabilities: Optional[List[str]] = None
    ) -> str:
        """Create a custom AG2 agent"""
        if not AG2_AVAILABLE:
            logger.warning("Cannot create custom agent - AG2 not available")
            return f"mock_agent_{name}"
        
        try:
            llm_config = {
                "model": self.config.model_name,
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
            }
            
            agent = ConversableAgent(
                name=name,
                system_message=system_message,
                llm_config=llm_config,
                code_execution_config={"executor": self.code_executor} if self.code_executor else False,
                human_input_mode="NEVER"
            )
            
            self.agents[name] = agent
            
            logger.info("Custom AG2 agent created", name=name)
            return name
            
        except Exception as e:
            logger.error("Failed to create custom AG2 agent", name=name, error=str(e))
            raise

    async def list_agents(self) -> List[str]:
        """List available agents"""
        return list(self.agents.keys())

    async def get_agent_info(self, agent_name: str) -> Dict[str, Any]:
        """Get information about a specific agent"""
        if agent_name not in self.agents:
            raise ValueError(f"Agent '{agent_name}' not found")
        
        if not AG2_AVAILABLE:
            return {
                "name": agent_name,
                "type": "mock",
                "system_message": "Mock agent - AG2 not available"
            }
        
        agent = self.agents[agent_name]
        
        return {
            "name": agent_name,
            "type": "ag2_conversable",
            "system_message": agent.system_message,
            "llm_config": agent.llm_config,
            "code_execution_enabled": bool(agent.code_execution_config)
        }

    async def shutdown(self) -> None:
        """Shutdown the AG2 engine"""
        logger.info("Shutting down AG2 Engine...")
        
        self.agents.clear()
        self.group_chats.clear()
        
        if self.code_executor:
            # Clean up code executor if needed
            pass
        
        self._initialized = False
        
        logger.info("AG2 Engine shutdown complete")
