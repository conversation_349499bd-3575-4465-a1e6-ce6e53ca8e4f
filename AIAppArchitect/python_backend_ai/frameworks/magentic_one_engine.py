"""
Magentic-One Engine - Real Implementation
Based on Microsoft's Magentic-One framework from AutoGen
A generalist multi-agent system for solving complex tasks
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import structlog
from datetime import datetime

# Magentic-One imports (part of AutoGen)
try:
    from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
    from autogen_agentchat.teams import RoundRobinGroupChat
    from autogen_agentchat.conditions import TextMentionTermination
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    from autogen_ext.agents.web_surfer import MultimodalWebSurfer
    # Magentic-One specific imports
    from magentic_one import MagenticOneOrchestrator, MagenticOneAgent
    MAGENTIC_ONE_AVAILABLE = True
except ImportError:
    MAGENTIC_ONE_AVAILABLE = False
    logger.warning("Magentic-One not available, using fallback implementation")

logger = structlog.get_logger(__name__)


@dataclass
class MagenticOneConfig:
    """Configuration for Magentic-One engine"""
    model_name: str = "gpt-4o"
    temperature: float = 0.7
    max_tokens: int = 4096
    max_rounds: int = 10
    enable_web_browsing: bool = True
    enable_code_execution: bool = True
    enable_file_handling: bool = True


class AgentRole(str, Enum):
    """Magentic-One agent roles"""
    ORCHESTRATOR = "orchestrator"
    WEB_SURFER = "web_surfer"
    FILE_SURFER = "file_surfer"
    CODER = "coder"
    COMPUTER_TERMINAL = "computer_terminal"


@dataclass
class MagenticTask:
    """Task definition for Magentic-One"""
    id: str
    description: str
    status: str = "pending"  # pending, in_progress, completed, failed
    result: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    agent_interactions: List[Dict[str, Any]] = field(default_factory=list)
    artifacts: List[str] = field(default_factory=list)


class MagenticOneEngine:
    """
    Production Magentic-One Engine - Generalist multi-agent system
    Based on Microsoft's Magentic-One framework from AutoGen
    """
    
    def __init__(self, config: Optional[MagenticOneConfig] = None):
        self.config = config or MagenticOneConfig()
        self.agents: Dict[str, Any] = {}
        self.tasks: Dict[str, MagenticTask] = {}
        self.orchestrator = None
        self.team = None
        self.model_client = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the Magentic-One engine"""
        if self._initialized:
            return
        
        logger.info("Initializing Magentic-One Engine...")
        
        try:
            if MAGENTIC_ONE_AVAILABLE:
                await self._initialize_real_magentic_one()
            else:
                await self._initialize_fallback()
            
            self._initialized = True
            logger.info("Magentic-One Engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Magentic-One Engine", error=str(e))
            raise

    async def _initialize_real_magentic_one(self) -> None:
        """Initialize the real Magentic-One framework"""
        # Initialize model client
        self.model_client = OpenAIChatCompletionClient(
            model=self.config.model_name,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens
        )
        
        # Create Magentic-One agents
        await self._create_magentic_one_agents()
        
        # Create orchestrator
        self.orchestrator = MagenticOneOrchestrator(
            "orchestrator",
            model_client=self.model_client,
            agents=list(self.agents.values()),
            max_rounds=self.config.max_rounds
        )
        
        # Create team
        agent_list = [self.orchestrator] + list(self.agents.values())
        termination = TextMentionTermination("TERMINATE", sources=["orchestrator"])
        
        self.team = RoundRobinGroupChat(
            agents=agent_list,
            termination_condition=termination
        )

    async def _create_magentic_one_agents(self) -> None:
        """Create the specialized Magentic-One agents"""
        
        # Web Surfer Agent - for web browsing and information gathering
        if self.config.enable_web_browsing:
            web_surfer = MultimodalWebSurfer(
                "web_surfer",
                model_client=self.model_client,
                headless=True,  # Run in headless mode for production
                animate_actions=False
            )
            self.agents[AgentRole.WEB_SURFER] = web_surfer
        
        # File Surfer Agent - for file operations and document handling
        if self.config.enable_file_handling:
            file_surfer = AssistantAgent(
                "file_surfer",
                model_client=self.model_client,
                system_message="""You are a File Surfer agent specialized in file operations.
                Your capabilities include:
                - Reading and analyzing various file formats (PDF, Word, Excel, etc.)
                - Searching through file contents
                - Extracting information from documents
                - Organizing and managing files
                - Converting between file formats
                
                Always provide detailed analysis and clear summaries of file contents."""
            )
            self.agents[AgentRole.FILE_SURFER] = file_surfer
        
        # Coder Agent - for code generation and programming tasks
        if self.config.enable_code_execution:
            coder = AssistantAgent(
                "coder",
                model_client=self.model_client,
                system_message="""You are a Coder agent specialized in programming and software development.
                Your capabilities include:
                - Writing code in multiple programming languages
                - Debugging and fixing code issues
                - Code review and optimization
                - Creating scripts and automation tools
                - Explaining code functionality
                
                Always write clean, well-documented, and efficient code."""
            )
            self.agents[AgentRole.CODER] = coder
        
        # Computer Terminal Agent - for system operations and command execution
        computer_terminal = AssistantAgent(
            "computer_terminal",
            model_client=self.model_client,
            system_message="""You are a Computer Terminal agent specialized in system operations.
            Your capabilities include:
            - Executing system commands and scripts
            - Managing processes and services
            - File system operations
            - Network operations and diagnostics
            - System monitoring and analysis
            
            Always prioritize security and provide safe command recommendations."""
        )
        self.agents[AgentRole.COMPUTER_TERMINAL] = computer_terminal

    async def _initialize_fallback(self) -> None:
        """Initialize fallback implementation when Magentic-One is not available"""
        # Create mock agents
        self.agents = {
            AgentRole.WEB_SURFER: {"name": "web_surfer", "type": "mock"},
            AgentRole.FILE_SURFER: {"name": "file_surfer", "type": "mock"},
            AgentRole.CODER: {"name": "coder", "type": "mock"},
            AgentRole.COMPUTER_TERMINAL: {"name": "computer_terminal", "type": "mock"}
        }
        
        self.orchestrator = {"name": "orchestrator", "type": "mock"}

    async def execute_task(self, task_description: str) -> Dict[str, Any]:
        """Execute a task using the Magentic-One team"""
        if not self._initialized:
            await self.initialize()
        
        task_id = str(uuid.uuid4())
        task = MagenticTask(
            id=task_id,
            description=task_description,
            status="in_progress"
        )
        
        self.tasks[task_id] = task
        
        try:
            logger.info("Executing Magentic-One task", task_id=task_id, description=task_description[:100])
            
            if MAGENTIC_ONE_AVAILABLE:
                result = await self._execute_real_task(task)
            else:
                result = await self._execute_fallback_task(task)
            
            task.status = "completed"
            task.result = result
            task.completed_at = datetime.now()
            
            return {
                "success": True,
                "task_id": task_id,
                "result": result,
                "agents_used": list(self.agents.keys()),
                "interactions": len(task.agent_interactions)
            }
            
        except Exception as e:
            task.status = "failed"
            task.result = f"Error: {str(e)}"
            
            logger.error("Magentic-One task failed", task_id=task_id, error=str(e))
            
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e)
            }

    async def _execute_real_task(self, task: MagenticTask) -> str:
        """Execute task using real Magentic-One framework"""
        # Use the orchestrator to coordinate the task
        result = await self.orchestrator.run(task=task.description)
        
        # Extract interactions and artifacts
        if hasattr(result, 'messages'):
            for message in result.messages:
                task.agent_interactions.append({
                    "agent": getattr(message, 'source', 'unknown'),
                    "content": str(message.content),
                    "timestamp": datetime.now().isoformat()
                })
        
        return str(result)

    async def _execute_fallback_task(self, task: MagenticTask) -> str:
        """Execute task using fallback implementation"""
        # Simulate multi-agent collaboration
        agents_used = []
        
        # Determine which agents to use based on task content
        task_lower = task.description.lower()
        
        if any(word in task_lower for word in ["web", "search", "browse", "internet"]):
            agents_used.append("web_surfer")
        
        if any(word in task_lower for word in ["file", "document", "pdf", "read"]):
            agents_used.append("file_surfer")
        
        if any(word in task_lower for word in ["code", "program", "script", "develop"]):
            agents_used.append("coder")
        
        if any(word in task_lower for word in ["system", "command", "terminal", "execute"]):
            agents_used.append("computer_terminal")
        
        if not agents_used:
            agents_used = ["web_surfer", "coder"]  # Default agents
        
        # Simulate agent interactions
        for agent in agents_used:
            interaction = {
                "agent": agent,
                "content": f"Mock {agent} processing: {task.description}",
                "timestamp": datetime.now().isoformat()
            }
            task.agent_interactions.append(interaction)
        
        return f"Magentic-One team completed task using agents: {', '.join(agents_used)}. Task: {task.description}"

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of a specific task"""
        if task_id not in self.tasks:
            return {"error": f"Task '{task_id}' not found"}
        
        task = self.tasks[task_id]
        
        return {
            "task_id": task_id,
            "description": task.description,
            "status": task.status,
            "result": task.result,
            "created_at": task.created_at.isoformat(),
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "interactions": len(task.agent_interactions),
            "artifacts": len(task.artifacts)
        }

    async def list_agents(self) -> List[Dict[str, Any]]:
        """List all available agents"""
        if MAGENTIC_ONE_AVAILABLE:
            return [
                {
                    "role": role.value,
                    "name": agent.name if hasattr(agent, 'name') else str(agent),
                    "type": "real",
                    "capabilities": self._get_agent_capabilities(role)
                }
                for role, agent in self.agents.items()
            ]
        else:
            return [
                {
                    "role": role.value,
                    "name": agent["name"],
                    "type": "mock",
                    "capabilities": self._get_agent_capabilities(role)
                }
                for role, agent in self.agents.items()
            ]

    def _get_agent_capabilities(self, role: AgentRole) -> List[str]:
        """Get capabilities for each agent role"""
        capabilities_map = {
            AgentRole.WEB_SURFER: [
                "Web browsing and navigation",
                "Information gathering from websites",
                "Screenshot capture",
                "Form filling and interaction"
            ],
            AgentRole.FILE_SURFER: [
                "File reading and analysis",
                "Document processing",
                "File format conversion",
                "Content extraction"
            ],
            AgentRole.CODER: [
                "Code generation and editing",
                "Debugging and optimization",
                "Multiple programming languages",
                "Script automation"
            ],
            AgentRole.COMPUTER_TERMINAL: [
                "System command execution",
                "Process management",
                "File system operations",
                "Network diagnostics"
            ],
            AgentRole.ORCHESTRATOR: [
                "Task coordination",
                "Agent management",
                "Workflow orchestration",
                "Result synthesis"
            ]
        }
        
        return capabilities_map.get(role, [])

    async def get_engine_status(self) -> Dict[str, Any]:
        """Get the current status of the Magentic-One engine"""
        task_counts = {
            "pending": len([t for t in self.tasks.values() if t.status == "pending"]),
            "in_progress": len([t for t in self.tasks.values() if t.status == "in_progress"]),
            "completed": len([t for t in self.tasks.values() if t.status == "completed"]),
            "failed": len([t for t in self.tasks.values() if t.status == "failed"])
        }
        
        return {
            "initialized": self._initialized,
            "framework_available": MAGENTIC_ONE_AVAILABLE,
            "agents": len(self.agents),
            "task_counts": task_counts,
            "config": {
                "model_name": self.config.model_name,
                "max_rounds": self.config.max_rounds,
                "web_browsing": self.config.enable_web_browsing,
                "code_execution": self.config.enable_code_execution,
                "file_handling": self.config.enable_file_handling
            }
        }

    async def list_tasks(self) -> List[Dict[str, Any]]:
        """List all tasks"""
        return [
            {
                "task_id": task.id,
                "description": task.description[:100] + "..." if len(task.description) > 100 else task.description,
                "status": task.status,
                "created_at": task.created_at.isoformat(),
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "interactions": len(task.agent_interactions)
            }
            for task in self.tasks.values()
        ]

    async def shutdown(self) -> None:
        """Shutdown the Magentic-One engine"""
        logger.info("Shutting down Magentic-One Engine...")
        
        # Close model client if available
        if self.model_client and hasattr(self.model_client, 'close'):
            await self.model_client.close()
        
        # Close web surfer if available
        if AgentRole.WEB_SURFER in self.agents:
            web_surfer = self.agents[AgentRole.WEB_SURFER]
            if hasattr(web_surfer, 'close'):
                await web_surfer.close()
        
        self.agents.clear()
        self.tasks.clear()
        self.orchestrator = None
        self.team = None
        self.model_client = None
        self._initialized = False
        
        logger.info("Magentic-One Engine shutdown complete")
