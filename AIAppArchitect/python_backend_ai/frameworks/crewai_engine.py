"""
CrewAI Engine - Production Implementation
Multi-agent collaboration using CrewAI framework
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import structlog

# CrewAI imports
try:
    from crewai import Agent, Task, Crew, Process
    from crewai.tools import BaseTool
    from langchain_openai import ChatOpenAI
    from langchain_anthropic import ChatAnthropic
    CREWAI_AVAILABLE = True
except ImportError:
    CREWAI_AVAILABLE = False
    logger.warning("CrewAI not available, using fallback implementation")

logger = structlog.get_logger(__name__)


@dataclass
class CrewAIConfig:
    """Configuration for CrewAI engine"""
    model_name: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4096
    max_execution_time: int = 300
    verbose: bool = True


class ProcessType(str, Enum):
    """CrewAI process types"""
    SEQUENTIAL = "sequential"
    HIERARCHICAL = "hierarchical"


@dataclass
class AgentDefinition:
    """Definition for a CrewAI agent"""
    role: str
    goal: str
    backstory: str
    tools: List[str] = field(default_factory=list)
    allow_delegation: bool = True
    verbose: bool = True


@dataclass
class TaskDefinition:
    """Definition for a CrewAI task"""
    description: str
    expected_output: str
    agent_role: str
    tools: List[str] = field(default_factory=list)
    context: Optional[Dict[str, Any]] = None


class CrewAIEngine:
    """
    Production CrewAI Engine for multi-agent collaboration
    """
    
    def __init__(self, config: Optional[CrewAIConfig] = None):
        self.config = config or CrewAIConfig()
        self.agents: Dict[str, Any] = {}
        self.crews: Dict[str, Any] = {}
        self.tools: Dict[str, Any] = {}
        self.llm = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the CrewAI engine"""
        if self._initialized:
            return
        
        if not CREWAI_AVAILABLE:
            logger.warning("CrewAI not available, using mock implementation")
            self._initialized = True
            return
        
        logger.info("Initializing CrewAI Engine...")
        
        try:
            # Initialize LLM
            if "gpt" in self.config.model_name:
                self.llm = ChatOpenAI(
                    model=self.config.model_name,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens
                )
            elif "claude" in self.config.model_name:
                self.llm = ChatAnthropic(
                    model=self.config.model_name,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens
                )
            else:
                self.llm = ChatOpenAI(model="gpt-4-turbo-preview")
            
            # Initialize tools
            await self._initialize_tools()
            
            # Create default agents
            await self._create_default_agents()
            
            self._initialized = True
            logger.info("CrewAI Engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize CrewAI Engine", error=str(e))
            raise

    async def _initialize_tools(self) -> None:
        """Initialize CrewAI tools"""
        if not CREWAI_AVAILABLE:
            return
        
        class CodeAnalyzerTool(BaseTool):
            name: str = "code_analyzer"
            description: str = "Analyze code for quality, security, and performance issues"
            
            def _run(self, code: str) -> str:
                """Analyze the provided code"""
                issues = []
                
                # Basic code analysis
                if "eval(" in code:
                    issues.append("Security: Use of eval() function")
                if "exec(" in code:
                    issues.append("Security: Use of exec() function")
                if len(code.split('\n')) > 100:
                    issues.append("Maintainability: Function too long")
                if code.count("if ") > 10:
                    issues.append("Complexity: Too many conditional statements")
                
                return f"Code analysis results: {'; '.join(issues) if issues else 'No major issues found'}"
        
        class FileManagerTool(BaseTool):
            name: str = "file_manager"
            description: str = "Read and write files in the workspace"
            
            def _run(self, action: str, filename: str, content: str = "") -> str:
                """Perform file operations"""
                try:
                    if action == "read":
                        with open(filename, 'r') as f:
                            return f.read()
                    elif action == "write":
                        with open(filename, 'w') as f:
                            f.write(content)
                        return f"Successfully wrote to {filename}"
                    else:
                        return f"Unknown action: {action}"
                except Exception as e:
                    return f"Error: {str(e)}"
        
        class TestGeneratorTool(BaseTool):
            name: str = "test_generator"
            description: str = "Generate unit tests for code"
            
            def _run(self, code: str, test_framework: str = "unittest") -> str:
                """Generate tests for the provided code"""
                if test_framework == "unittest":
                    return f"""
import unittest

class TestGeneratedCode(unittest.TestCase):
    def test_functionality(self):
        # TODO: Implement specific test cases
        self.assertTrue(True)
    
    def test_edge_cases(self):
        # TODO: Test edge cases
        self.assertTrue(True)

if __name__ == '__main__':
    unittest.main()
"""
                else:
                    return f"Test framework {test_framework} not supported"
        
        self.tools = {
            "code_analyzer": CodeAnalyzerTool(),
            "file_manager": FileManagerTool(),
            "test_generator": TestGeneratorTool()
        }

    async def _create_default_agents(self) -> None:
        """Create default CrewAI agents"""
        if not CREWAI_AVAILABLE:
            return
        
        # Software Engineer Agent
        engineer = Agent(
            role="Senior Software Engineer",
            goal="Write high-quality, maintainable code that meets requirements",
            backstory="""You are a senior software engineer with 10+ years of experience.
            You excel at writing clean, efficient code and following best practices.
            You always consider security, performance, and maintainability.""",
            llm=self.llm,
            tools=list(self.tools.values()),
            allow_delegation=True,
            verbose=self.config.verbose
        )
        
        # Code Reviewer Agent
        reviewer = Agent(
            role="Code Reviewer",
            goal="Ensure code quality and identify potential issues",
            backstory="""You are an expert code reviewer with deep knowledge of software engineering.
            You have a keen eye for bugs, security vulnerabilities, and performance issues.
            You provide constructive feedback to improve code quality.""",
            llm=self.llm,
            tools=list(self.tools.values()),
            allow_delegation=False,
            verbose=self.config.verbose
        )
        
        # QA Engineer Agent
        qa_engineer = Agent(
            role="QA Engineer",
            goal="Create comprehensive tests and ensure software quality",
            backstory="""You are a QA engineer specializing in test automation and quality assurance.
            You design thorough test strategies and create comprehensive test suites.
            You focus on edge cases and potential failure scenarios.""",
            llm=self.llm,
            tools=list(self.tools.values()),
            allow_delegation=False,
            verbose=self.config.verbose
        )
        
        # Technical Writer Agent
        tech_writer = Agent(
            role="Technical Writer",
            goal="Create clear, comprehensive documentation",
            backstory="""You are a technical writer who specializes in software documentation.
            You excel at explaining complex technical concepts in clear, accessible language.
            You create documentation that helps developers and users understand the software.""",
            llm=self.llm,
            tools=list(self.tools.values()),
            allow_delegation=False,
            verbose=self.config.verbose
        )
        
        # DevOps Engineer Agent
        devops = Agent(
            role="DevOps Engineer",
            goal="Ensure reliable deployment and infrastructure",
            backstory="""You are a DevOps engineer with expertise in CI/CD, containerization, and cloud platforms.
            You focus on automation, scalability, and reliability.
            You ensure smooth deployment and operation of software systems.""",
            llm=self.llm,
            tools=list(self.tools.values()),
            allow_delegation=False,
            verbose=self.config.verbose
        )
        
        self.agents = {
            "software_engineer": engineer,
            "code_reviewer": reviewer,
            "qa_engineer": qa_engineer,
            "technical_writer": tech_writer,
            "devops_engineer": devops
        }
        
        logger.info("Default CrewAI agents created", agent_count=len(self.agents))

    async def create_crew(
        self, 
        crew_name: str,
        agent_roles: List[str],
        tasks: List[TaskDefinition],
        process_type: ProcessType = ProcessType.SEQUENTIAL
    ) -> str:
        """Create a new crew with specified agents and tasks"""
        if not self._initialized:
            await self.initialize()
        
        if not CREWAI_AVAILABLE:
            return await self._mock_crew_creation(crew_name, agent_roles, tasks)
        
        try:
            # Get agents for the crew
            crew_agents = []
            for role in agent_roles:
                if role not in self.agents:
                    raise ValueError(f"Agent role '{role}' not found")
                crew_agents.append(self.agents[role])
            
            # Create tasks
            crew_tasks = []
            for task_def in tasks:
                # Find the agent for this task
                task_agent = None
                for role, agent in self.agents.items():
                    if role == task_def.agent_role:
                        task_agent = agent
                        break
                
                if not task_agent:
                    raise ValueError(f"Agent role '{task_def.agent_role}' not found for task")
                
                # Get tools for the task
                task_tools = []
                for tool_name in task_def.tools:
                    if tool_name in self.tools:
                        task_tools.append(self.tools[tool_name])
                
                task = Task(
                    description=task_def.description,
                    expected_output=task_def.expected_output,
                    agent=task_agent,
                    tools=task_tools
                )
                crew_tasks.append(task)
            
            # Create the crew
            if process_type == ProcessType.HIERARCHICAL:
                crew = Crew(
                    agents=crew_agents,
                    tasks=crew_tasks,
                    process=Process.hierarchical,
                    manager_llm=self.llm,
                    verbose=self.config.verbose
                )
            else:
                crew = Crew(
                    agents=crew_agents,
                    tasks=crew_tasks,
                    process=Process.sequential,
                    verbose=self.config.verbose
                )
            
            self.crews[crew_name] = crew
            
            logger.info("CrewAI crew created", 
                       crew_name=crew_name,
                       agents=len(crew_agents),
                       tasks=len(crew_tasks),
                       process=process_type)
            
            return crew_name
            
        except Exception as e:
            logger.error("Failed to create CrewAI crew", 
                        crew_name=crew_name, 
                        error=str(e))
            raise

    async def execute_crew(self, crew_name: str, inputs: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Execute a crew and return results"""
        if crew_name not in self.crews:
            raise ValueError(f"Crew '{crew_name}' not found")
        
        if not CREWAI_AVAILABLE:
            return await self._mock_crew_execution(crew_name, inputs)
        
        crew = self.crews[crew_name]
        
        try:
            logger.info("Executing CrewAI crew", crew_name=crew_name)
            
            # Execute the crew
            result = await asyncio.to_thread(crew.kickoff, inputs=inputs or {})
            
            logger.info("CrewAI crew execution completed", 
                       crew_name=crew_name,
                       success=True)
            
            return {
                "success": True,
                "crew_name": crew_name,
                "result": str(result),
                "tasks_completed": len(crew.tasks),
                "agents_involved": len(crew.agents)
            }
            
        except Exception as e:
            logger.error("CrewAI crew execution failed", 
                        crew_name=crew_name, 
                        error=str(e))
            
            return {
                "success": False,
                "crew_name": crew_name,
                "error": str(e)
            }

    async def create_software_development_crew(self, project_description: str) -> str:
        """Create a specialized crew for software development"""
        tasks = [
            TaskDefinition(
                description=f"Analyze requirements and design architecture for: {project_description}",
                expected_output="Detailed architecture design and implementation plan",
                agent_role="software_engineer"
            ),
            TaskDefinition(
                description="Implement the solution based on the architecture design",
                expected_output="Complete, working code implementation",
                agent_role="software_engineer",
                tools=["file_manager"]
            ),
            TaskDefinition(
                description="Review the implemented code for quality and issues",
                expected_output="Code review report with feedback and suggestions",
                agent_role="code_reviewer",
                tools=["code_analyzer"]
            ),
            TaskDefinition(
                description="Create comprehensive tests for the implementation",
                expected_output="Complete test suite with unit and integration tests",
                agent_role="qa_engineer",
                tools=["test_generator"]
            ),
            TaskDefinition(
                description="Create documentation for the project",
                expected_output="Comprehensive project documentation",
                agent_role="technical_writer",
                tools=["file_manager"]
            )
        ]
        
        crew_name = f"dev_crew_{hash(project_description) % 10000}"
        
        return await self.create_crew(
            crew_name,
            ["software_engineer", "code_reviewer", "qa_engineer", "technical_writer"],
            tasks,
            ProcessType.SEQUENTIAL
        )

    async def _mock_crew_creation(
        self, 
        crew_name: str, 
        agent_roles: List[str], 
        tasks: List[TaskDefinition]
    ) -> str:
        """Mock crew creation when CrewAI is not available"""
        logger.info("Using mock CrewAI crew creation", crew_name=crew_name)
        
        self.crews[crew_name] = {
            "mock": True,
            "agents": agent_roles,
            "tasks": [task.description for task in tasks]
        }
        
        return crew_name

    async def _mock_crew_execution(
        self, 
        crew_name: str, 
        inputs: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Mock crew execution when CrewAI is not available"""
        logger.info("Using mock CrewAI crew execution", crew_name=crew_name)
        
        crew = self.crews[crew_name]
        
        return {
            "success": True,
            "crew_name": crew_name,
            "result": f"Mock execution result for {crew_name} - CrewAI not available",
            "tasks_completed": len(crew.get("tasks", [])),
            "agents_involved": len(crew.get("agents", [])),
            "mock": True
        }

    async def list_crews(self) -> List[str]:
        """List available crews"""
        return list(self.crews.keys())

    async def list_agents(self) -> List[str]:
        """List available agents"""
        return list(self.agents.keys())

    async def get_crew_info(self, crew_name: str) -> Dict[str, Any]:
        """Get information about a specific crew"""
        if crew_name not in self.crews:
            raise ValueError(f"Crew '{crew_name}' not found")
        
        crew = self.crews[crew_name]
        
        if not CREWAI_AVAILABLE or crew.get("mock"):
            return {
                "name": crew_name,
                "type": "mock",
                "agents": crew.get("agents", []),
                "tasks": crew.get("tasks", [])
            }
        
        return {
            "name": crew_name,
            "type": "crewai",
            "agents": [agent.role for agent in crew.agents],
            "tasks": [task.description for task in crew.tasks],
            "process": str(crew.process)
        }

    async def shutdown(self) -> None:
        """Shutdown the CrewAI engine"""
        logger.info("Shutting down CrewAI Engine...")
        
        self.agents.clear()
        self.crews.clear()
        self.tools.clear()
        self.llm = None
        self._initialized = False
        
        logger.info("CrewAI Engine shutdown complete")


# Alias for backward compatibility
SwarmIntelligenceEngine = CrewAIEngine
