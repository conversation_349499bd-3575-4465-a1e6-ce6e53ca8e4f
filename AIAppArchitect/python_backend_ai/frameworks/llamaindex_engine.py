"""
LlamaIndex Engine - Production Implementation
Advanced RAG and agent framework using LlamaIndex
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import structlog

# LlamaIndex imports
try:
    from llama_index.core import VectorStoreIndex, Document, Settings
    from llama_index.core.agent import ReActAgent
    from llama_index.core.tools import FunctionTool, QueryEngineTool
    from llama_index.core.memory import ChatMemoryBuffer
    from llama_index.llms.openai import OpenAI
    from llama_index.llms.anthropic import Anthropic
    from llama_index.embeddings.openai import OpenAIEmbedding
    from llama_index.core.node_parser import SentenceSplitter
    from llama_index.core.storage.storage_context import StorageContext
    from llama_index.vector_stores.chroma import ChromaVectorStore
    LLAMAINDEX_AVAILABLE = True
except ImportError:
    LLAMAINDEX_AVAILABLE = False
    logger.warning("LlamaIndex not available, using fallback implementation")

logger = structlog.get_logger(__name__)


@dataclass
class LlamaIndexConfig:
    """Configuration for LlamaIndex engine"""
    model_name: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4096
    embedding_model: str = "text-embedding-3-small"
    chunk_size: int = 1024
    chunk_overlap: int = 200
    similarity_top_k: int = 5


class AgentType(str, Enum):
    """Types of LlamaIndex agents"""
    REACT = "react"
    FUNCTION_CALLING = "function_calling"
    RAG = "rag"
    MULTI_MODAL = "multi_modal"


@dataclass
class DocumentSource:
    """Document source for RAG"""
    id: str
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    source_type: str = "text"


class LlamaIndexEngine:
    """
    Production LlamaIndex Engine for RAG and agent workflows
    """
    
    def __init__(self, config: Optional[LlamaIndexConfig] = None):
        self.config = config or LlamaIndexConfig()
        self.agents: Dict[str, Any] = {}
        self.indexes: Dict[str, Any] = {}
        self.tools: Dict[str, Any] = {}
        self.documents: Dict[str, DocumentSource] = {}
        self.llm = None
        self.embedding_model = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the LlamaIndex engine"""
        if self._initialized:
            return
        
        if not LLAMAINDEX_AVAILABLE:
            logger.warning("LlamaIndex not available, using mock implementation")
            self._initialized = True
            return
        
        logger.info("Initializing LlamaIndex Engine...")
        
        try:
            # Initialize LLM
            if "gpt" in self.config.model_name:
                self.llm = OpenAI(
                    model=self.config.model_name,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens
                )
            elif "claude" in self.config.model_name:
                self.llm = Anthropic(
                    model=self.config.model_name,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens
                )
            else:
                self.llm = OpenAI(model="gpt-4-turbo-preview")
            
            # Initialize embedding model
            self.embedding_model = OpenAIEmbedding(
                model=self.config.embedding_model
            )
            
            # Set global settings
            Settings.llm = self.llm
            Settings.embed_model = self.embedding_model
            Settings.node_parser = SentenceSplitter(
                chunk_size=self.config.chunk_size,
                chunk_overlap=self.config.chunk_overlap
            )
            
            # Initialize default tools
            await self._initialize_tools()
            
            self._initialized = True
            logger.info("LlamaIndex Engine initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize LlamaIndex Engine", error=str(e))
            raise

    async def _initialize_tools(self) -> None:
        """Initialize default LlamaIndex tools"""
        if not LLAMAINDEX_AVAILABLE:
            return
        
        # Code analysis tool
        def analyze_code_quality(code: str) -> str:
            """Analyze code quality and provide suggestions"""
            issues = []
            
            # Basic analysis
            lines = code.split('\n')
            if len(lines) > 100:
                issues.append("Function is too long (>100 lines)")
            
            if code.count("if ") > 10:
                issues.append("Too many conditional statements")
            
            if "TODO" in code:
                issues.append("Contains TODO comments")
            
            if "eval(" in code or "exec(" in code:
                issues.append("Security risk: uses eval() or exec()")
            
            return f"Code analysis: {'; '.join(issues) if issues else 'Code looks good!'}"
        
        # File operations tool
        def read_file_content(filepath: str) -> str:
            """Read content from a file"""
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                return f"File content ({len(content)} characters):\n{content[:1000]}..."
            except Exception as e:
                return f"Error reading file: {str(e)}"
        
        # Web search simulation tool
        def search_documentation(query: str) -> str:
            """Search for documentation and examples"""
            # Mock implementation - in real scenario would use actual search
            return f"Documentation search results for '{query}': [Mock results - implement with real search API]"
        
        # Create LlamaIndex tools
        self.tools = {
            "code_analyzer": FunctionTool.from_defaults(
                fn=analyze_code_quality,
                name="code_analyzer",
                description="Analyze code quality and provide improvement suggestions"
            ),
            "file_reader": FunctionTool.from_defaults(
                fn=read_file_content,
                name="file_reader", 
                description="Read content from a file"
            ),
            "doc_search": FunctionTool.from_defaults(
                fn=search_documentation,
                name="doc_search",
                description="Search for documentation and examples"
            )
        }

    async def add_documents(self, documents: List[DocumentSource]) -> str:
        """Add documents to the knowledge base"""
        if not LLAMAINDEX_AVAILABLE:
            return await self._mock_add_documents(documents)
        
        try:
            # Convert to LlamaIndex documents
            llama_docs = []
            for doc_source in documents:
                doc = Document(
                    text=doc_source.content,
                    metadata=doc_source.metadata,
                    id_=doc_source.id
                )
                llama_docs.append(doc)
                self.documents[doc_source.id] = doc_source
            
            # Create or update index
            if "main_index" not in self.indexes:
                self.indexes["main_index"] = VectorStoreIndex.from_documents(llama_docs)
            else:
                # Add to existing index
                for doc in llama_docs:
                    self.indexes["main_index"].insert(doc)
            
            logger.info("Documents added to index", count=len(documents))
            return f"Successfully added {len(documents)} documents to the knowledge base"
            
        except Exception as e:
            logger.error("Failed to add documents", error=str(e))
            return f"Error adding documents: {str(e)}"

    async def create_rag_agent(
        self, 
        agent_name: str,
        system_prompt: str = None,
        tools: List[str] = None
    ) -> str:
        """Create a RAG-enabled agent"""
        if not LLAMAINDEX_AVAILABLE:
            return await self._mock_create_agent(agent_name, "rag")
        
        try:
            # Create query engine from main index
            if "main_index" not in self.indexes:
                # Create empty index if none exists
                self.indexes["main_index"] = VectorStoreIndex.from_documents([])
            
            query_engine = self.indexes["main_index"].as_query_engine(
                similarity_top_k=self.config.similarity_top_k
            )
            
            # Create query engine tool
            query_tool = QueryEngineTool.from_defaults(
                query_engine=query_engine,
                name="knowledge_base",
                description="Search the knowledge base for relevant information"
            )
            
            # Combine with other tools
            agent_tools = [query_tool]
            if tools:
                for tool_name in tools:
                    if tool_name in self.tools:
                        agent_tools.append(self.tools[tool_name])
            
            # Create ReAct agent
            memory = ChatMemoryBuffer.from_defaults(token_limit=3000)
            
            agent = ReActAgent.from_tools(
                tools=agent_tools,
                llm=self.llm,
                memory=memory,
                system_prompt=system_prompt or f"You are {agent_name}, a helpful AI assistant with access to a knowledge base and various tools.",
                verbose=True
            )
            
            self.agents[agent_name] = {
                "agent": agent,
                "type": AgentType.RAG,
                "tools": [tool.metadata.name for tool in agent_tools],
                "created_at": asyncio.get_event_loop().time()
            }
            
            logger.info("RAG agent created", name=agent_name)
            return agent_name
            
        except Exception as e:
            logger.error("Failed to create RAG agent", name=agent_name, error=str(e))
            raise

    async def create_function_calling_agent(
        self, 
        agent_name: str,
        tools: List[str],
        system_prompt: str = None
    ) -> str:
        """Create a function-calling agent"""
        if not LLAMAINDEX_AVAILABLE:
            return await self._mock_create_agent(agent_name, "function_calling")
        
        try:
            # Get selected tools
            agent_tools = []
            for tool_name in tools:
                if tool_name in self.tools:
                    agent_tools.append(self.tools[tool_name])
            
            if not agent_tools:
                raise ValueError("No valid tools specified")
            
            # Create function calling agent
            memory = ChatMemoryBuffer.from_defaults(token_limit=3000)
            
            agent = ReActAgent.from_tools(
                tools=agent_tools,
                llm=self.llm,
                memory=memory,
                system_prompt=system_prompt or f"You are {agent_name}, a helpful AI assistant that can use various tools to help users.",
                verbose=True
            )
            
            self.agents[agent_name] = {
                "agent": agent,
                "type": AgentType.FUNCTION_CALLING,
                "tools": tools,
                "created_at": asyncio.get_event_loop().time()
            }
            
            logger.info("Function calling agent created", name=agent_name)
            return agent_name
            
        except Exception as e:
            logger.error("Failed to create function calling agent", name=agent_name, error=str(e))
            raise

    async def chat_with_agent(
        self, 
        agent_name: str, 
        message: str
    ) -> Dict[str, Any]:
        """Chat with a LlamaIndex agent"""
        if agent_name not in self.agents:
            raise ValueError(f"Agent '{agent_name}' not found")
        
        if not LLAMAINDEX_AVAILABLE:
            return await self._mock_chat(agent_name, message)
        
        try:
            agent_data = self.agents[agent_name]
            agent = agent_data["agent"]
            
            logger.info("Chatting with agent", agent=agent_name, message=message[:100])
            
            # Get response from agent
            response = await asyncio.to_thread(agent.chat, message)
            
            result = {
                "agent": agent_name,
                "message": message,
                "response": str(response),
                "sources": getattr(response, 'source_nodes', []),
                "metadata": {
                    "agent_type": agent_data["type"].value,
                    "tools_used": agent_data["tools"]
                }
            }
            
            logger.info("Agent response generated", agent=agent_name, response_length=len(str(response)))
            
            return result
            
        except Exception as e:
            logger.error("Agent chat failed", agent=agent_name, error=str(e))
            return {
                "agent": agent_name,
                "message": message,
                "error": str(e)
            }

    async def query_knowledge_base(
        self, 
        query: str, 
        index_name: str = "main_index"
    ) -> Dict[str, Any]:
        """Query the knowledge base directly"""
        if not LLAMAINDEX_AVAILABLE:
            return await self._mock_query(query)
        
        if index_name not in self.indexes:
            raise ValueError(f"Index '{index_name}' not found")
        
        try:
            query_engine = self.indexes[index_name].as_query_engine(
                similarity_top_k=self.config.similarity_top_k
            )
            
            response = await asyncio.to_thread(query_engine.query, query)
            
            return {
                "query": query,
                "response": str(response),
                "sources": [
                    {
                        "node_id": node.node_id,
                        "text": node.text[:200] + "...",
                        "score": node.score,
                        "metadata": node.metadata
                    }
                    for node in response.source_nodes
                ],
                "metadata": {
                    "index": index_name,
                    "similarity_top_k": self.config.similarity_top_k
                }
            }
            
        except Exception as e:
            logger.error("Knowledge base query failed", query=query, error=str(e))
            return {
                "query": query,
                "error": str(e)
            }

    async def add_custom_tool(
        self, 
        name: str, 
        function: callable, 
        description: str
    ) -> None:
        """Add a custom tool to the engine"""
        if not LLAMAINDEX_AVAILABLE:
            logger.warning("Cannot add custom tool - LlamaIndex not available")
            return
        
        tool = FunctionTool.from_defaults(
            fn=function,
            name=name,
            description=description
        )
        
        self.tools[name] = tool
        logger.info("Custom tool added", name=name)

    async def _mock_add_documents(self, documents: List[DocumentSource]) -> str:
        """Mock document addition when LlamaIndex is not available"""
        for doc in documents:
            self.documents[doc.id] = doc
        return f"Mock: Added {len(documents)} documents"

    async def _mock_create_agent(self, agent_name: str, agent_type: str) -> str:
        """Mock agent creation when LlamaIndex is not available"""
        self.agents[agent_name] = {
            "type": agent_type,
            "mock": True,
            "created_at": asyncio.get_event_loop().time()
        }
        return agent_name

    async def _mock_chat(self, agent_name: str, message: str) -> Dict[str, Any]:
        """Mock chat when LlamaIndex is not available"""
        return {
            "agent": agent_name,
            "message": message,
            "response": f"Mock response from {agent_name} - LlamaIndex not available",
            "mock": True
        }

    async def _mock_query(self, query: str) -> Dict[str, Any]:
        """Mock query when LlamaIndex is not available"""
        return {
            "query": query,
            "response": f"Mock response for query: {query}",
            "mock": True
        }

    async def list_agents(self) -> List[Dict[str, Any]]:
        """List all agents"""
        return [
            {
                "name": name,
                "type": data.get("type", "unknown"),
                "tools": data.get("tools", []),
                "created_at": data.get("created_at", 0),
                "mock": data.get("mock", False)
            }
            for name, data in self.agents.items()
        ]

    async def list_documents(self) -> List[Dict[str, Any]]:
        """List all documents in the knowledge base"""
        return [
            {
                "id": doc.id,
                "content_length": len(doc.content),
                "metadata": doc.metadata,
                "source_type": doc.source_type
            }
            for doc in self.documents.values()
        ]

    async def get_agent_info(self, agent_name: str) -> Dict[str, Any]:
        """Get detailed information about an agent"""
        if agent_name not in self.agents:
            raise ValueError(f"Agent '{agent_name}' not found")
        
        agent_data = self.agents[agent_name]
        
        return {
            "name": agent_name,
            "type": agent_data.get("type", "unknown"),
            "tools": agent_data.get("tools", []),
            "created_at": agent_data.get("created_at", 0),
            "mock": agent_data.get("mock", False),
            "available": LLAMAINDEX_AVAILABLE
        }

    async def shutdown(self) -> None:
        """Shutdown the LlamaIndex engine"""
        logger.info("Shutting down LlamaIndex Engine...")
        
        self.agents.clear()
        self.indexes.clear()
        self.tools.clear()
        self.documents.clear()
        self.llm = None
        self.embedding_model = None
        self._initialized = False
        
        logger.info("LlamaIndex Engine shutdown complete")
