"""
Aizen AI Base Agent - Production Implementation
Abstract base class for all AI agents with self-evolving capabilities
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import structlog
from pydantic import BaseModel, Field

logger = structlog.get_logger(__name__)


class AgentStatus(str, Enum):
    """Agent status enumeration"""
    IDLE = "idle"
    WORKING = "working"
    THINKING = "thinking"
    ERROR = "error"
    OFFLINE = "offline"


class TaskPriority(str, Enum):
    """Task priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class Task:
    """Task definition for agents"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: str = ""
    description: str = ""
    priority: TaskPriority = TaskPriority.MEDIUM
    working_directory: str = ""
    files: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    deadline: Optional[datetime] = None
    result: Optional[str] = None
    error: Optional[str] = None


@dataclass
class TaskResult:
    """Task execution result"""
    task_id: str
    success: bool
    output: str = ""
    error: Optional[str] = None
    execution_time: float = 0.0
    files_created: List[str] = field(default_factory=list)
    files_modified: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentConfig:
    """Agent configuration"""
    name: str
    agent_type: str
    ai_provider: str = "openai"
    model: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4096
    max_concurrent_tasks: int = 3
    working_directory: str = ""
    evolution_enabled: bool = True
    isolation_enabled: bool = True
    settings: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentMetrics:
    """Agent performance metrics"""
    tasks_completed: int = 0
    tasks_failed: int = 0
    average_execution_time: float = 0.0
    success_rate: float = 0.0
    evolution_score: float = 0.0
    last_activity: datetime = field(default_factory=datetime.now)


class BaseAgent(ABC):
    """
    Abstract base class for all AI agents
    Provides core functionality for task execution, evolution, and communication
    """
    
    def __init__(self, agent_id: str, config: AgentConfig, manager=None):
        self.agent_id = agent_id
        self.config = config
        self.manager = manager
        self.status = AgentStatus.OFFLINE
        self.current_tasks: Dict[str, Task] = {}
        self.task_history: List[Task] = []
        self.metrics = AgentMetrics()
        
        # AI service will be initialized during setup
        self.ai_service = None
        self.isolation_manager = None
        
        # Evolution tracking
        self.evolution_generation = 1
        self.evolution_history: List[Dict[str, Any]] = []
        
        logger.info("Agent created", agent_id=agent_id, type=config.agent_type)

    async def initialize(self) -> None:
        """Initialize the agent and its dependencies"""
        try:
            # Initialize AI service
            await self._setup_ai_service()
            
            # Initialize isolation if enabled
            if self.config.isolation_enabled:
                await self._setup_isolation()
            
            self.status = AgentStatus.IDLE
            logger.info("Agent initialized", agent_id=self.agent_id)
            
        except Exception as e:
            self.status = AgentStatus.ERROR
            logger.error("Agent initialization failed", agent_id=self.agent_id, error=str(e))
            raise

    async def _setup_ai_service(self) -> None:
        """Setup AI service based on configuration"""
        from .ai_service import AizenAIService
        
        self.ai_service = AizenAIService(
            provider=self.config.ai_provider,
            model=self.config.model,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens
        )
        await self.ai_service.initialize()

    async def _setup_isolation(self) -> None:
        """Setup isolation environment for secure execution"""
        if self.manager and hasattr(self.manager, 'e2b_manager'):
            self.isolation_manager = self.manager.e2b_manager
            await self.isolation_manager.create_environment(self.agent_id)

    async def assign_task(self, task: Task) -> None:
        """Assign a new task to the agent"""
        if len(self.current_tasks) >= self.config.max_concurrent_tasks:
            raise ValueError(f"Agent {self.agent_id} is at maximum task capacity")
        
        self.current_tasks[task.id] = task
        self.status = AgentStatus.WORKING
        
        logger.info("Task assigned", agent_id=self.agent_id, task_id=task.id)
        
        # Execute task asynchronously
        asyncio.create_task(self._execute_task_wrapper(task))

    async def _execute_task_wrapper(self, task: Task) -> None:
        """Wrapper for task execution with error handling and metrics"""
        start_time = datetime.now()
        
        try:
            # Execute the task
            result = await self.execute_task(task)
            
            # Update task with result
            task.result = result.output
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Update metrics
            self.metrics.tasks_completed += 1
            self._update_average_execution_time(execution_time)
            self._update_success_rate()
            
            logger.info("Task completed", agent_id=self.agent_id, task_id=task.id, 
                       execution_time=execution_time)
            
        except Exception as e:
            # Handle task failure
            task.error = str(e)
            self.metrics.tasks_failed += 1
            self._update_success_rate()
            
            logger.error("Task failed", agent_id=self.agent_id, task_id=task.id, 
                        error=str(e))
        
        finally:
            # Clean up
            self.current_tasks.pop(task.id, None)
            self.task_history.append(task)
            
            # Update status
            if not self.current_tasks:
                self.status = AgentStatus.IDLE
            
            # Trigger evolution if enabled
            if self.config.evolution_enabled:
                asyncio.create_task(self._evolve_if_needed())

    @abstractmethod
    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a specific task - must be implemented by subclasses"""
        pass

    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """Get agent capabilities - must be implemented by subclasses"""
        pass

    @abstractmethod
    def get_specialization(self) -> str:
        """Get agent specialization - must be implemented by subclasses"""
        pass

    async def _evolve_if_needed(self) -> None:
        """Check if agent should evolve and trigger evolution"""
        # Evolution criteria
        should_evolve = (
            len(self.task_history) % 10 == 0 and  # Every 10 tasks
            len(self.task_history) > 0 and
            self.metrics.success_rate < 0.9  # If success rate is below 90%
        )
        
        if should_evolve:
            await self.evolve()

    async def evolve(self) -> None:
        """Evolve the agent using EvoAgentX framework"""
        if not self.manager or not hasattr(self.manager, 'evoagentx_engine'):
            return
        
        try:
            logger.info("Starting agent evolution", agent_id=self.agent_id)
            
            # Get evolution recommendations
            evolution_data = {
                "agent_id": self.agent_id,
                "task_history": [
                    {
                        "type": task.type,
                        "success": task.result is not None and task.error is None,
                        "execution_time": (datetime.now() - task.created_at).total_seconds()
                    }
                    for task in self.task_history[-10:]  # Last 10 tasks
                ],
                "current_metrics": {
                    "success_rate": self.metrics.success_rate,
                    "avg_execution_time": self.metrics.average_execution_time
                }
            }
            
            evolution_result = await self.manager.evoagentx_engine.evolve_agent(
                self.agent_id, evolution_data
            )
            
            # Apply evolution changes
            if evolution_result.get("improved_prompts"):
                # Update agent prompts/behavior
                pass
            
            if evolution_result.get("new_capabilities"):
                # Add new capabilities
                pass
            
            self.evolution_generation += 1
            self.evolution_history.append({
                "generation": self.evolution_generation,
                "timestamp": datetime.now(),
                "changes": evolution_result,
                "metrics_before": dict(self.metrics.__dict__),
            })
            
            logger.info("Agent evolution completed", agent_id=self.agent_id, 
                       generation=self.evolution_generation)
            
        except Exception as e:
            logger.error("Agent evolution failed", agent_id=self.agent_id, error=str(e))

    def _update_average_execution_time(self, execution_time: float) -> None:
        """Update average execution time metric"""
        total_tasks = self.metrics.tasks_completed + self.metrics.tasks_failed
        if total_tasks > 1:
            self.metrics.average_execution_time = (
                (self.metrics.average_execution_time * (total_tasks - 1) + execution_time) 
                / total_tasks
            )
        else:
            self.metrics.average_execution_time = execution_time

    def _update_success_rate(self) -> None:
        """Update success rate metric"""
        total_tasks = self.metrics.tasks_completed + self.metrics.tasks_failed
        if total_tasks > 0:
            self.metrics.success_rate = self.metrics.tasks_completed / total_tasks

    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status and metrics"""
        return {
            "agent_id": self.agent_id,
            "status": self.status,
            "config": self.config.__dict__,
            "metrics": self.metrics.__dict__,
            "current_tasks": len(self.current_tasks),
            "evolution_generation": self.evolution_generation,
            "capabilities": self.get_capabilities(),
            "specialization": self.get_specialization()
        }

    async def shutdown(self) -> None:
        """Shutdown the agent and clean up resources"""
        logger.info("Shutting down agent", agent_id=self.agent_id)
        
        # Cancel any running tasks
        for task in self.current_tasks.values():
            task.error = "Agent shutdown"
        
        # Shutdown AI service
        if self.ai_service:
            await self.ai_service.shutdown()
        
        # Clean up isolation environment
        if self.isolation_manager:
            await self.isolation_manager.cleanup_environment(self.agent_id)
        
        self.status = AgentStatus.OFFLINE
        logger.info("Agent shutdown complete", agent_id=self.agent_id)
