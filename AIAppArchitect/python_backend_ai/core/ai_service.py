"""
Aizen AI Service - Production Implementation
Unified AI service supporting multiple providers with advanced capabilities
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
from abc import ABC, abstractmethod
from dataclasses import dataclass
import structlog
from pydantic import BaseModel

# AI Provider imports
import openai
from anthropic import Anthropic
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain.schema import HumanMessage, SystemMessage, AIMessage

# Google Gemini imports
try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger.warning("Google Gemini not available, install google-generativeai package")

logger = structlog.get_logger(__name__)


@dataclass
class AIResponse:
    """AI response with metadata"""
    content: str
    model: str
    provider: str
    tokens_used: int = 0
    response_time: float = 0.0
    finish_reason: str = ""
    metadata: Dict[str, Any] = None


@dataclass
class AIConfig:
    """AI service configuration"""
    provider: str = "openai"
    model: str = "gpt-4-turbo-preview"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    temperature: float = 0.7
    max_tokens: int = 4096
    timeout: int = 30
    max_retries: int = 3
    rate_limit_rpm: int = 60


class AIProvider(ABC):
    """Abstract base class for AI providers"""
    
    def __init__(self, config: AIConfig):
        self.config = config
        self.client = None
        self._initialized = False

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the AI provider"""
        pass

    @abstractmethod
    async def generate_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AIResponse:
        """Generate a response from the AI model"""
        pass

    @abstractmethod
    async def stream_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """Stream a response from the AI model"""
        pass

    @abstractmethod
    async def shutdown(self) -> None:
        """Shutdown the AI provider"""
        pass


class OpenAIProvider(AIProvider):
    """OpenAI provider implementation"""
    
    async def initialize(self) -> None:
        """Initialize OpenAI client"""
        try:
            self.client = openai.AsyncOpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url,
                timeout=self.config.timeout,
                max_retries=self.config.max_retries
            )
            
            # Test connection
            await self.client.models.list()
            self._initialized = True
            logger.info("OpenAI provider initialized", model=self.config.model)
            
        except Exception as e:
            logger.error("Failed to initialize OpenAI provider", error=str(e))
            raise

    async def generate_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AIResponse:
        """Generate response using OpenAI"""
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            messages = []
            
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            
            # Add context if provided
            if context:
                context_str = self._format_context(context)
                messages.append({"role": "system", "content": f"Context: {context_str}"})
            
            messages.append({"role": "user", "content": prompt})
            
            response = await self.client.chat.completions.create(
                model=self.config.model,
                messages=messages,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )
            
            response_time = time.time() - start_time
            
            return AIResponse(
                content=response.choices[0].message.content,
                model=response.model,
                provider="openai",
                tokens_used=response.usage.total_tokens if response.usage else 0,
                response_time=response_time,
                finish_reason=response.choices[0].finish_reason,
                metadata={"usage": response.usage.__dict__ if response.usage else {}}
            )
            
        except Exception as e:
            logger.error("OpenAI request failed", error=str(e))
            raise

    async def stream_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """Stream response using OpenAI"""
        if not self._initialized:
            await self.initialize()
        
        try:
            messages = []
            
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            
            if context:
                context_str = self._format_context(context)
                messages.append({"role": "system", "content": f"Context: {context_str}"})
            
            messages.append({"role": "user", "content": prompt})
            
            stream = await self.client.chat.completions.create(
                model=self.config.model,
                messages=messages,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error("OpenAI streaming failed", error=str(e))
            raise

    def _format_context(self, context: Dict[str, Any]) -> str:
        """Format context dictionary into string"""
        return "\n".join([f"{k}: {v}" for k, v in context.items()])

    async def shutdown(self) -> None:
        """Shutdown OpenAI provider"""
        if self.client:
            await self.client.close()
        self._initialized = False
        logger.info("OpenAI provider shutdown")


class AnthropicProvider(AIProvider):
    """Anthropic Claude provider implementation"""
    
    async def initialize(self) -> None:
        """Initialize Anthropic client"""
        try:
            self.client = Anthropic(
                api_key=self.config.api_key,
                timeout=self.config.timeout,
                max_retries=self.config.max_retries
            )
            
            self._initialized = True
            logger.info("Anthropic provider initialized", model=self.config.model)
            
        except Exception as e:
            logger.error("Failed to initialize Anthropic provider", error=str(e))
            raise

    async def generate_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AIResponse:
        """Generate response using Anthropic Claude"""
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            # Prepare messages
            messages = []
            
            # Add context if provided
            if context:
                context_str = self._format_context(context)
                prompt = f"Context: {context_str}\n\n{prompt}"
            
            messages.append({"role": "user", "content": prompt})
            
            response = await asyncio.to_thread(
                self.client.messages.create,
                model=self.config.model,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                system=system_prompt or "",
                messages=messages
            )
            
            response_time = time.time() - start_time
            
            return AIResponse(
                content=response.content[0].text,
                model=self.config.model,
                provider="anthropic",
                tokens_used=response.usage.input_tokens + response.usage.output_tokens,
                response_time=response_time,
                finish_reason=response.stop_reason,
                metadata={"usage": response.usage.__dict__}
            )
            
        except Exception as e:
            logger.error("Anthropic request failed", error=str(e))
            raise

    async def stream_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """Stream response using Anthropic Claude"""
        if not self._initialized:
            await self.initialize()
        
        try:
            messages = []
            
            if context:
                context_str = self._format_context(context)
                prompt = f"Context: {context_str}\n\n{prompt}"
            
            messages.append({"role": "user", "content": prompt})
            
            # Note: Anthropic streaming implementation would go here
            # For now, we'll simulate streaming by yielding the full response
            response = await self.generate_response(prompt, system_prompt, context)
            
            # Simulate streaming by yielding chunks
            words = response.content.split()
            for i in range(0, len(words), 5):  # Yield 5 words at a time
                chunk = " ".join(words[i:i+5]) + " "
                yield chunk
                await asyncio.sleep(0.1)  # Small delay to simulate streaming
                
        except Exception as e:
            logger.error("Anthropic streaming failed", error=str(e))
            raise

    def _format_context(self, context: Dict[str, Any]) -> str:
        """Format context dictionary into string"""
        return "\n".join([f"{k}: {v}" for k, v in context.items()])

    async def shutdown(self) -> None:
        """Shutdown Anthropic provider"""
        self._initialized = False
        logger.info("Anthropic provider shutdown")


class GeminiProvider(AIProvider):
    """Google Gemini provider implementation"""

    async def initialize(self) -> None:
        """Initialize Google Gemini client"""
        if not GEMINI_AVAILABLE:
            raise ImportError("Google Gemini not available. Install: pip install google-generativeai")

        try:
            # Configure Gemini API
            genai.configure(api_key=self.config.api_key)

            # Initialize the model
            self.client = genai.GenerativeModel(
                model_name=self.config.model,
                generation_config=genai.types.GenerationConfig(
                    temperature=self.config.temperature,
                    max_output_tokens=self.config.max_tokens,
                ),
                safety_settings={
                    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                }
            )

            # Test connection by listing models
            models = genai.list_models()
            available_models = [m.name for m in models]
            logger.info("Available Gemini models", models=available_models[:5])  # Log first 5

            self._initialized = True
            logger.info("Gemini provider initialized", model=self.config.model)

        except Exception as e:
            logger.error("Failed to initialize Gemini provider", error=str(e))
            raise

    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AIResponse:
        """Generate response using Google Gemini"""
        if not self._initialized:
            await self.initialize()

        start_time = time.time()

        try:
            # Prepare the full prompt
            full_prompt = ""

            if system_prompt:
                full_prompt += f"System: {system_prompt}\n\n"

            if context:
                context_str = self._format_context(context)
                full_prompt += f"Context: {context_str}\n\n"

            full_prompt += f"User: {prompt}"

            # Generate response
            response = await asyncio.to_thread(
                self.client.generate_content,
                full_prompt
            )

            response_time = time.time() - start_time

            # Extract response content
            content = response.text if response.text else ""

            # Calculate token usage (approximate)
            tokens_used = len(full_prompt.split()) + len(content.split())

            return AIResponse(
                content=content,
                model=self.config.model,
                provider="gemini",
                tokens_used=tokens_used,
                response_time=response_time,
                finish_reason=response.candidates[0].finish_reason.name if response.candidates else "unknown",
                metadata={
                    "safety_ratings": [
                        {
                            "category": rating.category.name,
                            "probability": rating.probability.name
                        }
                        for rating in response.candidates[0].safety_ratings
                    ] if response.candidates else []
                }
            )

        except Exception as e:
            logger.error("Gemini request failed", error=str(e))
            raise

    async def stream_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """Stream response using Google Gemini"""
        if not self._initialized:
            await self.initialize()

        try:
            # Prepare the full prompt
            full_prompt = ""

            if system_prompt:
                full_prompt += f"System: {system_prompt}\n\n"

            if context:
                context_str = self._format_context(context)
                full_prompt += f"Context: {context_str}\n\n"

            full_prompt += f"User: {prompt}"

            # Generate streaming response
            response = await asyncio.to_thread(
                self.client.generate_content,
                full_prompt,
                stream=True
            )

            async for chunk in response:
                if chunk.text:
                    yield chunk.text

        except Exception as e:
            logger.error("Gemini streaming failed", error=str(e))
            # Fallback to non-streaming
            response = await self.generate_response(prompt, system_prompt, context)
            words = response.content.split()
            for i in range(0, len(words), 3):  # Yield 3 words at a time
                chunk = " ".join(words[i:i+3]) + " "
                yield chunk
                await asyncio.sleep(0.05)  # Small delay to simulate streaming

    def _format_context(self, context: Dict[str, Any]) -> str:
        """Format context dictionary into string"""
        return "\n".join([f"{k}: {v}" for k, v in context.items()])

    async def shutdown(self) -> None:
        """Shutdown Gemini provider"""
        self._initialized = False
        logger.info("Gemini provider shutdown")


class AizenAIService:
    """
    Unified AI service supporting multiple providers
    """
    
    def __init__(
        self, 
        provider: str = "openai",
        model: str = "gpt-4-turbo-preview",
        **kwargs
    ):
        self.config = AIConfig(
            provider=provider,
            model=model,
            **kwargs
        )
        
        self.provider: Optional[AIProvider] = None
        self._initialized = False
        
        # Rate limiting
        self._last_request_time = 0.0
        self._request_count = 0
        self._rate_limit_window = 60.0  # 1 minute

    async def initialize(self) -> None:
        """Initialize the AI service with the specified provider"""
        if self._initialized:
            return
        
        try:
            # Create provider instance
            if self.config.provider == "openai":
                self.provider = OpenAIProvider(self.config)
            elif self.config.provider == "anthropic":
                self.provider = AnthropicProvider(self.config)
            elif self.config.provider == "gemini":
                self.provider = GeminiProvider(self.config)
            else:
                raise ValueError(f"Unsupported AI provider: {self.config.provider}")
            
            # Initialize provider
            await self.provider.initialize()
            self._initialized = True
            
            logger.info("AI service initialized", 
                       provider=self.config.provider, 
                       model=self.config.model)
            
        except Exception as e:
            logger.error("Failed to initialize AI service", error=str(e))
            raise

    async def generate_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AIResponse:
        """Generate AI response with rate limiting"""
        if not self._initialized:
            await self.initialize()
        
        # Apply rate limiting
        await self._apply_rate_limit()
        
        try:
            response = await self.provider.generate_response(prompt, system_prompt, context)
            logger.info("AI response generated", 
                       provider=self.config.provider,
                       tokens=response.tokens_used,
                       time=response.response_time)
            return response
            
        except Exception as e:
            logger.error("AI response generation failed", error=str(e))
            raise

    async def stream_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """Stream AI response with rate limiting"""
        if not self._initialized:
            await self.initialize()
        
        # Apply rate limiting
        await self._apply_rate_limit()
        
        try:
            async for chunk in self.provider.stream_response(prompt, system_prompt, context):
                yield chunk
                
        except Exception as e:
            logger.error("AI streaming failed", error=str(e))
            raise

    async def _apply_rate_limit(self) -> None:
        """Apply rate limiting to prevent API abuse"""
        current_time = time.time()
        
        # Reset counter if window has passed
        if current_time - self._last_request_time > self._rate_limit_window:
            self._request_count = 0
            self._last_request_time = current_time
        
        # Check rate limit
        if self._request_count >= self.config.rate_limit_rpm:
            sleep_time = self._rate_limit_window - (current_time - self._last_request_time)
            if sleep_time > 0:
                logger.warning("Rate limit reached, sleeping", sleep_time=sleep_time)
                await asyncio.sleep(sleep_time)
                self._request_count = 0
                self._last_request_time = time.time()
        
        self._request_count += 1

    async def shutdown(self) -> None:
        """Shutdown the AI service"""
        if self.provider:
            await self.provider.shutdown()
        
        self._initialized = False
        logger.info("AI service shutdown")
