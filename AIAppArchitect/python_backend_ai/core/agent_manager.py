"""
Aizen AI Agent Manager - Production Implementation
Manages AI agents with self-evolving capabilities and swarm intelligence
"""

import asyncio
import uuid
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import structlog
from pydantic import BaseModel, Field

from core.base_agent import BaseAgent, AgentConfig, AgentStatus, Task, TaskResult
from frameworks.langgraph_engine import LangGraphEngine
from frameworks.pydantic_ai_engine import PydanticAIEngine
from frameworks.ag2_engine import AG2Engine
from frameworks.crewai_engine import CrewAIEngine
from frameworks.taskweaver_engine import TaskWeaverEngine
from isolation.e2b_manager import E2BExecutionManager

logger = structlog.get_logger(__name__)


class AgentType(str, Enum):
    """Agent specialization types"""
    CODE_GENERATION = "code-generation"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    REVIEW = "review"
    ARCHITECTURE = "architecture"
    SECURITY = "security"
    PERFORMANCE = "performance"
    DEBUGGING = "debugging"


@dataclass
class AgentMetrics:
    """Agent performance metrics"""
    total_agents: int = 0
    active_agents: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    average_execution_time: float = 0.0
    swarm_efficiency: float = 0.0
    evolution_score: float = 0.0


@dataclass
class SwarmConfig:
    """Swarm intelligence configuration"""
    max_agents: int = 50
    auto_scaling: bool = True
    emergence_detection: bool = True
    collaboration_threshold: float = 0.8
    evolution_enabled: bool = True


class AizenAgentManager:
    """
    Production-ready AI Agent Manager with self-evolving capabilities
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.agents: Dict[str, BaseAgent] = {}
        self.active_tasks: Dict[str, Task] = {}
        self.metrics = AgentMetrics()
        self.swarm_config = SwarmConfig()
        
        # Initialize AI frameworks
        self.langgraph_engine = LangGraphEngine()
        self.pydantic_ai_engine = PydanticAIEngine()
        self.ag2_engine = AG2Engine()
        self.crewai_engine = CrewAIEngine()
        self.taskweaver_engine = TaskWeaverEngine()
        self.e2b_manager = E2BExecutionManager()
        
        # Agent pools by specialization
        self.agent_pools: Dict[AgentType, List[str]] = {
            agent_type: [] for agent_type in AgentType
        }
        
        self._initialized = False
        logger.info("AizenAgentManager initialized", config=self.config)

    async def initialize(self) -> None:
        """Initialize the agent manager and all frameworks"""
        if self._initialized:
            return
            
        logger.info("Initializing Aizen Agent Manager...")
        
        try:
            # Initialize AI frameworks
            await self.langgraph_engine.initialize()
            await self.pydantic_ai_engine.initialize()
            await self.ag2_engine.initialize()
            await self.crewai_engine.initialize()
            await self.taskweaver_engine.initialize()
            await self.e2b_manager.initialize()
            
            # Create initial agent pool
            await self._create_initial_agents()
            
            self._initialized = True
            logger.info("Aizen Agent Manager initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize agent manager", error=str(e))
            raise

    async def _create_initial_agents(self) -> None:
        """Create initial pool of specialized agents"""
        initial_agents = [
            (AgentType.CODE_GENERATION, 2),
            (AgentType.TESTING, 1),
            (AgentType.DOCUMENTATION, 1),
            (AgentType.REVIEW, 1),
            (AgentType.ARCHITECTURE, 1),
            (AgentType.SECURITY, 1),
            (AgentType.PERFORMANCE, 1),
        ]
        
        for agent_type, count in initial_agents:
            for i in range(count):
                agent_id = await self.create_agent(agent_type)
                logger.info("Created initial agent", agent_id=agent_id, type=agent_type)

    async def create_agent(
        self, 
        agent_type: AgentType,
        config: Optional[AgentConfig] = None
    ) -> str:
        """Create a new specialized agent"""
        agent_id = str(uuid.uuid4())
        
        if config is None:
            config = AgentConfig(
                name=f"{agent_type.value}-{agent_id[:8]}",
                agent_type=agent_type,
                ai_provider="openai",  # Default provider
                max_concurrent_tasks=3,
                evolution_enabled=True
            )
        
        # Create agent with appropriate specialization
        agent = await self._create_specialized_agent(agent_id, agent_type, config)
        
        # Initialize agent
        await agent.initialize()
        
        # Store agent
        self.agents[agent_id] = agent
        self.agent_pools[agent_type].append(agent_id)
        self.metrics.total_agents += 1
        
        logger.info("Agent created", agent_id=agent_id, type=agent_type)
        return agent_id

    async def _create_specialized_agent(
        self, 
        agent_id: str, 
        agent_type: AgentType, 
        config: AgentConfig
    ) -> BaseAgent:
        """Create a specialized agent based on type"""
        # Import specialized agent classes
        from agents.code_generation_agent import CodeGenerationAgent
        from agents.testing_agent import TestingAgent
        from agents.documentation_agent import DocumentationAgent
        from agents.review_agent import ReviewAgent
        from agents.architecture_agent import ArchitectureAgent
        from agents.security_agent import SecurityAgent
        from agents.performance_agent import PerformanceAgent
        
        agent_classes = {
            AgentType.CODE_GENERATION: CodeGenerationAgent,
            AgentType.TESTING: TestingAgent,
            AgentType.DOCUMENTATION: DocumentationAgent,
            AgentType.REVIEW: ReviewAgent,
            AgentType.ARCHITECTURE: ArchitectureAgent,
            AgentType.SECURITY: SecurityAgent,
            AgentType.PERFORMANCE: PerformanceAgent,
        }
        
        agent_class = agent_classes.get(agent_type)
        if not agent_class:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        return agent_class(agent_id, config, self)

    async def assign_task(
        self, 
        task: Task, 
        preferred_agent_type: Optional[AgentType] = None
    ) -> Optional[str]:
        """Assign a task to the most suitable agent"""
        # Determine best agent type for task
        if preferred_agent_type is None:
            preferred_agent_type = await self._determine_agent_type_for_task(task)
        
        # Check if we should use swarm intelligence
        if await self._should_use_swarm(task):
            return await self._assign_to_swarm(task)
        
        # Find available agent
        agent_id = await self._find_available_agent(preferred_agent_type)
        
        if agent_id is None:
            # Create new agent if under limit
            if self.metrics.total_agents < self.swarm_config.max_agents:
                agent_id = await self.create_agent(preferred_agent_type)
            else:
                logger.warning("No available agents and at capacity limit")
                return None
        
        # Assign task to agent
        agent = self.agents[agent_id]
        await agent.assign_task(task)
        
        self.active_tasks[task.id] = task
        self.metrics.active_agents += 1
        
        logger.info("Task assigned", task_id=task.id, agent_id=agent_id)
        return agent_id

    async def _determine_agent_type_for_task(self, task: Task) -> AgentType:
        """Determine the best agent type for a given task"""
        task_type_mapping = {
            "code-generation": AgentType.CODE_GENERATION,
            "implement-feature": AgentType.CODE_GENERATION,
            "create-component": AgentType.CODE_GENERATION,
            "fix-bug": AgentType.CODE_GENERATION,
            "refactor": AgentType.CODE_GENERATION,
            "test": AgentType.TESTING,
            "unit-test": AgentType.TESTING,
            "integration-test": AgentType.TESTING,
            "documentation": AgentType.DOCUMENTATION,
            "readme": AgentType.DOCUMENTATION,
            "api-docs": AgentType.DOCUMENTATION,
            "review": AgentType.REVIEW,
            "code-review": AgentType.REVIEW,
            "security-review": AgentType.SECURITY,
            "architecture": AgentType.ARCHITECTURE,
            "design": AgentType.ARCHITECTURE,
            "performance": AgentType.PERFORMANCE,
            "optimization": AgentType.PERFORMANCE,
        }
        
        return task_type_mapping.get(task.type, AgentType.CODE_GENERATION)

    async def _should_use_swarm(self, task: Task) -> bool:
        """Determine if a task should use swarm intelligence"""
        swarm_indicators = [
            "complex", "large-scale", "multi-component", "full-stack",
            "architecture", "system-design", "enterprise", "distributed"
        ]
        
        task_description = task.description.lower()
        return any(indicator in task_description for indicator in swarm_indicators)

    async def _assign_to_swarm(self, task: Task) -> str:
        """Assign task to swarm intelligence system"""
        swarm_id = await self.swarm_engine.create_swarm_for_task(task)
        logger.info("Task assigned to swarm", task_id=task.id, swarm_id=swarm_id)
        return swarm_id

    async def _find_available_agent(self, agent_type: AgentType) -> Optional[str]:
        """Find an available agent of the specified type"""
        for agent_id in self.agent_pools[agent_type]:
            agent = self.agents[agent_id]
            if agent.status == AgentStatus.IDLE:
                return agent_id
        return None

    async def get_agent_status(self, agent_id: str) -> Optional[AgentStatus]:
        """Get the status of a specific agent"""
        agent = self.agents.get(agent_id)
        return agent.status if agent else None

    async def get_metrics(self) -> AgentMetrics:
        """Get current system metrics"""
        # Update active agents count
        active_count = sum(
            1 for agent in self.agents.values() 
            if agent.status in [AgentStatus.WORKING, AgentStatus.THINKING]
        )
        self.metrics.active_agents = active_count
        
        return self.metrics

    async def enable_swarm_intelligence(self) -> None:
        """Enable swarm intelligence capabilities"""
        await self.swarm_engine.enable()
        logger.info("Swarm intelligence enabled")

    async def enable_self_evolution(self) -> None:
        """Enable self-evolution capabilities"""
        await self.evoagentx_engine.enable()
        logger.info("Self-evolution enabled")

    async def shutdown(self) -> None:
        """Shutdown the agent manager and all agents"""
        logger.info("Shutting down Aizen Agent Manager...")
        
        # Shutdown all agents
        for agent in self.agents.values():
            await agent.shutdown()
        
        # Shutdown frameworks
        await self.evoagentx_engine.shutdown()
        await self.taskweaver_engine.shutdown()
        await self.swarm_engine.shutdown()
        await self.e2b_manager.shutdown()
        
        logger.info("Aizen Agent Manager shutdown complete")
