"""
Aizen AI Extension - FastAPI Backend
Production-ready API server for AI agent management
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import structlog
import uvicorn

from core.agent_manager import AizenAgentManager, AgentType, AgentMetrics
from core.base_agent import Task, TaskResult, AgentConfig, TaskPriority
from core.ai_service import AizenAIService
from frameworks.evoagentx_engine import EvoAgentXEngine, EvolutionAlgorithm
from frameworks.taskweaver_engine import TaskWeaverEngine
from frameworks.swarm_intelligence import SwarmIntelligenceEngine
from frameworks.magentic_one_gemini_test import MagenticOneGeminiTest, GeminiMagenticConfig
from isolation.e2b_manager import E2BExecutionManager, ExecutionRequest

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Global manager instances
agent_manager: Optional[AizenAgentManager] = None
magentic_one_framework: Optional[MagenticOneGeminiTest] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global agent_manager, magentic_one_framework

    # Startup
    logger.info("Starting Aizen AI Backend...")

    try:
        # Initialize main agent manager
        agent_manager = AizenAgentManager()
        await agent_manager.initialize()

        # Initialize Magentic One framework (optional, requires API key)
        try:
            import os
            gemini_api_key = os.getenv("GEMINI_API_KEY")
            if gemini_api_key:
                config = GeminiMagenticConfig(
                    model_name="gemini-pro",
                    gemini_api_key=gemini_api_key,
                    temperature=0.7,
                    max_tokens=2048,
                    max_rounds=3
                )
                magentic_one_framework = MagenticOneGeminiTest(config)
                await magentic_one_framework.initialize()
                logger.info("Magentic One Gemini framework initialized")
            else:
                logger.warning("GEMINI_API_KEY not found, Magentic One endpoints will be unavailable")
        except Exception as e:
            logger.warning("Failed to initialize Magentic One framework", error=str(e))
            magentic_one_framework = None

        logger.info("Aizen AI Backend started successfully")

        yield

    finally:
        # Shutdown
        logger.info("Shutting down Aizen AI Backend...")
        if agent_manager:
            await agent_manager.shutdown()
        if magentic_one_framework:
            await magentic_one_framework.shutdown()
        logger.info("Aizen AI Backend shutdown complete")


# Create FastAPI app
app = FastAPI(
    title="Aizen AI Extension Backend",
    description="Production-ready AI agent management system",
    version="2.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Pydantic models for API
class TaskRequest(BaseModel):
    description: str = Field(..., description="Task description")
    task_type: str = Field(default="code-generation", description="Type of task")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="Task priority")
    working_directory: str = Field(default="", description="Working directory")
    files: List[str] = Field(default_factory=list, description="Related files")
    dependencies: List[str] = Field(default_factory=list, description="Task dependencies")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")
    preferred_agent_type: Optional[AgentType] = Field(default=None, description="Preferred agent type")


class AgentCreateRequest(BaseModel):
    agent_type: AgentType = Field(..., description="Type of agent to create")
    name: Optional[str] = Field(default=None, description="Agent name")
    ai_provider: str = Field(default="openai", description="AI provider")
    model: str = Field(default="gpt-4-turbo-preview", description="AI model")
    temperature: float = Field(default=0.7, description="AI temperature")
    max_tokens: int = Field(default=4096, description="Max tokens")
    evolution_enabled: bool = Field(default=True, description="Enable evolution")
    isolation_enabled: bool = Field(default=True, description="Enable isolation")


class ExecutionCodeRequest(BaseModel):
    code: str = Field(..., description="Code to execute")
    language: str = Field(default="python", description="Programming language")
    timeout: int = Field(default=300, description="Execution timeout in seconds")
    environment_type: str = Field(default="e2b_sandbox", description="Execution environment")


class EvolutionRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID to evolve")
    algorithm: EvolutionAlgorithm = Field(default=EvolutionAlgorithm.TEXTGRAD, description="Evolution algorithm")
    performance_data: Dict[str, Any] = Field(..., description="Performance data for evolution")


class TaskWeaverRequest(BaseModel):
    user_query: str = Field(..., description="User query for TaskWeaver")
    context: Dict[str, Any] = Field(default_factory=dict, description="Execution context")


class MagenticOneRequest(BaseModel):
    task_description: str = Field(..., description="Task description for Magentic One")
    model_name: str = Field(default="gemini-pro", description="Gemini model to use")
    temperature: float = Field(default=0.7, description="AI temperature")
    max_tokens: int = Field(default=2048, description="Maximum tokens")
    max_rounds: int = Field(default=3, description="Maximum agent interaction rounds")
    gemini_api_key: Optional[str] = Field(default=None, description="Gemini API key (optional)")


class MagenticOneConfigRequest(BaseModel):
    model_name: str = Field(default="gemini-pro", description="Gemini model to use")
    temperature: float = Field(default=0.7, description="AI temperature")
    max_tokens: int = Field(default=2048, description="Maximum tokens")
    max_rounds: int = Field(default=3, description="Maximum agent interaction rounds")
    enable_code_execution: bool = Field(default=True, description="Enable code execution")
    enable_file_handling: bool = Field(default=True, description="Enable file handling")
    gemini_api_key: Optional[str] = Field(default=None, description="Gemini API key")


# Dependency to get agent manager
async def get_agent_manager() -> AizenAgentManager:
    if agent_manager is None:
        raise HTTPException(status_code=503, detail="Agent manager not initialized")
    return agent_manager


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Aizen AI Backend",
        "version": "2.0.0",
        "timestamp": "2025-01-27T00:00:00Z"
    }


# Agent management endpoints
@app.post("/agents", response_model=Dict[str, str])
async def create_agent(
    request: AgentCreateRequest,
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Create a new AI agent"""
    try:
        config = AgentConfig(
            name=request.name or f"{request.agent_type.value}-agent",
            agent_type=request.agent_type.value,
            ai_provider=request.ai_provider,
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            evolution_enabled=request.evolution_enabled,
            isolation_enabled=request.isolation_enabled
        )
        
        agent_id = await manager.create_agent(request.agent_type, config)
        
        logger.info("Agent created via API", agent_id=agent_id, type=request.agent_type)
        
        return {"agent_id": agent_id, "status": "created"}
        
    except Exception as e:
        logger.error("Failed to create agent", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/agents", response_model=List[Dict[str, Any]])
async def list_agents(
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """List all agents"""
    try:
        agents_info = []
        for agent_id, agent in manager.agents.items():
            status = await agent.get_status()
            agents_info.append(status)
        
        return agents_info
        
    except Exception as e:
        logger.error("Failed to list agents", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/agents/{agent_id}", response_model=Dict[str, Any])
async def get_agent(
    agent_id: str,
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Get agent details"""
    try:
        if agent_id not in manager.agents:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        agent = manager.agents[agent_id]
        return await agent.get_status()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get agent", agent_id=agent_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/metrics", response_model=AgentMetrics)
async def get_metrics(
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Get system metrics"""
    try:
        return await manager.get_metrics()
        
    except Exception as e:
        logger.error("Failed to get metrics", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Task management endpoints
@app.post("/tasks", response_model=Dict[str, str])
async def create_task(
    request: TaskRequest,
    background_tasks: BackgroundTasks,
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Create and assign a new task"""
    try:
        # Create task
        task = Task(
            description=request.description,
            type=request.task_type,
            priority=request.priority,
            working_directory=request.working_directory,
            files=request.files,
            dependencies=request.dependencies,
            context=request.context
        )
        
        # Assign task to agent
        agent_id = await manager.assign_task(task, request.preferred_agent_type)
        
        if agent_id is None:
            raise HTTPException(status_code=503, detail="No available agents")
        
        logger.info("Task created and assigned", task_id=task.id, agent_id=agent_id)
        
        return {
            "task_id": task.id,
            "agent_id": agent_id,
            "status": "assigned"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create task", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Evolution endpoints
@app.post("/evolution/evolve", response_model=Dict[str, Any])
async def evolve_agent(
    request: EvolutionRequest,
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Evolve an agent using EvoAgentX"""
    try:
        if not hasattr(manager, 'evoagentx_engine'):
            raise HTTPException(status_code=501, detail="Evolution not available")
        
        result = await manager.evoagentx_engine.evolve_agent(
            request.agent_id,
            request.performance_data,
            request.algorithm
        )
        
        logger.info("Agent evolved", agent_id=request.agent_id, success=result.success)
        
        return {
            "success": result.success,
            "generation": result.generation,
            "fitness_improvement": result.fitness_improvement,
            "changes_applied": result.changes_applied,
            "error": result.error
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to evolve agent", agent_id=request.agent_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/evolution/enable")
async def enable_evolution(
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Enable self-evolution capabilities"""
    try:
        await manager.enable_self_evolution()
        return {"status": "evolution_enabled"}
        
    except Exception as e:
        logger.error("Failed to enable evolution", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Swarm intelligence endpoints
@app.post("/swarm/enable")
async def enable_swarm_intelligence(
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Enable swarm intelligence capabilities"""
    try:
        await manager.enable_swarm_intelligence()
        return {"status": "swarm_intelligence_enabled"}
        
    except Exception as e:
        logger.error("Failed to enable swarm intelligence", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/swarm/metrics/{swarm_id}")
async def get_swarm_metrics(
    swarm_id: str,
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Get swarm metrics"""
    try:
        if not hasattr(manager, 'swarm_engine'):
            raise HTTPException(status_code=501, detail="Swarm intelligence not available")
        
        metrics = await manager.swarm_engine.get_swarm_metrics(swarm_id)
        return metrics.__dict__
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get swarm metrics", swarm_id=swarm_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# TaskWeaver endpoints
@app.post("/taskweaver/execute", response_model=Dict[str, Any])
async def execute_taskweaver(
    request: TaskWeaverRequest,
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Execute task using TaskWeaver"""
    try:
        if not hasattr(manager, 'taskweaver_engine'):
            raise HTTPException(status_code=501, detail="TaskWeaver not available")
        
        # Set AI service for TaskWeaver
        ai_service = AizenAIService()
        await ai_service.initialize()
        await manager.taskweaver_engine.set_ai_service(ai_service)
        
        result = await manager.taskweaver_engine.execute_task(
            request.user_query,
            request.context
        )
        
        logger.info("TaskWeaver execution completed", 
                   query=request.user_query,
                   success=result.success)
        
        return {
            "plan_id": result.plan_id,
            "success": result.success,
            "output": result.output,
            "execution_time": result.execution_time,
            "artifacts": result.artifacts,
            "errors": result.errors,
            "subtask_results": result.subtask_results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("TaskWeaver execution failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Code execution endpoints
@app.post("/execution/execute", response_model=Dict[str, Any])
async def execute_code(
    request: ExecutionCodeRequest,
    manager: AizenAgentManager = Depends(get_agent_manager)
):
    """Execute code in isolated environment"""
    try:
        if not hasattr(manager, 'e2b_manager'):
            raise HTTPException(status_code=501, detail="Code execution not available")
        
        # Create temporary environment
        env_id = await manager.e2b_manager.create_environment("temp_agent")
        
        try:
            # Create execution request
            exec_request = ExecutionRequest(
                code=request.code,
                language=request.language,
                timeout=request.timeout
            )
            
            # Execute code
            result = await manager.e2b_manager.execute_code(env_id, exec_request)
            
            return {
                "success": result.success,
                "output": result.output,
                "error": result.error,
                "execution_time": result.execution_time,
                "files_created": result.files_created,
                "files_modified": result.files_modified
            }
            
        finally:
            # Clean up environment
            await manager.e2b_manager.cleanup_environment(env_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Code execution failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Magentic One Gemini endpoints
async def get_magentic_one_framework() -> MagenticOneGeminiTest:
    """Dependency to get Magentic One framework"""
    if magentic_one_framework is None:
        raise HTTPException(status_code=503, detail="Magentic One framework not available. Set GEMINI_API_KEY environment variable.")
    return magentic_one_framework


@app.post("/magentic-one/execute", response_model=Dict[str, Any])
async def execute_magentic_one_task(
    request: MagenticOneRequest,
    framework: MagenticOneGeminiTest = Depends(get_magentic_one_framework)
):
    """Execute a task using Magentic One with Gemini"""
    try:
        logger.info("Executing Magentic One task", task=request.task_description[:100])

        # Update configuration if provided
        if request.gemini_api_key:
            # Create new framework instance with provided API key
            config = GeminiMagenticConfig(
                model_name=request.model_name,
                gemini_api_key=request.gemini_api_key,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                max_rounds=request.max_rounds
            )
            temp_framework = MagenticOneGeminiTest(config)
            await temp_framework.initialize()

            try:
                result = await temp_framework.execute_test_task(request.task_description)
            finally:
                await temp_framework.shutdown()
        else:
            # Use global framework instance
            result = await framework.execute_test_task(request.task_description)

        logger.info("Magentic One task completed",
                   success=result["success"],
                   task_id=result.get("task_id"))

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Magentic One task execution failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/magentic-one/status", response_model=Dict[str, Any])
async def get_magentic_one_status(
    framework: MagenticOneGeminiTest = Depends(get_magentic_one_framework)
):
    """Get Magentic One framework status"""
    try:
        status = await framework.get_framework_status()
        return status

    except Exception as e:
        logger.error("Failed to get Magentic One status", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/magentic-one/agents", response_model=List[Dict[str, Any]])
async def list_magentic_one_agents(
    framework: MagenticOneGeminiTest = Depends(get_magentic_one_framework)
):
    """List available Magentic One agents"""
    try:
        agents = await framework.list_agents()
        return agents

    except Exception as e:
        logger.error("Failed to list Magentic One agents", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/magentic-one/tasks/{task_id}", response_model=Dict[str, Any])
async def get_magentic_one_task_status(
    task_id: str,
    framework: MagenticOneGeminiTest = Depends(get_magentic_one_framework)
):
    """Get status of a specific Magentic One task"""
    try:
        task_status = await framework.get_task_status(task_id)
        return task_status

    except Exception as e:
        logger.error("Failed to get Magentic One task status", task_id=task_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/magentic-one/initialize", response_model=Dict[str, str])
async def initialize_magentic_one_with_config(
    request: MagenticOneConfigRequest
):
    """Initialize or reinitialize Magentic One with custom configuration"""
    global magentic_one_framework

    try:
        # Shutdown existing framework if any
        if magentic_one_framework:
            await magentic_one_framework.shutdown()

        # Create new configuration
        config = GeminiMagenticConfig(
            model_name=request.model_name,
            gemini_api_key=request.gemini_api_key,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            max_rounds=request.max_rounds,
            enable_code_execution=request.enable_code_execution,
            enable_file_handling=request.enable_file_handling
        )

        # Initialize new framework
        magentic_one_framework = MagenticOneGeminiTest(config)
        await magentic_one_framework.initialize()

        logger.info("Magentic One framework reinitialized with custom config")

        return {"status": "initialized", "message": "Magentic One framework initialized successfully"}

    except Exception as e:
        logger.error("Failed to initialize Magentic One framework", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/magentic-one/health", response_model=Dict[str, Any])
async def magentic_one_health_check():
    """Health check for Magentic One framework"""
    try:
        if magentic_one_framework is None:
            return {
                "status": "unavailable",
                "message": "Magentic One framework not initialized",
                "requires": "GEMINI_API_KEY environment variable"
            }

        # Test basic functionality
        status = await magentic_one_framework.get_framework_status()

        return {
            "status": "healthy" if status["initialized"] else "unhealthy",
            "framework_status": status,
            "message": "Magentic One framework is operational"
        }

    except Exception as e:
        logger.error("Magentic One health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Magentic One framework health check failed"
        }


def create_app() -> FastAPI:
    """Create and configure the FastAPI application"""
    return app


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_config={
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
            },
            "root": {
                "level": "INFO",
                "handlers": ["default"],
            },
        }
    )
