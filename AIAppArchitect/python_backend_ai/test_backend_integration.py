#!/usr/bin/env python3
"""
Backend Integration Test for Magentic One Gemini
Tests the FastAPI backend endpoints for the Magentic One implementation
"""

import asyncio
import aiohttp
import json
import os
import sys
import time
from typing import Dict, Any, Optional

# Test configuration
BASE_URL = "http://localhost:8000"
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")


class BackendTestClient:
    """Test client for the Aizen AI Backend"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def get(self, endpoint: str) -> Dict[str, Any]:
        """Make GET request"""
        async with self.session.get(f"{self.base_url}{endpoint}") as response:
            return await response.json()

    async def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make POST request"""
        async with self.session.post(
            f"{self.base_url}{endpoint}",
            json=data,
            headers={"Content-Type": "application/json"}
        ) as response:
            return await response.json()


async def test_backend_health():
    """Test basic backend health"""
    print("🏥 Testing Backend Health...")
    
    async with BackendTestClient() as client:
        try:
            # Test main health endpoint
            health = await client.get("/health")
            print(f"  ✅ Main Backend: {health['status']}")
            
            # Test Magentic One health endpoint
            magentic_health = await client.get("/magentic-one/health")
            print(f"  🤖 Magentic One: {magentic_health['status']}")
            
            if magentic_health['status'] == 'unavailable':
                print(f"     ⚠️  {magentic_health['message']}")
                return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ Health check failed: {e}")
            return False


async def test_magentic_one_initialization():
    """Test Magentic One initialization with custom config"""
    print("\n🔧 Testing Magentic One Initialization...")
    
    if not GEMINI_API_KEY:
        print("  ⚠️  Skipping - GEMINI_API_KEY not set")
        return False
    
    async with BackendTestClient() as client:
        try:
            config_data = {
                "model_name": "gemini-pro",
                "temperature": 0.7,
                "max_tokens": 1024,
                "max_rounds": 2,
                "enable_code_execution": True,
                "enable_file_handling": True,
                "gemini_api_key": GEMINI_API_KEY
            }
            
            result = await client.post("/magentic-one/initialize", config_data)
            print(f"  ✅ Initialization: {result['status']}")
            print(f"     Message: {result['message']}")
            
            return result['status'] == 'initialized'
            
        except Exception as e:
            print(f"  ❌ Initialization failed: {e}")
            return False


async def test_magentic_one_status():
    """Test Magentic One status endpoint"""
    print("\n📊 Testing Magentic One Status...")
    
    async with BackendTestClient() as client:
        try:
            status = await client.get("/magentic-one/status")
            
            print(f"  ✅ Framework Status:")
            print(f"     Initialized: {status['initialized']}")
            print(f"     Model: {status['config']['model_name']}")
            print(f"     Agents: {status['agents']}")
            print(f"     Total Tasks: {sum(status['task_counts'].values())}")
            
            return status['initialized']
            
        except Exception as e:
            print(f"  ❌ Status check failed: {e}")
            return False


async def test_magentic_one_agents():
    """Test listing Magentic One agents"""
    print("\n🤖 Testing Magentic One Agents...")
    
    async with BackendTestClient() as client:
        try:
            agents = await client.get("/magentic-one/agents")
            
            print(f"  ✅ Available Agents ({len(agents)}):")
            for agent in agents:
                print(f"     - {agent['name']} ({agent['role']})")
            
            return len(agents) > 0
            
        except Exception as e:
            print(f"  ❌ Agent listing failed: {e}")
            return False


async def test_magentic_one_task_execution():
    """Test Magentic One task execution"""
    print("\n🎯 Testing Magentic One Task Execution...")
    
    async with BackendTestClient() as client:
        try:
            # Simple test task
            task_data = {
                "task_description": "What is the capital of Japan?",
                "model_name": "gemini-pro",
                "temperature": 0.5,
                "max_tokens": 512,
                "max_rounds": 2
            }
            
            print("  📝 Executing simple query task...")
            start_time = time.time()
            
            result = await client.post("/magentic-one/execute", task_data)
            
            execution_time = time.time() - start_time
            
            if result['success']:
                print(f"  ✅ Task completed in {execution_time:.2f}s")
                print(f"     Task ID: {result['task_id']}")
                print(f"     Agents Used: {', '.join(result['agents_used'])}")
                print(f"     Result Preview: {result['result'][:100]}...")
                
                # Test task status endpoint
                task_status = await client.get(f"/magentic-one/tasks/{result['task_id']}")
                print(f"     Task Status: {task_status['status']}")
                
                return True
            else:
                print(f"  ❌ Task failed: {result.get('error', 'Unknown error')}")
                return False
            
        except Exception as e:
            print(f"  ❌ Task execution failed: {e}")
            return False


async def test_magentic_one_complex_task():
    """Test Magentic One with a more complex task"""
    print("\n🧠 Testing Complex Task Execution...")
    
    async with BackendTestClient() as client:
        try:
            # Complex task requiring multiple agents
            task_data = {
                "task_description": "Write a Python function to calculate the factorial of a number and explain how it works",
                "model_name": "gemini-pro",
                "temperature": 0.7,
                "max_tokens": 1024,
                "max_rounds": 3
            }
            
            print("  📝 Executing complex coding task...")
            start_time = time.time()
            
            result = await client.post("/magentic-one/execute", task_data)
            
            execution_time = time.time() - start_time
            
            if result['success']:
                print(f"  ✅ Complex task completed in {execution_time:.2f}s")
                print(f"     Agents Used: {', '.join(result['agents_used'])}")
                print(f"     Agent Interactions: {result['agent_interactions']}")
                
                # Check if coder agent was used
                if 'coder' in result['agents_used']:
                    print("     ✅ Coder agent was properly utilized")
                
                return True
            else:
                print(f"  ❌ Complex task failed: {result.get('error', 'Unknown error')}")
                return False
            
        except Exception as e:
            print(f"  ❌ Complex task execution failed: {e}")
            return False


async def test_performance_metrics():
    """Test performance with multiple quick tasks"""
    print("\n⚡ Testing Performance Metrics...")
    
    async with BackendTestClient() as client:
        try:
            tasks = [
                "What is 2 + 2?",
                "Name a programming language",
                "What color is the sky?",
                "Define AI in one word"
            ]
            
            total_time = 0
            successful_tasks = 0
            
            for i, task_desc in enumerate(tasks, 1):
                print(f"  📝 Task {i}: {task_desc}")
                
                task_data = {
                    "task_description": task_desc,
                    "model_name": "gemini-pro",
                    "temperature": 0.3,
                    "max_tokens": 256,
                    "max_rounds": 1
                }
                
                start_time = time.time()
                result = await client.post("/magentic-one/execute", task_data)
                execution_time = time.time() - start_time
                
                total_time += execution_time
                
                if result['success']:
                    successful_tasks += 1
                    print(f"     ✅ {execution_time:.2f}s")
                else:
                    print(f"     ❌ {execution_time:.2f}s - Failed")
            
            avg_time = total_time / len(tasks)
            success_rate = (successful_tasks / len(tasks)) * 100
            
            print(f"\n  📊 Performance Summary:")
            print(f"     Total Time: {total_time:.2f}s")
            print(f"     Average Time: {avg_time:.2f}s")
            print(f"     Success Rate: {success_rate:.1f}%")
            print(f"     Tasks/Minute: {(len(tasks) / total_time) * 60:.1f}")
            
            return success_rate >= 75  # 75% success threshold
            
        except Exception as e:
            print(f"  ❌ Performance test failed: {e}")
            return False


async def run_all_tests():
    """Run all backend integration tests"""
    print("🚀 Starting Backend Integration Tests")
    print("=" * 50)
    
    if not GEMINI_API_KEY:
        print("❌ GEMINI_API_KEY environment variable not set")
        print("   Get your API key from: https://makersuite.google.com/app/apikey")
        print("   Set it with: export GEMINI_API_KEY='your-key-here'")
        return False
    
    tests = [
        ("Backend Health", test_backend_health),
        ("Magentic One Initialization", test_magentic_one_initialization),
        ("Framework Status", test_magentic_one_status),
        ("Agent Listing", test_magentic_one_agents),
        ("Simple Task Execution", test_magentic_one_task_execution),
        ("Complex Task Execution", test_magentic_one_complex_task),
        ("Performance Metrics", test_performance_metrics)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            success = await test_func()
            if success:
                passed_tests += 1
            
        except Exception as e:
            print(f"\n❌ Test '{test_name}' crashed: {e}")
    
    print(f"\n📋 Test Results Summary:")
    print("=" * 50)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    overall_success = passed_tests >= (total_tests * 0.8)  # 80% threshold
    
    if overall_success:
        print("\n🎉 Backend Integration Tests: SUCCESS")
        print("   The Magentic One Gemini backend is working correctly!")
    else:
        print("\n❌ Backend Integration Tests: FAILURE")
        print("   Some tests failed. Check the output above for details.")
    
    return overall_success


async def main():
    """Main test function"""
    print("🔧 Make sure the backend server is running:")
    print("   cd AIAppArchitect/python_backend_ai")
    print("   python start_server.py")
    print("   (or python api/main.py)")
    print()
    
    try:
        success = await run_all_tests()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite crashed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
