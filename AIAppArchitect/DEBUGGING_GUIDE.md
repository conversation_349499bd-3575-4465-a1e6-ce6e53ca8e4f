# 🐛 Aizen AI Extension Debugging Guide

## ❌ **Error: "Failed loading extension - property 'name' is mandatory"**

### **Root Cause**
This error occurs when VS Code is opened from the wrong directory. VS Code needs to be opened from the `AIAppArchitect` directory specifically, not the parent directory.

### **✅ Solution**

#### **Method 1: Use the Launch Script**
```bash
cd "/media/bik/c dive(ii)/aizen_ai_extension/AIAppArchitect"
./launch_extension.sh
```

#### **Method 2: Manual Launch**
```bash
cd "/media/bik/c dive(ii)/aizen_ai_extension/AIAppArchitect"
npm run build
code --extensionDevelopmentPath="$(pwd)" --new-window
```

#### **Method 3: Open in VS Code First**
1. Open VS Code
2. File → Open Folder
3. Navigate to `/media/bik/c dive(ii)/aizen_ai_extension/AIAppArchitect`
4. Press F5 to run the extension

---

## 🔍 **Debugging Steps**

### **1. Verify Extension Structure**
```bash
node test_extension_complete.js
```
Should show: `🎉 ALL TESTS PASSED!`

### **2. Check Package.json**
```bash
node -e "console.log('Extension name:', JSON.parse(require('fs').readFileSync('package.json', 'utf8')).name)"
```
Should show: `Extension name: aizen-revolutionary-ai`

### **3. Verify Build Output**
```bash
ls -la out/
```
Should show:
- `extension.js` (main extension file)
- `ui/` directory with CSS, HTML, and JS files

### **4. Check VS Code Extension Host Logs**
1. In the Extension Development Host window
2. Help → Toggle Developer Tools
3. Check Console tab for errors

---

## 🎯 **Expected Behavior**

### **When Extension Loads Successfully:**
1. ✅ New VS Code window opens (Extension Development Host)
2. ✅ "Aizen AI" icon appears in Activity Bar (left sidebar)
3. ✅ Clicking icon opens the AI Chat webview
4. ✅ Modern Liquid Glass UI loads with:
   - Glass morphism effects
   - Gradient backgrounds
   - HugeIcons integration
   - Interactive chat interface

### **If Webview Doesn't Load:**
1. Check Developer Tools Console for CSP errors
2. Verify all UI files exist in `out/ui/`
3. Check webview provider logs

---

## 🚨 **Common Issues & Fixes**

### **Issue: Extension Icon Missing**
**Fix:** Ensure `media/icons/aizen-logo-white.svg` exists

### **Issue: Webview Shows Blank Page**
**Fix:** Check Content Security Policy in provider

### **Issue: CSS Not Loading**
**Fix:** Verify files copied to `out/ui/` directory

### **Issue: TypeScript Compilation Errors**
**Fix:** Run `npm run compile` and check for errors

---

## 📞 **Getting Help**

If you're still having issues:

1. **Run the comprehensive test:**
   ```bash
   node test_extension_complete.js
   ```

2. **Check the build:**
   ```bash
   npm run build
   ```

3. **Verify the directory structure:**
   ```bash
   find . -name "*.js" -o -name "*.css" -o -name "*.html" | grep out/
   ```

4. **Check VS Code logs:**
   - Help → Toggle Developer Tools → Console
   - Look for red error messages

The extension should work perfectly when opened from the correct directory! 🎉
