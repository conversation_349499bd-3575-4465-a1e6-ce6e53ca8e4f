NEW

Apply for [Startups](https://e2b.dev/startups) and [Research](https://e2b.dev/research) programs and get free Pro Tier

AI-generated code

A1-G3NER4–ED c0de

4I-GE7ER#T3D ©–de

A\*-GEN€RATED c0de

—I-G3NERA+ED c°de

# RUN AI-GENERATED code SECURELY in your APP

E2B is an open-source runtime for executing AI-generated code in secure cloud sandboxes. Made for agentic & AI use cases.

[START FOR FREE](https://e2b.dev/auth/sign-up) [VIEW DOCS](https://e2b.dev/docs)

TRUSTED BY

LLM

\[.500\]

\[.873\]

\[.542\]

\[.704\]

\[.285\]

\[.717\]

\[.598\]

\[.557\]

\[.232\]

\[.746\]

\[.211\]

\[.013\]

\[.510\]

\[.718\]

\[.621\]

\[.223\]

\[.124\]

\[.801\]

\[.798\]

\[.117\]️

\[.817\]

\[.070\]

\[.353\]

‍

\[.833\]

\[.477\]

\[.620\]

\[.829\]

\[.195\]

\[.245\]

\[.891\]

\[.454\]

\[.145\]

\[.984\]

\[.634\]

\[.342\]

\[.746\]

\[.330\]

\[.103\]

\[.742\]

\[.004\]

\[.165\]

\[.459\]

\[.597\]

\[.910\]

\[.072\]

\[.336\]

\[.788\]

\[.400\]

\[.410\]

\[.273\]

\[.477\]

\[.087\]

\[.707\]

\[.212\]

\[.642\]

\[.829\]

\[.616\]

\[.805\]

\[.206\]

\[.505\]

\[.265\]

\[.043\]

\[.829\]

\[.195\]

\[.245\]

\[.891\]

\[.505\]

\[.265\]

\[.043\]

‍

\[.195\]

\[.245\]

\[.891\]

\[.410\]

\[.273\]

\[.505\]

\[.765\]

\[.143\]

\[.095\]

\[.335\]

\[.891\]

\[.287\]

\[.921\]

\[.206\]

\[.813\]

\[.104\]

\[.665\]

\[.083\]

\[.900\]

\[.040\]

\[.784\]

\[.087\]

\[.171\]

\[.616\]

\[.805\]

\[.206\]

\[.505\]

\[.265\]

\[.043\]

\[.829\]

\[.195\]

\[.245\]

\[.891\]

\[.921\]

\[.820\]

\[.061\]️

\[.679\]

\[.034\]

\[.810\]

\[.322\]

\[.061\]

\[.381\]️

\[.285\]

\[.679\]

\[.034\]

\[.810\]

‍

\[.061\]

\[.381\]️

\[.285\]

\[.179\]

\[.034\]

\[.810\]

\[.061\]

\[.001\]️

\[.275\]

\[.551\]

\[.707\]

\[.212\]

\[.642\]

\[.660\]

\[.102\]

\[.790\]

\[.041\]

\[.081\]️

\[.445\]

\[.021\]

\[.517\]

\[.019\]

\[.311\]

\[.921\]

\[.820\]

\[.061\]️

\[.679\]

\[.034\]

\[.810\]

\[.322\]

\[.061\]

\[.381\]️

\[.285\]

\[.817\]

\[.070\]

\[.353\]

\[.744\]

\[.663\]

\[.844\]

\[.452\]

\[.045\]

\[.305\]

\[.027\]

\[.744\]

\[.663\]

\[.844\]

‍

\[.452\]

\[.045\]

\[.027\]

\[.733\]

\[.463\]

\[.824\]

\[.452\]

\[.145\]

\[.677\]

\[.505\]

\[.265\]

\[.043\]

\[.829\]

\[.733\]

\[.463\]

\[.824\]

\[.452\]

\[.145\]

\[.677\]

\[.505\]

\[.265\]

\[.043\]

\[.829\]

\[.817\]

\[.070\]

\[.353\]

\[.744\]

\[.663\]

\[.844\]

\[.452\]

\[.045\]

\[.305\]

\[.027\]

\[.820\]

\[.061\]️

\[.679\]

\[.034\]

\[.810\]

\[.322\]

\[.070\]

\[.353\]

\[.744\]

\[.663\]

\[.034\]

\[.810\]

\[.322\]

‍

\[.070\]

\[.353\]

\[.663\]

\[.114\]

\[.077\]

\[.722\]

\[.084\]

\[.253\]

\[.665\]

\[.452\]

\[.045\]

\[.305\]

\[.027\]

\[.874\]

\[.104\]

\[.022\]

\[.604\]

\[.310\]

\[.103\]

\[.502\]

\[.178\]

\[.285\]

\[.006\]

\]·········\[\
\
\]·········\[\
\
\]·········\[\
\
\]·\*·······\[\
\
\]·········\[\
\
\]··\*······\[\
\
\]···\*·····\[\
\
\]·\*·······\[\
\
\]····\*····\[\
\
\]·····\*···\[\
\
\]···\*·····\[\
\
\]·······\*·\[\
\
\]·······\*·\[\
\
\]·····\*···\[\
\
\]·········\[\
\
\]·········\[\
\
\]·······\*·\[\
\
\]·········\[\
\
E2B SANDBOX\
\
RUNNING CODE…\
\
\]·····\[\
\
\]·····\[\
\
\]·····\[\
\
\]·\*···\[\
\
\]·····\[\
\
\]··\*··\[\
\
\]···\*·\[\
\
\]·\*···\[\
\
\]·····\[\
\
\]·····\[\
\
\]··\*··\[\
\
\]·····\[\
\
\]·····\[\
\
\]···\*·\[\
\
\]·····\[\
\
\]·····\[\
\
\]·····\[\
\
\]·····\[\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
✶✶\
\
\]·····\[\
\
\]·····\[\
\
\]·····\[\
\
\]·\*···\[\
\
\]·····\[\
\
\]··\*··\[\
\
\]···\*·\[\
\
\]·\*···\[\
\
\]·····\[\
\
\]·····\[\
\
\]··\*··\[\
\
\]·····\[\
\
\]·····\[\
\
\]···\*·\[\
\
\]·····\[\
\
\]·····\[\
\
\]·····\[\
\
\]·····\[\
\
\[\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\]\
\
\[%%%%%\_\_\_\_\_\_\_\_\_\_\]\
\
\[%%%%%%%%%%\_\_\_\_\_\]\
\
\[%%%%%%%%%%%%%%%\]\
\
CPU: 8 × ▤  /  RAM: 4 GB\
\
\]·········\[\
\
\]·········\[\
\
\]·········\[\
\
\]·\*·······\[\
\
\]·········\[\
\
\]··\*······\[\
\
\]···\*·····\[\
\
\]·\*·······\[\
\
\]····\*····\[\
\
\]·····\*···\[\
\
\]···\*·····\[\
\
\]·······\*·\[\
\
\]·······\*·\[\
\
\]·····\*···\[\
\
\]·········\[\
\
\]·········\[\
\
\]·······\*·\[\
\
\]·········\[\
\
OUTPUT\
\
8 – ––––– ––– ––––– ––– ––––– –––\
\
7 – ––––– –––@@@@@––– ––––– –––\
\
6 – ––––– –––@@@@@––– ––––– –––\
\
5 – @@@@@ –––@@@@@––– ––––– –––\
\
4 – @@@@@ –––@@@@@––– ––––– –––\
\
3 – @@@@@ –––@@@@@––– @@@@@ –––\
\
2 – @@@@@ –––@@@@@––– @@@@@ –––\
\
1 – @@@@@ –––@@@@@––– @@@@@ –––\
\
–––––––––––––––––––––––––––––––––\
\
      A         B         C\
\
✓ CHART-1\
\
OUTPUT\
\
\_\_\_\_\_\_  \_\_\_\_\_\_    \_\_\_\_\_\_\
\
❘    ❘\_\❘    ❘\_\  ❘    ❘\_\\
\
╔═══════╗╔═══════╗ ╔═══════╗\
\
║  CSV  ║║  TXT  ║ ║  .JS  ║\
\
╚═══════╝╚═══════╝ ╚═══════╝\
\
❘\_\_\_\_\_\_❘ ❘\_\_\_\_\_\_❘  ❘\_\_\_\_\_\_❘\
\
✓ File\
\
OUTPUT\
\
╔ Email ══════════════╗\
\
║ <EMAIL>      ║\
\
╚═════════════════════╝\
\
╔ Pw ═════════════════╗\
\
║ \*\*\*\*\*\*\*\*            ║\
\
╚═════════════════════╝\
\
╔═════════════════════╗\
\
║       Sign In       ║\
\
╚═════════════════════╝\
\
✓ UI\
\
OUTPUT\
\
NVDA                            @\
\
$120.91 @\
\
+32%                          @\
\
                       @@@   @\
\
            @@@       @   @ @\
\
          @@   @     @     @\
\
      @@@@      @   @\
\
@@@@           @@@\
\
@\
\
@\
\
✓ CHART-2\
\
OUTPUT\
\
1999 @@@@@@@@@@ │         │\
\
1998 @@@        │         │\
\
1997 @@@@@@@@@@@@@        │\
\
1996 @@@@@@@    │         │\
\
1995 @@@@@@@@@@@@@@@@     │\
\
1994 @@@@@@@@@@@@@@@@@@@@@@@@@\
\
1993 @@@@@      │         │\
\
1992 @@@@@@@@@@ │         │\
\
–––––––––––––––––––––––––––––––––\
\
      2         4         6\
\
✓ CHART-3\
\
OUTPUT\
\
/!\\
\
‍\
\
Error: \[$rootScope:inprog\] $apply\
\
already in progress\
\
http://errors.angular.js.org/1.3\
\
.15/$rootScope/inprog?p0=\
\
%24apply\
\
atangular.js:63\
\
☓ Error\
\
OUTPUT\
\
8 ––––│–––––––––│–––––––––│–––––@\
\
7 ––––│–––––––––│–––@–––––│––––@–\
\
6 ––––│–––––––––│––@–@–@@@@–––@––\
\
5 ––––│–––––@@@–│–@–––@–––│@–@–@@\
\
4 @@@@│–––@@–––@│@–––@–@––│–@–@––\
\
3 ––––@@@@––@@@@@–––@–––@@│@@@–––\
\
2 –––@│@@@@@––––│@@@––––––│––––––\
\
1 –@@–│–––––––––│–––––––––│––––––\
\
–––––––––––––––––––––––––––––––––\
\
      A         B         C\
\
✓ CHART-4\
\
20K+\
\
DEVELOPERS\
\
250K+\
\
MONTHLY DOWNLOADS\
\
10M+\
\
STARTED SANDBOXES\
\
\[\
\
USE CASES\
\
\]\
\
AI\
\
A1\
\
–I\
\
A\*\
\
4I\
\
## Build for AI  Use Cases\
\
From running short AI-generated code snippets, up to fully autonomous AI agents.\
\
\> HOVER (↓↓)\
\
/EXPLORE\
\
.CSV\
\
.PDF\
\
.XLS\
\
.TSV\
\
\\_\\_\\_\_\_\
\
❘   ❘\_\\
\
❘\_\_\_\_\_❘\
\
### AI Data Analysis\
\
From running short AI-generated code snippets, up to fully autonomous AI agents.\
\
[LEARN MORE](https://github.com/e2b-dev/ai-analyst)\
\
@@@\
\
@@@@@@\
\
@@@@@@@@@\
\
     /\\\_/\
\
/\\/\
\
\_/     X%\
\
%%%%%%   %%%%%%%%%\
\
%%%\
\
@     @\
\
@ @@ @\
\
@ @ @ @ @\
\
### AI Data Visualization\
\
Run AI-generated code to render charts, plots, and visual outputs based on your data.\
\
[LEARN MORE](https://github.com/e2b-dev/ai-analyst)\
\
======\
\
========\
\
=== ===\
\
<\
\
======\
\
========\
\
=== ===\
\
====<\
\
========\
\
=== ===\
\
====\
\
<\
\
========\
\
=== ===\
\
====\
\
‍===== =<\
\
=== ===\
\
====\
\
===== =\
\
<\
\
=== ===\
\
====\
\
===== =\
\
= ==<\
\
====\
\
===== =\
\
= ==\
\
<\
\
====\
\
===== =\
\
= ==\
\
== =====<\
\
===== =\
\
= ==\
\
== =====\
\
<\
\
===== =\
\
= ==\
\
== =====\
\
======<\
\
= ==\
\
== =====\
\
======\
\
<\
\
= ==\
\
== =====\
\
======\
\
========<\
\
== =====\
\
======\
\
========\
\
<\
\
== =====\
\
======\
\
========\
\
=== ===<\
\
### Coding Agents\
\
Use sandbox to execute code, use I/O, access the internet, or start terminal commands.\
\
[LEARN MORE](https://e2b.dev/docs/quickstart)\
\
╔   ═   ╗\
\
        ╣\
\
╚     ═\
\
╔ ═ ═   ╗\
\
═ ═══  ╣\
\
╚   ═ ═ ╝\
\
╔ ═ ═ ═ ╗\
\
╠═ ═══ ═╣\
\
╚ ═ ═ ══╝\
\
╔═══════╗\
\
╠═══════╣\
\
║       ║\
\
╚═══════╝\
\
╔═══════╗\
\
╠═══════╣\
\
║   ✓   ║\
\
╚═══════╝\
\
### Generative UI\
\
Use sandbox as a code runtime for AI-generated apps. Supports any language and framework.\
\
[LEARN MORE](https://github.com/e2b-dev/fragments)\
\
==╔═══╗==\
\
==║ ✓ ║== ==╚═══╝==\
\
==╔═══╗==\
\
==║ ✓ ║== ==╚═══╝==\
\
==╔═══╗==\
\
==║ ✓ ║== ==╚═══╝==\
\
==╔═══╗==\
\
==║ ✓ ║== ==╚═══╝==\
\
==╔═══╗==\
\
==║ ✓ ║== ==╚═══╝==\
\
==╔═══╗==\
\
==║ × ║== ==╚═══╝==\
\
==╔═══╗==\
\
==║ × ║====╚═══╝==\
\
### Codegen Evals\
\
Use sandboxes for your codegen gym for popular evals like swe-benchmark or internal evals.\
\
[LEARN MORE](https://e2b.dev/docs/quickstart)\
\
### Computer Use\
\
Use Desktop Sandbox to provide secure virtual computers in cloud for your LLM.\
\
[LEARN MORE](https://github.com/e2b-dev/surf)\
\
### Deep Research Agents\
\
Let your agent work on a complex task in a sandboxed environment.\
\
[LEARN MORE](https://e2b.dev/docs/quickstart)\
\
### Read about how companies  and developers use E2B\
\
[SEE CUSTOMER CASE STUDIES](https://www.e2b.dev/blog/category/case-studies)\
\
[JOIN DISCORD COMMUNITY](https://discord.com/invite/U7KEcGErtQ)\
\
\[\
\
GET STARTED\
\
\]\
\
A FEW LINES\
\
A F3W L1NES\
\
4 FE# L!NES\
\
A FEW 7INE5\
\
– FEW ILNES\
\
## IN YOUR CODE  WITH A FEW LINES\
\
Need help? Join [Discord](https://discord.com/invite/U7KEcGErtQ), check [Docs](https://e2b.dev/docs) or [Email us](mailto:<EMAIL>).\
\
+\
\
+\
\
+\
\
+\
\
+\
\
+\
\
[NODE.JS](https://e2b.dev/#w-tabs-0-data-w-pane-0) [PYTHON](https://e2b.dev/#w-tabs-0-data-w-pane-1) [VERCEL](https://e2b.dev/#w-tabs-0-data-w-pane-2) [OPEN AI](https://e2b.dev/#w-tabs-0-data-w-pane-3) [ANTHROPIC](https://e2b.dev/#w-tabs-0-data-w-pane-4) [Mistral](https://e2b.dev/#w-tabs-0-data-w-pane-5) [Llama](https://e2b.dev/#w-tabs-0-data-w-pane-6) [LangChain](https://e2b.dev/#w-tabs-0-data-w-pane-7) [LlamaIndex](https://e2b.dev/#w-tabs-0-data-w-pane-8) [More](https://e2b.dev/#w-tabs-0-data-w-pane-9)\
\
1\
\
2\
\
3\
\
4\
\
5\
\
6\
\
7\
\
8\
\
9\
\
10\
\
11\
\
12\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
‍~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
```typescript\
// npm install @e2b/code-interpreter\
import { Sandbox } from '@e2b/code-interpreter'\
\
// Create a E2B Code Interpreter with JavaScript kernel\
const sandbox = await Sandbox.create()\
\
// Execute JavaScript cells\
await sandbox.runCode('x = 1')\
const execution = await sandbox.runCode('x+=1; x')\
\
// Outputs 2\
console.log(execution.text)\
```\
\
“~/index.ts”\
\
1\
\
2\
\
3\
\
4\
\
5\
\
6\
\
7\
\
8\
\
9\
\
10\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
‍~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
```python\
# pip install e2b-code-interpreter\
from e2b_code_interpreter import Sandbox\
\
# Create a E2B Sandbox\
with Sandbox() as sandbox:\
    # Run code\
    sandbox.run_code("x = 1")\
    execution = sandbox.run_code("x+=1; x")\
\
    print(execution.text) # outputs 2\
```\
\
“~/index.py”\
\
1\
\
2\
\
3\
\
4\
\
5\
\
6\
\
7\
\
8\
\
9\
\
10\
\
11\
\
12\
\
13\
\
14\
\
15\
\
16\
\
17\
\
18\
\
19\
\
20\
\
21\
\
22\
\
23\
\
24\
\
25\
\
26\
\
27\
\
28\
\
29\
\
30\
\
31\
\
32\
\
33\
\
34\
\
35‍‍\
\
```typescript\
// npm install ai @ai-sdk/openai zod @e2b/code-interpreter\
import { openai } from '@ai-sdk/openai'\
import { generateText } from 'ai'\
import z from 'zod'\
import { Sandbox } from '@e2b/code-interpreter'\
\
// Create OpenAI client\
const model = openai('gpt-4o')\
\
const prompt = "Calculate how many r's are in the word 'strawberry'"\
\
// Generate text with OpenAI\
const { text } = await generateText({\
  model,\
  prompt,\
  tools: {\
    // Define a tool that runs code in a sandbox\
    codeInterpreter: {\
      description: 'Execute python code in a Jupyter notebook cell and return result',\
      parameters: z.object({\
        code: z.string().describe('The python code to execute in a single cell'),\
      }),\
      execute: async ({ code }) => {\
        // Create a sandbox, execute LLM-generated code, and return the result\
        const sandbox = await Sandbox.create()\
        const { text, results, logs, error } = await sandbox.runCode(code)\
        return results\
      },\
    },\
  },\
  // This is required to feed the tool call result back to the LLM\
  maxSteps: 2\
})\
\
console.log(text)\
```\
\
“~/aisdk\_tools.ts”\
\
1\
\
2\
\
3\
\
4\
\
5\
\
6\
\
7\
\
8\
\
9\
\
10\
\
11\
\
12\
\
13\
\
14\
\
15\
\
16\
\
17\
\
18\
\
19\
\
20\
\
21\
\
22\
\
23\
\
24\
\
25\
\
26\
\
27\
\
28‍‍\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
```python\
# pip install openai e2b-code-interpreter\
from openai import OpenAI\
from e2b_code_interpreter import Sandbox\
\
# Create OpenAI client\
client = OpenAI()\
system = "You are a helpful assistant that can execute python code in a Jupyter notebook. Only respond with the code to be executed and nothing else. Strip backticks in code blocks."\
prompt = "Calculate how many r's are in the word 'strawberry'"\
\
# Send messages to OpenAI API\
response = client.chat.completions.create(\
    model="gpt-4o",\
    messages=[\
        {"role": "system", "content": system},\
        {"role": "user", "content": prompt}\
    ]\
)\
\
# Extract the code from the response\
code = response.choices[0].message.content\
\
# Execute code in E2B Sandbox\
if code:\
    with Sandbox() as sandbox:\
        execution = sandbox.run_code(code)\
        result = execution.text\
\
    print(result)\
```\
\
“~/oai.py”\
\
1\
\
2\
\
3\
\
4\
\
5\
\
6\
\
7\
\
8\
\
9\
\
10\
\
11\
\
12\
\
13\
\
14\
\
15\
\
16\
\
17\
\
18\
\
19\
\
20\
\
21\
\
22\
\
23\
\
24\
\
25\
\
26\
\
27\
\
28‍‍\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
```python\
# pip install anthropic e2b-code-interpreter\
from anthropic import Anthropic\
from e2b_code_interpreter import Sandbox\
\
# Create Anthropic client\
anthropic = Anthropic()\
system_prompt = "You are a helpful assistant that can execute python code in a Jupyter notebook. Only respond with the code to be executed and nothing else. Strip backticks in code blocks."\
prompt = "Calculate how many r's are in the word 'strawberry'"\
\
# Send messages to Anthropic API\
response = anthropic.messages.create(\
    model="claude-3-5-sonnet-20240620",\
    max_tokens=1024,\
    messages=[\
        {"role": "assistant", "content": system_prompt},\
        {"role": "user", "content": prompt}\
    ]\
)\
\
# Extract code from response\
code = response.content[0].text\
\
# Execute code in E2B Sandbox\
with Sandbox() as sandbox:\
    execution = sandbox.run_code(code)\
    result = execution.logs.stdout\
\
print(result)\
```\
\
“~/anth.py”\
\
1\
\
2\
\
3\
\
4\
\
5\
\
6\
\
7\
\
8\
\
9\
\
10\
\
11\
\
12\
\
13\
\
14\
\
15\
\
16\
\
17\
\
18\
\
19\
\
20\
\
21\
\
22\
\
23\
\
24\
\
25\
\
26\
\
27\
\
28\
\
29\
\
30‍‍\
\
~\
\
~\
\
~\
\
~\
\
~\
\
```python\
# pip install mistralai e2b-code-interpreter\
import os\
from mistralai import Mistral\
from e2b_code_interpreter import Sandbox\
\
api_key = os.environ["MISTRAL_API_KEY"]\
\
# Create Mistral client\
client = Mistral(api_key=api_key)\
system_prompt = "You are a helpful assistant that can execute python code in a Jupyter notebook. Only respond with the code to be executed and nothing else. Strip backticks in code blocks."\
prompt = "Calculate how many r's are in the word 'strawberry'"\
\
# Send the prompt to the model\
response = client.chat.complete(\
    model="codestral-latest",\
    messages=[\
        {"role": "system", "content": system_prompt},\
        {"role": "user", "content": prompt}\
    ]\
)\
\
# Extract the code from the response\
code = response.choices[0].message.content\
\
# Execute code in E2B Sandbox\
with Sandbox() as sandbox:\
    execution = sandbox.run_code(code)\
    result = execution.text\
\
print(result)\
```\
\
“~/mistral.py”\
\
1\
\
2\
\
3\
\
4\
\
5\
\
6\
\
7\
\
8\
\
9\
\
10\
\
11\
\
12\
\
13\
\
14\
\
15\
\
16\
\
17\
\
18\
\
19\
\
20\
\
21\
\
22\
\
23\
\
24\
\
25‍‍\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
```python\
# pip install ollama\
import ollama\
from e2b_code_interpreter import Sandbox\
\
# Send the prompt to the model\
response = ollama.chat(model="llama3.2", messages=[\
    {\
        "role": "system",\
        "content": "You are a helpful assistant that can execute python code in a Jupyter notebook. Only respond with the code to be executed and nothing else. Strip backticks in code blocks."\
    },\
    {\
        "role": "user",\
        "content": "Calculate how many r's are in the word 'strawberry'"\
    }\
])\
\
# Extract the code from the response\
code = response['message']['content']\
\
# Execute code in E2B Sandbox\
with Sandbox() as sandbox:\
    execution = sandbox.run_code(code)\
    result = execution.logs.stdout\
\
print(result)\
```\
\
“~/llama.py”\
\
1\
\
2\
\
3\
\
4\
\
5\
\
6\
\
7\
\
8\
\
9\
\
10\
\
11\
\
12\
\
13\
\
14\
\
15\
\
16\
\
17\
\
18\
\
19\
\
20\
\
21\
\
22\
\
23\
\
24\
\
25\
\
26\
\
27\
\
28\
\
29\
\
30‍‍\
\
~\
\
~\
\
~\
\
~\
\
~\
\
```python\
# pip install langchain langchain-openai e2b-code-interpreter\
from langchain_openai import ChatOpenAI\
from langchain_core.prompts import ChatPromptTemplate\
from langchain_core.output_parsers import StrOutputParser\
from e2b_code_interpreter import Sandbox\
\
system_prompt = "You are a helpful assistant that can execute python code in a Jupyter notebook. Only respond with the code to be executed and nothing else. Strip backticks in code blocks."\
prompt = "Calculate how many r's are in the word 'strawberry'"\
\
# Create LangChain components\
llm = ChatOpenAI(model="gpt-4o")\
prompt_template = ChatPromptTemplate.from_messages([\
    ("system", system_prompt),\
    ("human", "{input}")\
])\
\
output_parser = StrOutputParser()\
\
# Create the chain\
chain = prompt_template | llm | output_parser\
\
# Run the chain\
code = chain.invoke({"input": prompt})\
\
# Execute code in E2B Sandbox\
with Sandbox() as sandbox:\
    execution = sandbox.run_code(code)\
    result = execution.text\
\
print(result)\
```\
\
“~/lchain.py”\
\
1\
\
2\
\
3\
\
4\
\
5\
\
6\
\
7\
\
8\
\
9\
\
10\
\
11\
\
12\
\
13\
\
14\
\
15\
\
16\
\
17\
\
18\
\
19\
\
20\
\
21\
\
22\
\
23\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
~\
\
```python\
from llama_index.core.tools import FunctionTool\
from llama_index.llms.openai import OpenAI\
from llama_index.core.agent import ReActAgent\
from e2b_code_interpreter import Sandbox\
\
# Define the tool\
def execute_python(code: str):\
    with Sandbox() as sandbox:\
        execution = sandbox.run_code(code)\
        return execution.text\
\
e2b_interpreter_tool = FunctionTool.from_defaults(\
    name="execute_python",\
    description="Execute python code in a Jupyter notebook cell and return result",\
    fn=execute_python\
)\
\
# Initialize LLM\
llm = OpenAI(model="gpt-4o")\
\
# Initialize ReAct agent\
agent = ReActAgent.from_tools([e2b_interpreter_tool], llm=llm, verbose=True)\
agent.chat("Calculate how many r's are in the word 'strawberry'")\
```\
\
“~/llindex.py”\
\
\[\
\
FEATURES\
\
\]\
\
FEATURES\
\
FE4TUR3S\
\
FEA–U?E5\
\
FE^TURES\
\
FEA+UR3S\
\
## FEATURES FOR THE  llm-powered DEVELOPERs\
\
We built E2B with the next generation of developers in mind — software engineering AI agents.\
\
\> MADE FOR AI\
\
\> DSCVR ALL (↓↓)\
\
### Works with any LLM\
\
Use OpenAI, Llama, Anthropic, Mistral, or your\
\
own custom models. E2B is LLM-agnostic\
\
and compatible with any model.\
\
### Quick start\
\
The E2B Sandboxes in the same region as\
\
the client start in less than 200 ms.\
\
NO COLD STARTS\
\
### Run\
\
### ... or just any other AI-generated code.\
\
AI-generated Python, JavaScript, Ruby, or C++? Popular framework or custom library? If you can run it on a Linux box, you can run it in the E2B sandbox.\
\
### Quick start\
\
The E2B Sandboxes in the same region as\
\
the client start in less than 200 ms.\
\
NO COLD STARTS\
\
Control code execution context\
\
inspect errors\
\
install packages\
\
interactive charts\
\
Filesystem I/O\
\
### Features made for LLM\
\
E2B features are made to turn your\
\
LLM into a competent coder.\
\
tailor-made for ai\
\
^\
\
^ ^\
\
^^^^^\
\
^^ ^^\
\
^^^\
\
    ^\
\
^ ^\
\
^ ^^\
\
^^^^\
\
^^^\
\
^ ^\
\
^^^^^\
\
^^ ^\
\
^^^\
\
^\
\
^ ^\
\
^ ^^^\
\
^^^^^\
\
^^^\
\
^\
\
^^ ^\
\
^^^\
\
^^^^\
\
^^^\
\
^\
\
^\
\
^ ^ ^\
\
^ ^^^\
\
^^^\
\
^\
\
^ ^^\
\
^^^^\
\
^^^ ^\
\
^^^\
\
^\
\
^ ^\
\
^ ^ ^\
\
^^^\
\
^^^\
\
### Secure & battle-tested\
\
Sandboxes are powered by [Firecracker](https://e2b.dev/#) microVM,\
\
a VM made for running untrusted code.\
\
battle-tested\
\
24H\
\
\[\
\
SANDBOX RUNNING\
\
\]\
\
### Up to 24h long sessions\
\
Run for a few seconds or several hours, each E2B\
\
sandbox can run up to 24 hours.\
\
AVAILABLE IN PRO\
\
### Install any package or system library with\
\
### and more.\
\
Completely customize the sandbox for your use case by creating a [custom sandbox template](https://e2b.dev/docs/sandbox-template) or installing a package when the sandbox is running.\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
\*\
\
·\
\
### Self-hosting\
\
Deploy E2B in your AWS, or GCP account\
\
and run sandboxes in your VPC.\
\
[I'm interested](mailto:<EMAIL>)\
\
SOON\
\
\[\
\
Cookbook\
\
\]\
\
COOKBOOK\
\
C00K8OOK\
\
COO4B––K\
\
C\*OK8\*OK\
\
©OOKBO°K\
\
## GET INSPIRed BY  OUR COOKBOOK\
\
Production use cases & full-fledged apps.\
\
HOVER (↓↓)\
\
All\
\
Language/Framework\
\
JS\
\
Python\
\
Next.js\
\
LangChain\
\
LangGraph\
\
LLM Providers\
\
Meta\
\
OpenAI\
\
Anthropic\
\
Mistral\
\
Fireworks AI\
\
Together AI\
\
[\+ Suggest new](https://cookbook-suggestion-e2b.zapier.app/)\
\
[Text Link](https://e2b.dev/cookbook/code-interpreter-with-ibm-watsonx-ai-in-js-ts) [**Code Interpreter with IBM WatsonX AI in JS/TS** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-js)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-js)\
\
[IBM](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-js)[Text Link](https://e2b.dev/cookbook-tags/ibm)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)\
\
[Text Link](https://e2b.dev/cookbook/code-interpreter-with-ibm-watsonx-ai) [**Code Interpreter with IBM WatsonX AI in Python** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-python)\
\
[IBM](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/watsonx-ai-code-interpreter-python)[Text Link](https://e2b.dev/cookbook-tags/ibm)\
\
Python\
\
[Text Link](https://e2b.dev/cookbook-tags/python)\
\
[Text Link](https://e2b.dev/cookbook/llama-3-with-code-interpreting-and-analyzing-uploaded-dataset) [**Llama 3 with code interpreting and analyzing uploaded dataset** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/upload-dataset-code-interpreter)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/upload-dataset-code-interpreter)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/upload-dataset-code-interpreter)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/upload-dataset-code-interpreter)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/upload-dataset-code-interpreter)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/upload-dataset-code-interpreter)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/upload-dataset-code-interpreter)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
Meta\
\
[Text Link](https://e2b.dev/cookbook-tags/meta)\
\
[Text Link](https://e2b.dev/cookbook/ai-code-execution-with-together-ai-models-js-ts) [**AI Code Execution with Together AI models, JS/TS** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-js)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-js)\
\
[Together AI](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-js)[Text Link](https://e2b.dev/cookbook-tags/together-ai)\
\
Meta\
\
[Text Link](https://e2b.dev/cookbook-tags/meta)\
\
TS\
\
[Text Link](https://e2b.dev/cookbook-tags/ts)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)\
\
[Text Link](https://e2b.dev/cookbook/ai-code-execution-with-together-ai-models-python) [**AI Code Execution with Together AI models, Python** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-python)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/together-ai-code-interpreter-python)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
Together AI\
\
[Text Link](https://e2b.dev/cookbook-tags/together-ai)\
\
Meta\
\
[Text Link](https://e2b.dev/cookbook-tags/meta)\
\
[Text Link](https://e2b.dev/cookbook/scrape-and-analyze-airbnb-data-with-firecrawl-and-e2b) [**Scrape and Analyze Airbnb Data with Firecrawl and E2B** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/firecrawl-scrape-and-analyze-airbnb-data)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/firecrawl-scrape-and-analyze-airbnb-data)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/firecrawl-scrape-and-analyze-airbnb-data)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/firecrawl-scrape-and-analyze-airbnb-data)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/firecrawl-scrape-and-analyze-airbnb-data)\
\
[Firecrawl](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/firecrawl-scrape-and-analyze-airbnb-data)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/firecrawl-scrape-and-analyze-airbnb-data)[Text Link](https://e2b.dev/cookbook-tags/firecrawl)\
\
TS\
\
[Text Link](https://e2b.dev/cookbook-tags/ts)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)\
\
[Text Link](https://e2b.dev/cookbook/openai-o1-code-interpreter-in-python) [**OpenAI o1 Code Interpreter in Python** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-python)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-python)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
OpenAI\
\
[Text Link](https://e2b.dev/cookbook-tags/openai)\
\
[Text Link](https://e2b.dev/cookbook/openai-o1-code-interpreter-in-js-ts) [**OpenAI o1 Code Interpreter in JS/TS** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-js)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-js)\
\
[OpenAI](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/o1-and-gpt-4-js)[Text Link](https://e2b.dev/cookbook-tags/openai)\
\
TS\
\
[Text Link](https://e2b.dev/cookbook-tags/ts)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)\
\
[Text Link](https://e2b.dev/cookbook/next-js-app-with-llm-code-interpreter-and-streaming) [**Next.js app with LLM + Code Interpreter and streaming** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/nextjs-code-interpreter)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/nextjs-code-interpreter)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/nextjs-code-interpreter)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/nextjs-code-interpreter)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/nextjs-code-interpreter)\
\
[OpenAI](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/nextjs-code-interpreter)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/nextjs-code-interpreter)[Text Link](https://e2b.dev/cookbook-tags/openai)\
\
TS\
\
[Text Link](https://e2b.dev/cookbook-tags/ts)\
\
Next.js\
\
[Text Link](https://e2b.dev/cookbook-tags/next-js)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)\
\
[Text Link](https://e2b.dev/cookbook/llama-3-function-calling-e2b-code-interpreter-in-python) [**Llama 3 + function calling + E2B Code interpreter in Python** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/blob/main/examples/groq-code-interpreter-python/llama_3_code_interpreter.ipynb)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/blob/main/examples/groq-code-interpreter-python/llama_3_code_interpreter.ipynb)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/blob/main/examples/groq-code-interpreter-python/llama_3_code_interpreter.ipynb)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/blob/main/examples/groq-code-interpreter-python/llama_3_code_interpreter.ipynb)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/blob/main/examples/groq-code-interpreter-python/llama_3_code_interpreter.ipynb)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/blob/main/examples/groq-code-interpreter-python/llama_3_code_interpreter.ipynb)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/blob/main/examples/groq-code-interpreter-python/llama_3_code_interpreter.ipynb)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
Meta\
\
[Text Link](https://e2b.dev/cookbook-tags/meta)\
\
[Text Link](https://e2b.dev/cookbook/llama-3-function-calling-e2b-code-interpreter) [**Llama 3 + function calling + E2B Code interpreter** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/groq-code-interpreter-js)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/groq-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/groq-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/groq-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/groq-code-interpreter-js)\
\
[Meta](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/groq-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/groq-code-interpreter-js)[Text Link](https://e2b.dev/cookbook-tags/meta)\
\
TS\
\
[Text Link](https://e2b.dev/cookbook-tags/ts)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)\
\
[Text Link](https://e2b.dev/cookbook/langchain-with-code-interpreting) [**LangChain with Code Interpreting** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langchain-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langchain-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langchain-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langchain-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langchain-python)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langchain-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langchain-python)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
LangChain\
\
[Text Link](https://e2b.dev/cookbook-tags/langchain)\
\
[Text Link](https://e2b.dev/cookbook/langgraph-with-code-interpreting) [**LangGraph with Code Interpreting** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langgraph-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langgraph-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langgraph-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langgraph-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langgraph-python)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langgraph-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/langgraph-python)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
LangGraph\
\
[Text Link](https://e2b.dev/cookbook-tags/langgraph)\
\
[Text Link](https://e2b.dev/cookbook/gpt-4o-code-interpreter-in-js-ts) [**GPT-4o Code Interpreter in JS/TS** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-js)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-js)\
\
[OpenAI](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-js)[Text Link](https://e2b.dev/cookbook-tags/openai)\
\
TS\
\
[Text Link](https://e2b.dev/cookbook-tags/ts)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)\
\
[Text Link](https://e2b.dev/cookbook/running-code-generated-by-autogen-via-e2b-sandbox) [**Running code generated by Autogen via E2B Sandbox** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/autogen-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/autogen-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/autogen-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/autogen-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/autogen-python)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/autogen-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/autogen-python)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
Autogen\
\
[Text Link](https://e2b.dev/cookbook-tags/autogen)\
\
[Text Link](https://e2b.dev/cookbook/code-interpreting-with-fireworks) [**Code interpreting with Fireworks** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/fireworks-code-interpreter-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/fireworks-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/fireworks-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/fireworks-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/fireworks-code-interpreter-python)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/fireworks-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/fireworks-code-interpreter-python)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
Fireworks AI\
\
[Text Link](https://e2b.dev/cookbook-tags/fireworks-ai)\
\
[Text Link](https://e2b.dev/cookbook/visualizing-website-topics--claude-firecrawl-e2b) [**Visualizing Website Topics (Claude + Firecrawl + E2B)** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-visualize-website-topics)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-visualize-website-topics)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-visualize-website-topics)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-visualize-website-topics)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-visualize-website-topics)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-visualize-website-topics)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-visualize-website-topics)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
Firecrawl\
\
[Text Link](https://e2b.dev/cookbook-tags/firecrawl)\
\
Anthropic\
\
[Text Link](https://e2b.dev/cookbook-tags/anthropic)\
\
[Text Link](https://e2b.dev/cookbook/ai-code-execution-with-mistral-s-codestral-in-python) [**AI Code Execution with Mistral's Codestral in Python** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-python)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-python)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
Mistral\
\
[Text Link](https://e2b.dev/cookbook-tags/mistral)\
\
[Text Link](https://e2b.dev/cookbook/ai-code-execution-with-mistral-s-codestral-in-js) [**AI Code Execution with Mistral's Codestral in JS** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-js)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-js)\
\
[Mistral](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/codestral-code-interpreter-js)[Text Link](https://e2b.dev/cookbook-tags/mistral)\
\
TS\
\
[Text Link](https://e2b.dev/cookbook-tags/ts)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)\
\
[Text Link](https://e2b.dev/cookbook/gpt-4o-code-interpreter-in-python) [**GPT-4o Code Interpreter in Python** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-python)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/gpt-4o-python)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
OpenAI\
\
[Text Link](https://e2b.dev/cookbook-tags/openai)\
\
[Text Link](https://e2b.dev/cookbook/claude-code-interpreter-in-python) [**Claude Code Interpreter in Python** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-python)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-python)\
\
[Python](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-python)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-python)[Text Link](https://e2b.dev/cookbook-tags/python)\
\
Anthropic\
\
[Text Link](https://e2b.dev/cookbook-tags/anthropic)\
\
[Text Link](https://e2b.dev/cookbook/claude-code-interpreter-in-js-ts) [**Claude Code Interpreter in JS/TS** \\
Example · Github](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-js)\
\
[NEW](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-js)\
\
[Anthropic](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-js)\
\
[cookbook post](https://github.com/e2b-dev/e2b-cookbook/tree/main/examples/claude-code-interpreter-js)[Text Link](https://e2b.dev/cookbook-tags/anthropic)\
\
TS\
\
[Text Link](https://e2b.dev/cookbook-tags/ts)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)\
\
No results found.\
\
Try a different keyword.\
\
Didn't find what you were looking for?\
\
[\+ Suggest new](https://cookbook-suggestion-e2b.zapier.app/)\
\
[LOAD MORE](https://e2b.dev/#)\
\
\[\
\
CUSTOMERS\
\
\]\
\
COMPANIES\
\
C–MP4NI3S\
\
C0MPAN\*ES\
\
CO7PA#IES\
\
COMPANI–S\
\
## USED BY TOP  COMPANIES\
\
From running short AI-generated code snippets, up to fully autonomous AI agents.\
\
/print("\
\
")\
\
(↓↓)\
\
[customer logo](https://www.perplexity.ai/)\
\
“E2B runtime **unlocked** **rich answers and interactive charts**  for our users. We love working with both the product and team behind it.”\
\
— Denis Yarats, CTO\
\
[Read THE CASE STUDY](https://e2b.dev/blog/how-perplexity-implemented-advanced-data-analysis-for-pro-users-in-1-week) →\
\
Data Analysis\
\
[customer logo](https://www.perplexity.ai/)\
\
“E2B allows us to scale-out training runs by launching hundreds of sandboxes in our experiments, which was essential in Open R1.”\
\
— Lewis Tunstall, Research Engineer\
\
[Read THE CASE STUDY](https://e2b.dev/blog/how-hugging-face-is-using-e2b-to-replicate-deepseek-r1) →\
\
CODE TESTS\
\
REINFORCEMENT LEARNING\
\
[customer logo](https://streetbeat.com/en)\
\
“It took just one hour to integrate E2B end-to-end. The **performance is excellent**, and the support is on another level. Issues are resolved in minutes.”\
\
— Maciej Donajski, CTO\
\
Finance\
\
Data Processing\
\
[customer logo](https://www.pga.org/)\
\
“E2B has revolutionized our agents' capabilities. This advanced alternative to OpenAI's Code Interpreter helps us focus on our unique product.”\
\
— Kevin J. Scott, CTO/CIO\
\
AI CHATBOT\
\
[customer logo](https://www.pga.org/)\
\
“Manus doesn’t just run some pieces of code. It uses 27 different tools, and it needs E2B to have a full virtual computer to work as a real human.”\
\
— Tao Zhang, Co-founder\
\
[Read THE CASE STUDY](https://e2b.dev/blog/how-manus-uses-e2b-to-provide-agents-with-virtual-computers) →\
\
DEEP RESEARCH\
\
[customer logo](https://www.athenaintelligence.ai/)\
\
“Executing Athena’s code inside the sandbox makes it easy to check and **automatically fix errors** **. E2B helps us gain enterprises’ trust**.”\
\
— Brendon Geils, CEO\
\
Data Analysis\
\
[customer logo](https://www.athenaintelligence.ai/)\
\
“We needed a **fast, secure, and scalable** way for code execution. E2B’s API interface made their infrastructure almost effortless to integrate.”\
\
— Benjamin Klieger, Compound AI Lead\
\
[Read THE CASE STUDY](https://e2b.dev/blog/groqs-compound-ai-models-are-powered-by-e2b) →\
\
COMPOUND AI SYSTEM\
\
[customer logo](https://www.athenaintelligence.ai/)\
\
“We implemented E2B in a week, needing just one engineer working in spare cycles. **Building it in-house would've taken weeks** and multiple people.”\
\
— Luiz Scheidegger, Head of Engineering\
\
[Read THE CASE STUDY](https://e2b.dev/blog/lindy-powers-ai-workflows-with-e2b-code-action) →\
\
WORKFLOW BUILDING\
\
[customer logo](https://www.gumloop.com/)\
\
“ **LLM-generated** **API integrations to external services** make Gumloop incredibly useful. E2B is **essential to make that happen at scale** and to deliver reliable performance to our users.”\
\
— Max Brodeur-Urbas, CEO\
\
Workflow Automation\
\
Enterprise\
\
Contact us for custom enterprise\
\
solution with special pricing.\
\
[Contact Us](https://e2b.dev/contact)\
\
(◔) GET REPLY IN 24H\
\
NEW\
\
Apply for [Startups](https://e2b.dev/startups) and [Research](https://e2b.dev/research) programs and get free Pro Tier\
\
Today\
\
T0D–Y\
\
TOD4Y\
\
7O#AY\
\
T0D4Y\
\
## GET STARTED TODAY\
\
E2B is an open-source runtime for executing AI-generated code in secure cloud sandboxes. Made for agentic & AI use cases.\
\
[START FOR FREE](https://e2b.dev/auth/sign-up) [VIEW DOCS](https://e2b.dev/docs)\
\
/RUN CODE\
\
>\
\
>\
\
‍\
\
>>\
\
[Github link](https://github.com/e2b-dev)\
\
(=^･^=)\
\
(==^･^)\
\
(=^･^=)\
\
(^･^==)\
\
(= ･ =)\
\
### Github\
\
See our complete codebase, Cookbook examples, and more — all in one place.\
\
[STAR (7.1K+) ↗](https://github.com/e2b-dev)\
\
[Discod link](https://discord.com/invite/U7KEcGErtQ)\
\
(o\_o)\
\
(o\_–)\
\
(o\_o)\
\
(^\_^)\
\
( \_ )\
\
### Join our Discord\
\
Become part of AI developers community & get support from the E2B team.\
\
[Join Today ↗](https://discord.com/invite/U7KEcGErtQ)\
\
[Docs](https://e2b.dev/docs)\
\
\[===\]\
\
### Docs\
\
See the walkthrough of how E2B works, including hello world examples.\
\
[Browse](https://e2b.dev/docs)\
\
IBM\
\
[Text Link](https://e2b.dev/cookbook-tags/ibm)\
\
Python\
\
[Text Link](https://e2b.dev/cookbook-tags/python)\
\
Mistral\
\
[Text Link](https://e2b.dev/cookbook-tags/mistral)\
\
Fireworks AI\
\
[Text Link](https://e2b.dev/cookbook-tags/fireworks-ai)\
\
Autogen\
\
[Text Link](https://e2b.dev/cookbook-tags/autogen)\
\
LangGraph\
\
[Text Link](https://e2b.dev/cookbook-tags/langgraph)\
\
LangChain\
\
[Text Link](https://e2b.dev/cookbook-tags/langchain)\
\
Magentic\
\
[Text Link](https://e2b.dev/cookbook-tags/magentic)\
\
OpenAI\
\
[Text Link](https://e2b.dev/cookbook-tags/openai)\
\
Firecrawl\
\
[Text Link](https://e2b.dev/cookbook-tags/firecrawl)\
\
Together AI\
\
[Text Link](https://e2b.dev/cookbook-tags/together-ai)\
\
Meta\
\
[Text Link](https://e2b.dev/cookbook-tags/meta)\
\
Anthropic\
\
[Text Link](https://e2b.dev/cookbook-tags/anthropic)\
\
TS\
\
[Text Link](https://e2b.dev/cookbook-tags/ts)\
\
Next.js\
\
[Text Link](https://e2b.dev/cookbook-tags/next-js)\
\
JS\
\
[Text Link](https://e2b.dev/cookbook-tags/js)