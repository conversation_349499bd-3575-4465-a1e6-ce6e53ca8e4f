make sure its multi agentic so use most powerful rust framework for the agentic systems, 2nd. user can build custom agents for that user going to give a prompt how they wanted be as the agents and the system is builded to act upon based on that for that use Google a2a with our base agentic system
3rd. user can add claude mcp servers in the app so our agentic system use tools however user initiated mcp server and they also can add mcp server in their custom agent Workflow with one click that they can create mcp servers and they can use as the tools for the agentic systems as well as for them,
4th. the agentic systems has total codebase knowledge of the users so its knows what to make change and create etc so that means we had codebase indexing/groking etc just like cursor.ai
5th. make the ui modern
6th. the user can ask for specific files or folders to the agents so they can ask or what they want
7th. the agents has memory inbuilt between them so they didn't forget anything in the process
8th. its can work on large scale repos so it has context engine like augment.dev and windsurf.dev

4 hours ago
CI

cimeje7492
I understand you want to significantly upgrade the multi-agent system with advanced capabilities. Let me rebuild this as a comprehensive Rust-based multi-agent system with the features you requested.

I'll continue building the core multi-agent system modules with Rust backend implementation.

I'll continue building the advanced multi-agent system with the remaining core modules. Let me create the context engine, MCP integration, memory system, and Google A2A integration.

I'll continue building the Google A2A integration and complete the remaining core modules for the advanced multi-agent system.

Now I'll create the remaining core modules and complete the modern UI components for the advanced multi-agent system.

3 hours ago
•
Implement multi-agent system for parallel AI-assisted app development
I'll continue building the comprehensive multi-agent system by creating the remaining core modules including the codebase analyzer, custom agent implementations, and the modern UI components.

