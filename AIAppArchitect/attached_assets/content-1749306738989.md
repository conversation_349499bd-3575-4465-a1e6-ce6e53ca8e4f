[Skip to content](https://evoagentx.github.io/EvoAgentX/#evoagentx)

# [**EvoAgentX**](https://evoagentx.github.io/EvoAgentX/\#evoagentx) [¶](https://evoagentx.github.io/EvoAgentX/\#evoagentx "Permanent link")

_An automated framework for evaluating and evolving agentic workflows._

![](https://evoagentx.github.io/EvoAgentX/assets/framework_en.jpg)

## [🚀 Introduction](https://evoagentx.github.io/EvoAgentX/\#-introduction) [¶](https://evoagentx.github.io/EvoAgentX/\#-introduction "Permanent link")

EvoAgentX is an open-source framework designed to automate the generation, execution, evaluation and optimization of agentic workflows. By leveraging large language models (LLMs), EvoAgentX enables developers and researchers to prototype, test, and deploy multi-agent systems that grow in complexity and capability over time.

## [✨ Key Features](https://evoagentx.github.io/EvoAgentX/\#-key-features) [¶](https://evoagentx.github.io/EvoAgentX/\#-key-features "Permanent link")

- **Easy Agent and Workflow Customization**: Easily create customized agents and workflows using natural language prompts. EvoAgentX makes it easy to turn your high-level ideas to working systems.
- **Automatic Workflow Generation & Execution**: Automatically generate and execute agentic workflows from simple goal descriptions, reducing manual workload in multi-agent system design.
- **WorkFlow Optimization**: Integrates existing workflow optimization techniques that iteratively refine workflows for improved performance.
- **Benchmarking & Evaluation**: Includes built-in benchmarks and standardized evaluation metrics to measure workflow. effectiveness across different tasks and agent configurations
- **Workflow Execution Toolkit**: Offers a suite of tools essential for executing complex workflows, such as search components and the Model Context Protocol (MCP).

## [🔍 How It Works](https://evoagentx.github.io/EvoAgentX/\#-how-it-works) [¶](https://evoagentx.github.io/EvoAgentX/\#-how-it-works "Permanent link")

EvoAgentX uses a modular architecture with the following core components:

1. **Workflow Generator**: Creates agentic workflows based on your goals
2. **Agent Manager**: Handles agent creation, configuration, and deployment
3. **Workflow Executor**: Runs workflows efficiently with proper agent communication
4. **Evaluators**: Provides performance metrics and improvement suggestions
5. **Optimizers**: Evolves workflows to enhance performance over time

## [👥 Community](https://evoagentx.github.io/EvoAgentX/\#-community) [¶](https://evoagentx.github.io/EvoAgentX/\#-community "Permanent link")

- **Discord**: Join our [Discord Channel](https://discord.gg/w3x2YrCa) for discussions and support
- **GitHub**: Contribute to the project on [GitHub](https://github.com/EvoAgentX/EvoAgentX/)
- **Email**: Contact us at [<EMAIL>](mailto:<EMAIL>)
- **WeChat**: Connect with us on [WeChat](https://github.com/EvoAgentX/EvoAgentX/blob/main/assets/wechat_info.md) for updates and support.

## [🤝 Contributing](https://evoagentx.github.io/EvoAgentX/\#-contributing) [¶](https://evoagentx.github.io/EvoAgentX/\#-contributing "Permanent link")

We welcome contributions from the community! Please refer to our [Contributing Guidelines](https://github.com/EvoAgentX/EvoAgentX/blob/main/CONTRIBUTING.md) for more details.

Back to top