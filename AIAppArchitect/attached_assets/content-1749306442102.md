[Skip to content](https://github.com/microsoft/TaskWeaver#start-of-content)

You signed in with another tab or window. [Reload](https://github.com/microsoft/TaskWeaver) to refresh your session.You signed out in another tab or window. [Reload](https://github.com/microsoft/TaskWeaver) to refresh your session.You switched accounts on another tab or window. [Reload](https://github.com/microsoft/TaskWeaver) to refresh your session.Dismiss alert

[microsoft](https://github.com/microsoft)/ **[TaskWeaver](https://github.com/microsoft/TaskWeaver)** Public

- [Notifications](https://github.com/login?return_to=%2Fmicrosoft%2FTaskWeaver) You must be signed in to change notification settings
- [Fork\\
737](https://github.com/login?return_to=%2Fmicrosoft%2FTaskWeaver)
- [Star\\
5.8k](https://github.com/login?return_to=%2Fmicrosoft%2FTaskWeaver)


A code-first agent framework for seamlessly planning and executing data analytics tasks.


[microsoft.github.io/TaskWeaver/](https://microsoft.github.io/TaskWeaver/ "https://microsoft.github.io/TaskWeaver/")

### License

MIT, MIT licenses found


### Licenses found

[MIT\\
\\
LICENSE](https://github.com/microsoft/TaskWeaver/blob/main/LICENSE) [MIT\\
\\
LICENSE.txt](https://github.com/microsoft/TaskWeaver/blob/main/LICENSE.txt)

[5.8k\\
stars](https://github.com/microsoft/TaskWeaver/stargazers) [737\\
forks](https://github.com/microsoft/TaskWeaver/forks) [Branches](https://github.com/microsoft/TaskWeaver/branches) [Tags](https://github.com/microsoft/TaskWeaver/tags) [Activity](https://github.com/microsoft/TaskWeaver/activity)

[Star](https://github.com/login?return_to=%2Fmicrosoft%2FTaskWeaver)

[Notifications](https://github.com/login?return_to=%2Fmicrosoft%2FTaskWeaver) You must be signed in to change notification settings

# microsoft/TaskWeaver

main

[**14** Branches](https://github.com/microsoft/TaskWeaver/branches) [**2** Tags](https://github.com/microsoft/TaskWeaver/tags)

[Go to Branches page](https://github.com/microsoft/TaskWeaver/branches)[Go to Tags page](https://github.com/microsoft/TaskWeaver/tags)

Go to file

Code

Open more actions menu

## Folders and files

| Name | Name | Last commit message | Last commit date |
| --- | --- | --- | --- |
| ## Latest commit<br>[![liqul](https://avatars.githubusercontent.com/u/7489260?v=4&size=40)](https://github.com/liqul)[liqul](https://github.com/microsoft/TaskWeaver/commits?author=liqul)<br>[Bump undici from 6.21.1 to 6.21.3 in /website (](https://github.com/microsoft/TaskWeaver/commit/b4305b336b97d84dfc8ad4de95c811127e9f61c8) [#486](https://github.com/microsoft/TaskWeaver/pull/486) [)](https://github.com/microsoft/TaskWeaver/commit/b4305b336b97d84dfc8ad4de95c811127e9f61c8)<br>Open commit details<br>May 18, 2025<br>[b4305b3](https://github.com/microsoft/TaskWeaver/commit/b4305b336b97d84dfc8ad4de95c811127e9f61c8) · May 18, 2025<br>## History<br>[605 Commits](https://github.com/microsoft/TaskWeaver/commits/main/) <br>Open commit details |
| [.asset](https://github.com/microsoft/TaskWeaver/tree/main/.asset ".asset") | [.asset](https://github.com/microsoft/TaskWeaver/tree/main/.asset ".asset") | [add architecture figure, revise readme](https://github.com/microsoft/TaskWeaver/commit/5141c21ee65024f530d9dbf9b31b65d9d2eae44d "add architecture figure, revise readme") | Jan 7, 2024 |
| [.devcontainer](https://github.com/microsoft/TaskWeaver/tree/main/.devcontainer ".devcontainer") | [.devcontainer](https://github.com/microsoft/TaskWeaver/tree/main/.devcontainer ".devcontainer") | [Add devcontainer and fix env naming issue (](https://github.com/microsoft/TaskWeaver/commit/5be037f44a4869d82aceac0b0d586664ad94c9d3 "Add devcontainer and fix env naming issue (#24)  * add devcontainer and renaming env variables  * fix encoding error  * add new line  * change image base  * add linters") [#24](https://github.com/microsoft/TaskWeaver/pull/24) [)](https://github.com/microsoft/TaskWeaver/commit/5be037f44a4869d82aceac0b0d586664ad94c9d3 "Add devcontainer and fix env naming issue (#24)  * add devcontainer and renaming env variables  * fix encoding error  * add new line  * change image base  * add linters") | Dec 5, 2023 |
| [.github](https://github.com/microsoft/TaskWeaver/tree/main/.github ".github") | [.github](https://github.com/microsoft/TaskWeaver/tree/main/.github ".github") | [Bump peaceiris/actions-gh-pages from 3 to 4 in the github\_actions group](https://github.com/microsoft/TaskWeaver/commit/c109b6a4fe24953ff26989c1cf7dd10ae01e56dd "Bump peaceiris/actions-gh-pages from 3 to 4 in the github_actions group  Bumps the github_actions group with 1 update: [peaceiris/actions-gh-pages](https://github.com/peaceiris/actions-gh-pages).   Updates `peaceiris/actions-gh-pages` from 3 to 4 - [Release notes](https://github.com/peaceiris/actions-gh-pages/releases) - [Changelog](https://github.com/peaceiris/actions-gh-pages/blob/main/CHANGELOG.md) - [Commits](https://github.com/peaceiris/actions-gh-pages/compare/v3...v4)  --- updated-dependencies: - dependency-name: peaceiris/actions-gh-pages   dependency-type: direct:production   update-type: version-update:semver-major   dependency-group: github_actions ...  Signed-off-by: dependabot[bot] <<EMAIL>>") | Apr 15, 2024 |
| [.linters](https://github.com/microsoft/TaskWeaver/tree/main/.linters ".linters") | [.linters](https://github.com/microsoft/TaskWeaver/tree/main/.linters ".linters") | [Add devcontainer and fix env naming issue (](https://github.com/microsoft/TaskWeaver/commit/5be037f44a4869d82aceac0b0d586664ad94c9d3 "Add devcontainer and fix env naming issue (#24)  * add devcontainer and renaming env variables  * fix encoding error  * add new line  * change image base  * add linters") [#24](https://github.com/microsoft/TaskWeaver/pull/24) [)](https://github.com/microsoft/TaskWeaver/commit/5be037f44a4869d82aceac0b0d586664ad94c9d3 "Add devcontainer and fix env naming issue (#24)  * add devcontainer and renaming env variables  * fix encoding error  * add new line  * change image base  * add linters") | Dec 5, 2023 |
| [auto\_eval](https://github.com/microsoft/TaskWeaver/tree/main/auto_eval "auto_eval") | [auto\_eval](https://github.com/microsoft/TaskWeaver/tree/main/auto_eval "auto_eval") | [fix doc mismatching (](https://github.com/microsoft/TaskWeaver/commit/40f838db09fe26f24a444ae876fa2c6453a36d65 "fix doc mismatching (#422)  Co-authored-by: Jack-Q <<EMAIL>>") [#422](https://github.com/microsoft/TaskWeaver/pull/422) [)](https://github.com/microsoft/TaskWeaver/commit/40f838db09fe26f24a444ae876fa2c6453a36d65 "fix doc mismatching (#422)  Co-authored-by: Jack-Q <<EMAIL>>") | Oct 8, 2024 |
| [docker](https://github.com/microsoft/TaskWeaver/tree/main/docker "docker") | [docker](https://github.com/microsoft/TaskWeaver/tree/main/docker "docker") | [Liqun/custom image (](https://github.com/microsoft/TaskWeaver/commit/cf76c3b70b29ef64185fd3c9af0510c9e2fcc51e "Liqun/custom image (#427)") [#427](https://github.com/microsoft/TaskWeaver/pull/427) [)](https://github.com/microsoft/TaskWeaver/commit/cf76c3b70b29ef64185fd3c9af0510c9e2fcc51e "Liqun/custom image (#427)") | Oct 10, 2024 |
| [playground/UI](https://github.com/microsoft/TaskWeaver/tree/main/playground/UI "This path skips through empty directories") | [playground/UI](https://github.com/microsoft/TaskWeaver/tree/main/playground/UI "This path skips through empty directories") | [Liqun/Constrained\_Generation \[Ready\] (](https://github.com/microsoft/TaskWeaver/commit/87f804f9649b0e9b7e352e32ed514e620e7b2b22 "Liqun/Constrained_Generation [Ready] (#378)  1. Changed planner and code generator's response schema 2. Modify the json stream parsing logic 3. Add a blog on optimizating locally served LLMs") [#378](https://github.com/microsoft/TaskWeaver/pull/378) [)](https://github.com/microsoft/TaskWeaver/commit/87f804f9649b0e9b7e352e32ed514e620e7b2b22 "Liqun/Constrained_Generation [Ready] (#378)  1. Changed planner and code generator's response schema 2. Modify the json stream parsing logic 3. Add a blog on optimizating locally served LLMs") | Jul 7, 2024 |
| [project](https://github.com/microsoft/TaskWeaver/tree/main/project "project") | [project](https://github.com/microsoft/TaskWeaver/tree/main/project "project") | [rename](https://github.com/microsoft/TaskWeaver/commit/f1f305e986a3ffbd776964e3a4e742ae65575e86 "rename") | Mar 12, 2025 |
| [scripts](https://github.com/microsoft/TaskWeaver/tree/main/scripts "scripts") | [scripts](https://github.com/microsoft/TaskWeaver/tree/main/scripts "scripts") | [Support json\_schema for openai endpoint (](https://github.com/microsoft/TaskWeaver/commit/7f48bb74aae86ac7130372f3164033e42a11070c "Support json_schema for openai endpoint (#432)") [#432](https://github.com/microsoft/TaskWeaver/pull/432) [)](https://github.com/microsoft/TaskWeaver/commit/7f48bb74aae86ac7130372f3164033e42a11070c "Support json_schema for openai endpoint (#432)") | Oct 21, 2024 |
| [taskweaver](https://github.com/microsoft/TaskWeaver/tree/main/taskweaver "taskweaver") | [taskweaver](https://github.com/microsoft/TaskWeaver/tree/main/taskweaver "taskweaver") | [Merge branch 'main' into liqun/fix\_env\_client](https://github.com/microsoft/TaskWeaver/commit/29a8f35f65fcfa035a47ed749f1593f5d1370619 "Merge branch 'main' into liqun/fix_env_client") | May 8, 2025 |
| [tests/unit\_tests](https://github.com/microsoft/TaskWeaver/tree/main/tests/unit_tests "This path skips through empty directories") | [tests/unit\_tests](https://github.com/microsoft/TaskWeaver/tree/main/tests/unit_tests "This path skips through empty directories") | [Liqun/custom image (](https://github.com/microsoft/TaskWeaver/commit/cf76c3b70b29ef64185fd3c9af0510c9e2fcc51e "Liqun/custom image (#427)") [#427](https://github.com/microsoft/TaskWeaver/pull/427) [)](https://github.com/microsoft/TaskWeaver/commit/cf76c3b70b29ef64185fd3c9af0510c9e2fcc51e "Liqun/custom image (#427)") | Oct 10, 2024 |
| [tracing](https://github.com/microsoft/TaskWeaver/tree/main/tracing "tracing") | [tracing](https://github.com/microsoft/TaskWeaver/tree/main/tracing "tracing") | [Xz/docker run (](https://github.com/microsoft/TaskWeaver/commit/82fb58d6ae544954715a3be1a29948539c642441 "Xz/docker run (#218)  run taskweaver in a docker container  ---------  Co-authored-by: Shilin HE <<EMAIL>> Co-authored-by: Liqun Li <<EMAIL>> Co-authored-by: Liqun Li <<EMAIL>>") [#218](https://github.com/microsoft/TaskWeaver/pull/218) [)](https://github.com/microsoft/TaskWeaver/commit/82fb58d6ae544954715a3be1a29948539c642441 "Xz/docker run (#218)  run taskweaver in a docker container  ---------  Co-authored-by: Shilin HE <<EMAIL>> Co-authored-by: Liqun Li <<EMAIL>> Co-authored-by: Liqun Li <<EMAIL>>") | Mar 28, 2024 |
| [website](https://github.com/microsoft/TaskWeaver/tree/main/website "website") | [website](https://github.com/microsoft/TaskWeaver/tree/main/website "website") | [Bump undici from 6.21.1 to 6.21.3 in /website](https://github.com/microsoft/TaskWeaver/commit/b79c049b1636dae7f44de515c4d2c8d3c8e9beff "Bump undici from 6.21.1 to 6.21.3 in /website  Bumps [undici](https://github.com/nodejs/undici) from 6.21.1 to 6.21.3. - [Release notes](https://github.com/nodejs/undici/releases) - [Commits](https://github.com/nodejs/undici/compare/v6.21.1...v6.21.3)  --- updated-dependencies: - dependency-name: undici   dependency-version: 6.21.3   dependency-type: indirect ...  Signed-off-by: dependabot[bot] <<EMAIL>>") | May 15, 2025 |
| [.gitattributes](https://github.com/microsoft/TaskWeaver/blob/main/.gitattributes ".gitattributes") | [.gitattributes](https://github.com/microsoft/TaskWeaver/blob/main/.gitattributes ".gitattributes") | [Fix container related issues (](https://github.com/microsoft/TaskWeaver/commit/683501b3d179043164c05bd89647d3de4bd2af4b "Fix container related issues (#426)  1. support custom image  2. fix the issue of import failure") [#426](https://github.com/microsoft/TaskWeaver/pull/426) [)](https://github.com/microsoft/TaskWeaver/commit/683501b3d179043164c05bd89647d3de4bd2af4b "Fix container related issues (#426)  1. support custom image  2. fix the issue of import failure") | Oct 9, 2024 |
| [.gitignore](https://github.com/microsoft/TaskWeaver/blob/main/.gitignore ".gitignore") | [.gitignore](https://github.com/microsoft/TaskWeaver/blob/main/.gitignore ".gitignore") | [update zip](https://github.com/microsoft/TaskWeaver/commit/04fc78cdf3978551f87bfb8cfa8d255cb9966c2b "update zip") | May 12, 2024 |
| [.pre-commit-config.yaml](https://github.com/microsoft/TaskWeaver/blob/main/.pre-commit-config.yaml ".pre-commit-config.yaml") | [.pre-commit-config.yaml](https://github.com/microsoft/TaskWeaver/blob/main/.pre-commit-config.yaml ".pre-commit-config.yaml") | [add detect secrets support in pre-commit](https://github.com/microsoft/TaskWeaver/commit/43e6193fff0c65009037b4311535ee85b6c74e46 "add detect secrets support in pre-commit") | Dec 10, 2023 |
| [.secrets.baseline](https://github.com/microsoft/TaskWeaver/blob/main/.secrets.baseline ".secrets.baseline") | [.secrets.baseline](https://github.com/microsoft/TaskWeaver/blob/main/.secrets.baseline ".secrets.baseline") | [add ui readme](https://github.com/microsoft/TaskWeaver/commit/e4f9b5ebaf2d0ec1aac27cc5dc56f0f07cb90b3b "add ui readme") | Dec 11, 2023 |
| [CODE\_OF\_CONDUCT.md](https://github.com/microsoft/TaskWeaver/blob/main/CODE_OF_CONDUCT.md "CODE_OF_CONDUCT.md") | [CODE\_OF\_CONDUCT.md](https://github.com/microsoft/TaskWeaver/blob/main/CODE_OF_CONDUCT.md "CODE_OF_CONDUCT.md") | [CODE\_OF\_CONDUCT.md committed](https://github.com/microsoft/TaskWeaver/commit/323ad6abdc2b78d8d02249a865eb5c152af34800 "CODE_OF_CONDUCT.md committed") | Sep 10, 2023 |
| [CONTRIBUTING.md](https://github.com/microsoft/TaskWeaver/blob/main/CONTRIBUTING.md "CONTRIBUTING.md") | [CONTRIBUTING.md](https://github.com/microsoft/TaskWeaver/blob/main/CONTRIBUTING.md "CONTRIBUTING.md") | [init commit](https://github.com/microsoft/TaskWeaver/commit/e993335c1fc0f3bec55db50371c5fb75edec0bb1 "init commit") | Nov 29, 2023 |
| [LICENSE](https://github.com/microsoft/TaskWeaver/blob/main/LICENSE "LICENSE") | [LICENSE](https://github.com/microsoft/TaskWeaver/blob/main/LICENSE "LICENSE") | [LICENSE committed](https://github.com/microsoft/TaskWeaver/commit/99009d958fca2e46929ff4fe733286c6ba75a50e "LICENSE committed") | Sep 10, 2023 |
| [README.md](https://github.com/microsoft/TaskWeaver/blob/main/README.md "README.md") | [README.md](https://github.com/microsoft/TaskWeaver/blob/main/README.md "README.md") | [update readme](https://github.com/microsoft/TaskWeaver/commit/e27a9cab2321ce567358a7043a7a01a1d997daf0 "update readme") | Mar 13, 2025 |
| [SECURITY.md](https://github.com/microsoft/TaskWeaver/blob/main/SECURITY.md "SECURITY.md") | [SECURITY.md](https://github.com/microsoft/TaskWeaver/blob/main/SECURITY.md "SECURITY.md") | [SECURITY.md committed](https://github.com/microsoft/TaskWeaver/commit/09ac9c106bb7e647da5cb5f9d3434eafb1be976d "SECURITY.md committed") | Sep 10, 2023 |
| [SUPPORT.md](https://github.com/microsoft/TaskWeaver/blob/main/SUPPORT.md "SUPPORT.md") | [SUPPORT.md](https://github.com/microsoft/TaskWeaver/blob/main/SUPPORT.md "SUPPORT.md") | [SUPPORT.md committed](https://github.com/microsoft/TaskWeaver/commit/cc3fcc48f91bd7589aec8c08f4b9d9fc20d35db0 "SUPPORT.md committed") | Sep 10, 2023 |
| [pytest.ini](https://github.com/microsoft/TaskWeaver/blob/main/pytest.ini "pytest.ini") | [pytest.ini](https://github.com/microsoft/TaskWeaver/blob/main/pytest.ini "pytest.ini") | [Improve console chat launch speed by defer unnecessary package loading (](https://github.com/microsoft/TaskWeaver/commit/18e8f8be2f55ab821f82faaceea1e9a241bbaccc "Improve console chat launch speed by defer unnecessary package loading (#396)") | Aug 20, 2024 |
| [requirements.txt](https://github.com/microsoft/TaskWeaver/blob/main/requirements.txt "requirements.txt") | [requirements.txt](https://github.com/microsoft/TaskWeaver/blob/main/requirements.txt "requirements.txt") | [Support json\_schema for openai endpoint (](https://github.com/microsoft/TaskWeaver/commit/7f48bb74aae86ac7130372f3164033e42a11070c "Support json_schema for openai endpoint (#432)") [#432](https://github.com/microsoft/TaskWeaver/pull/432) [)](https://github.com/microsoft/TaskWeaver/commit/7f48bb74aae86ac7130372f3164033e42a11070c "Support json_schema for openai endpoint (#432)") | Oct 21, 2024 |
| [setup.py](https://github.com/microsoft/TaskWeaver/blob/main/setup.py "setup.py") | [setup.py](https://github.com/microsoft/TaskWeaver/blob/main/setup.py "setup.py") | [update zip](https://github.com/microsoft/TaskWeaver/commit/04fc78cdf3978551f87bfb8cfa8d255cb9966c2b "update zip") | May 12, 2024 |
| [version.json](https://github.com/microsoft/TaskWeaver/blob/main/version.json "version.json") | [version.json](https://github.com/microsoft/TaskWeaver/blob/main/version.json "version.json") | [init commit](https://github.com/microsoft/TaskWeaver/commit/e993335c1fc0f3bec55db50371c5fb75edec0bb1 "init commit") | Nov 29, 2023 |
| View all files |

## Repository files navigation

# [![](https://github.com/microsoft/TaskWeaver/raw/main/.asset/logo.color.svg)](https://github.com/microsoft/TaskWeaver/blob/main/.asset/logo.color.svg) TaskWeaver

[Permalink:  TaskWeaver\
](https://github.com/microsoft/TaskWeaver#-----taskweaver)

[![Discord Follow](https://camo.githubusercontent.com/30b88236049e47f26269d6d57db5d5946b2be3b491a426d33be2f64ba4141236/68747470733a2f2f646362616467652e76657263656c2e6170702f6170692f7365727665722f5a35364d586d5a674d623f7374796c653d666c6174)](https://discord.gg/Z56MXmZgMb)[![Python Version](https://camo.githubusercontent.com/28d03799367b09ed493b311ddfc9f7019a0bfa632a2174f2adef73ba29976335/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f507974686f6e2d3337373641423f266c6f676f3d707974686f6e266c6f676f436f6c6f723d77686974652d626c7565266c6162656c3d332e3130253230253743253230332e3131)](https://camo.githubusercontent.com/28d03799367b09ed493b311ddfc9f7019a0bfa632a2174f2adef73ba29976335/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f507974686f6e2d3337373641423f266c6f676f3d707974686f6e266c6f676f436f6c6f723d77686974652d626c7565266c6162656c3d332e3130253230253743253230332e3131)[![License: MIT](https://camo.githubusercontent.com/6cd0120cc4c5ac11d28b2c60f76033b52db98dac641de3b2644bb054b449d60c/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c6963656e73652d4d49542d79656c6c6f772e737667)](https://opensource.org/licenses/MIT)[![Welcome](https://camo.githubusercontent.com/a93286920599112849c7c2af9d239294be27738b440248e434813b1bd0ffb368/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f636f6e747269627574696f6e732d77656c636f6d652d627269676874677265656e2e7376673f7374796c653d666c6174)](https://camo.githubusercontent.com/a93286920599112849c7c2af9d239294be27738b440248e434813b1bd0ffb368/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f636f6e747269627574696f6e732d77656c636f6d652d627269676874677265656e2e7376673f7374796c653d666c6174)

TaskWeaver is A **code-first** agent framework for seamlessly planning and executing data analytics tasks.
This innovative framework interprets user requests through code snippets and efficiently coordinates a variety
of plugins in the form of functions to execute data analytics tasks in a stateful manner.

Unlike many agent frameworks that only track the chat history with LLMs in text, TaskWeaver preserves both the **chat history** and the **code execution history**, including the in-memory data. This feature enhances the _expressiveness_ of the agent framework, making it ideal for processing complex data structures like high-dimensional tabular data.

# [![](https://github.com/microsoft/TaskWeaver/raw/main/.asset/taskweaver_arch.png)](https://github.com/microsoft/TaskWeaver/blob/main/.asset/taskweaver_arch.png)

[Permalink: ](https://github.com/microsoft/TaskWeaver#-----)

## 🆕 News

[Permalink: 🆕 News](https://github.com/microsoft/TaskWeaver#-news)

- 📅2025-03-13: TaskWeaver now supports vision input for the Planner role. Please check the [vision input](https://microsoft.github.io/TaskWeaver/blog/vision) for more details.👀
- 📅2025-01-16: TaskWeaver has been enhanced with an experimental role called [Recepta](https://microsoft.github.io/TaskWeaver/blog/reasoning) for its reasoning power.🧠
- 📅2024-12-23: TaskWeaver has been integrated with the [AgentOps](https://microsoft.github.io/TaskWeaver/docs/observability) for better observability and monitoring.🔍
- 📅2024-09-13: We introduce the shared memory to store information that is shared between the roles in TaskWeaver. Please check the [memory](https://microsoft.github.io/TaskWeaver/docs/memory) for more details.🧠
- 📅2024-09-13: We have enhanced the experience feature by allowing static and dynamic experience selection. Please check the [experience](https://microsoft.github.io/TaskWeaver/blog/experience) for more details.📚
- 📅2024-07-02: We have optimized TaskWeaver to support not-that-large language models served locally. Please check this [post](https://microsoft.github.io/TaskWeaver/blog/local_llm) for more details.🔗
- 📅2024-05-07: We have added two blog posts on [Evaluating a LLM agent](https://microsoft.github.io/TaskWeaver/blog/evaluation) and [Adding new roles to TaskWeaver](https://microsoft.github.io/TaskWeaver/blog/role) in the documentation.📝
- 📅2024-03-28: TaskWeaver now offers all-in-one Docker image, providing a convenient one-stop experience for users. Please check the [docker](https://microsoft.github.io/TaskWeaver/docs/usage/docker) for more details.🐳
- 📅2024-03-27: TaskWeaver now switches to `container` mode by default for code execution. Please check the [code execution](https://microsoft.github.io/TaskWeaver/docs/code_execution) for more details.🐳

- ......
- 📅2023-11-30: TaskWeaver is released on GitHub🎈.

## 💥 Highlights

[Permalink: 💥 Highlights](https://github.com/microsoft/TaskWeaver#-highlights)

- [x] **Planning for complex tasks** \- TaskWeaver, which features task decomposition and progress tracking, is designed to solve complex tasks.
- [x] **Reflective execution** \- TaskWeaver supports reflective execution, which allows the agent to reflect on the execution process and make adjustments.
- [x] **Rich data structure** \- TaskWeaver allows you to work with rich data structures in Python, such as DataFrames, instead of dealing with strings.
- [x] **Customized algorithms** \- TaskWeaver allows you to encapsulate your own algorithms into plugins and orchestrate them.
- [x] **Incorporating domain-specific knowledge** \- TaskWeaver is designed to incorporate domain-specific knowledge easily to improve the reliability.
- [x] **Stateful execution** \- TaskWeaver is designed to support stateful execution of the generated code to ensure consistent and smooth user experience.
- [x] **Code verification** \- TaskWeaver is designed to verify the generated code before execution. It can detect potential issues in the generated code and provide suggestions to fix them.
- [x] **Easy to use** \- TaskWeaver is easy to use with sample plugins, examples and tutorials to help you get started. TaskWeaver offers an open-box experience, allowing users to run it immediately after installation.
- [x] **Easy to debug** \- TaskWeaver is easy to debug with detailed and transparent logs to help you understand the entire process, including LLM prompts, the code generation, and execution process.
- [x] **Security consideration** \- TaskWeaver supports a basic session management to keep different users' data separate. The code execution is separated into different processes to avoid mutal interference.
- [x] **Easy extension** \- TaskWeaver is easy to extend to accomplish more complex tasks with multiple agents as roles and plugins.

## 📚 Asking for Contributions

[Permalink: 📚 Asking for Contributions](https://github.com/microsoft/TaskWeaver#-asking-for-contributions)

There are still many features and improvements can be made. But due to our limited resources, we are not able to implement all of them or the progress will be slow.
We are looking forward to your contributions to make TaskWeaver better.

- [ ]  Easy-to-use and maintainable UX/UI
- [ ]  Support for prompt template management
- [ ]  Better plugin experiences, such as displaying updates or stopping in the middle of running the plugin and user confirmation before running the plugin
- [ ]  Async interaction with LLMs
- [ ]  Support for remote code execution

## ✨ Quick Start

[Permalink: ✨ Quick Start](https://github.com/microsoft/TaskWeaver#-quick-start)

### 🛠️ Step 1: Installation

[Permalink: 🛠️ Step 1: Installation](https://github.com/microsoft/TaskWeaver#%EF%B8%8F-step-1-installation)

TaskWeaver requires **Python >= 3.10**. It can be installed by running the following command:

```
# [optional to create conda environment]
# conda create -n taskweaver python=3.10
# conda activate taskweaver

# clone the repository
git clone https://github.com/microsoft/TaskWeaver.git
cd TaskWeaver
# install the requirements
pip install -r requirements.txt
```

If you want to install an earlier version of TaskWeaver, you may check the [release](https://github.com/microsoft/TaskWeaver/releases) page, find the tag (e.g., `v0.0.1`) and install it by

```
pip install git+https://github.com/microsoft/TaskWeaver@<TAG>

```

### 🖊️ Step 2: Configure the LLMs

[Permalink: 🖊️ Step 2: Configure the LLMs](https://github.com/microsoft/TaskWeaver#%EF%B8%8F-step-2-configure-the-llms)

Before running TaskWeaver, you need to provide your LLM configurations. Taking OpenAI as an example, you can configure `taskweaver_config.json` file as follows.

#### OpenAI

[Permalink: OpenAI](https://github.com/microsoft/TaskWeaver#openai)

```
{
  "llm.api_key": "the api key",
  "llm.model": "the model name, e.g., gpt-4"
}
```

💡 TaskWeaver also supports other LLMs and advanced configurations, please check the [documents](https://microsoft.github.io/TaskWeaver/docs/overview) for more details.

### 🚩 Step 3: Start TaskWeaver

[Permalink: 🚩 Step 3: Start TaskWeaver](https://github.com/microsoft/TaskWeaver#-step-3-start-taskweaver)

💡 TaskWeaver has switched to `container` mode by default for code execution, which means the code is run in a container.
You may need to install Docker and take care of the dependencies in the container.
Please check the [code execution](https://microsoft.github.io/TaskWeaver/docs/code_execution) for more details.

#### ⌨️ Command Line (CLI)

[Permalink: ⌨️ Command Line (CLI)](https://github.com/microsoft/TaskWeaver#%EF%B8%8F-command-line-cli)

```
# assume you are in the cloned TaskWeaver folder
python -m taskweaver -p ./project/
```

This will start the TaskWeaver process and you can interact with it through the command line interface.
If everything goes well, you will see the following prompt:

```
=========================================================
 _____         _     _       __
|_   _|_ _ ___| | _ | |     / /__  ____ __   _____  _____
  | |/ _` / __| |/ /| | /| / / _ \/ __ `/ | / / _ \/ ___/
  | | (_| \__ \   < | |/ |/ /  __/ /_/ /| |/ /  __/ /
  |_|\__,_|___/_|\_\|__/|__/\___/\__,_/ |___/\___/_/
=========================================================
TaskWeaver: I am TaskWeaver, an AI assistant. To get started, could you please enter your request?
Human: ___

```

#### or 💻 Web UI

[Permalink: or 💻 Web UI](https://github.com/microsoft/TaskWeaver#or--web-ui)

TaskWeaver also supports WebUI for demo purpose, please refer to [web UI docs](https://microsoft.github.io/TaskWeaver/docs/usage/webui) for more details.

#### or 📋 Import as a Library

[Permalink: or 📋 Import as a Library](https://github.com/microsoft/TaskWeaver#or--import-as-a-library)

TaskWeaver can be imported as a library to integrate with your existing project, more information can be found in [docs](https://microsoft.github.io/TaskWeaver/docs/usage/library)

## 📖 Documentation

[Permalink: 📖 Documentation](https://github.com/microsoft/TaskWeaver#-documentation)

More documentations can be found on [TaskWeaver Website](https://microsoft.github.io/TaskWeaver).

### ❓Get help

[Permalink: ❓Get help](https://github.com/microsoft/TaskWeaver#get-help)

- ❔GitHub Issues ( **Preferred**)
- [💬 Discord](https://discord.gg/Z56MXmZgMb) for discussion
- For other communications, please contact [<EMAIL>](mailto:<EMAIL>)

* * *

## 🎬 Demo Examples

[Permalink: 🎬 Demo Examples](https://github.com/microsoft/TaskWeaver#-demo-examples)

The demos were made based on the [web UI](https://microsoft.github.io/TaskWeaver/docs/usage/webui), which is better for displaying the generated artifacts such as images.
The demos could also be conducted in the command line interface.

#### 1️⃣📉 Example 1: Pull data from a database and apply an anomaly detection algorithm

[Permalink: 1️⃣📉 Example 1: Pull data from a database and apply an anomaly detection algorithm](https://github.com/microsoft/TaskWeaver#1%EF%B8%8F%E2%83%A3-example-1-pull-data-from-a-database-and-apply-an-anomaly-detection-algorithm)

In this example, we will show you how to use TaskWeaver to pull data from a database and apply an anomaly detection algorithm.

anomaly\_detection\_chainlit.mp4

If you want to follow this example, you need to configure the `sql_pull_data` plugin in the `project/plugins/sql_pull_data.yaml` file.
You need to provide the following information:

```
api_type: azure or openai
api_base: ...
api_key: ...
api_version: ...
deployment_name: ...
sqlite_db_path: sqlite:///../../../sample_data/anomaly_detection.db
```

The `sql_pull_data` plugin is a plugin that pulls data from a database. It takes a natural language request as input and returns a DataFrame as output.

This plugin is implemented based on [Langchain](https://www.langchain.com/).
If you want to follow this example, you need to install the Langchain package:

```
pip install langchain
pip install tabulate
```

#### 2️⃣🏦 Example 2: Forecast QQQ's price in the next 7 days

[Permalink: 2️⃣🏦 Example 2: Forecast QQQ's price in the next 7 days](https://github.com/microsoft/TaskWeaver#2%EF%B8%8F%E2%83%A3-example-2-forecast-qqqs-price-in-the-next-7-days)

In this example, we will show you how to use TaskWeaver to forecast QQQ's price in the next 7 days.

forecast\_price\_chainlit.mp4

If you want to follow this example, you need to ensure you have these two requirements installed:

```
pip install yfinance
pip install statsmodels
```

For more examples, please refer to our [paper](http://export.arxiv.org/abs/2311.17541).

> 💡 The planning of TaskWeaver are based on the LLM model. Therefore, if you want to repeat the examples, the execution process may be different
> from what you see in the videos. For example, in the second demo, the assistant may ask the user which prediction algorithm should be used.
> Typically, more concrete prompts will help the model to generate better plans and code.

## 📚 Citation

[Permalink: 📚 Citation](https://github.com/microsoft/TaskWeaver#-citation)

Our paper could be found [here](http://export.arxiv.org/abs/2311.17541).
If you use TaskWeaver in your research, please cite our paper:

```
@article{taskweaver,
  title={TaskWeaver: A Code-First Agent Framework},
  author={Bo Qiao, Liqun Li, Xu Zhang, Shilin He, Yu Kang, Chaoyun Zhang, Fangkai Yang, Hang Dong, Jue Zhang, Lu Wang, Minghua Ma, Pu Zhao, Si Qin, Xiaoting Qin, Chao Du, Yong Xu, Qingwei Lin, Saravan Rajmohan, Dongmei Zhang},
  journal={arXiv preprint arXiv:2311.17541},
  year={2023}
}

```

## Trademarks

[Permalink: Trademarks](https://github.com/microsoft/TaskWeaver#trademarks)

This project may contain trademarks or logos for projects, products, or services. Authorized use of Microsoft
trademarks or logos is subject to and must follow
[Microsoft's Trademark & Brand Guidelines](https://www.microsoft.com/en-us/legal/intellectualproperty/trademarks/usage/general).
Use of Microsoft trademarks or logos in modified versions of this project must not cause confusion or imply Microsoft sponsorship.
Any use of third-party trademarks or logos are subject to those third-party's policies.

## Disclaimer

[Permalink: Disclaimer](https://github.com/microsoft/TaskWeaver#disclaimer)

The recommended models in this Repo are just examples, used to explore the potential of agent systems with the paper at [TaskWeaver: A Code-First Agent Framework](https://export.arxiv.org/abs/2311.17541). Users can replace the models in this Repo according to their needs. When using the recommended models in this Repo, you need to comply with the licenses of these models respectively. Microsoft shall not be held liable for any infringement of third-party rights resulting from your usage of this repo. Users agree to defend, indemnify and hold Microsoft harmless from and against all damages, costs, and attorneys' fees in connection with any claims arising from this Repo. If anyone believes that this Repo infringes on your rights, please notify the project owner email.

## About

A code-first agent framework for seamlessly planning and executing data analytics tasks.


[microsoft.github.io/TaskWeaver/](https://microsoft.github.io/TaskWeaver/ "https://microsoft.github.io/TaskWeaver/")

### Topics

[agent](https://github.com/topics/agent "Topic: agent") [openai](https://github.com/topics/openai "Topic: openai") [data-analysis](https://github.com/topics/data-analysis "Topic: data-analysis") [copilot](https://github.com/topics/copilot "Topic: copilot") [ai-agents](https://github.com/topics/ai-agents "Topic: ai-agents") [llm](https://github.com/topics/llm "Topic: llm") [code-interpreter](https://github.com/topics/code-interpreter "Topic: code-interpreter")

### Resources

[Readme](https://github.com/microsoft/TaskWeaver#readme-ov-file)

### License

MIT, MIT licenses found


### Licenses found

[MIT\\
\\
LICENSE](https://github.com/microsoft/TaskWeaver/blob/main/LICENSE) [MIT\\
\\
LICENSE.txt](https://github.com/microsoft/TaskWeaver/blob/main/LICENSE.txt)

### Code of conduct

[Code of conduct](https://github.com/microsoft/TaskWeaver#coc-ov-file)

### Security policy

[Security policy](https://github.com/microsoft/TaskWeaver#security-ov-file)

### Uh oh!

There was an error while loading. [Please reload this page](https://github.com/microsoft/TaskWeaver).

[Activity](https://github.com/microsoft/TaskWeaver/activity)

[Custom properties](https://github.com/microsoft/TaskWeaver/custom-properties)

### Stars

[**5.8k**\\
stars](https://github.com/microsoft/TaskWeaver/stargazers)

### Watchers

[**66**\\
watching](https://github.com/microsoft/TaskWeaver/watchers)

### Forks

[**737**\\
forks](https://github.com/microsoft/TaskWeaver/forks)

[Report repository](https://github.com/contact/report-content?content_url=https%3A%2F%2Fgithub.com%2Fmicrosoft%2FTaskWeaver&report=microsoft+%28user%29)

## [Releases\  2](https://github.com/microsoft/TaskWeaver/releases)

[v0.0.1 release of TaskWeaver\\
Latest\\
\\
Apr 11, 2024](https://github.com/microsoft/TaskWeaver/releases/tag/v0.0.1)

[\+ 1 release](https://github.com/microsoft/TaskWeaver/releases)

## [Packages\  0](https://github.com/orgs/microsoft/packages?repo_name=TaskWeaver)

No packages published

### Uh oh!

There was an error while loading. [Please reload this page](https://github.com/microsoft/TaskWeaver).

## [Contributors\  26](https://github.com/microsoft/TaskWeaver/graphs/contributors)

- [![@liqul](https://avatars.githubusercontent.com/u/7489260?s=64&v=4)](https://github.com/liqul)
- [![@ShilinHe](https://avatars.githubusercontent.com/u/10590876?s=64&v=4)](https://github.com/ShilinHe)
- [![@Jack-Q](https://avatars.githubusercontent.com/u/9014072?s=64&v=4)](https://github.com/Jack-Q)
- [![@zhangxu0307](https://avatars.githubusercontent.com/u/32928431?s=64&v=4)](https://github.com/zhangxu0307)
- [![@dependabot[bot]](https://avatars.githubusercontent.com/in/29110?s=64&v=4)](https://github.com/apps/dependabot)
- [![@m-c-frank](https://avatars.githubusercontent.com/u/61345033?s=64&v=4)](https://github.com/m-c-frank)
- [![@microsoftopensource](https://avatars.githubusercontent.com/u/22527892?s=64&v=4)](https://github.com/microsoftopensource)
- [![@developer-hassan](https://avatars.githubusercontent.com/u/111445095?s=64&v=4)](https://github.com/developer-hassan)
- [![@Copilot](https://avatars.githubusercontent.com/in/946600?s=64&v=4)](https://github.com/apps/copilot-pull-request-reviewer)
- [![@shism2](https://avatars.githubusercontent.com/u/629169?s=64&v=4)](https://github.com/shism2)
- [![@halcyondude](https://avatars.githubusercontent.com/u/3286990?s=64&v=4)](https://github.com/halcyondude)
- [![@cclauss](https://avatars.githubusercontent.com/u/3709715?s=64&v=4)](https://github.com/cclauss)
- [![@PallHaraldsson](https://avatars.githubusercontent.com/u/8005416?s=64&v=4)](https://github.com/PallHaraldsson)

[\+ 12 contributors](https://github.com/microsoft/TaskWeaver/graphs/contributors)

## Languages

- [Python96.2%](https://github.com/microsoft/TaskWeaver/search?l=python)
- [JavaScript1.8%](https://github.com/microsoft/TaskWeaver/search?l=javascript)
- Other2.0%

You can’t perform that action at this time.