(Files content cropped to 300k characters, download full ingest to see more)
================================================
FILE: README.md
================================================
# CGM: Code Graph LLM

![CodefuseLogo](./assets/github-codefuse-logo-update.jpg)

## Contents
- [News](#news)
- [Introduction](#introduction)
- [Installation](#installation)
- [Examples](#examples)
  - [Rewriter](#rewriter)
  - [Retriever](#retriever)
  - [Reranker](#reranker)
  - [Reader](#reader)
- [Contributing](#contributing)
- [Citation](#citation)
- [Join Us](#join-us)

## News

![SWE-Bench-Lite](./assets/SWE-Bench-Lite.png)

🔥🔥🔥 [2025/01/15] We are pleased to announce the updated version of the CGM-72B-V1.2. The model further achieves a remarkable 44.00% resolve rate on the SWE-Bench-Lite leaderboard.

🔥🔥🔥 [2024/12/28] We are pleased to announce the updated version of the CGM-72B-V1.1. The model further achieves a remarkable 41.67% resolve rate on the SWE-Bench-Lite leaderboard.

🔥🔥🔥 [2024/10/28] We are pleased to announce that CGM-72B achieves a remarkable 35.67% resolve rate on the SWE-Bench-Lite leaderboard.

🔥🔥🔥 [2024/10/28] We released **CGM**, mainly for repository-level coding tasks.

## Introduction
We propose a graph-based framework CGM for real-world SE tasks. Before CGM starts its work, we construct a repository-level code graph to better represent the repository context and its structure by Code Graph Generator. Inspired by the Retrieval-Augmented Generation (RAG) approach, CGM framework is designed as a chain structure consisting of four atomic nodes, termed as R4 (Rewriter, Retriever, Reranker, and Reader) chain for this scenario. Given an issue, the initial input to the CGM framework includes the issue description and the corresponding code graph. Rewriter will first rewrite the original issue by extracting keywords and generating relevant queries for code graph. Then a heuristic code subgraph is retrieved through Retriever based on the matching anchor nodes from rewriter output. Given that the resulting subgraph provides a relatively broad context necessary for reference, we need a Reranker to identify the files most likely to be modified as a further hint. Subsequently, both the retrieved subgraph and the identified files are input into a trainable, graph-based Reader to generate the corresponding code patch.

### Framework

![Framework](./assets/cgm_method_v3.png)

### Highlights
:white_check_mark: **Code Graph**: Train models on multiple tasks while maintaining a balance between them. The models can even generalize to new, previously unseen tasks.

:white_check_mark: **Multi-framework**: It provides support for both Accelerate (with Deepspeed and FSDP)

:white_check_mark: **Efficient fine-tuning**: It supports LoRA, QLoRA as well as Full-parameters training, enabling fine-tuning of large models with minimal resources. The training speed meets the demands of almost all fine-tuning scenarios.

## Installation
### Prerequisites
- Python 3.8+
- pip

### Required Packages

```bash
transformers==4.46.1
tokenizers==0.20.0
accelerate==1.0.1
peft==0.13.2
jinja2==2.11.3
fuzzywuzzy==0.18.0
python-Levenshtein==0.25.1
networkx==3.0
```

## Examples

### Rewriter
Use function ```generate_prompt_for_extractor``` and ```generate_prompt_for_inferer``` in ```rewriter/prompt.py```
```python
def generate_prompt_for_extractor(problem_statement, repo_name):
    prompt = """
    <issue>
    {}
    </issue> 
    This is an issue related to repository '{}'. 
    Instructions:
    1. Analysis:
    ○ Analyze the provided issue description. Identify the relevant File, Class, or Function involved.
    ○ Determine the specific problem or error encountered and note any clues that may assist in locating the relevant or problematic area.
    2. Extraction:
    ○ After the analysis, extract ALL the mentioned code entities (File, Class, or Function), especially Files.
    ○ Then extract three potential and meaningful keywords, responding in the following format:

    [start_of_analysis] 
    <detailed_analysis> 
    [end_of_analysis] 

    [start_of_related_code_entities] 
    <entity_name_with_path>
    [end_of_related_code_entities] 

    [start_of_related_keywords] 
    <keywords>
    [end_of_related_keywords]

    Notes:
    - Pay attention to the information in the error logs (if exists).
    - The buggy code exists solely in the project described in the issue (e.g., django, sklearn). Buggy location is usually not in the tests files or external packages.
    - Your extracted entities should be CONCISE, ACCURATE and INFORMATIVE.
    - Provide the relative path for code entities if specified (e.g., package/foo.py). Relative path is relative to the repository itself, do not include suffix like '/home/<USER>/', '/etc/service/' or '/tree/master'.
    - Do not include any additional information such as line numbers or explanations in your extraction result.

    Preferred extraction Examples of Code Entities:
    - repo/cart.py
    - Class User()
    - def getData()
    Preferred extraction Examples of Keywords:
    - train_loop
    - hooks
    - docker
    
    Unpreferred extraction Examples of keywords:
    - something wrong
    - input validation
    - TypeError
    """.format(problem_statement, repo_name)
        
    return prompt

def generate_prompt_for_inferer(problem_statement, repo_name):
    prompt = """
    <issue>
    {}
    </issue> 
    This is an issue related to repository '{}'. 
    Task:
    Based on the issue description provided, identify the characteristics of code entities (files, functions, class) that might need to be modified. 
    For each characteristic, generate a search query that could help locate relevant code entities in a codebase.
    Instructions:
    First, analyze the issue description and identify keywords, features, and functionalities that are likely relevant to the modification of code entities.
    Then, create queries that capture these characteristics, focusing on:
    ● File names that may implement relevant functionalities.
    ● Functions or methods that are related to the features described in the issue.
    ● Any patterns or structures that might be relevant to the functionalities mentioned.
    For example:
    ● File related to the initialization of a neural network.
    ● Function related to the training process.
    ● Code used to configure the service.
    Please answer in the following format:

    [start_of_analysis] 
    <detailed_analysis> 
    [end_of_analysis] 

    [start_of_related_queries] 
    query 1:
    query 2:
    ...
    [end_of_related_queries] 

    Notes:
    - Your queries should be DETAILED, ACCURATE and INFORMATIVE. 
    - Your queries should be a complete sentences and do not include additional explanation.
    - The number of queries is up to five, so be focus on the important characteristics.
    - Your queries should focus on the repository code itself, rather than other information like commit history.
    - Pay attention to the information in the error logs (if exists).

    Preferred Query Examples:
    - Look for references to "tqdm" or "progress_bar" within the training loop files to find where progress bars are currently updated.
    - Code snippets where 'gethostbyname' function from 'socket' module is called.
    - File name containing 'mysql.py' AND functions related to 'MySQLStatementSamples' initialization.
    - Functions or methods handling hostname resolution or encoding within 'datadog_checks' directory.
    - Find all occurrences of "early_stopping" within files that also mention "Trainer" to identify where early stopping logic is implemented and potentially needs adjustment for non-default 'val_check_interval'.
    """.format(problem_statement, repo_name)
        
    return prompt
```
You can use the rewriter prompt by
```python
from rewriter.prompt import generate_prompt_for_extractor, generate_prompt_for_inferer

# Generate extraction prompt
extraction_prompt = generate_prompt_for_extractor(problem_statement, repo_name)

# Generate inference prompt
inference_prompt = generate_prompt_for_inferer(problem_statement, repo_name)
```

### Retriever
The Retriever module consists of two main components:

1. ```locate.py``` - Anchor Node Identification
   
Identifies relevant anchor nodes in the code graph based on Rewriter output.

2. ```subgraph.py``` - Relevant Subgraph Extraction

Extracts relevant subgraph around the identified anchor nodes.

### Reranker
Use function ```generate_prompt_for_reranker_stage_1``` and ```generate_prompt_for_reranker_stage_2``` in ```reranker/prompt.py```
```python
"""
Prompt Template for Reranker
"""

reranker_stage_1_system_prompt = """
You are an experienced software developer who specializes in extracting the most relevant files for solving issues from many reference files.

Task:
Based on the information received about the issue from a repository, find the most likely few files from among those that may be able to resolve the issue.

Instructions:
1. Analysis:
- Analyze the provided issue description and files, and pay attention to the relevance of the provided files with the given issue, especially those might be modified during fixing the issue.
- Determine the specific problem or error mentioned in the issue and note any clues that could help your judgment.
2. Extraction:
- Based on your analysis, choose the Top **1** relevant files which might be used in fixing the issue.
- You should choose files from the provided files, and should not modify their name in any way.

Respond in the following format:
[start_of_analysis]
<detailed_analysis> 
[end_of_analysis] 

[start_of_relevant_files] 
1. <file_with_its_path>
2. <file_with_its_path>
3. ...
[end_of_relevant_files] 

Notes:
- You can refer to to the information in the error logs (if exists).
- The relevant file usually exists in the project described in the issue (e.g., django, sklearn). File need modification is usually not in the tests files or external packages.
- The file you choose should be contained in the provided files.
- Provide the file path with files. Do not include redundant suffix like '/home/<USER>/', '/etc/service/' or '/tree/master'.
- Do not include any additional information such as line numbers or explanations in your extraction result.
- Files for initialization and configuration might be modified during changing the code.

Preferred extraction Examples of Related Files:
1. src/utils/file_handler.py
2. core/services/service_manager.py
3. ...
""".strip()

reranker_stage_1_user_prompt_template = """
<repository>
{}
</repository>

<issue>
{}
</issue>
 
<reference_python_file_list>
{}
</reference_python_file_list>

<other_reference_file_list>
{}
</other_reference_file_list>
"""

reranker_stage_2_system_prompt = """
You are an experienced software developer who specializes in assessing the relevance of the file for solving the issue in software repositories.

Task:
For a file provided, evaluate the likelihood that modifying this file would resolve the given issue, and assign a score based on specific criteria.

Instructions:
1. Analysis:
- Analyze the provided issue description and the content of the single relevant file, pay attention to any keywords, error messages, or specific functionalities mentioned that relate to the file.
- Determine how closely the contents and functionality of the file are tied to the problem or error described in the issue.
- Consider the role of the file in the overall project structure (e.g., configuration files, core logic files versus test files, or utility scripts).
2. Scoring:
- Based on your analysis, assign a score from 1 to 5 that represents the relevance of modifying the given file in order to solve the issue.

Score Specifications:
1. **Score 1**: The file is almost certainly unrelated to the issue, with no apparent connection to the functionality or error described in the issue.
2. **Score 2**: The file may be tangentially related, but modifying it is unlikely to resolve the issue directly; possible in rare edge cases.
3. **Score 3**: The file has some relevance to the issue; it might interact with the affected functionality indirectly and tweaking it could be part of a broader fix.
4. **Score 4**: The file is likely related to the issue; it includes code that interacts directly with the functionality in question and could plausibly contain bugs that lead to the issue.
5. **Score 5**: The file is very likely the root cause or heavily involved in the issue and modifying it should directly address the error or problem mentioned.

Respond in the following format:
[start_of_analysis]
<detailed_analysis>
[end_of_analysis]

[start_of_score]
Score <number>
[end_of_score]

Notes:
- The content of the file shows only the structure of this file, including the names of the classes and functions defined in this file.
- You can refer to to the information in the error logs (if exists).
""".strip()

reranker_stage_2_user_prompt_template = """
<repository>
{}
</repository>

<issue>
{}
</issue>

<file_name>
{}
</file_name>

<file_content>
{}
</file_content>
"""

def generate_prompt_for_reranker_stage_1(problem_statement, repo_name, py_file, other_file):
  """
  problem_statement: issue
  repo_name: repo
  py_file: py file list
  other_file: related file list
  """
  return reranker_stage_1_system_prompt, reranker_stage_1_user_prompt_template.format(repo_name, problem_statement, py_file, other_file)

def generate_prompt_for_reranker_stage_2(problem_statement, repo_name, file_name, file_content):
  """
  problem_statement: issue
  repo_name: repo
  file_name: file
  file_content: file content（class xxx和def xxx）
  """
  return reranker_stage_2_system_prompt, reranker_stage_2_user_prompt_template.format(repo_name, problem_statement, file_name, file_content)
```
You can use the reranker prompt by
```python
from reranker.prompt import generate_prompt_for_reranker_stage_1, generate_prompt_for_reranker_stage_2

# Stage 1: Identify relevant files
system_prompt, user_prompt = generate_prompt_for_reranker_stage_1(
    problem_statement, 
    repo_name, 
    py_file_list, 
    other_file_list
)

# Stage 2: Score file relevance
system_prompt, user_prompt = generate_prompt_for_reranker_stage_2(
    problem_statement,
    repo_name,
    target_file,
    file_content
)
```

### Reader
Execute the Reader module with DeepSpeed configurations:
```bash
# Zero-2 Configuration
EXPORT N_NODE={YOUR_MACHINE_NUM} && \
EXPORT N_GPU_PER_NODE={YOUR_GPU_NUM} && \
EXPORT TRAIN_CONFIG={TRAIN_CONFIG}.json && \
bash launch/zero2.sh

# Zero-3 Configuration
EXPORT N_NODE={YOUR_MACHINE_NUM} && \
EXPORT N_GPU_PER_NODE={YOUR_GPU_NUM} && \
EXPORT TRAIN_CONFIG={TRAIN_CONFIG}.json && \
bash launch/zero3.sh
```

## Contributing
Contributions are welcome! If you have any suggestions, ideas, bug reports, or new model/feature supported, please open an issue or submit a pull request.

We welcome contributions from the community! Please follow these guidelines:

1. Fork the repository

2. Create your feature branch

3. Commit your changes

4. Push to the branch

5. Open a Pull Request

For major changes, please open an issue first to discuss the proposed changes.


## Citation
If you find our work useful or helpful for your R&D works, please feel free to cite our paper as below.
```bibtex
@misc{tao2025codegraphmodelcgm,
      title={Code Graph Model (CGM): A Graph-Integrated Large Language Model for Repository-Level Software Engineering Tasks}, 
      author={Hongyuan Tao and Ying Zhang and Zhenhao Tang and Hongen Peng and Xukun Zhu and Bingchang Liu and Yingguang Yang and Ziyin Zhang and Zhaogui Xu and Haipeng Zhang and Linchao Zhu and Rui Wang and Hang Yu and Jianguo Li and Peng Di},
      year={2025},
      eprint={2505.16901},
      archivePrefix={arXiv},
      primaryClass={cs.SE},
      url={https://arxiv.org/abs/2505.16901}, 
}
```
## Join-US

We are the AI Native team within the Platform Technology Business Group at Ant Group, dedicated to the intelligentization of Ant Group's platform engineering. Established for over three years, our team has played a pivotal role in supporting the intelligent operation and maintenance of Ant Group's cloud computing infrastructure. Our mission is to build algorithm services and platforms with a wide user base through world-class technological innovation and impact, supporting the implementation of internal and external products and businesses.
Embracing an innovation-driven ethos, our team not only supports business implementation but also propels technological influence. Over the past three years, we have published more than 20 papers at top conferences like ICLR, NeurIPS, KDD, and ACL. Our innovative business outcomes have earned us two Ant Technology's highest T-Star awards and one SuperMA award from Ant Group. Our open-source project CodeFuse has received 4K stars as of February 2024, and our models have been downloaded over 1.5 million times on Huggingface and Modelscope.

We are on the lookout for top talents to join our vibrant team! If you're eager to develop your career in an environment filled with energy, innovation, and a culture of excellence, we welcome you to explore our career opportunities for both campus and experienced hires. Join us and be a part of creating the next milestone in the industry.

**Contact**: <EMAIL> 



================================================
FILE: LEGAL.md
================================================
Legal Disclaimer

Within this source code, the comments in Chinese shall be the original, governing version. Any comment in other languages are for reference only. In the event of any conflict between the Chinese language version comments and other language version comments, the Chinese language version shall prevail.

法律免责声明

关于代码注释部分，中文注释为官方版本，其它语言注释仅做参考。中文注释可能与其它语言注释存在不一致，当中文注释与其它语言注释存在不一致时，请以中文注释为准。



================================================
FILE: cgm/config/template.json
================================================
{
  "graph_dir": [],
  "train_files":[],
  "valid_files":[],
  "output_dir": "",
  "tb_dir": "",

  "embedding_dim": 256,
  "load_pretrained_encoder": false,
  "pretrained_encoder_path": null, 

  "load_pretrained_adapter": false,
  "pretrained_adapter_path": null,
  "adapter_hidden_dim": 4096,
  "adapter_num_layers": 1,
  "adapter_num_heads": 8,

  "load_pretrained_tokenizer": true,
  "pretrained_tokenizer_path": "Qwen/Qwen2.5-Coder-7B-Instruct",

  "pretrained_model_path": "Qwen/Qwen2.5-Coder-7B-Instruct",
  "self_defined": false,
  "framework_type": "T1",
  "model_type": "Qwen",

  "pretrained_lora_path": null,
  "quantization": "4bit",

  "mode": "eal",
  "task": "unit_test",
  "use_chat": false,
  "use_adj": true,

  "peft": "LoRA",
  "lora_rank": 32,
  "lora_alpha": 32,
  "lora_dropout": 0.05,
  "lora_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],

  "enc_peft": "LoRA",
  "enc_lora_rank": 32,
  "enc_lora_alpha": 32,
  "enc_lora_dropout": 0.05,
  "enc_lora_modules": "all-linear",

  "graph_pad_token": "<｜graph_pad｜>",
  "graph_pad_id": 32022,
  "graph_token_num": 512,

  "learning_rate": 1e-4,
  "min_lr": 1e-7,
  "weight_decay": 0.1,
  "lr_scheduler_type": "reduce_lr_on_plateau",

  "gradient_accumulation_steps": 1,
  "num_warmup_steps": 0,
  "adapter_warmup": false,
  "adapter_warmup_steps": 500,
  "num_train_epochs": 20,

  "data_split": "0.98,0.02",
  "max_train_steps": null,
  "max_train_samples": null,
  "max_valid_samples": 2048,
  "per_device_train_batch_size": 1,
  "per_device_eval_batch_size": 1,

  "seed": 42,
  "seq_length": 8192,
  "log_interval": 5,

  

  "step_checkpointing": false,
  "checkpointing_steps": 500,
  "step_evaluation": true,
  "evaluation_steps": 5000,
  "epoch_evaluation": false,
  "epoch_checkpointing": false,

  "early_stopping": true,
  "early_stopping_stall_num": 6,

  "attn_implementation": "sdpa"
}



================================================
FILE: cgm/data/encode.py
================================================

# Template: '<|im_start|>system\nYou are a helpful assistant.<|im_end|>\n' 
# CodeQwen1.5-7B-Chat, Qwen2-72B-Instruct
QwenTokenizerConfig = {
    'seq_length': 8192,
    'SYSTEM': 'system',
    'HUMAN': 'user',
    'BOT': 'assistant',
    'SENTENCE_START_MARKER': '',
    'SENTENCE_END_MARKER': '',
    'SYSTEM_START_MARKER': '<|im_start|>',
    'SYSTEM_START_MARKER_2': '\n',
    'SYSTEM_END_MARKER': '<|im_end|>\n',
    'HUMAN_START_MARKER': '<|im_start|>',
    'HUMAN_START_MARKER_2': '\n',
    'HUMAN_END_MARKER': '<|im_end|>\n',
    'BOT_START_MARKER': '<|im_start|>',
    'BOT_START_MARKER_2': '\n',
    'BOT_END_MARKER': '<|im_end|>\n',
}

# Template: <｜begin▁of▁sentence｜>{system_message}<｜User｜>{user_message_1}<｜Assistant｜>{assistant_message_1}<｜end▁of▁sentence｜><｜User｜>{user_message_2}<｜Assistant｜>
# DeepSeek-V2.5
DeepSeekTokenizerConfig = {
    'seq_length': 8192,
    'SYSTEM': 'system',
    'HUMAN': 'user',
    'BOT': 'assistant',
    'SENTENCE_START_MARKER': '<｜begin▁of▁sentence｜>',
    'SENTENCE_END_MARKER': '<｜end▁of▁sentence｜>',
    'SYSTEM_START_MARKER': '',
    'SYSTEM_START_MARKER_2': '',
    'SYSTEM_END_MARKER': '',
    'HUMAN_START_MARKER': '<｜User｜>',
    'HUMAN_START_MARKER_2': '',
    'HUMAN_END_MARKER': '',
    'BOT_START_MARKER': '<｜Assistant｜>',
    'BOT_START_MARKER_2': '',
    'BOT_END_MARKER': '',
}

DeepSeekCoderTokenizerConfig = {
    'seq_length': 8192,
    'SYSTEM': 'system',
    'HUMAN': 'user',
    'BOT': 'assistant',
    'SENTENCE_START_MARKER': '<｜begin▁of▁sentence｜>',
    'SENTENCE_END_MARKER': '<｜end▁of▁sentence｜>',
    'SYSTEM_START_MARKER': '',
    'SYSTEM_START_MARKER_2': '',
    'SYSTEM_END_MARKER': '\n\n',
    'HUMAN_START_MARKER': 'User: ',
    'HUMAN_START_MARKER_2': '',
    'HUMAN_END_MARKER': '\n\n',
    'BOT_START_MARKER': 'Assistant: ',
    'BOT_START_MARKER_2': '',
    'BOT_END_MARKER': '',
}

def format_eol(text):
    if not text.endswith("\n"):
        text += "\n"
    return text

def get_template(data):
    template = [
        {'role': 'system', 'content': ''},
        {'role': 'user', 'content': data['prompt']},
        {'role': 'assistant', 'content': data['answer']}
    ]

def get_config(name):
    if name == 'Qwen':
        return QwenTokenizerConfig
    elif name == 'DeepSeek':
        return DeepSeekTokenizerConfig
    elif name == 'DeepSeek-Coder':
        return DeepSeekCoderTokenizerConfig
    else:
        raise NotImplementedError

class BaseEncoder(object):
    def __init__(self, tokenizer, config_name):
        # self.args = args
        # seq_length - 1 for shifting
        # self.seq_length = args.seq_length - 1
        config = get_config(config_name)
        self.tokenizer = tokenizer
        # self.seq_length = tokenizer.model_max_length
        self.seq_length = config.get('seq_length')
        
        # TODO: default Qwen
        self.SYSTEM = config.get('SYSTEM')
        self.HUMAN = config.get('HUMAN')
        self.BOT = config.get('BOT')

        self.SENTENCE_START_MARKER = config.get('SENTENCE_START_MARKER')
        self.SENTENCE_END_MARKER = config.get('SENTENCE_END_MARKER'),

        self.SYSTEM_START_MARKER = config.get('SYSTEM_START_MARKER')
        self.SYSTEM_START_MARKER_2 = config.get('SYSTEM_START_MARKER_2')
        self.SYSTEM_END_MARKER = config.get('SYSTEM_END_MARKER')

        self.HUMAN_START_MARKER = config.get('HUMAN_START_MARKER')
        self.HUMAN_START_MARKER_2 = config.get('HUMAN_START_MARKER_2')
        self.HUMAN_END_MARKER = config.get('HUMAN_END_MARKER')

        self.BOT_START_MARKER = config.get('BOT_START_MARKER')
        self.BOT_START_MARKER_2 = config.get('BOT_START_MARKER_2')
        self.BOT_END_MARKER = config.get('BOT_END_MARKER')

        self.sentence_start_ids = self.tokenizer.encode(f"{self.SENTENCE_START_MARKER}", add_special_tokens=False) if self.SENTENCE_START_MARKER != '' else []
        self.sentence_end_ids = self.tokenizer.encode(f"{self.SENTENCE_END_MARKER}", add_special_tokens=False) if self.SENTENCE_END_MARKER != '' else []

        self.system_start_ids = self.tokenizer.encode(f"{self.SYSTEM_START_MARKER}{self.SYSTEM}{self.SYSTEM_START_MARKER_2}", add_special_tokens=False) 
        self.system_end_ids = self.tokenizer.encode(f"{self.SYSTEM_END_MARKER}", add_special_tokens=False) if self.SYSTEM_END_MARKER != '' else []
        
        self.human_start_ids = self.tokenizer.encode(f"{self.HUMAN_START_MARKER}{self.HUMAN}{self.HUMAN_START_MARKER_2}", add_special_tokens=False)
        self.human_end_ids = self.tokenizer.encode(f"{self.HUMAN_END_MARKER}", add_special_tokens=False) if self.HUMAN_END_MARKER != '' else []
        
        self.bot_start_ids = self.tokenizer.encode(f"{self.BOT_START_MARKER}{self.BOT}{self.BOT_START_MARKER_2}", add_special_tokens=False)
        self.bot_end_ids = self.tokenizer.encode(f"{self.BOT_END_MARKER}", add_special_tokens=False) if self.BOT_END_MARKER != '' else []

        self.end_ids = [self.tokenizer.eos_token_id]

    def padding(self, input_ids, loss_mask, qa_mask):
        pad_id = self.tokenizer.pad_token_id

        assert len(input_ids) <= self.seq_length, f"padding sequence: {len(input_ids)} > {self.seq_length}"
        input_ids += [pad_id] * (self.seq_length - len(input_ids))
        loss_mask += [0] * (self.seq_length - len(loss_mask))
        qa_mask += [0] * (self.seq_length - len(loss_mask))
        return {
            "input_ids": input_ids,
            "loss_mask": loss_mask,
            "qa_mask": qa_mask
        }

class CGMEncoder(BaseEncoder):
    def __init__(self, tokenizer, config_name):
        super().__init__(tokenizer, config_name)

    def dataToInput(self, data, seg_role = None):
        input_ids, loss_mask, qa_mask = [], [], []
        # TODO: expand
        message = [
            {'role': self.HUMAN, 'content': 'prompt', 'marker': True, 'loss': 0},
            {'role': self.BOT, 'content': 'answer', 'marker': True, 'loss': 1}
        ]
        if seg_role is not None:
            message = [item for item in message if item['role'] == seg_role]

        input_ids += self.sentence_start_ids
        loss_mask += [0] * len(self.sentence_start_ids)
        qa_mask += [0] * len(self.sentence_start_ids)

        for segment in message:
            role = segment['role']
            content = segment['content']
            marker = segment['marker']
            loss = segment['loss']

            if role == self.SYSTEM:
                system_ids = self.tokenizer.encode(str(data[content]), add_special_tokens=False)
                if marker:
                    input_ids += self.system_start_ids + system_ids + self.system_end_ids
                    loss_mask += [0] * len(self.system_start_ids) + [loss] * len(system_ids) + [0] * len(self.system_end_ids)
                    qa_mask += [0] * len(self.system_start_ids) + [1] * len(system_ids) + [0] * len(self.system_end_ids)
                else:
                    input_ids += system_ids
                    loss_mask += [loss] * len(system_ids)
                    qa_mask += [loss] * len(system_ids)

            elif role == self.HUMAN:

                human_ids = self.tokenizer.encode(str(data[content]), add_special_tokens=False)
                if marker:
                    input_ids += self.human_start_ids + human_ids + self.human_end_ids
                    loss_mask += [0] * len(self.human_start_ids) + [loss] * len(human_ids) + [0] * len(self.human_end_ids)
                    qa_mask += [0] * len(self.human_start_ids) + [1] * len(human_ids) + [0] * len(self.human_end_ids)
                else:
                    input_ids += human_ids
                    loss_mask += [loss] * len(human_ids)
                    qa_mask += [1] * len(human_ids)

            elif role == self.BOT:
                bot_ids = self.tokenizer.encode(str(data[content]), add_special_tokens=False)
                if marker:
                    input_ids += self.bot_start_ids + bot_ids + self.bot_end_ids
                    loss_mask += [0] * len(self.bot_start_ids) + [loss] * len(bot_ids) + [0] * len(self.bot_end_ids)
                    qa_mask += [0] * len(self.bot_start_ids) + [0] * len(bot_ids) + [0] * len(self.bot_end_ids)
                else:
                    input_ids += bot_ids
                    loss_mask += [loss] * len(bot_ids)
                    qa_mask += [0] * len(bot_ids)

            else:
                raise ValueError(f"wrong {role} for {config_name}")

        input_ids += self.sentence_end_ids
        loss_mask += [1] * len(self.sentence_end_ids)
        qa_mask += [0] * len(self.sentence_end_ids)

        assert len(input_ids) == len(loss_mask)

        if len(input_ids) <= self.seq_length:
            # features = self.padding(input_ids, loss_mask, qa_mask)
            features = {}
            features['input_ids'] = input_ids
            features['loss_mask'] = loss_mask
            features['qa_mask'] = qa_mask
        else:
            features = {}
            features['input_ids'] = input_ids[:self.seq_length - 1]
            features['loss_mask'] = loss_mask[:self.seq_length - 1]
            features['qa_mask'] = qa_mask[:self.seq_length - 1]

            features['input_ids'] += self.sentence_end_ids
            features['loss_mask'] += [1] * len(self.sentence_end_ids)
            features['qa_mask'] += [0] * len(self.sentence_end_ids)

        assert len(features['input_ids']) == len(features['loss_mask'])

        return features




================================================
FILE: cgm/data/preprocess.py
================================================
import json
import json
# from codegraph import *

from torch.utils.data.dataset import Dataset
from transformers import AutoModel, AutoTokenizer

# from FlagEmbedding import BGEM3FlagModel
# from sentence_transformers import SentenceTransformer

from datasets import Dataset as HFDataset
from datasets import load_dataset

import torch
import numpy as np
import logging
import time
import gc

import random
import string
import os
import sys

import json
from collections import defaultdict
import random

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def getJavaSentence(node, nodeType, reponame, max_len):
    def process_Repo(node):
        return reponame

    def process_Module(node):
        return node['name']

    def process_Package(node):
        return node['name']

    def process_File(node):
        path = node.get('path','')
        if len(path) > 0:
            path = path + '/'
        return f"{path}{node['name']}"

    def process_TextFile(node):
        return f"{node['name']}\n{node.get('text','')}"

    def process_Class(node):
        return f"{node.get('modifiers','')} {node['name']}\n{node.get('comment','')}".strip(' ')

    def process_Field(node):
        return f"{node.get('modifiers','')} {node['fieldType']} {node['name']}\n{node.get('comment','')}".strip(' ')

    def process_Method(node):
        className = node.get('className','')
        methodName = node.get('methodName', '')
        if len(methodName) == 0 or len(className) == 0:
            split = node['signature'].split('#')
            className = split[0]
            methodName = split[1].split('(')[0]
        name = className + '.' + methodName
        comment = f"{node.get('comment','')}\n" if not node.get('comment','') == '' else ''
        text = f"{node.get('modifiers','')} {node.get('text','')}" if not node.get('modifiers','') == '' else node.get('text','')
        return f"{name}\n{comment}{text}"

    def process_default(node):
        raise ValueError(f"unrecognized nodeType for node.keys {node['nodeType']} {str(node.keys())}")
        return ""

    processors = {
        'Repo': process_Repo,
        'Module': process_Module,
        'Package': process_Package,
        'File': process_File,
        'TextFile': process_TextFile,
        'Textfile': process_TextFile,
        'Class': process_Class,
        'Field': process_Field,
        'Method': process_Method
    }

    sentence = processors.get(nodeType, process_default)(node)

    # TODO: limit token not str size
    if len(sentence) > max_len:
        sentence = sentence[:max_len]

    return sentence

def getPythonSentence(node, nodeType, reponame, max_len):
    def process_Repo(node):
        return reponame

    def process_Package(node):
        return node['name']

    def process_File(node):
        path = node.get('filePath','')
        if len(path) > 0:
            path = path + '/'
        return f"{path}{node['fileName']}\n{node.get('text','')}"

    def process_TextFile(node):
        return f"{node['name']}\n{node.get('text','')}"

    def process_Class(node):
        return f"{node.get('classType','')} {node['className']}\n{node.get('comment','')}\n{node.get('text','')}".strip(' ')

    def process_Attribute(node):
        return f"{node.get('attributeType','')} {node['name']}\n{node.get('comment','')}\n{node.get('text','')}".strip(' ')

    def process_Function(node):
        comment = f"{node.get('comment','')}\n" if not node.get('comment','') == '' else ''
        return f"{node.get('header','')} {node['name']}\n{comment}{node.get('text','')}".strip(' ')

    def process_Lambda(node):
        return f"{node.get('text','')}".strip(' ')

    def process_default(node):
        raise ValueError(f"unrecognized nodeType for node.keys {node['nodeType']} {str(node.keys())}")
        return ""

    processors = {
        'Repo': process_Repo,
        'Package': process_Package,
        'File': process_File,
        'TextFile': process_TextFile,
        'Textfile': process_TextFile,
        'Class': process_Class,
        'Attribute': process_Attribute,
        'Function': process_Function,
        'Lambda': process_Lambda
    }

    sentence = processors.get(nodeType, process_default)(node)

    # TODO: limit token not str size
    if len(sentence) > max_len:
        sentence = sentence[:max_len]

    return sentence

def graph2embedding(data, model, tokenizor, reponame, language, save_adj):
    node_embeddings = {}
    sentence_dict = {}
    node_id_to_index = {}
    index_counter = 0

    for node in data['nodes']:
        nodeType = node['nodeType']

        if 'nodeId' in node.keys():
            node_id = node['nodeId']
        elif 'id' in node.keys():
            node_id = node['id']
        else:
            raise ValueError("No key named id/nodeId")

        if language == 'java':
            sentence = getJavaSentence(node, nodeType, reponame, 1024000)
        elif language == 'python':
            sentence = getPythonSentence(node, nodeType, reponame, 1024000)
        else:
            raise ValueError(f"Language {language} not supported")

        if sentence == "":
            node_embedding = torch.zeros((1, 256), dtype=torch.float32).to(device)
            node_embeddings[node_id] = [node_embedding]
            sentence_dict[index_counter] = ""
            node_id_to_index[node_id] = [index_counter]
            index_counter += 1
        else:
            # 手动切词
            tokens = tokenizor.tokenize(sentence)
            num_tokens = len(tokens)
            num_segments = (num_tokens + 511) // 512  # Calculate number of segments
            embeddings = []
            segments = []
            node_id_to_index[node_id] = list(range(index_counter, index_counter + num_segments))
            for i in range(num_segments):
                start = i * 512
                end = min((i + 1) * 512, num_tokens)
                segment_tokens = tokens[start:end]
                segment_sentence = tokenizor.convert_tokens_to_string(segment_tokens)
                segment_ids = tokenizor.encode(segment_sentence, return_tensors="pt").to(device)
                with torch.no_grad():
                    segment_embedding = model(segment_ids)
                embeddings.append(segment_embedding)
                segments.append(segment_sentence)
                sentence_dict[index_counter] = segment_sentence
                index_counter += 1

            node_embeddings[node_id] = embeddings

    num_nodes = index_counter
    
    if save_adj:
        adj_matrix = torch.zeros((num_nodes, num_nodes))

        for edge in data['edges']:
            source_id = edge['source']
            target_id = edge['target']
            source_indices = node_id_to_index.get(source_id)
            target_indices = node_id_to_index.get(target_id)
            if source_indices is None or target_indices is None:
                # if source_indices is None: 
                #     print(f"{source_id} not exists")
                # if target_indices is None:
                #     print(f"{target_id} not exists")
                continue
            
            for source_index in source_indices:
                for target_index in target_indices:
                    adj_matrix[source_index, target_index] = 1

        # Connect embeddings of the same node
        for node_id, indices in node_id_to_index.items():
            for i in range(len(indices)):
                for j in range(i + 1, len(indices)):
                    adj_matrix[indices[i], indices[j]] = 1
                    adj_matrix[indices[j], indices[i]] = 1
    else:
        adj_matrix = None

    all_embeddings = []
    for value in node_embeddings.values():
        if isinstance(value, torch.Tensor):
            all_embeddings.append(value)
        elif isinstance(value, list):
            for tensor in value:
                all_embeddings.append(tensor)

    embeddings = torch.stack(all_embeddings, dim=0)

    # embeddings = torch.stack(list(node_embeddings.values()))
    # embeddings = torch.stack(sum(node_embeddings.values(), []))
    # embeddings = torch.cat(list(node_embeddings.values()), dim=0)

    return embeddings, adj_matrix, sentence_dict

def preprocess_graph(graphdir, savedir, recdir, jsondir, language = 'java', model = None, tokenizor = None, filenum = 1, suffix = 'pt', node_limit = 20000, save_adj = True, save_rec = True):
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    logger.info(f"Parsing json file: {jsondir}{filenum}.json")

    if not os.path.exists(savedir):
        os.makedirs(savedir)

    if not os.path.exists(recdir):
        os.makedirs(recdir)

    if jsondir == graphdir:
        glist = os.listdir(graphdir)
    else:
        with open(f'{jsondir}{filenum}.json', 'r') as f:
            glist = json.load(f)
        f.close()

    for gname in glist:
        if gname.startswith('._'):
            gname = gname[2:]
        raw_file = os.path.join(graphdir, gname)
        print(gname)

        if len(gname.split('#')) == 2:
            appName = gname.split('#')[1].split('-graph.json')[0]
            repoName = appName
            groupName = gname.split('#')[0]
            commitId = '0'
        elif len(gname.split('#')) == 3:
            appName = gname.split('#')[1]
            repoName = appName
            groupName = gname.split('#')[0]
            commitId = gname.split('#')[2].split('.graph.json')[0]
        elif len(gname.split('___')) == 3:
            parts = gname.split('___')
            appName = parts[0]
            repoName = parts[1].split('__')[1]
            groupName = parts[1].split('__')[0]
            commitId = parts[2].split('.')[0]
        else:
            print(f"{gname} can't be renamed")
            continue
        tmp1 = f"{appName}___{repoName}___{groupName}___{commitId}.{suffix}"
        tmp2 = f"{appName}___{repoName}___{groupName}___{commitId}.json"
        print(tmp1)

        save_file = os.path.join(savedir, tmp1)
        rec_file = os.path.join(recdir, tmp2)

        if not os.path.exists(raw_file):
            continue
        if os.path.exists(save_file) and os.path.exists(rec_file):
            continue
        logger.info(f'Start {gname} transforming...')
        try:
            with open(raw_file, 'r') as f1:
                content = f1.read()
                data = json.loads(content)

                if len(data['nodes']) > node_limit:
                    continue
                embeddings, adj_matrix, sentence_dict = graph2embedding(data, model, tokenizor, gname, language, save_adj)
            f1.close()

            if suffix == 'json':
                if save_adj:
                    data_dict = {
                        "embeddings": embeddings.tolist(),
                        "adj_matrix": adj_matrix.tolist()
                    }
                else:
                    data_dict = {
                        "embeddings": embeddings.tolist(),
                    }

                with open(save_file, 'w') as f:
                    json.dump(data_dict, f)
                f.close()

            elif suffix == 'pt':
                if save_adj:
                    data_dict = {
                        "embeddings": embeddings.detach(),
                        "adj_matrix": adj_matrix.detach()
                    }
                else:
                    data_dict = {
                        "embeddings": embeddings.detach(),
                    }
                torch.save(data_dict, save_file)

            if save_rec:
                rec_dict = {
                    "text": list(sentence_dict.values())
                }

                with open(rec_file, 'w') as f:
                    json.dump(rec_dict, f)
                f.close()
        except json.JSONDecodeError as e:
            print('Json Decode Error: '+ gname)

def preprocess(graphdir, savedir, recdir, jsondir, language = 'java', mode = 'pretrain', filenum = 1, suffix = 'pt', node_limit = 20000, save_adj = True, save_rec = True):

    model1_path = "salesforce/codet5p-110m-embedding"
    tokenizer1 = AutoTokenizer.from_pretrained(model1_path, trust_remote_code=True, device = device)
    model1 = AutoModel.from_pretrained(model1_path, trust_remote_code=True, torch_dtype="auto").to(device).eval()

    if mode == 'pretrain':
        preprocess_graph(
            graphdir=graphdir, 
            savedir=savedir, 
            recdir=recdir, 
            jsondir=jsondir, 
            language=language,
            model=model1, 
            tokenizor=tokenizer1, 
            filenum=filenum, 
            suffix=suffix, 
            node_limit=node_limit,
            save_adj=save_adj,
            save_rec=save_rec)
    else:
        raise NotImplementedError

def json_split(loaddirs, savedir, split_num=64):
    if not os.path.exists(savedir):
        os.makedirs(savedir)

    file_list = []
    for loaddir in loaddirs:
        file_list += os.listdir(loaddir)
    total_num = len(file_list)
    sep_num = total_num // split_num
    print(f'total num: {total_num}, sep num: {sep_num}')

    for i in range(split_num):
        start = i * sep_num
        end = start + sep_num if i != split_num - 1 else total_num
        with open(f'{savedir}/{i+1}.json', 'w') as f:
            json.dump(file_list[start:end], f)
        f.close()

def json_split_from_json(input_json, savedir, split_num=64):
    with open(input_json, 'r') as file:
        data = json.load(file)

    total_items = len(data)
    num_files = (total_items + split_num - 1) // split_num  # 向上取整

    if not os.path.exists(savedir):
        os.makedirs(savedir)

    for i in range(num_files):
        start = i * split_num
        end = min(start + split_num, total_items)
        split_data = data[start:end]

        save_file = os.path.join(savedir, f"{i+1}.json")

        with open(save_file, 'w') as file:
            json.dump(split_data, file, indent=4)

def detect_pt_file_errors(directory, output_json):
    error_files = []

    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.pt'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'rb') as f:
                        tmp = torch.load(f)
                        del tmp
                    gc.collect()
                except Exception as e:
                    error_files.append(file_path)
                    print(f"Error loading {file_path}: {e}")

    with open(output_json, 'w') as f:
        json.dump(error_files, f, indent=4)

    print(f"Detected {len(error_files)} error files. Details saved in {output_json}")

def transfer_pt_file_errors(input_json, output_json):
    with open(input_json, 'r') as file:
        data = json.load(file)

    def transform_path(path):
        repo_str = path.split('/')[-1]
        appName = repo_str.split('___')[0]
        groupName = repo_str.split('___')[2]
        new_repo_str = f"{groupName}#{appName}-graph.json"
        return new_repo_str

    transformed_data = [transform_path(path) for path in data]

    with open(output_json, 'w') as file:
        json.dump(transformed_data, file, indent=4)

def get_list(graph_dirs):
    all_files = [os.path.join(graph_dir, file) 
                 for graph_dir in graph_dirs 
                 for file in os.listdir(graph_dir)]
    return all_files

def get_list_constrained(graph_dirs, size_limit = 500 * 1024 * 1024):  
    filtered_files = []
    for graph_dir in graph_dirs:
        glist = os.listdir(graph_dir)
        for file_name in glist:
            file_path = os.path.join(graph_dir, file_name)
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                if file_size < size_limit:
                    filtered_files.append(file_path)

    return filtered_files

def get_graph_path(glist, filename, suffix):
    sp = filename.split('___')
    if len(sp) == 4:
        appName = sp[0]
        repoName = sp[1]
        groupName = sp[2]
        commitId = sp[3].split('.')[0]

        matched_graphs = []
        for graph in glist:
            graph_parts = graph.split('/')[-1].split('___')
            if len(graph_parts) == 4:
                graph_appName = graph_parts[0]
                graph_repoName = graph_parts[1]
                graph_groupName = graph_parts[2]
                graph_commitId = graph_parts[3].split('.')[0]

                if graph_appName == appName:
                    matched_graphs.append((graph, graph_repoName, graph_groupName, graph_commitId))

        if not matched_graphs:
            return None

        if not commitId == '0':
            for graph, graph_repoName, graph_groupName, graph_commitId in matched_graphs:
                if commitId == graph_commitId:
                    return graph

        best_match = None
        best_match_score = -2
        for graph, graph_repoName, graph_groupName, _ in matched_graphs:
            score = (repoName == graph_repoName) + (groupName == graph_groupName)
            if score > best_match_score:
                best_match_score = score
                best_match = graph
        
        return best_match
    else:
        raise ValueError(f"{filename} to graph not supported")

def split_jsonl_dataset(input_file, train_file, test_file, train_ratio=0.98):
    def read_jsonl(file_path):
        with open(file_path, 'r') as file:
            for line in file:
                yield json.loads(line)

    data = list(read_jsonl(input_file))
    repo_dict = defaultdict(list)

    for item in data:
        repo_dict[item['repo']].append(item)

    repos = list(repo_dict.keys())
    random.shuffle(repos)

    split_index = int(len(repos) * train_ratio)
    train_repos = repos[:split_index]
    test_repos = repos[split_index:]
    
    train_data = []
    test_data = []

    for repo in train_repos:
        train_data.extend(repo_dict[repo])
    for repo in test_repos:
        test_data.extend(repo_dict[repo])

    with open(train_file, 'w') as file:
        for item in train_data:
            file.write(json.dumps(item) + '\n')
    
    with open(test_file, 'w') as file:
        for item in test_data:
            file.write(json.dumps(item) + '\n')





================================================
FILE: cgm/inference/layer.py
================================================
"""Attention layer."""
from typing import Any, Dict, List, Optional

import torch
import torch.nn as nn

from vllm.attention import AttentionMetadata, AttentionType
from vllm.attention.selector import get_attn_backend
from vllm.config import CacheConfig
from vllm.model_executor.layers.quantization.base_config import (
    QuantizationConfig)
from vllm.model_executor.layers.quantization.kv_cache import BaseKVCacheMethod


class Attention(nn.Module):
    """Attention layer.

    This class takes query, key, and value tensors as input. The input tensors
    can either contain prompt tokens or generation tokens.
    The class does the following:

    1. Store the input key and value tensors in the KV cache.
    2. Perform (multi-head/multi-query/grouped-query) attention.
    3. Return the output tensor.
    """

    def __init__(
        self,
        num_heads: int,
        head_size: int,
        scale: float,
        num_kv_heads: Optional[int] = None,
        alibi_slopes: Optional[List[float]] = None,
        cache_config: Optional[CacheConfig] = None,
        quant_config: Optional[QuantizationConfig] = None,
        blocksparse_params: Optional[Dict[str, Any]] = None,
        logits_soft_cap: Optional[float] = None,
        prefix: str = "",
    ) -> None:
        super().__init__()
        if cache_config is not None:
            kv_cache_dtype = cache_config.cache_dtype
            block_size = cache_config.block_size
            sliding_window = cache_config.sliding_window
            is_attention_free = cache_config.is_attention_free
        else:
            kv_cache_dtype = "auto"
            block_size = 16
            sliding_window = None
            is_attention_free = False
        if num_kv_heads is None:
            num_kv_heads = num_heads

        # The default k/v_scale is set to 1.0. This is ignored
        # when kv-cache is not fp8, and should be used with
        # kv-cache in fp8_e5m2. For kv-cache in fp8_e4m3, we
        # expect the pre-quantized k/v_scale to be loaded along
        # with the model weights.
        self.kv_cache_dtype = kv_cache_dtype
        self._k_scale = 1.0
        self._v_scale = 1.0
        quant_method = quant_config.get_quant_method(
            self, prefix=prefix) if quant_config else None
        if quant_method is not None:
            assert isinstance(quant_method, BaseKVCacheMethod)
            # TODO (mgoin): kv cache dtype should be specified in the FP8
            # checkpoint config and become the "auto" behavior
            if self.kv_cache_dtype == "fp8_e5m2":
                raise ValueError("fp8_e5m2 kv-cache is not supported with "
                                 "fp8 checkpoints.")
            # If quantization is enabled, we make "k_scale" and "v_scale"
            # parameters so that it can be loaded from the model checkpoint.
            # The k/v_scale will then be converted back to native float32
            # values after weight loading.
            self.quant_method = quant_method
            self.quant_method.create_weights(self)

        # During model initialization, the default dtype is set as the model
        # weight and activation dtype.
        dtype = torch.get_default_dtype()
        attn_backend = get_attn_backend(head_size, dtype, kv_cache_dtype,
                                        block_size, is_attention_free,
                                        blocksparse_params is not None)
        impl_cls = attn_backend.get_impl_cls()
        self.impl = impl_cls(num_heads, head_size, scale, num_kv_heads,
                             alibi_slopes, sliding_window, kv_cache_dtype,
                             blocksparse_params, logits_soft_cap)

    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        kv_cache: torch.Tensor,
        attn_metadata: AttentionMetadata,
        attn_type: AttentionType = AttentionType.DECODER,
    ) -> torch.Tensor:

        return self.impl.forward(query,
                                 key,
                                 value,
                                 kv_cache,
                                 attn_metadata,
                                 self._k_scale,
                                 self._v_scale,
                                 attn_type=attn_type)

    def extra_repr(self) -> str:
        s = f"head_size={self.impl.head_size}"  # type: ignore
        s += f", num_heads={self.impl.num_heads}"  # type: ignore
        s += f", num_kv_heads={self.impl.num_kv_heads}"  # type: ignore
        s += f", scale={self.impl.scale}"  # type: ignore
        s += f", backend={self.impl.__class__.__name__}"
        return s


================================================
FILE: cgm/inference/vllm.py
================================================
# coding=utf-8
# Adapted from
# https://github.com/vllm-project/vllm/blob/main/vllm/model_executor/models/qwen2.py
# Copyright 2024 The Codefuse team.
# Copyright 2024 The Qwen team.
# Copyright 2023 The vLLM team.
# Copyright 2022 EleutherAI and the HuggingFace Inc. team. All rights reserved.
#
# This code is based on EleutherAI's GPT-NeoX library and the GPT-NeoX
# and OPT implementations in this library. It has been modified from its
# original forms to accommodate minor architectural differences compared
# to GPT-NeoX and OPT used by the Meta AI team that trained the model.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Inference-only Qwen2 model compatible with HuggingFace weights."""
from typing import Iterable, List, Optional, Tuple

import time
import torch
from torch import nn
from transformers import Qwen2Config

from vllm.attention import AttentionMetadata
from vllm.config import CacheConfig, LoRAConfig
from vllm.distributed import get_tensor_model_parallel_world_size
from vllm.model_executor.layers.activation import SiluAndMul
from vllm.model_executor.layers.layernorm import RMSNorm
from vllm.model_executor.layers.linear import (MergedColumnParallelLinear,
                                               QKVParallelLinear,
                                               RowParallelLinear)
from vllm.model_executor.layers.logits_processor import LogitsProcessor
from vllm.model_executor.layers.quantization.base_config import (
    QuantizationConfig)
from vllm.model_executor.layers.rotary_embedding import get_rope
from vllm.model_executor.layers.sampler import Sampler
from vllm.model_executor.layers.vocab_parallel_embedding import (
    ParallelLMHead, VocabParallelEmbedding)
from vllm.model_executor.model_loader.weight_utils import default_weight_loader
from vllm.model_executor.sampling_metadata import SamplingMetadata
from vllm.sequence import SamplerOutput
from vllm.multimodal import MULTIMODAL_REGISTRY
from vllm.multimodal.image import get_dummy_image_data

from vllm.attention.backends.xformers import XFormersBackend


class Attention(nn.Module):
    """Attention layer.

    This class takes query, key, and value tensors as input. The input tensors
    can either contain prompt tokens or generation tokens.
    The class does the following:

    1. Store the input key and value tensors in the KV cache.
    2. Perform (multi-head/multi-query/grouped-query) attention.
    3. Return the output tensor.
    """

    def __init__(
        self,
        num_heads: int,
        head_size: int,
        scale: float,
        num_kv_heads: Optional[int] = None,
        alibi_slopes: Optional[List[float]] = None,
        cache_config: Optional[CacheConfig] = None,
        quant_config: Optional[QuantizationConfig] = None,
        blocksparse_params: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__()
        if cache_config is not None:
            kv_cache_dtype = cache_config.cache_dtype
            block_size = cache_config.block_size
            sliding_window = cache_config.sliding_window
        else:
            kv_cache_dtype = "auto"
            block_size = 16
            sliding_window = None
        if num_kv_heads is None:
            num_kv_heads = num_heads

        # The default kv_scale is set to 1.0. This is ignored
        # when kv-cache is not fp8, and should be used with
        # kv-cache in fp8_e5m2. For kv-cache in fp8_e4m3, we
        # expect the pre-quantized kv_scale to be loaded along
        # with the model weights.
        self.kv_cache_dtype = kv_cache_dtype
        self._kv_scale = 1.0
        quant_method = quant_config.get_quant_method(
            self) if quant_config else None
        if quant_method is not None:
            if self.kv_cache_dtype == "fp8_e5m2":
                raise ValueError("fp8_e5m2 kv-cache is not supported with "
                                 "fp8 checkpoints.")
            # When FP8 quantization is enabled, we make a parameter
            # "kv_scale" so that it can be loaded from FP8 checkpoint.
            # The kv_scale will then be converted back
            # to self._kv_scale in a native float32 value after weight loading.
            self.quant_method = quant_method
            self.quant_method.create_weights(self)

        # During model initialization, the default dtype is set as the model
        # weight and activation dtype.
        dtype = torch.get_default_dtype()
        attn_backend = XFormersBackend
        # attn_backend = get_attn_backend(num_heads, head_size, num_kv_heads,
        #                                 sliding_window, dtype, kv_cache_dtype,
        #                                 block_size, blocksparse_params
        #                                 is not None)
        impl_cls = attn_backend.get_impl_cls()
        self.impl = impl_cls(num_heads, head_size, scale, num_kv_heads,
                             alibi_slopes, sliding_window, kv_cache_dtype,
                             blocksparse_params)

    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        kv_cache: Optional[torch.Tensor],
        attn_metadata: AttentionMetadata,
    ) -> torch.Tensor:
        return self.impl.forward(query, key, value, kv_cache, attn_metadata,
                                 self._kv_scale)

    def extra_repr(self) -> str:
        s = f"head_size={self.impl.head_size}"  # type: ignore
        s += f", num_heads={self.impl.num_heads}"  # type: ignore
        s += f", num_kv_heads={self.impl.num_kv_heads}"  # type: ignore
        s += f", scale={self.impl.scale}"  # type: ignore
        s += f", backend={self.impl.__class__.__name__}"
        return s



class Qwen2MLP(nn.Module):

    def __init__(
            self,
            hidden_size: int,
            intermediate_size: int,
            hidden_act: str,
            quant_config: Optional[QuantizationConfig] = None,
    ) -> None:
        super().__init__()
        self.gate_up_proj = MergedColumnParallelLinear(
            hidden_size, [intermediate_size] * 2,
            bias=False,
            quant_config=quant_config)
        self.down_proj = RowParallelLinear(intermediate_size,
                                           hidden_size,
                                           bias=False,
                                           quant_config=quant_config)
        if hidden_act != "silu":
            raise ValueError(f"Unsupported activation: {hidden_act}. "
                             "Only silu is supported for now.")
        self.act_fn = SiluAndMul()

    def forward(self, x):
        gate_up, _ = self.gate_up_proj(x)
        x = self.act_fn(gate_up)
        x, _ = self.down_proj(x)
        return x


class Qwen2Attention(nn.Module):

    def __init__(self,
                 hidden_size: int,
                 num_heads: int,
                 num_kv_heads: int,
                 max_position: int = 4096 * 32,
                 rope_theta: float = 10000,
                 cache_config: Optional[CacheConfig] = None,
                 quant_config: Optional[QuantizationConfig] = None,
                 rope_scaling: Optional[Tuple] = None) -> None:
        super().__init__()
        self.hidden_size = hidden_size
        tp_size = get_tensor_model_parallel_world_size()
        self.total_num_heads = num_heads
        assert self.total_num_heads % tp_size == 0
        self.num_heads = self.total_num_heads // tp_size
        self.total_num_kv_heads = num_kv_heads
        if self.total_num_kv_heads >= tp_size:
            # Number of KV heads is greater than TP size, so we partition
            # the KV heads across multiple tensor parallel GPUs.
            assert self.total_num_kv_heads % tp_size == 0
        else:
            # Number of KV heads is less than TP size, so we replicate
            # the KV heads across multiple tensor parallel GPUs.
            assert tp_size % self.total_num_kv_heads == 0
        self.num_kv_heads = max(1, self.total_num_kv_heads // tp_size)
        self.head_dim = hidden_size // self.total_num_heads
        self.q_size = self.num_heads * self.head_dim
        self.kv_size = self.num_kv_heads * self.head_dim
        self.scaling = self.head_dim ** -0.5
        self.rope_theta = rope_theta

        self.qkv_proj = QKVParallelLinear(
            hidden_size,
            self.head_dim,
            self.total_num_heads,
            self.total_num_kv_heads,
            bias=True,
            quant_config=quant_config,
        )
        self.o_proj = RowParallelLinear(
            self.total_num_heads * self.head_dim,
            hidden_size,
            bias=False,
            quant_config=quant_config,
        )

        self.rotary_emb = get_rope(
            self.head_dim,
            rotary_dim=self.head_dim,
            max_position=max_position,
            base=self.rope_theta,
            rope_scaling=rope_scaling,
        )
        self.attn = Attention(self.num_heads,
                              self.head_dim,
                              self.scaling,
                              num_kv_heads=self.num_kv_heads,
                              cache_config=cache_config,
                              quant_config=quant_config)

    def forward(
            self,
            positions: torch.Tensor,
            hidden_states: torch.Tensor,
            kv_cache: torch.Tensor,
            attn_metadata: AttentionMetadata,
    ) -> torch.Tensor:
        qkv, _ = self.qkv_proj(hidden_states)
        q, k, v = qkv.split([self.q_size, self.kv_size, self.kv_size], dim=-1)
        q, k = self.rotary_emb(positions, q, k)
        attn_output = self.attn(q, k, v, kv_cache, attn_metadata)
        output, _ = self.o_proj(attn_output)
        return output


class Qwen2DecoderLayer(nn.Module):

    def __init__(
            self,
            config: Qwen2Config,
            cache_config: Optional[CacheConfig] = None,
            quant_config: Optional[QuantizationConfig] = None,
    ) -> None:
        super().__init__()
        self.hidden_size = config.hidden_size
        # Requires transformers > 4.32.0
        rope_theta = getattr(config, "rope_theta", 1000000)
        rope_scaling = getattr(config, "rope_scaling", None)
        self.self_attn = Qwen2Attention(
            hidden_size=self.hidden_size,
            num_heads=config.num_attention_heads,
            max_position=config.max_position_embeddings,
            num_kv_heads=config.num_key_value_heads,
            rope_theta=rope_theta,
            cache_config=cache_config,
            quant_config=quant_config,
            rope_scaling=rope_scaling)
        self.mlp = Qwen2MLP(
            hidden_size=self.hidden_size,
            intermediate_size=config.intermediate_size,
            hidden_act=config.hidden_act,
            quant_config=quant_config,
        )
        self.input_layernorm = RMSNorm(config.hidden_size,
                                       eps=config.rms_norm_eps)
        self.post_attention_layernorm = RMSNorm(config.hidden_size,
                                                eps=config.rms_norm_eps)

    def forward(
            self,
            positions: torch.Tensor,
            hidden_states: torch.Tensor,
            kv_cache: torch.Tensor,
            attn_metadata: AttentionMetadata,
            residual: Optional[torch.Tensor],
            attention_mask: Optional[torch.Tensor],
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        # Self Attention
        if residual is None:
            residual = hidden_states
            hidden_states = self.input_layernorm(hidden_states)
        else:
            hidden_states, residual = self.input_layernorm(
                hidden_states, residual)
        hidden_states = self.self_attn(
            positions=positions,
            hidden_states=hidden_states,
            kv_cache=kv_cache,
            attn_metadata=attn_metadata,
        )

        # Fully Connected
        hidden_states, residual = self.post_attention_layernorm(
            hidden_states, residual)
        hidden_states = self.mlp(hidden_states)
        return hidden_states, residual


class CGMQwen2Model(nn.Module):

    def __init__(
            self,
            config: Qwen2Config,
            cache_config: Optional[CacheConfig] = None,
            quant_config: Optional[QuantizationConfig] = None,
    ) -> None:
        super().__init__()
        self.config = config
        self.padding_idx = config.pad_token_id
        self.vocab_size = config.vocab_size

        self.embed_tokens = VocabParallelEmbedding(
            config.vocab_size,
            config.hidden_size,
        )
        self.layers = nn.ModuleList([
            Qwen2DecoderLayer(config, cache_config, quant_config)
            for _ in range(config.num_hidden_layers)
        ])
        self.norm = RMSNorm(config.hidden_size, eps=config.rms_norm_eps)

    def forward(
            self,
            input_ids: torch.Tensor,
            positions: torch.Tensor,
            kv_caches: List[torch.Tensor],
            attn_metadata: AttentionMetadata,
            inputs_embeds: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        if inputs_embeds is None:
            inputs_embeds = self.embed_tokens(input_ids)

        hidden_states = inputs_embeds
        residual = None
        for i in range(len(self.layers)):
            layer = self.layers[i]
            hidden_states, residual = layer(
                positions,
                hidden_states,
                kv_caches[i],
                attn_metadata,
                residual,
            )
        hidden_states, _ = self.norm(hidden_states, residual)
        return hidden_states


class PerceiverResamplerLayer(nn.Module):
    def __init__(self, args, layer_idx):
        super(PerceiverResamplerLayer, self).__init__()
        self.attn = nn.MultiheadAttention(
            embed_dim=args['lm_hidden_size'],
            num_heads=args['num_heads'],
            kdim=args['graph_embedding_dim'],
            vdim=args['graph_embedding_dim'],
            batch_first=True
        )
        self.layer_idx = layer_idx

    def forward(self, graph_embedding, queries):
        features = graph_embedding.to(queries.dtype).unsqueeze(0)
        hidden = self.attn(queries, features, features, need_weights=False)[0]
        return hidden


class PerceiverResampler(nn.Module):
    def __init__(self, args):
        super(PerceiverResampler, self).__init__()
        self.num_layers = args.adapter_num_layers
        self.layers = nn.ModuleList([PerceiverResamplerLayer(args, i) for i in range(self.num_layers)])

    def forward(self, graph_embedding, queries):
        for layer in self.layers:
            queries = layer(graph_embedding, queries)
        return queries


class Adapter(nn.Module):
    """Define the Adapter part of Code Graph Model (CGM).
    """
    def __init__(self, args):
        super(Adapter, self).__init__()
        self.args = args

        self.q = nn.Parameter(torch.randn(args['graph_token_num'], args['lm_hidden_size']))
        self.attn = nn.MultiheadAttention(
            embed_dim=args['lm_hidden_size'],
            num_heads=args['num_heads'],
            kdim=args['graph_embedding_dim'],
            vdim=None,
            batch_first=True
        )

    def forward(self, features):
        """Forward.
        Args:
            features: torch.Tensor
        """
        # print(f'LBC - Adapter features type: {features.dtype}, shape: {features.shape}')
        features_2d = features.to(self.q.dtype)
        queries = self.q
        embeddings = self.attn(queries, features_2d, features_2d, need_weights=False)[0]
        return embeddings


class Adapter_v2(nn.Module):
    def __init__(self, args):
        super(Adapter_v2, self).__init__()
        # self.fc1 = nn.Linear(args.embedding_dim, args.adapter_hidden_dim)
        self.fc1 = RowParallelLinear(args.embedding_dim, args.adapter_hidden_dim)
        self.gelu = nn.GELU()
        # self.fc2 = nn.Linear(args.adapter_hidden_dim, args.lm_hidden_dim)
        self.fc2 = RowParallelLinear(args.adapter_hidden_dim, args.lm_hidden_dim)

    def forward(self, x):
        return self.fc2(self.gelu(self.fc1(x)))

@MULTIMODAL_REGISTRY.register_image_feature_input()
@MULTIMODAL_REGISTRY.register_image_pixel_input()
@MULTIMODAL_REGISTRY.register_dummy_data(get_dummy_image_data)
class CGMQwen2ForCausalLM(nn.Module):
    packed_modules_mapping = {
        "qkv_proj": [
            "q_proj",
            "k_proj",
            "v_proj",
        ],
        "gate_up_proj": [
            "gate_proj",
            "up_proj",
        ],
    }

    # LoRA specific attributes
    supported_lora_modules = [
        "qkv_proj",
        "o_proj",
        "gate_up_proj",
        "down_proj",
    ]
    embedding_modules = {}
    embedding_padding_modules = []

    def __init__(
            self,
            config: Qwen2Config,
            cache_config: Optional[CacheConfig] = None,
            quant_config: Optional[QuantizationConfig] = None,
            lora_config: Optional[LoRAConfig] = None,
    ) -> None:
        # print(f'LBC - config in CGMQwen2: {vars(config)}')
        del lora_config
        # TODO (@robertgshaw2): see if this can be moved out
        if (cache_config.sliding_window is not None
                and hasattr(config, "max_window_layers")):
            raise ValueError("Sliding window for some but all layers is not "
                             "supported. This model uses sliding window "
                             "but `max_window_layers` = %s is less than "
                             "`num_hidden_layers` = %s. Please open an issue "
                             "to discuss this feature." % (
                                 config.max_window_layers,
                                 config.num_hidden_layers,
                             ))

        super().__init__()
        self.config = config
        self.quant_config = quant_config
        self.model = CGMQwen2Model(config, cache_config, quant_config)

        if config.tie_word_embeddings:
            self.lm_head_weight = self.model.embed_tokens.weight
        else:
            self.lm_head = ParallelLMHead(config.vocab_size,
                                          config.hidden_size)
            self.lm_head_weight = self.lm_head.weight

        self.logits_processor = LogitsProcessor(config.vocab_size)
        self.sampler = Sampler()

        adapter_args = None
        if 'adapter_config' in vars(config):
            adapter_args = vars(config)['adapter_config']
            adapter_args['lm_hidden_size'] = config.hidden_size
            # print(f'LBC - adatper args: {adapter_args}')
            start_time = time.time()
            self.adapter = Adapter_v2(adapter_args)
            self.adapter.load_state_dict(torch.load(f"{config._name_or_path}/adapter.pth"))
            end_time = time.time()
            # print(f'Loading adapter model takes {end_time - start_time} seconds')
        else:
            self.adapter = None

        self.adapter_args = adapter_args

    def _parse_and_validate_graph_input(self, **kwargs: object) -> torch.Tensor:
        image_features = kwargs.pop("image_features", None)

        return image_features

    # def forward(
    #         self,
    #         input_ids: torch.Tensor,
    #         positions: torch.Tensor,
    #         kv_caches: List[torch.Tensor],
    #         attn_metadata: AttentionMetadata,
    #         node_repre: Optional[torch.Tensor] = None,
    #         **kwargs: object,
    # ) -> torch.Tensor:
    #     node_repre = self._parse_and_validate_graph_input(**kwargs)
    # 
    #     # if self.args.peft == "LoRA":
    #     #     inputs_embeds = self.lm.model.model.embed_tokens(x)
    #     # else:
    #     #     inputs_embeds = self.lm.model.embed_tokens(x)
    #     # embeddings = self.adapter(node_repre, inputs_embeds)
    #     # lm_embeds = torch.cat((embeddings, inputs_embeds), dim=1)
    #     #
    #     # outputs = self.lm(inputs_embeds=lm_embeds,
    #     #                   return_dict=True)
    # 
    #     # Merge Adapter.forward with LLM forward
    #     if self.adapter and node_repre is not None:
    #         inputs_embeds = self.model.embed_tokens(input_ids)
    #         # print(f'LBC - node_repre: {node_repre[0][:100]}')
    #         embeddings = self.adapter(node_repre[0], inputs_embeds)
    #         inputs_embeds = torch.cat((embeddings, inputs_embeds), dim=1)
    # 
    #         input_ids = None
    # 
    #     else:
    #         inputs_embeds = None
    # 
    #     hidden_states = self.model(
    #         input_ids=input_ids,
    #         positions=positions,
    #         kv_caches=kv_caches,
    #         attn_metadata=attn_metadata,
    #         inputs_embeds=inputs_embeds
    #     )
    # 
    #     return hidden_states

    def forward(
            self,
            input_ids: torch.Tensor,
            positions: torch.Tensor,
            kv_caches: List[torch.Tensor],
            attn_metadata: AttentionMetadata,
            node_repre: Optional[torch.Tensor] = None,
            adj_matrix: Optional[torch.Tensor] = None,
            **kwargs: object,
    ) -> torch.Tensor:
        node_repre = self._parse_and_validate_graph_input(**kwargs)
        
        # Merge Adapter.forward with LLM forward
        if self.adapter and node_repre is not None:
            inputs_embeds = self.model.embed_tokens(input_ids)
            # print(f'LBC - node_repre: {node_repre[0][:100]}')
            embeddings = self.adapter(node_repre[0])
            inputs_embeds = torch.cat((embeddings, inputs_embeds), dim=-2)

            if adj_matrix is not None:
                batch_size, seq_len_x, _ = adj_matrix.shape
                seq_len_q = input_ids.size(1)
            
                qa_matrix = torch.ones(batch_size, seq_len_q, seq_len_q, device=inputs_embeds.device)
                matrix_xq = torch.ones(batch_size, seq_len_x, seq_len_q, device=inputs_embeds.device)
                matrix_qx = torch.ones(batch_size, seq_len_q, seq_len_x, device=inputs_embeds.device)
            
                attention_mask = torch.cat([
                    torch.cat([adj_matrix, matrix_xq], dim=2),
                    torch.cat([matrix_qx, qa_matrix], dim=2)
                ], dim=1).unsqueeze(1)
            else:
                attention_mask = None

            input_ids = None

        else:
            inputs_embeds = None

        if attention_mask:
            attn_metadata.attn_bias = attention_mask

        hidden_states = self.model(
            input_ids=input_ids,
            positions=positions,
            kv_caches=kv_caches,
            attn_metadata=attn_metadata,
            inputs_embeds=inputs_embeds
        )

        return hidden_states

    def compute_logits(self, hidden_states: torch.Tensor,
                       sampling_metadata: SamplingMetadata) -> torch.Tensor:
        logits = self.logits_processor(self.lm_head_weight, hidden_states,
                                       sampling_metadata)
        return logits

    def sample(
            self,
            logits: torch.Tensor,
            sampling_metadata: SamplingMetadata,
    ) -> Optional[SamplerOutput]:
        next_tokens = self.sampler(logits, sampling_metadata)
        return next_tokens

    def load_weights(self, weights: Iterable[Tuple[str, torch.Tensor]]):
        # Only LLM but adpter is loaded now.
        stacked_params_mapping = [
            # (param_name, shard_name, shard_id)
            ("qkv_proj", "q_proj", "q"),
            ("qkv_proj", "k_proj", "k"),
            ("qkv_proj", "v_proj", "v"),
            ("gate_up_proj", "gate_proj", 0),
            ("gate_up_proj", "up_proj", 1),
        ]
        params_dict = dict(self.named_parameters(remove_duplicate=False))
        for name, loaded_weight in weights:
            if "rotary_emb.inv_freq" in name:
                continue
            if self.config.tie_word_embeddings and "lm_head.weight" in name:
                continue
            for (param_name, weight_name, shard_id) in stacked_params_mapping:
                if weight_name not in name:
                    continue
                name = name.replace(weight_name, param_name)
                # Skip loading extra bias for GPTQ models.
                if name.endswith(".bias") and name not in params_dict:
                    continue
                param = params_dict[name]
                weight_loader = param.weight_loader
                weight_loader(param, loaded_weight, shard_id)
                break
            else:
                # Skip loading extra bias for GPTQ models.
                if name.endswith(".bias") and name not in params_dict:
                    continue
                param = params_dict[name]
                weight_loader = getattr(param, "weight_loader",
                                        default_weight_loader)
                weight_loader(param, loaded_weight)



================================================
FILE: cgm/launch/zero2.sh
================================================
accelerate launch \
  --num_machines $N_NODE \
  --num_processes $(($N_NODE*$N_GPU_PER_NODE)) \
  --use_deepspeed \
  --deepspeed_multinode_launcher 'standard' \
  --zero_stage 2 \
  --offload_optimizer_device 'none' \
  --offload_param_device 'none' \
  --gradient_accumulation_steps 32 \
  --gradient_clipping 1.0 \
  --zero3_init_flag false \
  --zero3_save_16bit_model false \
  --main_training_function 'main' \
  --mixed_precision 'bf16' \
  --dynamo_backend 'no' \
  --same_network \
  --machine_rank $RANK \
  --main_process_ip $MASTER_ADDR \
  --main_process_port $MASTER_PORT \
  --rdzv_backend 'static' \
  train/train.py --c config/$TRAIN_CONFIG 




================================================
FILE: cgm/launch/zero3.sh
================================================
accelerate launch \
  --num_machines $N_NODE \
  --num_processes $(($N_NODE*$N_GPU_PER_NODE)) \
  --use_deepspeed \
  --deepspeed_multinode_launcher 'standard' \
  --zero_stage 3 \
  --offload_optimizer_device 'none' \
  --offload_param_device 'none' \
  --gradient_accumulation_steps 32 \
  --gradient_clipping 1.0 \
  --zero3_init_flag true \
  --zero3_save_16bit_model true \
  --main_training_function 'main' \
  --mixed_precision 'bf16' \
  --dynamo_backend 'no' \
  --same_network \
  --machine_rank $RANK \
  --main_process_ip $MASTER_ADDR \
  --main_process_port $MASTER_PORT \
  --rdzv_backend 'static' \
  train/train.py --c config/$TRAIN_CONFIG



================================================
FILE: cgm/modeling/cgm.py
================================================
import torch
import torch.nn as nn

from data.preprocess import getJavaSentence, getPythonSentence, getSentence
from transformers import AutoModelForCausalLM, AutoModel, AutoTokenizer, BitsAndBytesConfig
from utils.common_utils import count_parameters, print_rank_0
import torch.nn.functional as F

from models.qwen2._4_46_1.modeling_qwen2 import Qwen2ForCausalLM
from models.qwen2._4_46_1.modeling_attn_mask_utils import AttentionMaskConverter

def graph2embedding(self, data, model, tokenizer, reponame, language, save_adj, peft, return_type=None):
    node_embeddings = {}
    node_id_to_index = {}
    index_counter = 0

    device = model.device

    for node in data['nodes']:
        nodeType = node['nodeType']

        if 'nodeId' in node.keys():
            node_id = node['nodeId']
        elif 'id' in node.keys():
            node_id = node['id']
        else:
            raise ValueError("No key named id/nodeId")

        sentence = getSentence(node, nodeType, reponame, 1024000)

        if sentence == "":
            node_embedding = torch.zeros((1, self.args.embedding_dim), dtype=torch.float32).to(device)
            node_embeddings[node_id] = [node_embedding]
            # sentence_dict[index_counter] = ""
            node_id_to_index[node_id] = [index_counter]
            index_counter += 1
        else:
            # 手动切词
            tokens = tokenizer.tokenize(sentence)
            num_tokens = len(tokens)
            num_segments = (num_tokens + 511) // 512  # Calculate number of segments
            embeddings = []
            # segments = []
            node_id_to_index[node_id] = list(range(index_counter, index_counter + num_segments))
            for i in range(num_segments):
                start = i * 512
                end = min((i + 1) * 512, num_tokens)
                segment_tokens = tokens[start:end]
                segment_ids = torch.tensor(tokenizer.convert_tokens_to_ids(segment_tokens), device=device).unsqueeze(0)

                if peft:
                    # return_type: ALL_256, ALL_768
                    segment_embedding = model.model(segment_ids, return_type=return_type)
                else:
                    segment_embedding = model(segment_ids)
                embeddings.append(segment_embedding)
                index_counter += 1

            node_embeddings[node_id] = embeddings

    num_nodes = index_counter

    # TODO: add sparse adj
    if save_adj:
        adj_matrix = torch.zeros((num_nodes, num_nodes)).to(device)

        for edge in data['edges']:
            source_id = edge['source']
            target_id = edge['target']
            source_indices = node_id_to_index.get(source_id)
            target_indices = node_id_to_index.get(target_id)
            if source_indices is None or target_indices is None:
                # if source_indices is None:
                #     print(f"{source_id} not exists")
                # if target_indices is None:
                #     print(f"{target_id} not exists")
                continue

            for source_index in source_indices:
                for target_index in target_indices:
                    adj_matrix[source_index, target_index] = 1

        # Connect embeddings of the same node
        for node_id, indices in node_id_to_index.items():
            for i in range(len(indices)):
                for j in range(i + 1, len(indices)):
                    adj_matrix[indices[i], indices[j]] = 1
                    adj_matrix[indices[j], indices[i]] = 1
    else:
        adj_matrix = None

    all_embeddings = []
    for value in node_embeddings.values():
        if isinstance(value, torch.Tensor):
            all_embeddings.append(value)
        elif isinstance(value, list):
            for tensor in value:
                all_embeddings.append(tensor)

    embeddings = torch.stack(all_embeddings, dim=0).squeeze(1)

    # embeddings = torch.stack(list(node_embeddings.values()))
    # embeddings = torch.stack(sum(node_embeddings.values(), []))
    # embeddings = torch.cat(list(node_embeddings.values()), dim=0)

    return embeddings, adj_matrix  # sentence_dict

class adapter(nn.Module):
    def __init__(self, args):
        super(adapter, self).__init__()
        self.fc1 = nn.Linear(args.embedding_dim, args.adapter_hidden_dim)
        self.gelu = nn.GELU()
        self.fc2 = nn.Linear(args.adapter_hidden_dim, args.lm_hidden_dim)

    def forward(self, x):
        return self.fc2(self.gelu(self.fc1(x)))

class CGM(nn.Module):
    def __init__(self, args):
        super(CGM, self).__init__()
        # text encoder
        self.encoder_tokenizer = AutoTokenizer.from_pretrained(args.pretrained_encoder_path, trust_remote_code=True)
        self.encoder = AutoModel.from_pretrained(
            args.pretrained_encoder_path,
            torch_dtype="auto",
            trust_remote_code=True
        )

        if args.self_defined:
            if args.quantization == "8bit":
                self.lm = Qwen2ForCausalLM.from_pretrained(
                    args.pretrained_model_path,
                    attn_implementation=args.attn_implementation,
                    torch_dtype="auto",
                    trust_remote_code=False,
                    quantization_config=(
                        BitsAndBytesConfig(
                            load_in_8bit=(args.quantization == "8bit"),
                            bnb_8bit_compute_dtype=torch.float8,
                            bnb_8bit_use_double_quant=True,
                            bnb_8bit_quant_type="fp8",
                            bnb_8bit_quant_storage=torch.float8,
                        )
                        if args.quantization == "8bit"
                        else None
                    ),
                )
            elif args.quantization == "4bit":
                self.lm = Qwen2ForCausalLM.from_pretrained(
                    args.pretrained_model_path,
                    attn_implementation=args.attn_implementation,
                    torch_dtype="auto",
                    trust_remote_code=False,
                    quantization_config=(
                        BitsAndBytesConfig(
                            load_in_4bit=(args.quantization == "4bit"),
                            bnb_4bit_compute_dtype=torch.bfloat16,
                            bnb_4bit_use_double_quant=True,
                            bnb_4bit_quant_type="nf4",
                            bnb_4bit_quant_storage=torch.bfloat16,
                        )
                        if args.quantization == "4bit"
                        else None
                    ),
                )
            elif not args.quantization:
                self.lm = Qwen2ForCausalLM.from_pretrained(
                    args.pretrained_model_path,
                    attn_implementation=args.attn_implementation,
                    torch_dtype="auto",
                    trust_remote_code=False,
                )
            else:
                raise NotImplementedError(f"unrecognized args.qunatization: {args.quantization}")
        else:
            self.lm = AutoModelForCausalLM.from_pretrained(
                args.pretrained_model_path,
                attn_implementation=args.attn_implementation,
                torch_dtype="auto",
                trust_remote_code=True,
                quantization_config=(
                    BitsAndBytesConfig(
                        load_in_4bit=(args.quantization == "4bit"),
                        bnb_4bit_compute_dtype=torch.bfloat16,
                        bnb_4bit_use_double_quant=True,
                        bnb_4bit_quant_type="nf4",
                        bnb_4bit_quant_storage=torch.bfloat16,
                    )
                    if args.quantization == "4bit"
                    else None
                ),
            )

        args.lm_hidden_dim = self.lm.config.hidden_size
        self.args = args
        self.adapter = adapter(args)
        if args.load_pretrained_adapter:
            self.adapter.load_state_dict(torch.load(args.pretrained_adapter_path))
            print_rank_0(f"Adapter loaded from {args.pretrained_adapter_path}")
        else:
            print_rank_0("Adapter initialized")
        print_rank_0(f"Parameters of Encoder: {count_parameters(self.encoder) / 1e6:.1f}M")
        print_rank_0(f"Parameters of Adapter: {count_parameters(self.adapter) / 1e6:.1f}M")
        print_rank_0(f"Parameters of LLM: {count_parameters(self.lm) / 1e9:.2f}B")

    def graph2embedding(self, data, reponame, return_type=None):
        node_embeddings = {}
        node_id_to_index = {}
        index_counter = 0

        model = self.encoder
        tokenizer = self.encoder_tokenizer
        save_adj = self.args.use_adj,
        peft = self.args.peft

        device = model.device

        for node in data['nodes']:
            nodeType = node['nodeType']

            if 'nodeId' in node.keys():
                node_id = node['nodeId']
            elif 'id' in node.keys():
                node_id = node['id']
            else:
                raise ValueError("No key named id/nodeId")

            sentence = getSentence(node, nodeType, reponame, 1024000)

            if sentence == "":
                node_embedding = torch.zeros((1, self.args.embedding_dim), dtype=torch.float32).to(device)
                node_embeddings[node_id] = [node_embedding]
                node_id_to_index[node_id] = [index_counter]
                index_counter += 1
            else:
                tokens = tokenizer.tokenize(sentence)
                num_tokens = len(tokens)
                num_segments = (num_tokens + 511) // 512  # Calculate number of segments
                embeddings = []
                node_id_to_index[node_id] = list(range(index_counter, index_counter + num_segments))
                for i in range(num_segments):
                    start = i * 512
                    end = min((i + 1) * 512, num_tokens)
                    segment_tokens = tokens[start:end]
                    segment_ids = torch.tensor(tokenizer.convert_tokens_to_ids(segment_tokens),
                                               device=device).unsqueeze(0)

                    if peft:
                        # return_type: ALL_256, ALL_768
                        segment_embedding = model.model(segment_ids, return_type=return_type)
                    else:
                        segment_embedding = model(segment_ids)
                    embeddings.append(segment_embedding)
                    index_counter += 1

                node_embeddings[node_id] = embeddings

        num_nodes = index_counter

        # TODO: add sparse adj
        if save_adj:
            adj_matrix = torch.zeros((num_nodes, num_nodes)).to(device)

            for edge in data['edges']:
                source_id = edge['source']
                target_id = edge['target']
                source_indices = node_id_to_index.get(source_id)
                target_indices = node_id_to_index.get(target_id)
                if source_indices is None or target_indices is None:
                    continue

                for source_index in source_indices:
                    for target_index in target_indices:
                        adj_matrix[source_index, target_index] = 1

            # Connect embeddings of the same node
            for node_id, indices in node_id_to_index.items():
                for i in range(len(indices)):
                    for j in range(i + 1, len(indices)):
                        adj_matrix[indices[i], indices[j]] = 1
                        adj_matrix[indices[j], indices[i]] = 1
        else:
            adj_matrix = None

        all_embeddings = []
        for value in node_embeddings.values():
            if isinstance(value, torch.Tensor):
                all_embeddings.append(value)
            elif isinstance(value, list):
                for tensor in value:
                    all_embeddings.append(tensor)

        embeddings = torch.stack(all_embeddings, dim=0).squeeze(1)

        return embeddings, adj_matrix  # sentence_dict

    def forward(self, graph, qa_ids, qa_mask):
        graph_embeddings, adj_matrix = graph2embedding(
            data=graph,
            reponame=graph['reponame'],
            return_type="ALL_256",
        )

        embeddings = self.adapter(graph_embeddings)

        if self.args.peft:
            inputs_embeds = self.lm.model.model.embed_tokens(qa_ids)
        else:
            inputs_embeds = self.lm.model.embed_tokens(qa_ids)

        input_embeddings = torch.cat((embeddings, inputs_embeds), dim=-2)
        input_embeddings = input_embeddings.unsqueeze(0)

        if adj_matrix is not None and self.args.use_adj:

            if len(adj_matrix.shape) == 2:
                adj_matrix = adj_matrix.unsqueeze(0)
            batch_size, seq_len_x, _ = adj_matrix.shape

            seq_len_q = inputs_embeds.size(-2)

            qa_matrix = torch.ones(batch_size, seq_len_q, seq_len_q, device=qa_mask.device)
            qa_matrix = torch.tril(qa_matrix)

            matrix_xq = qa_mask.unsqueeze(1) * torch.ones(batch_size, seq_len_x, seq_len_q, device=qa_mask.device)

            matrix_qx = torch.ones(batch_size, seq_len_q, seq_len_x, device=qa_mask.device)

            # Construct the full attention mask
            attention_mask = torch.cat([
                torch.cat([adj_matrix, matrix_xq], dim=2),  # x_embeddings part
                torch.cat([matrix_qx, qa_matrix], dim=2)  # q_embeddings part
            ], dim=1).squeeze(1)

            outputs = self.lm(inputs_embeds=input_embeddings,
                              attention_mask=attention_mask,
                              return_dict=True)

        else:
            outputs = self.lm(inputs_embeds=input_embeddings,
                              return_dict=True)

        return outputs




================================================
FILE: cgm/models/qwen2/_4_46_1/modeling_attn_mask_utils.py
================================================
# Copyright 2023 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
from dataclasses import dataclass
from typing import List, Optional, Tuple, Union

import torch

from transformers.utils.import_utils import is_torchdynamo_compiling

# TODO: Copyied Version: transformers == 4.46.1

@dataclass
class AttentionMaskConverter:
    """
    A utility attention mask class that allows one to:
        - Create a causal 4d mask
        - Create a causal 4d mask with slided window
        - Convert a 2d attention mask (batch_size, query_length) to a 4d attention mask (batch_size, 1, query_length,
          key_value_length) that can be multiplied with attention scores

    Examples:

    ```python
    >>> import torch
    >>> from transformers.modeling_attn_mask_utils import AttentionMaskConverter

    >>> converter = AttentionMaskConverter(True)
    >>> converter.to_4d(torch.tensor([[0, 0, 0, 1, 1]]), 5, key_value_length=5, dtype=torch.float32)
    tensor([[[[-3.4028e+38, -3.4028e+38, -3.4028e+38, -3.4028e+38, -3.4028e+38],
            [-3.4028e+38, -3.4028e+38, -3.4028e+38, -3.4028e+38, -3.4028e+38],
            [-3.4028e+38, -3.4028e+38, -3.4028e+38, -3.4028e+38, -3.4028e+38],
            [-3.4028e+38, -3.4028e+38, -3.4028e+38,  0.0000e+00, -3.4028e+38],
            [-3.4028e+38, -3.4028e+38, -3.4028e+38,  0.0000e+00,  0.0000e+00]]]])
    ```

    Parameters:
        is_causal (`bool`):
            Whether the attention mask should be a uni-directional (causal) or bi-directional mask.

        sliding_window (`int`, *optional*):
            Optionally, the sliding window masks can be created if `sliding_window` is defined to a positive integer.
    """

    is_causal: bool
    sliding_window: int

    def __init__(self, is_causal: bool, sliding_window: Optional[int] = None):
        self.is_causal = is_causal
        self.sliding_window = sliding_window

        if self.sliding_window is not None and self.sliding_window <= 0:
            raise ValueError(
                f"Make sure that when passing `sliding_window` that its value is a strictly positive integer, not `{self.sliding_window}`"
            )

    def to_causal_4d(
        self,
        batch_size: int,
        query_length: int,
        key_value_length: int,
        dtype: torch.dtype,
        device: Union[torch.device, "str"] = "cpu",
    ) -> Optional[torch.Tensor]:
        """
        Creates a causal 4D mask of (bsz, head_dim=1, query_length, key_value_length) shape and adds large negative
        bias to upper right hand triangular matrix (causal mask).
        """
        if not self.is_causal:
            raise ValueError(f"Please use `to_causal_4d` only if {self.__class__} has `is_causal` set to True.")

        # If shape is not cached, create a new causal mask and cache it
        input_shape = (batch_size, query_length)
        past_key_values_length = key_value_length - query_length

        # create causal mask
        # [bsz, seq_len] -> [bsz, 1, tgt_seq_len, src_seq_len]
        causal_4d_mask = None
        if input_shape[-1] > 1 or self.sliding_window is not None:
            causal_4d_mask = self._make_causal_mask(
                input_shape,
                dtype,
                device=device,
                past_key_values_length=past_key_values_length,
                sliding_window=self.sliding_window,
            )

        return causal_4d_mask

    def to_4d(
        self,
        attention_mask_2d: torch.Tensor,
        query_length: int,
        dtype: torch.dtype,
        key_value_length: Optional[int] = None,
    ) -> torch.Tensor:
        """
        Converts 2D attention mask to 4D attention mask by expanding mask to (bsz, head_dim=1, query_length,
        key_value_length) shape and by adding a large negative bias to not-attended positions. If attention_mask is
        causal, a causal mask will be added.
        """
        input_shape = (attention_mask_2d.shape[0], query_length)

        # create causal mask
        # [bsz, seq_len] -> [bsz, 1, tgt_seq_len, src_seq_len]
        causal_4d_mask = None
        if (input_shape[-1] > 1 or self.sliding_window is not None) and self.is_causal:
            if key_value_length is None:
                raise ValueError(
                    "This attention mask converter is causal. Make sure to pass `key_value_length` to correctly create a causal mask."
                )

            past_key_values_length = key_value_length - query_length
            causal_4d_mask = self._make_causal_mask(
                input_shape,
                dtype,
                device=attention_mask_2d.device,
                past_key_values_length=past_key_values_length,
                sliding_window=self.sliding_window,
            )
        elif self.sliding_window is not None:
            raise NotImplementedError("Sliding window is currently only implemented for causal masking")

        # [bsz, seq_len] -> [bsz, 1, tgt_seq_len, src_seq_len]
        expanded_attn_mask = self._expand_mask(attention_mask_2d, dtype, tgt_len=input_shape[-1]).to(
            attention_mask_2d.device
        )

        if causal_4d_mask is not None:
            expanded_attn_mask = causal_4d_mask.masked_fill(expanded_attn_mask.bool(), torch.finfo(dtype).min)

        # expanded_attn_mask + causal_4d_mask can cause some overflow
        expanded_4d_mask = expanded_attn_mask

        return expanded_4d_mask

    def _3d_to_4d(
        self,
        attention_mask_3d: torch.Tensor,
        query_length: int,
        dtype: torch.dtype,
        key_value_length: Optional[int] = None,
    ) -> torch.Tensor:
        """
        Converts 3D attention mask to 4D attention mask by expanding mask to (bsz, head_dim=1, query_length,
        key_value_length) shape and by adding a large negative bias to not-attended positions. If attention_mask is
        causal, a causal mask will be added.
        """
        input_shape = (attention_mask_3d.shape[0], query_length, key_value_length)

        # create causal mask
        # [bsz, tgt_seq_len, src_seq_len] -> [bsz, 1, tgt_seq_len, src_seq_len]
        causal_4d_mask = None
        if (input_shape[-2] > 1 or self.sliding_window is not None) and self.is_causal:
            if key_value_length is None:
                raise ValueError(
                    "This attention mask converter is causal. Make sure to pass `key_value_length` to correctly create a causal mask."
                )

            past_key_values_length = key_value_length - query_length
            causal_4d_mask = self._make_causal_mask(
                input_shape,
                dtype,
                device=attention_mask_3d.device,
                past_key_values_length=past_key_values_length,
                sliding_window=self.sliding_window,
            )
        elif self.sliding_window is not None:
            raise NotImplementedError("Sliding window is currently only implemented for causal masking")

        # [bsz, tgt_seq_len, src_seq_len] -> [bsz, 1, tgt_seq_len, src_seq_len]
        expanded_attn_mask = self._expand_mask_3d(attention_mask_3d, dtype, tgt_len=input_shape[-2]).to(
            attention_mask_3d.device
        )

        if causal_4d_mask is not None:
            expanded_attn_mask = causal_4d_mask.masked_fill(expanded_attn_mask.bool(), torch.finfo(dtype).min)

        # expanded_attn_mask + causal_4d_mask can cause some overflow
        expanded_4d_mask = expanded_attn_mask

        return expanded_4d_mask

    @staticmethod
    def _make_causal_mask(
        input_ids_shape: torch.Size,
        dtype: torch.dtype,
        device: torch.device,
        past_key_values_length: int = 0,
        sliding_window: Optional[int] = None,
    ):
        """
        Make causal mask used for bi-directional self-attention.
        """
        bsz, tgt_len = input_ids_shape
        mask = torch.full((tgt_len, tgt_len), torch.finfo(dtype).min, device=device)
        mask_cond = torch.arange(mask.size(-1), device=device)
        mask.masked_fill_(mask_cond < (mask_cond + 1).view(mask.size(-1), 1), 0)

        mask = mask.to(dtype)

        if past_key_values_length > 0:
            mask = torch.cat([torch.zeros(tgt_len, past_key_values_length, dtype=dtype, device=device), mask], dim=-1)

        # add lower triangular sliding window mask if necessary
        if sliding_window is not None:
            diagonal = past_key_values_length - sliding_window - 1

            context_mask = torch.tril(torch.ones_like(mask, dtype=torch.bool), diagonal=diagonal)
            mask.masked_fill_(context_mask, torch.finfo(dtype).min)

        return mask[None, None, :, :].expand(bsz, 1, tgt_len, tgt_len + past_key_values_length)

    @staticmethod
    def _make_causal_mask_3d(
        input_ids_shape: torch.Size,
        dtype: torch.dtype,
        device: torch.device,
        past_key_values_length: int = 0,
        sliding_window: Optional[int] = None,
    ):
        """
        Make causal mask used for bi-directional self-attention.
        """
        bsz, tgt_len, src_len = input_ids_shape
        mask = torch.full((tgt_len, src_len), torch.finfo(dtype).min, device=device)
        mask_cond = torch.arange(mask.size(-1), device=device)
        mask.masked_fill_(mask_cond < (mask_cond + 1).view(mask.size(-1), 1), 0)

        mask = mask.to(dtype)

        if past_key_values_length > 0:
            mask = torch.cat([torch.zeros(tgt_len, past_key_values_length, dtype=dtype, device=device), mask], dim=-1)

        # add lower triangular sliding window mask if necessary
        if sliding_window is not None:
            diagonal = past_key_values_length - sliding_window - 1

            context_mask = torch.tril(torch.ones_like(mask, dtype=torch.bool), diagonal=diagonal)
            mask.masked_fill_(context_mask, torch.finfo(dtype).min)

        return mask[None, None, :, :].expand(bsz, 1, tgt_len, src_len + past_key_values_length)

    @staticmethod
    def _expand_mask(mask: torch.Tensor, dtype: torch.dtype, tgt_len: Optional[int] = None):
        """
        Expands attention_mask from `[bsz, seq_len]` to `[bsz, 1, tgt_seq_len, src_seq_len]`.
        """
        bsz, src_len = mask.size()
        tgt_len = tgt_len if tgt_len is not None else src_len

        expanded_mask = mask[:, None, None, :].expand(bsz, 1, tgt_len, src_len).to(dtype)

        inverted_mask = 1.0 - expanded_mask

        return inverted_mask.masked_fill(inverted_mask.to(torch.bool), torch.finfo(dtype).min)

    @staticmethod
    def _expand_mask_3d(mask: torch.Tensor, dtype: torch.dtype, tgt_len: Optional[int] = None):
        """
        Expands attention_mask from `[bsz, tgt_seq_len, src_seq_len]` to `[bsz, 1, tgt_seq_len, src_seq_len]`.
        """
        bsz, tgt_seq_len, src_seq_len = mask.size()
        tgt_len = tgt_len if tgt_len is not None else tgt_seq_len

        expanded_mask = mask[:, None, :, :].expand(bsz, 1, tgt_len, src_seq_len).to(dtype)

        inverted_mask = 1.0 - expanded_mask

        return inverted_mask.masked_fill(inverted_mask.to(torch.bool), torch.finfo(dtype).min)

    @staticmethod
    def _unmask_unattended(
        expanded_mask: torch.FloatTensor,
        min_dtype: float,
    ):
        # fmt: off
        """
        Attend to all tokens in masked rows from the expanded attention mask, for example the relevant first rows when
        using left padding. This is required by F.scaled_dot_product_attention memory-efficient attention path.
        Details: https://github.com/pytorch/pytorch/issues/110213

        `expanded_mask` is [bsz, num_masks, tgt_seq_len, src_seq_len] or [bsz, tgt_seq_len, src_seq_len].
        `attention_mask` is [bsz, src_seq_len].

        The dimension num_masks of `expanded_mask` is most often 1, but it can also be the number of heads in the case of alibi attention bias.

        For example, if `expanded_mask` is (e.g. here left-padding case)
        ```
        [[[[0, 0, 0],
           [0, 0, 0],
           [0, 0, 1]]],
         [[[1, 0, 0],
           [1, 1, 0],
           [1, 1, 1]]],
         [[[0, 0, 0],
           [0, 1, 0],
           [0, 1, 1]]]]
        ```
        then the modified `expanded_mask` will be
        ```
        [[[[1, 1, 1],   <-- modified
           [1, 1, 1],   <-- modified
           [0, 0, 1]]],
         [[[1, 0, 0],
           [1, 1, 0],
           [1, 1, 1]]],
         [[[1, 1, 1],   <-- modified
           [0, 1, 0],
           [0, 1, 1]]]]
        ```
        """
        # fmt: on
        if expanded_mask.dtype == torch.bool:
            raise ValueError(
                "AttentionMaskConverter._unmask_unattended expects a float `expanded_mask`, got a BoolTensor."
            )

        return expanded_mask.mul(~torch.all(expanded_mask == min_dtype, dim=-1, keepdim=True))

    @staticmethod
    def _ignore_causal_mask_sdpa(
        attention_mask: Optional[torch.Tensor],
        inputs_embeds: torch.Tensor,
        past_key_values_length: int,
        sliding_window: Optional[int] = None,
        is_training: bool = False,
    ) -> bool:
        """
        Detects whether the optional user-specified attention_mask & the automatically created causal mask can be
        ignored in case PyTorch's SDPA is used, rather relying on SDPA's `is_causal` argument.

        In case no token is masked in the `attention_mask` argument, if `query_length == 1` or
        `key_value_length == query_length`, we rather rely on SDPA `is_causal` argument to use causal/non-causal masks,
        allowing to dispatch to the flash attention kernel (that can otherwise not be used if a custom `attn_mask` is
        passed).
        """

        _, query_length = inputs_embeds.shape[0], inputs_embeds.shape[1]
        key_value_length = query_length + past_key_values_length

        is_tracing = torch.jit.is_tracing() or isinstance(inputs_embeds, torch.fx.Proxy) or is_torchdynamo_compiling()

        ignore_causal_mask = False

        if attention_mask is None:
            # TODO: When tracing with TorchDynamo with fullgraph=True, the model is recompiled depending on the input
            # shape, thus SDPA's `is_causal` argument is rightfully updated
            # (see https://gist.github.com/fxmarty/1313f39037fc1c112508989628c57363). However, when using
            # `torch.export` or `torch.onnx.dynamo_export`, we must pass an example input, and `is_causal` behavior is
            # hard-coded. If a user exports a model with q_len > 1, the exported model will hard-code `is_causal=True`
            # which is in general wrong (see https://github.com/pytorch/pytorch/issues/108108).
            # Thus, we only set `ignore_causal_mask = True` if the model is set to training.
            #
            # Besides, jit.trace can not handle the `q_len > 1` condition for `is_causal`
            # ("TypeError: scaled_dot_product_attention(): argument 'is_causal' must be bool, not Tensor").
            if (
                (is_training or not is_tracing)
                and (query_length == 1 or key_value_length == query_length)
                and (sliding_window is None or key_value_length < sliding_window)
            ):
                ignore_causal_mask = True
        elif sliding_window is None or key_value_length < sliding_window:
            if len(attention_mask.shape) == 4:
                return False
            elif not is_tracing and torch.all(attention_mask == 1):
                if query_length == 1 or key_value_length == query_length:
                    # For query_length == 1, causal attention and bi-directional attention are the same.
                    ignore_causal_mask = True

                # Unfortunately, for query_length > 1 and key_value_length != query_length, we cannot generally ignore
                # the attention mask, as SDPA causal mask generation may be wrong. We will set `is_causal=False` in
                # SDPA and rely on Transformers attention_mask instead, hence not setting it to None here.
                # Reference: https://github.com/pytorch/pytorch/issues/108108
                # TODO: maybe revisit this with https://github.com/pytorch/pytorch/pull/114823 in PyTorch 2.3.

        return ignore_causal_mask


def _prepare_4d_causal_attention_mask(
    attention_mask: Optional[torch.Tensor],
    input_shape: Union[torch.Size, Tuple, List],
    inputs_embeds: torch.Tensor,
    past_key_values_length: int,
    sliding_window: Optional[int] = None,
):
    """
    Creates a causal 4D mask of shape `(batch_size, 1, query_length, key_value_length)` from a 2D mask of shape
    `(batch_size, key_value_length)`

    Args:
        attention_mask (`torch.Tensor` or `None`):
            A 2D attention mask of shape `(batch_size, key_value_length)`
        input_shape (`tuple(int)` or `list(int)` or `torch.Size`):
            The input shape should be a tuple that defines `(batch_size, query_length)`.
        inputs_embeds (`torch.Tensor`):
            The embedded inputs as a torch Tensor.
        past_key_values_length (`int`):
            The length of the key value cache.
        sliding_window (`int`, *optional*):
            If the model uses windowed attention, a sliding window should be passed.
    """
    attn_mask_converter = AttentionMaskConverter(is_causal=True, sliding_window=sliding_window)

    key_value_length = input_shape[-1] + past_key_values_length

    # 4d mask is passed through the layers
    if attention_mask is not None and len(attention_mask.shape) == 2:
        attention_mask = attn_mask_converter.to_4d(
            attention_mask, input_shape[-1], key_value_length=key_value_length, dtype=inputs_embeds.dtype
        )
    elif attention_mask is not None and len(attention_mask.shape) == 4:
        expected_shape = (input_shape[0], 1, input_shape[1], key_value_length)
        if tuple(attention_mask.shape) != expected_shape:
            raise ValueError(
                f"Incorrect 4D attention_mask shape: {tuple(attention_mask.shape)}; expected: {expected_shape}."
            )
        else:
            # if the 4D mask has correct shape - invert it and fill with negative infinity
            inverted_mask = 1.0 - attention_mask
            attention_mask = inverted_mask.masked_fill(
                inverted_mask.to(torch.bool), torch.finfo(inputs_embeds.dtype).min
            )
    else:
        attention_mask = attn_mask_converter.to_causal_4d(
            input_shape[0], input_shape[-1], key_value_length, dtype=inputs_embeds.dtype, device=inputs_embeds.device
        )

    return attention_mask


# Adapted from _prepare_4d_causal_attention_mask
def _prepare_4d_causal_attention_mask_for_sdpa(
    attention_mask: Optional[torch.Tensor],
    input_shape: Union[torch.Size, Tuple, List],
    inputs_embeds: torch.Tensor,
    past_key_values_length: int,
    sliding_window: Optional[int] = None,
):
    """
    Prepares the correct `attn_mask` argument to be used by `torch.nn.functional.scaled_dot_product_attention`.

    In case no token is masked in the `attention_mask` argument, we simply set it to `None` for the cases `query_length == 1` and
    `key_value_length == query_length`, and rely instead on SDPA `is_causal` argument to use causal/non-causal masks,
    allowing to dispatch to the flash attention kernel (that can otherwise not be used if a custom `attn_mask` is passed).
    """
    attn_mask_converter = AttentionMaskConverter(is_causal=True, sliding_window=sliding_window)

    key_value_length = input_shape[-1] + past_key_values_length

    # torch.jit.trace, symbolic_trace and torchdynamo with fullgraph=True are unable to capture the controlflow `is_causal=attention_mask is None and q_len > 1`
    # used as an SDPA argument. We keep compatibility with these tracing tools by always using SDPA's `attn_mask` argument in case we are tracing.
    # TODO: For dynamo, rather use a check on fullgraph=True once this is possible (https://github.com/pytorch/pytorch/pull/120400).
    is_tracing = torch.jit.is_tracing() or isinstance(inputs_embeds, torch.fx.Proxy) or is_torchdynamo_compiling()

    ignore_causal_mask = AttentionMaskConverter._ignore_causal_mask_sdpa(
        attention_mask=attention_mask,
        inputs_embeds=inputs_embeds,
        past_key_values_length=past_key_values_length,
        sliding_window=sliding_window,
    )

    if ignore_causal_mask:
        expanded_4d_mask = None
    elif attention_mask is None:
        expanded_4d_mask = attn_mask_converter.to_causal_4d(
            input_shape[0], input_shape[-1], key_value_length, dtype=inputs_embeds.dtype, device=inputs_embeds.device
        )
    else:
        if attention_mask.dim() == 4:
            expanded_4d_mask = attention_mask
        else:
            expanded_4d_mask = attn_mask_converter.to_4d(
                attention_mask,
                input_shape[-1],
                dtype=inputs_embeds.dtype,
                key_value_length=key_value_length,
            )

        # Attend to all tokens in masked rows from the causal_mask, for example the relevant first rows when
        # using left padding. This is required by F.scaled_dot_product_attention memory-efficient attention path.
        # Details: https://github.com/pytorch/pytorch/issues/110213
        if not is_tracing and expanded_4d_mask.device.type == "cuda":
            expanded_4d_mask = AttentionMaskConverter._unmask_unattended(
                expanded_4d_mask, min_dtype=torch.finfo(inputs_embeds.dtype).min
            )

    return expanded_4d_mask


def _prepare_4d_attention_mask(mask: torch.Tensor, dtype: torch.dtype, tgt_len: Optional[int] = None):
    """
    Creates a non-causal 4D mask of shape `(batch_size, 1, query_length, key_value_length)` from a 2D mask of shape
    `(batch_size, key_value_length)`

    Args:
        mask (`torch.Tensor`):
            A 2D attention mask of shape `(batch_size, key_value_length)`
        dtype (`torch.dtype`):
            The torch dtype the created mask shall have.
        tgt_len (`int`):
            The target length or query length the created mask shall have.
    """
    return AttentionMaskConverter._expand_mask(mask=mask, dtype=dtype, tgt_len=tgt_len)


def _prepare_4d_attention_mask_for_sdpa(mask: torch.Tensor, dtype: torch.dtype, tgt_len: Optional[int] = None):
    """
    Creates a non-causal 4D mask of shape `(batch_size, 1, query_length, key_value_length)` from a 2D mask of shape
    `(batch_size, key_value_length)`

    Args:
        mask (`torch.Tensor`):
            A 2D attention mask of shape `(batch_size, key_value_length)`
        dtype (`torch.dtype`):
            The torch dtype the created mask shall have.
        tgt_len (`int`):
            The target length or query length the created mask shall have.
    """
    _, key_value_length = mask.shape
    tgt_len = tgt_len if tgt_len is not None else key_value_length

    is_tracing = torch.jit.is_tracing() or isinstance(mask, torch.fx.Proxy) or is_torchdynamo_compiling()

    # torch.jit.trace, symbolic_trace and torchdynamo with fullgraph=True are unable to capture data-dependent controlflows.
    if not is_tracing and torch.all(mask == 1):
        return None
    else:
        return AttentionMaskConverter._expand_mask(mask=mask, dtype=dtype, tgt_len=tgt_len)


def _create_4d_causal_attention_mask(
    input_shape: Union[torch.Size, Tuple, List],
    dtype: torch.dtype,
    device: torch.device,
    past_key_values_length: int = 0,
    sliding_window: Optional[int] = None,
) -> Optional[torch.Tensor]:
    """
    Creates a causal 4D mask of shape `(batch_size, 1, query_length, key_value_length)`

    Args:
        input_shape (`tuple(int)` or `list(int)` or `torch.Size`):
            The input shape should be a tuple that defines `(batch_size, query_length)`.
        dtype (`torch.dtype`):
            The torch dtype the created mask shall have.
        device (`int`):
            The torch device the created mask shall have.
        sliding_window (`int`, *optional*):
            If the model uses windowed attention, a sliding window should be passed.
    """
    attn_mask_converter = AttentionMaskConverter(is_causal=True, sliding_window=sliding_window)

    key_value_length = past_key_values_length + input_shape[-1]
    attention_mask = attn_mask_converter.to_causal_4d(
        input_shape[0], input_shape[-1], key_value_length, dtype=dtype, device=device
    )

    return attention_mask


================================================
FILE: cgm/models/qwen2/_4_46_1/modeling_qwen2.py
================================================
# coding=utf-8
# Copyright 2024 The Qwen team, Alibaba Group and the HuggingFace Inc. team. All rights reserved.
#
# This code is based on EleutherAI's GPT-NeoX library and the GPT-NeoX
# and OPT implementations in this library. It has been modified from its
# original forms to accommodate minor architectural differences compared
# to GPT-NeoX and OPT used by the Meta AI team that trained the model.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""PyTorch Qwen2 model."""

# TODO: Copyied Version: transformers == 4.46.1

import math
from typing import List, Optional, Tuple, Union

import torch
import torch.utils.checkpoint
from torch import nn
from torch.nn import BCEWithLogitsLoss, CrossEntropyLoss, MSELoss

from transformers.activations import ACT2FN
from transformers.cache_utils import Cache, DynamicCache, SlidingWindowCache, StaticCache
from transformers.generation import GenerationMixin
# from transformers.modeling_attn_mask_utils import AttentionMaskConverter
from models.qwen2._4_46_1.modeling_attn_mask_utils import AttentionMaskConverter
from transformers.modeling_outputs import (
    BaseModelOutputWithPast,
    CausalLMOutputWithPast,
    QuestionAnsweringModelOutput,
    SequenceClassifierOutputWithPast,
    TokenClassifierOutput,
)
from transformers.modeling_rope_utils import ROPE_INIT_FUNCTIONS
from transformers.modeling_utils import PreTrainedModel
from transformers.utils import (
    add_code_sample_docstrings,
    add_start_docstrings,
    add_start_docstrings_to_model_forward,
    is_flash_attn_2_available,
    is_flash_attn_greater_or_equal_2_10,
    logging,
    replace_return_docstrings,
)
from transformers.models.qwen2.configuration_qwen2 import Qwen2Config

from xformers.ops import memory_efficient_attention

if is_flash_attn_2_available():
    from transformers.modeling_flash_attention_utils import _flash_attention_forward


logger = logging.get_logger(__name__)


_CHECKPOINT_FOR_DOC = "Qwen/Qwen2-7B"
_CONFIG_FOR_DOC = "Qwen2Config"


# Copied from transformers.models.llama.modeling_llama.LlamaRMSNorm with Llama->Qwen2
class Qwen2RMSNorm(nn.Module):
    def __init__(self, hidden_size, eps=1e-6):
        """
        Qwen2RMSNorm is equivalent to T5LayerNorm
        """
        super().__init__()
        self.weight = nn.Parameter(torch.ones(hidden_size))
        self.variance_epsilon = eps

    def forward(self, hidden_states):
        input_dtype = hidden_states.dtype
        hidden_states = hidden_states.to(torch.float32)
        variance = hidden_states.pow(2).mean(-1, keepdim=True)
        hidden_states = hidden_states * torch.rsqrt(variance + self.variance_epsilon)
        return self.weight * hidden_states.to(input_dtype)

    def extra_repr(self):
        return f"{tuple(self.weight.shape)}, eps={self.variance_epsilon}"


# Copied from transformers.models.llama.modeling_llama.LlamaRotaryEmbedding with Llama->Qwen2
class Qwen2RotaryEmbedding(nn.Module):
    def __init__(
        self,
        dim=None,
        max_position_embeddings=2048,
        base=10000,
        device=None,
        scaling_factor=1.0,
        rope_type="default",
        config: Optional[Qwen2Config] = None,
    ):
        super().__init__()
        # TODO (joao): remove the `if` below, only used for BC
        self.rope_kwargs = {}
        if config is None:
            logger.warning_once(
                "`Qwen2RotaryEmbedding` can now be fully parameterized by passing the model config through the "
                "`config` argument. All other arguments will be removed in v4.46"
            )
            self.rope_kwargs = {
                "rope_type": rope_type,
                "factor": scaling_factor,
                "dim": dim,
                "base": base,
                "max_position_embeddings": max_position_embeddings,
            }
            self.rope_type = rope_type
            self.max_seq_len_cached = max_position_embeddings
            self.original_max_seq_len = max_position_embeddings
        else:
            # BC: "rope_type" was originally "type"
            if config.rope_scaling is not None:
                self.rope_type = config.rope_scaling.get("rope_type", config.rope_scaling.get("type"))
            else:
                self.rope_type = "default"
            self.max_seq_len_cached = config.max_position_embeddings
            self.original_max_seq_len = config.max_position_embeddings

        self.config = config
        self.rope_init_fn = ROPE_INIT_FUNCTIONS[self.rope_type]

        inv_freq, self.attention_scaling = self.rope_init_fn(self.config, device, **self.rope_kwargs)
        self.register_buffer("inv_freq", inv_freq, persistent=False)
        self.original_inv_freq = self.inv_freq

    def _dynamic_frequency_update(self, position_ids, device):
        """
        dynamic RoPE layers should recompute `inv_freq` in the following situations:
        1 - growing beyond the cached sequence length (allow scaling)
        2 - the current sequence length is in the original scale (avoid losing precision with small sequences)
        """
        seq_len = torch.max(position_ids) + 1
        if seq_len > self.max_seq_len_cached:  # growth
            inv_freq, self.attention_scaling = self.rope_init_fn(
                self.config, device, seq_len=seq_len, **self.rope_kwargs
            )
            self.register_buffer("inv_freq", inv_freq, persistent=False)  # TODO joao: may break with compilation
            self.max_seq_len_cached = seq_len

        if seq_len < self.original_max_seq_len and self.max_seq_len_cached > self.original_max_seq_len:  # reset
            self.register_buffer("inv_freq", self.original_inv_freq, persistent=False)
            self.max_seq_len_cached = self.original_max_seq_len

    @torch.no_grad()
    def forward(self, x, position_ids):
        if "dynamic" in self.rope_type:
            self._dynamic_frequency_update(position_ids, device=x.device)

        # Core RoPE block
        inv_freq_expanded = self.inv_freq[None, :, None].float().expand(position_ids.shape[0], -1, 1)
        position_ids_expanded = position_ids[:, None, :].float()
        # Force float32 (see https://github.com/huggingface/transformers/pull/29285)
        device_type = x.device.type
        device_type = device_type if isinstance(device_type, str) and device_type != "mps" else "cpu"
        with torch.autocast(device_type=device_type, enabled=False):
            freqs = (inv_freq_expanded.float() @ position_ids_expanded.float()).transpose(1, 2)
            emb = torch.cat((freqs, freqs), dim=-1)
            cos = emb.cos()
            sin = emb.sin()

        # Advanced RoPE types (e.g. yarn) apply a post-processing scaling factor, equivalent to scaling attention
        cos = cos * self.attention_scaling
        sin = sin * self.attention_scaling

        return cos.to(dtype=x.dtype), sin.to(dtype=x.dtype)


# Copied from transformers.models.llama.modeling_llama.rotate_half
def rotate_half(x):
    """Rotates half the hidden dims of the input."""
    x1 = x[..., : x.shape[-1] // 2]
    x2 = x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)


# Copied from transformers.models.llama.modeling_llama.apply_rotary_pos_emb
def apply_rotary_pos_emb(q, k, cos, sin, position_ids=None, unsqueeze_dim=1):
    """Applies Rotary Position Embedding to the query and key tensors.

    Args:
        q (`torch.Tensor`): The query tensor.
        k (`torch.Tensor`): The key tensor.
        cos (`torch.Tensor`): The cosine part of the rotary embedding.
        sin (`torch.Tensor`): The sine part of the rotary embedding.
        position_ids (`torch.Tensor`, *optional*):
            Deprecated and unused.
        unsqueeze_dim (`int`, *optional*, defaults to 1):
            The 'unsqueeze_dim' argument specifies the dimension along which to unsqueeze cos[position_ids] and
            sin[position_ids] so that they can be properly broadcasted to the dimensions of q and k. For example, note
            that cos[position_ids] and sin[position_ids] have the shape [batch_size, seq_len, head_dim]. Then, if q and
            k have the shape [batch_size, heads, seq_len, head_dim], then setting unsqueeze_dim=1 makes
            cos[position_ids] and sin[position_ids] broadcastable to the shapes of q and k. Similarly, if q and k have
            the shape [batch_size, seq_len, heads, head_dim], then set unsqueeze_dim=2.
    Returns:
        `tuple(torch.Tensor)` comprising of the query and key tensors rotated using the Rotary Position Embedding.
    """
    cos = cos.unsqueeze(unsqueeze_dim)
    sin = sin.unsqueeze(unsqueeze_dim)
    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed


# Copied from transformers.models.mistral.modeling_mistral.MistralMLP with Mistral->Qwen2
class Qwen2MLP(nn.Module):
    def __init__(self, config):
        super().__init__()
        self.hidden_size = config.hidden_size
        self.intermediate_size = config.intermediate_size
        self.gate_proj = nn.Linear(self.hidden_size, self.intermediate_size, bias=False)
        self.up_proj = nn.Linear(self.hidden_size, self.intermediate_size, bias=False)
        self.down_proj = nn.Linear(self.intermediate_size, self.hidden_size, bias=False)
        self.act_fn = ACT2FN[config.hidden_act]

    def forward(self, hidden_state):
        return self.down_proj(self.act_fn(self.gate_proj(hidden_state)) * self.up_proj(hidden_state))


# Copied from transformers.models.llama.modeling_llama.repeat_kv
def repeat_kv(hidden_states: torch.Tensor, n_rep: int) -> torch.Tensor:
    """
    This is the equivalent of torch.repeat_interleave(x, dim=1, repeats=n_rep). The hidden states go from (batch,
    num_key_value_heads, seqlen, head_dim) to (batch, num_attention_heads, seqlen, head_dim)
    """
    batch, num_key_value_heads, slen, head_dim = hidden_states.shape
    if n_rep == 1:
        return hidden_states
    hidden_states = hidden_states[:, :, None, :, :].expand(batch, num_key_value_heads, n_rep, slen, head_dim)
    return hidden_states.reshape(batch, num_key_value_heads * n_rep, slen, head_dim)


class Qwen2Attention(nn.Module):
    """
    Multi-headed attention from 'Attention Is All You Need' paper. Modified to use sliding window attention: Longformer
    and "Generating Long Sequences with Sparse Transformers".
    """

    def __init__(self, config: Qwen2Config, layer_idx: Optional[int] = None):
        super().__init__()
        self.config = config
        self.layer_idx = layer_idx
        if layer_idx is None:
            logger.warning_once(
                f"Instantiating {self.__class__.__name__} without passing `layer_idx` is not recommended and will "
                "to errors during the forward call, if caching is used. Please make sure to provide a `layer_idx` "
                "when creating this class."
            )

        self.hidden_size = config.hidden_size
        self.num_heads = config.num_attention_heads
        self.head_dim = self.hidden_size // self.num_heads
        self.num_key_value_heads = config.num_key_value_heads
        self.num_key_value_groups = self.num_heads // self.num_key_value_heads
        self.max_position_embeddings = config.max_position_embeddings
        self.rope_theta = config.rope_theta
        self.is_causal = True
        self.attention_dropout = config.attention_dropout

        if (self.head_dim * self.num_heads) != self.hidden_size:
            raise ValueError(
                f"hidden_size must be divisible by num_heads (got `hidden_size`: {self.hidden_size}"
                f" and `num_heads`: {self.num_heads})."
            )
        self.q_proj = nn.Linear(self.hidden_size, self.num_heads * self.head_dim, bias=True)
        self.k_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=True)
        self.v_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=True)
        self.o_proj = nn.Linear(self.num_heads * self.head_dim, self.hidden_size, bias=False)

        self.rotary_emb = Qwen2RotaryEmbedding(config=self.config)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Cache] = None,
        output_attentions: bool = False,
        use_cache: bool = False,
        cache_position: Optional[torch.LongTensor] = None,
        position_embeddings: Optional[Tuple[torch.Tensor, torch.Tensor]] = None,  # will become mandatory in v4.46
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]]]:
        bsz, q_len, _ = hidden_states.size()

        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(hidden_states)
        value_states = self.v_proj(hidden_states)

        query_states = query_states.view(bsz, q_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)

        if position_embeddings is None:
            logger.warning_once(
                "The attention layers in this model are transitioning from computing the RoPE embeddings internally "
                "through `position_ids` (2D tensor with the indexes of the tokens), to using externally computed "
                "`position_embeddings` (Tuple of tensors, containing cos and sin). In v4.46 `position_ids` will be "
                "removed and `position_embeddings` will be mandatory."
            )
            cos, sin = self.rotary_emb(value_states, position_ids)
        else:
            cos, sin = position_embeddings
        query_states, key_states = apply_rotary_pos_emb(query_states, key_states, cos, sin)

        if past_key_value is not None:
            cache_kwargs = {"sin": sin, "cos": cos, "cache_position": cache_position}  # Specific to RoPE models
            key_states, value_states = past_key_value.update(key_states, value_states, self.layer_idx, cache_kwargs)

        # repeat k/v heads if n_kv_heads < n_heads
        key_states = repeat_kv(key_states, self.num_key_value_groups)
        value_states = repeat_kv(value_states, self.num_key_value_groups)

        attn_weights = torch.matmul(query_states, key_states.transpose(2, 3)) / math.sqrt(self.head_dim)
        if attention_mask is not None:  # no matter the length, we just slice it
            causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]
            attn_weights = attn_weights + causal_mask

        # upcast attention to fp32
        attn_weights = nn.functional.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query_states.dtype)
        attn_weights = nn.functional.dropout(attn_weights, p=self.attention_dropout, training=self.training)
        attn_output = torch.matmul(attn_weights, value_states)

        if attn_output.size() != (bsz, self.num_heads, q_len, self.head_dim):
            raise ValueError(
                f"`attn_output` should be of size {(bsz, self.num_heads, q_len, self.head_dim)}, but is"
                f" {attn_output.size()}"
            )

        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.reshape(bsz, q_len, self.hidden_size)

        attn_output = self.o_proj(attn_output)

        if not output_attentions:
            attn_weights = None

        return attn_output, attn_weights, past_key_value


class Qwen2FlashAttention2(Qwen2Attention):
    """
    Qwen2 flash attention module, following Qwen2 attention module. This module inherits from `Qwen2Attention`
    as the weights of the module stays untouched. The only required change would be on the forward pass
    where it needs to correctly call the public API of flash attention and deal with padding tokens
    in case the input contains any of them. Additionally, for sliding window attention, we apply SWA only to the bottom
    config.max_window_layers layers.
    """

    # Copied from transformers.models.llama.modeling_llama.LlamaFlashAttention2.__init__
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # TODO: Should be removed once Flash Attention for RoCm is bumped to 2.1.
        # flash_attn<2.1 generates top-left aligned causal mask, while what is needed here is bottom-right alignement, that was made default for flash_attn>=2.1. This attribute is used to handle this difference. Reference: https://github.com/Dao-AILab/flash-attention/releases/tag/v2.1.0.
        # Beware that with flash_attn<2.1, using q_seqlen != k_seqlen (except for the case q_seqlen == 1) produces a wrong mask (top-left).
        self._flash_attn_uses_top_left_mask = not is_flash_attn_greater_or_equal_2_10()

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Cache] = None,
        output_attentions: bool = False,
        use_cache: bool = False,
        cache_position: Optional[torch.LongTensor] = None,
        position_embeddings: Optional[Tuple[torch.Tensor, torch.Tensor]] = None,  # will become mandatory in v4.46
    ):
        bsz, q_len, _ = hidden_states.size()

        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(hidden_states)
        value_states = self.v_proj(hidden_states)

        query_states = query_states.view(bsz, q_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)

        if position_embeddings is None:
            logger.warning_once(
                "The attention layers in this model are transitioning from computing the RoPE embeddings internally "
                "through `position_ids` (2D tensor with the indexes of the tokens), to using externally computed "
                "`position_embeddings` (Tuple of tensors, containing cos and sin). In v4.46 `position_ids` will be "
                "removed and `position_embeddings` will be mandatory."
            )
            cos, sin = self.rotary_emb(value_states, position_ids)
        else:
            cos, sin = position_embeddings
        query_states, key_states = apply_rotary_pos_emb(query_states, key_states, cos, sin)

        if past_key_value is not None:
            cache_kwargs = {"sin": sin, "cos": cos, "cache_position": cache_position}  # Specific to RoPE models
            key_states, value_states = past_key_value.update(key_states, value_states, self.layer_idx, cache_kwargs)

        # repeat k/v heads if n_kv_heads < n_heads
        key_states = repeat_kv(key_states, self.num_key_value_groups)
        value_states = repeat_kv(value_states, self.num_key_value_groups)
        dropout_rate = 0.0 if not self.training else self.attention_dropout

        # In PEFT, usually we cast the layer norms in float32 for training stability reasons
        # therefore the input hidden states gets silently casted in float32. Hence, we need
        # cast them back in float16 just to be sure everything works as expected.
        input_dtype = query_states.dtype
        if input_dtype == torch.float32:
            if torch.is_autocast_enabled():
                target_dtype = torch.get_autocast_gpu_dtype()
            # Handle the case where the model is quantized
            elif hasattr(self.config, "_pre_quantization_dtype"):
                target_dtype = self.config._pre_quantization_dtype
            else:
                target_dtype = self.q_proj.weight.dtype

            logger.warning_once(
                f"The input hidden states seems to be silently casted in float32, this might be related to"
                f" the fact you have upcasted embedding or layer norm layers in float32. We will cast back the input in"
                f" {target_dtype}."
            )

            query_states = query_states.to(target_dtype)
            key_states = key_states.to(target_dtype)
            value_states = value_states.to(target_dtype)

        # Reashape to the expected shape for Flash Attention
        query_states = query_states.transpose(1, 2)
        key_states = key_states.transpose(1, 2)
        value_states = value_states.transpose(1, 2)

        if (
            self.config.use_sliding_window
            and getattr(self.config, "sliding_window", None) is not None
            and self.layer_idx >= self.config.max_window_layers
        ):
            sliding_window = self.config.sliding_window
        else:
            sliding_window = None

        attn_output = _flash_attention_forward(
            query_states,
            key_states,
            value_states,
            attention_mask,
            q_len,
            position_ids=position_ids,
            dropout=dropout_rate,
            sliding_window=sliding_window,
            is_causal=self.is_causal,
            use_top_left_mask=self._flash_attn_uses_top_left_mask,
        )

        attn_output = attn_output.reshape(bsz, q_len, self.hidden_size).contiguous()
        attn_output = self.o_proj(attn_output)

        if not output_attentions:
            attn_weights = None

        return attn_output, attn_weights, past_key_value


class Qwen2SdpaAttention(Qwen2Attention):
    """
    Qwen2 attention module using torch.nn.functional.scaled_dot_product_attention. This module inherits from
    `Qwen2Attention` as the weights of the module stays untouched. The only changes are on the forward pass to adapt to
    SDPA API.
    """

    # Adapted from Qwen2Attention.forward
    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Cache] = None,
        output_attentions: bool = False,
        use_cache: bool = False,
        cache_position: Optional[torch.LongTensor] = None,
        position_embeddings: Optional[Tuple[torch.Tensor, torch.Tensor]] = None,  # will become mandatory in v4.46
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]]]:
        if output_attentions:
            # TODO: Improve this warning with e.g. `model.config.attn_implementation = "manual"` once this is implemented.
            logger.warning_once(
                "Qwen2Model is using Qwen2SdpaAttention, but `torch.nn.functional.scaled_dot_product_attention` does not support `output_attentions=True`. Falling back to the manual attention implementation, "
                'but specifying the manual implementation will be required from Transformers version v5.0.0 onwards. This warning can be removed using the argument `attn_implementation="eager"` when loading the model.'
            )
            return super().forward(
                hidden_states=hidden_states,
                attention_mask=attention_mask,
                position_ids=position_ids,
                past_key_value=past_key_value,
                output_attentions=output_attentions,
                use_cache=use_cache,
            )

        bsz, q_len, _ = hidden_states.size()

        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(hidden_states)
        value_states = self.v_proj(hidden_states)

        query_states = query_states.view(bsz, q_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)

        if position_embeddings is None:
            logger.warning_once(
                "The attention layers in this model are transitioning from computing the RoPE embeddings internally "
                "through `position_ids` (2D tensor with the indexes of the tokens), to using externally computed "
                "`position_embeddings` (Tuple of tensors, containing cos and sin). In v4.46 `position_ids` will be "
                "removed and `position_embeddings` will be mandatory."
            )
            cos, sin = self.rotary_emb(value_states, position_ids)
        else:
            cos, sin = position_embeddings
        query_states, key_states = apply_rotary_pos_emb(query_states, key_states, cos, sin)

        if past_key_value is not None:
            cache_kwargs = {"sin": sin, "cos": cos, "cache_position": cache_position}  # Specific to RoPE models
            key_states, value_states = past_key_value.update(key_states, value_states, self.layer_idx, cache_kwargs)

        key_states = repeat_kv(key_states, self.num_key_value_groups)
        value_states = repeat_kv(value_states, self.num_key_value_groups)

        causal_mask = attention_mask
        if attention_mask is not None:  # no matter the length, we just slice it
            causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]

        # SDPA with memory-efficient backend is currently (torch==2.1.2) bugged with non-contiguous inputs with custom attn_mask,
        # Reference: https://github.com/pytorch/pytorch/issues/112577.
        if query_states.device.type == "cuda" and attention_mask is not None:
            query_states = query_states.contiguous()
            key_states = key_states.contiguous()
            value_states = value_states.contiguous()

        # We dispatch to SDPA's Flash Attention or Efficient kernels via this `is_causal` if statement instead of an inline conditional assignment
        # in SDPA to support both torch.compile's dynamic shapes and full graph options. An inline conditional prevents dynamic shapes from compiling.
        # The q_len > 1 is necessary to match with AttentionMaskConverter.to_causal_4d that does not create a causal mask in case q_len == 1.
        is_causal = True if causal_mask is None and q_len > 1 else False

        attn_output = torch.nn.functional.scaled_dot_product_attention(
            query_states,
            key_states,
            value_states,
            attn_mask=causal_mask,
            dropout_p=self.attention_dropout if self.training else 0.0,
            is_causal=is_causal,
        )

        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.view(bsz, q_len, self.hidden_size)

        attn_output = self.o_proj(attn_output)

        return attn_output, None, past_key_value


class Qwen2XformerAttention(Qwen2Attention):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Cache] = None,
        output_attentions: bool = False,
        use_cache: bool = False,
        cache_position: Optional[torch.LongTensor] = None,
        position_embeddings: Optional[Tuple[torch.Tensor, torch.Tensor]] = None,  # will become mandatory in v4.46
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]]]:
        bsz, q_len, _ = hidden_states.size()

        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(hidden_states)
        value_states = self.v_proj(hidden_states)

        query_states = query_states.view(bsz, q_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)

        if position_embeddings is None:
            logger.warning_once(
                "The attention layers in this model are transitioning from computing the RoPE embeddings internally "
                "through `position_ids` (2D tensor with the indexes of the tokens), to using externally computed "
                "`position_embeddings` (Tuple of tensors, containing cos and sin). In v4.46 `position_ids` will be "
                "removed and `position_embeddings` will be mandatory."
            )
            cos, sin = self.rotary_emb(value_states, position_ids)
        else:
            cos, sin = position_embeddings
        query_states, key_states = apply_rotary_pos_emb(query_states, key_states, cos, sin)

        if past_key_value is not None:
            cache_kwargs = {"sin": sin, "cos": cos, "cache_position": cache_position}  # Specific to RoPE models
            key_states, value_states = past_key_value.update(key_states, value_states, self.layer_idx, cache_kwargs)

        # repeat k/v heads if n_kv_heads < n_heads
        key_states = repeat_kv(key_states, self.num_key_value_groups)
        value_states = repeat_kv(value_states, self.num_key_value_groups)

        if attention_mask is not None:  # no matter the length, we just slice it
            causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]

        attn_output = memory_efficient_attention(
            query_states, 
            key_states, 
            value_states,
            attn_bias = causal_mask if causal_mask else None
        )

        # attn_weights = torch.matmul(query_states, key_states.transpose(2, 3)) / math.sqrt(self.head_dim)
        # if attention_mask is not None:  # no matter the length, we just slice it
        #     causal_mask = attention_mask[:, :, :, : key_states.shape[-2]]
        #     attn_weights = attn_weights + causal_mask

        # # upcast attention to fp32
        # attn_weights = nn.functional.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query_states.dtype)
        # attn_weights = nn.functional.dropout(attn_weights, p=self.attention_dropout, training=self.training)
        # attn_output = torch.matmul(attn_weights, value_states)

        if attn_output.size() != (bsz, self.num_heads, q_len, self.head_dim):
            raise ValueError(
                f"`attn_output` should be of size {(bsz, self.num_heads, q_len, self.head_dim)}, but is"
                f" {attn_output.size()}"
            )

        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.reshape(bsz, q_len, self.hidden_size)

        attn_output = self.o_proj(attn_output)

        if not output_attentions:
            attn_weights = None

        return attn_output, attn_weights, past_key_value


QWEN2_ATTENTION_CLASSES = {
    "flash_attention_2": Qwen2FlashAttention2,
    "sdpa": Qwen2SdpaAttention,
    "eager": Qwen2XformerAttention,
}


class Qwen2DecoderLayer(nn.Module):
    def __init__(self, config: Qwen2Config, layer_idx: int):
        super().__init__()
        self.hidden_size = config.hidden_size

        if config.sliding_window and config._attn_implementation != "flash_attention_2":
            logger.warning_once(
                f"Sliding Window Attention is enabled but not implemented for `{config._attn_implementation}`; "
                "unexpected results may be encountered."
            )
        self.self_attn = QWEN2_ATTENTION_CLASSES[config._attn_implementation](config, layer_idx)

        self.mlp = Qwen2MLP(config)
        self.input_layernorm = Qwen2RMSNorm(config.hidden_size, eps=config.rms_norm_eps)
        self.post_attention_layernorm = Qwen2RMSNorm(config.hidden_size, eps=config.rms_norm_eps)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Tuple[torch.Tensor]] = None,
        output_attentions: Optional[bool] = False,
        use_cache: Optional[bool] = False,
        cache_position: Optional[torch.LongTensor] = None,
        position_embeddings: Optional[Tuple[torch.Tensor, torch.Tensor]] = None,  # will become mandatory in v4.46
        **kwargs,
    ) -> Tuple[torch.FloatTensor, Optional[Tuple[torch.FloatTensor, torch.FloatTensor]]]:
        """
        Args:
            hidden_states (`torch.FloatTensor`): input to the layer of shape `(batch, seq_len, embed_dim)`
            attention_mask (`torch.FloatTensor`, *optional*): attention mask of size
                `(batch, sequence_length)` where padding elements are indicated by 0.
            output_attentions (`bool`, *optional*):
                Whether or not to return the attentions tensors of all attention layers. See `attentions` under
                returned tensors for more detail.
            use_cache (`bool`, *optional*):
                If set to `True`, `past_key_values` key value states are returned and can be used to speed up decoding
                (see `past_key_values`).
            past_key_value (`Tuple(torch.FloatTensor)`, *optional*): cached past key and value projection states
            cache_position (`torch.LongTensor` of shape `(sequence_length)`, *optional*):
                Indices depicting the position of the input sequence tokens in the sequence.
            position_embeddings (`Tuple[torch.FloatTensor, torch.FloatTensor]`, *optional*):
                Tuple containing the cosine and sine positional embeddings of shape `(batch_size, seq_len, head_dim)`,
                with `head_dim` being the embedding dimension of each attention head.
            kwargs (`dict`, *optional*):
                Arbitrary kwargs to be ignored, used for FSDP and other methods that injects code
                into the model
        """

        residual = hidden_states

        hidden_states = self.input_layernorm(hidden_states)

        # Self Attention
        hidden_states, self_attn_weights, present_key_value = self.self_attn(
            hidden_states=hidden_states,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_value=past_key_value,
            output_attentions=output_attentions,
            use_cache=use_cache,
            cache_position=cache_position,
            position_embeddings=position_embeddings,
        )
        hidden_states = residual + hidden_states

        # Fully Connected
        residual = hidden_states
        hidden_states = self.post_attention_layernorm(hidden_states)
        hidden_states = self.mlp(hidden_states)
        hidden_states = residual + hidden_states

        outputs = (hidden_states,)

        if output_attentions:
            outputs += (self_attn_weights,)

        if use_cache:
            outputs += (present_key_value,)

        return outputs


QWEN2_START_DOCSTRING = r"""
    This model inherits from [`PreTrainedModel`]. Check the superclass documentation for the generic methods the
    library implements for all its model (such as downloading or saving, resizing the input embeddings, pruning heads
    etc.)

    This model is also a PyTorch [torch.nn.Module](https://pytorch.org/docs/stable/nn.html#torch.nn.Module) subclass.
    Use it as a regular PyTorch Module and refer to the PyTorch documentation for all matter related to general usage
    and behavior.

    Parameters:
        config ([`Qwen2Config`]):
            Model configuration class with all the parameters of the model. Initializing with a config file does not
            load the weights associated with the model, only the configuration. Check out the
            [`~PreTrainedModel.from_pretrained`] method to load the model weights.
"""


@add_start_docstrings(
    "The bare Qwen2 Model outputting raw hidden-states without any specific head on top.",
    QWEN2_START_DOCSTRING,
)
class Qwen2PreTrainedModel(PreTrainedModel):
    config_class = Qwen2Config
    base_model_prefix = "model"
    supports_gradient_checkpointing = True
    _no_split_modules = ["Qwen2DecoderLayer"]
    _skip_keys_device_placement = "past_key_values"
    _supports_flash_attn_2 = True
    _supports_sdpa = True
    _supports_cache_class = True
    _supports_quantized_cache = True
    _supports_static_cache = True

    def _init_weights(self, module):
        std = self.config.initializer_range
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=std)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Embedding):
            module.weight.data.normal_(mean=0.0, std=std)
            if module.padding_idx is not None:
                module.weight.data[module.padding_idx].zero_()


QWEN2_INPUTS_DOCSTRING = r"""
    Args:
        input_ids (`torch.LongTensor` of shape `(batch_size, sequence_length)`):
            Indices of input sequence tokens in the vocabulary. Padding will be ignored by default should you provide
            it.

            Indices can be obtained using [`AutoTokenizer`]. See [`PreTrainedTokenizer.encode`] and
            [`PreTrainedTokenizer.__call__`] for details.

            [What are input IDs?](../glossary#input-ids)
        attention_mask (`torch.Tensor` of shape `(batch_size, sequence_length)`, *optional*):
            Mask to avoid performing attention on padding token indices. Mask values selected in `[0, 1]`:

            - 1 for tokens that are **not masked**,
            - 0 for tokens that are **masked**.

            [What are attention masks?](../glossary#attention-mask)

            Indices can be obtained using [`AutoTokenizer`]. See [`PreTrainedTokenizer.encode`] and
            [`PreTrainedTokenizer.__call__`] for details.

            If `past_key_values` is used, optionally only the last `decoder_input_ids` have to be input (see
            `past_key_values`).

            If you want to change padding behavior, you should read [`modeling_opt._prepare_decoder_attention_mask`]
            and modify to your needs. See diagram 1 in [the paper](https://arxiv.org/abs/1910.13461) for more
            information on the default strategy.

            - 1 indicates the head is **not masked**,
            - 0 indicates the head is **masked**.
        position_ids (`torch.LongTensor` of shape `(batch_size, sequence_length)`, *optional*):
            Indices of positions of each input sequence tokens in the position embeddings. Selected in the range `[0,
            config.n_positions - 1]`.

            [What are position IDs?](../glossary#position-ids)
        past_key_values (`Cache` or `tuple(tuple(torch.FloatTensor))`, *optional*):
            Pre-computed hidden-states (key and values in the self-attention blocks and in the cross-attention
            blocks) that can be used to speed up sequential decoding. This typically consists in the `past_key_values`
            returned by the model at a previous stage of decoding, when `use_cache=True` or `config.use_cache=True`.

            Two formats are allowed:
            - a [`~cache_utils.Cache`] instance, see our
            [kv cache guide](https://huggingface.co/docs/transformers/en/kv_cache);
            - Tuple of `tuple(torch.FloatTensor)` of length `config.n_layers`, with each tuple having 2 tensors of
            shape `(batch_size, num_heads, sequence_length, embed_size_per_head)`). This is also known as the legacy
            cache format.

            The model will output the same cache format that is fed as input. If no `past_key_values` are passed, the
            legacy cache format will be returned.

            If `past_key_values` are used, the user can optionally input only the last `input_ids` (those that don't
            have their past key value states given to this model) of shape `(batch_size, 1)` instead of all `input_ids`
            of shape `(batch_size, sequence_length)`.
        inputs_embeds (`torch.FloatTensor` of shape `(batch_size, sequence_length, hidden_size)`, *optional*):
            Optionally, instead of passing `input_ids` you can choose to directly pass an embedded representation. This
            is useful if you want more control over how to convert `input_ids` indices into associated vectors than the
            model's internal embedding lookup matrix.
        use_cache (`bool`, *optional*):
            If set to `True`, `past_key_values` key value states are returned and can be used to speed up decoding (see
            `past_key_values`).
        output_attentions (`bool`, *optional*):
            Whether or not to return the attentions tensors of all attention layers. See `attentions` under returned
            tensors for more detail.
        output_hidden_states (`bool`, *optional*):
            Whether or not to return the hidden states of all layers. See `hidden_states` under returned tensors for
            more detail.
        return_dict (`bool`, *optional*):
            Whether or not to return a [`~utils.ModelOutput`] instead of a plain tuple.
        cache_position (`torch.LongTensor` of shape `(sequence_length)`, *optional*):
            Indices depicting the position of the input sequence tokens in the sequence. Contrarily to `position_ids`,
            this tensor is not affected by padding. It is used to update the cache in the correct position and to infer
            the complete sequence length.
"""


@add_start_docstrings(
    "The bare Qwen2 Model outputting raw hidden-states without any specific head on top.",
    QWEN2_START_DOCSTRING,
)
class Qwen2Model(Qwen2PreTrainedModel):
    """
    Transformer decoder consisting of *config.num_hidden_layers* layers. Each layer is a [`Qwen2DecoderLayer`]

    Args:
        config: Qwen2Config
    """

    def __init__(self, config: Qwen2Config):
        super().__init__(config)
        self.padding_idx = config.pad_token_id
        self.vocab_size = config.vocab_size

        self.embed_tokens = nn.Embedding(config.vocab_size, config.hidden_size, self.padding_idx)
        self.layers = nn.ModuleList(
            [Qwen2DecoderLayer(config, layer_idx) for layer_idx in range(config.num_hidden_layers)]
        )
        self._attn_implementation = config._attn_implementation
        self.norm = Qwen2RMSNorm(config.hidden_size, eps=config.rms_norm_eps)
        self.rotary_emb = Qwen2RotaryEmbedding(config=config)

        self.gradient_checkpointing = False
        # Initialize weights and apply final processing
        self.post_init()

    def get_input_embeddings(self):
        return self.embed_tokens

    def set_input_embeddings(self, value):
        self.embed_tokens = value

    @add_start_docstrings_to_model_forward(QWEN2_INPUTS_DOCSTRING)
    def forward(
        self,
        input_ids: torch.LongTensor = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        cache_position: Optional[torch.LongTensor] = None,
    ) -> Union[Tuple, BaseModelOutputWithPast]:
        output_attentions = output_attentions if output_attentions is not None else self.config.output_attentions
        output_hidden_states = (
            output_hidden_states if output_hidden_states is not None else self.config.output_hidden_states
        )
        use_cache = use_cache if use_cache is not None else self.config.use_cache

        return_dict = return_dict if return_dict is not None else self.config.use_return_dict

        if (input_ids is None) ^ (inputs_embeds is not None):
            raise ValueError("You must specify exactly one of input_ids or inputs_embeds")

        # TODO: use_cache should be false during inference
        if self.gradient_checkpointing and self.training:
            if use_cache:
                logger.warning_once(
                    "`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`..."
                )
                use_cache = False

        # kept for BC (non `Cache` `past_key_values` inputs)
        return_legacy_cache = False
        if use_cache and not isinstance(past_key_values, Cache):
            return_legacy_cache = True
            if past_key_values is None:
                past_key_values = DynamicCache()
            else:
                past_key_values = DynamicCache.from_legacy_cache(past_key_values)
                logger.warning_once(
                    "We detected that you are passing `past_key_values` as a tuple of tuples. This is deprecated and "
                    "will be removed in v4.47. Please convert your cache or use an appropriate `Cache` class "
                    "(https://huggingface.co/docs/transformers/kv_cache#legacy-cache-format)"
                )

        if inputs_embeds is None:
            inputs_embeds = self.embed_tokens(input_ids)

        if cache_position is None:
            past_seen_tokens = past_key_values.get_seq_length() if past_key_values is not None else 0
            cache_position = torch.arange(
                past_seen_tokens, past_seen_tokens + inputs_embeds.shape[1], device=inputs_embeds.device
            )
        if position_ids is None:
            position_ids = cache_position.unsqueeze(0)

        # use_cache = False, past_key_values = None
        causal_mask = self._update_causal_mask(
            attention_mask, inputs_embeds, cache_position, past_key_values, output_attentions
        )

        hidden_states = inputs_embeds

        # create position embeddings to be shared across the decoder layers
        position_embeddings = self.rotary_emb(hidden_states, position_ids)

        # decoder layers
        all_hidden_states = () if output_hidden_states else None
        all_self_attns = () if output_attentions else None
        next_decoder_cache = None

        for decoder_layer in self.layers:
            if output_hidden_states:
                all_hidden_states += (hidden_states,)

            if self.gradient_checkpointing and self.training:
                layer_outputs = self._gradient_checkpointing_func(
                    decoder_layer.__call__,
                    hidden_states,
                    causal_mask,
                    position_ids,
                    past_key_values,
                    output_attentions,
                    use_cache,
                    cache_position,
                    position_embeddings,
                )
            else:
                layer_outputs = decoder_layer(
                    hidden_states,
                    attention_mask=causal_mask,
                    position_ids=position_ids,
                    past_key_value=past_key_values,
                    output_attentions=output_attentions,
                    use_cache=use_cache,
                    cache_position=cache_position,
                    position_embeddings=position_embeddings,
                )

            hidden_states = layer_outputs[0]

            if use_cache:
                next_decoder_cache = layer_outputs[2 if output_attentions else 1]

            if output_attentions:
                all_self_attns += (layer_outputs[1],)

        hidden_states = self.norm(hidden_states)

        # add hidden states from the last decoder layer
        if output_hidden_states:
            all_hidden_states += (hidden_states,)

        next_cache = next_decoder_cache if use_cache else None
        if return_legacy_cache:
            next_cache = next_cache.to_legacy_cache()

        if not return_dict:
            return tuple(v for v in [hidden_states, next_cache, all_hidden_states, all_self_attns] if v is not None)
        return BaseModelOutputWithPast(
            last_hidden_state=hidden_states,
            past_key_values=next_cache,
            hidden_states=all_hidden_states,
            attentions=all_self_attns,
        )

    # Copied from transformers.models.phi3.modeling_phi3.Phi3Model._update_causal_mask
    def _update_causal_mask(
        self,
        attention_mask: torch.Tensor,
        input_tensor: torch.Tensor,
        cache_position: torch.Tensor,
        past_key_values: Cache,
        output_attentions: bool,
    ):
        if self.config._attn_implementation == "flash_attention_2":
            if attention_mask is not None and 0.0 in attention_mask:
                return attention_mask
            return None

        # For SDPA, when possible, we will rely on its `is_causal` argument instead of its `attn_mask` argument, in
        # order to dispatch on Flash Attention 2. This feature is not compatible with static cache, as SDPA will fail
        # to infer the attention mask.
        past_seen_tokens = past_key_values.get_seq_length() if past_key_values is not None else 0
        using_static_cache = isinstance(past_key_values, StaticCache)
        using_sliding_window_cache = isinstance(past_key_values, SlidingWindowCache)

        # When output attentions is True, sdpa implementation's forward method calls the eager implementation's forward
        if (
            self.config._attn_implementation == "sdpa"
            and not (using_static_cache or using_sliding_window_cache)
            and not output_attentions
        ):
            if AttentionMaskConverter._ignore_causal_mask_sdpa(
                attention_mask,
                inputs_embeds=input_tensor,
                past_key_values_length=past_seen_tokens,
                sliding_window=self.config.sliding_window,
                is_training=self.training,
            ):
                return None

        dtype, device = input_tensor.dtype, input_tensor.device
        min_dtype = torch.finfo(dtype).min
        sequence_length = input_tensor.shape[1]
        # SlidingWindowCache or StaticCache
        if using_sliding_window_cache or using_static_cache:
            target_length = past_key_values.get_max_cache_shape()
        # DynamicCache or no cache
        else:
            target_length = (
                attention_mask.shape[-1]
                if isinstance(attention_mask, torch.Tensor)
                else past_seen_tokens + sequence_length + 1
            )

        # In case the provided `attention` mask is 2D, we generate a causal mask here (4D).
        if attention_mask is None or attention_mask.dim() == 2:
            causal_mask = self._prepare_4d_causal_attention_mask_with_cache_position(
                attention_mask,
                sequence_length=sequence_length,
                target_length=target_length,
                dtype=dtype,
                device=device,
                cache_position=cache_position,
                batch_size=input_tensor.shape[0],
                config=self.config,
                past_key_values=past_key_values,
            )
        elif attention_mask.dim() == 3:
            causal_mask = AttentionMaskConverter(is_causal=False)._3d_to_4d(
                attention_mask_3d = attention_mask,
                query_length = sequence_length,
                dtype = dtype,
                key_value_length = target_length,
            )
        

        if (
            self.config._attn_implementation == "sdpa"
            and attention_mask is not None
            and attention_mask.device.type == "cuda"
            and not output_attentions
        ):
            # Attend to all tokens in fully masked rows in the causal_mask, for example the relevant first rows when
            # using left padding. This is required by F.scaled_dot_product_attention memory-efficient attention path.
            # Details: https://github.com/pytorch/pytorch/issues/110213
            causal_mask = AttentionMaskConverter._unmask_unattended(causal_mask, min_dtype)

        return causal_mask

    @staticmethod
    # Copied from transformers.models.mistral.modeling_mistral.MistralModel._prepare_4d_causal_attention_mask_with_cache_position with Mistral->Qwen2
    def _prepare_4d_causal_attention_mask_with_cache_position(
        attention_mask: torch.Tensor,
        sequence_length: int,
        target_length: int,
        dtype: torch.dtype,
        device: torch.device,
        cache_position: torch.Tensor,
        batch_size: int,
        config: Qwen2Config,
        past_key_values: Cache,
    ):
        """
        Creates a causal 4D mask of shape `(batch_size, 1, query_length, key_value_length)` from a 2D mask of shape
        `(batch_size, key_value_length)`, or if the input `attention_mask` is already 4D, do nothing.

        Args:
            attention_mask (`torch.Tensor`):
                A 2D attention mask of shape `(batch_size, key_value_length)` or a 4D attention mask of shape `(batch_size, 1, query_length, key_value_length)`.
            sequence_length (`int`):
                The sequence length being processed.
            target_length (`int`):
                The target length: when generating with static cache, the mask should be as long as the static cache, to account for the 0 padding, the part of the cache that is not filled yet.
            dtype (`torch.dtype`):
                The dtype to use for the 4D attention mask.
            device (`torch.device`):
                The device to plcae the 4D attention mask on.
            cache_position (`torch.Tensor`):
                Indices depicting the position of the input sequence tokens in the sequence.
            batch_size (`torch.Tensor`):
                Batch size.
            config (`Qwen2Config`):
                The model's configuration class
            past_key_values (`Cache`):
                The cache class that is being used currently to generate
        """
        if attention_mask is not None and attention_mask.dim() == 4:
            # In this case we assume that the mask comes already in inverted form and requires no inversion or slicing.
            causal_mask = attention_mask
        else:
            min_dtype = torch.finfo(dtype).min
            causal_mask = torch.full(
                (sequence_length, target_length), fill_value=min_dtype, dtype=dtype, device=device
            )
            diagonal_attend_mask = torch.arange(target_length, device=device) > cache_position.reshape(-1, 1)
            if config.sliding_window is not None:
                # if we have sliding window, we should not attend to tokens beyond sliding window length, so we mask them out also
                # the check is needed to verify is current checkpoint was trained with sliding window or not
                if not isinstance(past_key_values, SlidingWindowCache) or sequence_length > target_length:
                    sliding_attend_mask = torch.arange(target_length, device=device) <= (
                        cache_position.reshape(-1, 1) - config.sliding_window
                    )
                    diagonal_attend_mask.bitwise_or_(sliding_attend_mask)
            causal_mask *= diagonal_attend_mask
            causal_mask = causal_mask[None, None, :, :].expand(batch_size, 1, -1, -1)
            if attention_mask is not None:
                causal_mask = causal_mask.clone()  # copy to contiguous memory for in-place edit
                if attention_mask.shape[-1] > target_length:
                    attention_mask = attention_mask[:, :target_length]
                mask_length = attention_mask.shape[-1]
                padding_mask = causal_mask[:, :, :, :mask_length] + attention_mask[:, None, None, :]
                padding_mask = padding_mask == 0
                causal_mask[:, :, :, :mask_length] = causal_mask[:, :, :, :mask_length].masked_fill(
                    padding_mask, min_dtype
                )
        return causal_mask


class Qwen2ForCausalLM(Qwen2PreTrainedModel, GenerationMixin):
    _tied_weights_keys = ["lm_head.weight"]

    def __init__(self, config):
        super().__init__(config)
        self.model = Qwen2Model(config)
        self.vocab_size = config.vocab_size
        self.lm_head = nn.Linear(config.hidden_size, config.vocab_size, bias=False)

        # Initialize weights and apply final processing
        self.post_init()

    def get_input_embeddings(self):
        return self.model.embed_tokens

    def set_input_embeddings(self, value):
        self.model.embed_tokens = value

    def get_output_embeddings(self):
        return self.lm_head

    def set_output_embeddings(self, new_embeddings):
        self.lm_head = new_embeddings

    def set_decoder(self, decoder):
        self.model = decoder

    def get_decoder(self):
        return self.model

    @add_start_docstrings_to_model_forward(QWEN2_INPUTS_DOCSTRING)
    @replace_return_docstrings(output_type=CausalLMOutputWithPast, config_class=_CONFIG_FOR_DOC)
    def forward(
        self,
        input_ids: torch.LongTensor = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        cache_position: Optional[torch.LongTensor] = None,
        num_logits_to_keep: int = 0,
        **loss_kwargs,
    ) -> Union[Tuple, CausalLMOutputWithPast]:
        r"""
        Args:
            labels (`torch.LongTensor` of shape `(batch_size, sequence_length)`, *optional*):
                Labels for computing the masked language modeling loss. Indices should either be in `[0, ...,
                config.vocab_size]` or -100 (see `input_ids` docstring). Tokens with indices set to `-100` are ignored
                (masked), the loss is only computed for the tokens with labels in `[0, ..., config.vocab_size]`.

            num_logits_to_keep (`int`, *optional*):
                Calculate logits for the last `num_logits_to_keep` tokens. If `0`, calculate logits for all
                `input_ids` (special case). Only last token logits are needed for generation, and calculating them only for that
                token can save memory, which becomes pretty significant for long sequences or large vocabulary size.

        Returns:

        Example:

        ```python
        >>> from transformers import AutoTokenizer, Qwen2ForCausalLM

        >>> model = Qwen2ForCausalLM.from_pretrained(PATH_TO_CONVERTED_WEIGHTS)
        >>> tokenizer = AutoTokenizer.from_pretrained(PATH_TO_CONVERTED_TOKENIZER)

        >>> prompt = "Hey, are you conscious? Can you talk to me?"
        >>> inputs = tokenizer(prompt, return_tensors="pt")

        >>> # Generate
        >>> generate_ids = model.generate(inputs.input_ids, max_length=30)
        >>> tokenizer.batch_decode(generate_ids, skip_special_tokens=True, clean_up_tokenization_spaces=False)[0]
        "Hey, are you conscious? Can you talk to me?\nI'm not conscious, but I can talk to you."
        ```"""

        output_attentions = output_attentions if output_attentions is not None else self.config.output_attentions
        output_hidden_states = (
            output_hidden_states if output_hidden_states is not None else self.config.output_hidden_states
        )
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict

        # decoder outputs consists of (dec_features, layer_state, dec_hidden, dec_attn)
        outputs = self.model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_values=past_key_values,
            inputs_embeds=inputs_embeds,
            use_cache=use_cache,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
            cache_position=cache_position,
        )

        hidden_states = outputs[0]
        # Only compute necessary logits, and do not upcast them to float if we are not computing the loss
        logits = self.lm_head(hidden_states[:, -num_logits_to_keep:, :])

        loss = None
        if labels is not None:
            loss = self.loss_function(logits, labels, self.vocab_size, **loss_kwargs)

        if not return_dict:
            output = (logits,) + outputs[1:]
            return (loss,) + output if loss is not None else output

        return CausalLMOutputWithPast(
            loss=loss,
            logits=logits,
            past_key_values=outputs.past_key_values,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions,
        )


@add_start_docstrings(
    """
    The Qwen2 Model transformer with a sequence classification head on top (linear layer).

    [`Qwen2ForSequenceClassification`] uses the last token in order to do the classification, as other causal models
    (e.g. GPT-2) do.

    Since it does classification on the last token, it requires to know the position of the last token. If a
    `pad_token_id` is defined in the configuration, it finds the last token that is not a padding token in each row. If
    no `pad_token_id` is defined, it simply takes the last value in each row of the batch. Since it cannot guess the
    padding tokens when `inputs_embeds` are passed instead of `input_ids`, it does the same (take the last value in
    each row of the batch).
    """,
    QWEN2_START_DOCSTRING,
)
class Qwen2ForSequenceClassification(Qwen2PreTrainedModel):
    def __init__(self, config):
        super().__init__(config)
        self.num_labels = config.num_labels
        self.model = Qwen2Model(config)
        self.score = nn.Linear(config.hidden_size, self.num_labels, bias=False)

        # Initialize weights and apply final processing
        self.post_init()

    def get_input_embeddings(self):
        return self.model.embed_tokens

    def set_input_embeddings(self, value):
        self.model.embed_tokens = value

    @add_start_docstrings_to_model_forward(QWEN2_INPUTS_DOCSTRING)
    def forward(
        self,
        input_ids: torch.LongTensor = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
    ) -> Union[Tuple, SequenceClassifierOutputWithPast]:
        r"""
        labels (`torch.LongTensor` of shape `(batch_size,)`, *optional*):
            Labels for computing the sequence classification/regression loss. Indices should be in `[0, ...,
            config.num_labels - 1]`. If `config.num_labels == 1` a regression loss is computed (Mean-Square loss), If
            `config.num_labels > 1` a classification loss is computed (Cross-Entropy).
        """
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict

        transformer_outputs = self.model(
            input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_values=past_key_values,
            inputs_embeds=inputs_embeds,
            use_cache=use_cache,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )
        hidden_states = transformer_outputs[0]
        logits = self.score(hidden_states)

        if input_ids is not None:
            batch_size = input_ids.shape[0]
        else:
            batch_size = inputs_embeds.shape[0]

        if self.config.pad_token_id is None and batch_size != 1:
            raise ValueError("Cannot handle batch sizes > 1 if no padding token is defined.")
        if self.config.pad_token_id is None:
            sequence_lengths = -1
        else:
            if input_ids is not None:
                # if no pad token found, use modulo instead of reverse indexing for ONNX compatibility
                sequence_lengths = torch.eq(input_ids, self.config.pad_token_id).int().argmax(-1) - 1
                sequence_lengths = sequence_lengths % input_ids.shape[-1]
                sequence_lengths = sequence_lengths.to(logits.device)
            else:
                sequence_lengths = -1

        pooled_logits = logits[torch.arange(batch_size, device=logits.device), sequence_lengths]

        loss = None
        if labels is not None:
            labels = labels.to(logits.device)
            if self.config.problem_type is None:
                if self.num_labels == 1:
                    self.config.problem_type = "regression"
                elif self.num_labels > 1 and (labels.dtype == torch.long or labels.dtype == torch.int):
                    self.config.problem_type = "single_label_classification"
                else:
                    self.config.problem_type = "multi_label_classification"

            if self.config.problem_type == "regression":
                loss_fct = MSELoss()
                if self.num_labels == 1:
                    loss = loss_fct(pooled_logits.squeeze(), labels.squeeze())
                else:
                    loss = loss_fct(pooled_logits, labels)
            elif self.config.problem_type == "single_label_classification":
                loss_fct = CrossEntropyLoss()
                loss = loss_fct(pooled_logits.view(-1, self.num_labels), labels.view(-1))
            elif self.config.problem_type == "multi_label_classification":
                loss_fct = BCEWithLogitsLoss()
                loss = loss_fct(pooled_logits, labels)
        if not return_dict:
            output = (pooled_logits,) + transformer_outputs[1:]
            return ((loss,) + output) if loss is not None else output

        return SequenceClassifierOutputWithPast(
            loss=loss,
            logits=pooled_logits,
            past_key_values=transformer_outputs.past_key_values,
            hidden_states=transformer_outputs.hidden_states,
            attentions=transformer_outputs.attentions,
        )


@add_start_docstrings(
    """
    The Qwen2 Model transformer with a token classification head on top (a linear layer on top of the hidden-states
    output) e.g. for Named-Entity-Recognition (NER) tasks.
    """,
    QWEN2_START_DOCSTRING,
)
# Copied from transformers.models.llama.modeling_llama.LlamaForTokenClassification with Llama->Qwen2, LLAMA->QWEN2
class Qwen2ForTokenClassification(Qwen2PreTrainedModel):
    def __init__(self, config):
        super().__init__(config)
        self.num_labels = config.num_labels
        self.model = Qwen2Model(config)
        if getattr(config, "classifier_dropout", None) is not None:
            classifier_dropout = config.classifier_dropout
        elif getattr(config, "hidden_dropout", None) is not None:
            classifier_dropout = config.hidden_dropout
        else:
            classifier_dropout = 0.1
        self.dropout = nn.Dropout(classifier_dropout)
        self.score = nn.Linear(config.hidden_size, config.num_labels)

        # Initialize weights and apply final processing
        self.post_init()

    def get_input_embeddings(self):
        return self.model.embed_tokens

    def set_input_embeddings(self, value):
        self.model.embed_tokens = value

    @add_start_docstrings_to_model_forward(QWEN2_INPUTS_DOCSTRING)
    @add_code_sample_docstrings(
        checkpoint=_CHECKPOINT_FOR_DOC,
        output_type=TokenClassifierOutput,
        config_class=_CONFIG_FOR_DOC,
    )
    def forward(
        self,
        input_ids: Optional[torch.LongTensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
    ) -> Union[Tuple, TokenClassifierOutput]:
        r"""
        labels (`torch.LongTensor` of shape `(batch_size,)`, *optional*):
            Labels for computing the sequence classification/regression loss. Indices should be in `[0, ...,
            config.num_labels - 1]`. If `config.num_labels == 1` a regression loss is computed (Mean-Square loss), If
            `config.num_labels > 1` a classification loss is computed (Cross-Entropy).
        """
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict

        outputs = self.model(
            input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_values=past_key_values,
            inputs_embeds=inputs_embeds,
            use_cache=use_cache,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )
        sequence_output = outputs[0]
        sequence_output = self.dropout(sequence_output)
        logits = self.score(sequence_output)

        loss = None
        if labels is not None:
            loss = self.loss_function(logits, labels, self.config)

        if not return_dict:
            output = (logits,) + outputs[2:]
            return ((loss,) + output) if loss is not None else output

        return TokenClassifierOutput(
            loss=loss,
            logits=logits,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions,
        )


@add_start_docstrings(
    """
The Qwen2 Model transformer with a span classification head on top for extractive question-answering tasks like
SQuAD (a linear layer on top of the hidden-states output to compute `span start logits` and `span end logits`).
    """,
    QWEN2_START_DOCSTRING,
)
# Copied from transformers.models.mistral.modeling_mistral.MistralForQuestionAnswering with Mistral->Qwen2, MISTRAL->QWEN2
class Qwen2ForQuestionAnswering(Qwen2PreTrainedModel):
    base_model_prefix = "model"

    # Copied from models.models.bloom.modeling_bloom.BloomForQuestionAnswering.__init__ with Bloom->Qwen2
    def __init__(self, config):
        super().__init__(config)
        self.model = Qwen2Model(config)
        self.qa_outputs = nn.Linear(config.hidden_size, 2)

        # Initialize weights and apply final processing
        self.post_init()

    def get_input_embeddings(self):
        return self.model.embed_tokens

    def set_input_embeddings(self, value):
        self.model.embed_tokens = value

    @add_start_docstrings_to_model_forward(QWEN2_INPUTS_DOCSTRING)
    def forward(
        self,
        input_ids: Optional[torch.LongTensor] = None,
        attention_mask: Optional[torch.FloatTensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[Union[Cache, List[torch.FloatTensor]]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        start_positions: Optional[torch.LongTensor] = None,
        end_positions: Optional[torch.LongTensor] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        **kwargs,
    ) -> Union[Tuple, QuestionAnsweringModelOutput]:
        r"""
        start_positions (`torch.LongTensor` of shape `(batch_size,)`, *optional*):
            Labels for position (index) of the start of the labelled span for computing the token classification loss.
            Positions are clamped to the length of the sequence (`sequence_length`). Position outside of the sequence
            are not taken into account for computing the loss.
        end_positions (`torch.LongTensor` of shape `(batch_size,)`, *optional*):
            Labels for position (index) of the end of the labelled span for computing the token classification loss.
            Positions are clamped to the length of the sequence (`sequence_length`). Position outside of the sequence
            are not taken into account for computing the loss.
        """
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict

        outputs = self.model(
            input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_values=past_key_values,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )

        sequence_output = outputs[0]

        logits = self.qa_outputs(sequence_output)
        start_logits, end_logits = logits.split(1, dim=-1)
        start_logits = start_logits.squeeze(-1).contiguous()
        end_logits = end_logits.squeeze(-1).contiguous()

        loss = None
        if start_positions is not None and end_positions is not None:
            loss = self.loss_function(start_logits, end_logits, start_positions, end_positions, **kwargs)

        if not return_dict:
            output = (start_logits, end_logits) + outputs[2:]
            return ((loss,) + output) if loss is not None else output

        return QuestionAnsweringModelOutput(
            loss=loss,
            start_logits=start_logits,
            end_logits=end_logits,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions,
        )


================================================
FILE: cgm/train/train.py
================================================
import os
import sys

sys.path.append(os.getcwd())

import time, os, json, math, logging
import numpy as np
import torch
import random
from torch.utils.data import DataLoader, random_split, Subset
import random
# from deepspeed.ops.adam import FusedAdam as AdamW
from torch.optim import AdamW
from transformers import AutoModel, AutoTokenizer
from accelerate import Accelerator
from accelerate.logging import get_logger
from transformers import (
    set_seed,
    get_scheduler,
)
from utils.arguments import prepare_args

from modeling.cgm import CGM

from data.encode import CGMEncoder

from utils.common_utils import print_args, print_with_rank, print_rank_0
from utils.train_utils import accelerate_train_CGM

from datasets import load_dataset
import datetime
from peft import (
    LoraConfig,
    TaskType,
    get_peft_model,
    prepare_model_for_kbit_training,
    PeftModel,
)

from torch.optim.lr_scheduler import ReduceLROnPlateau


# from load_hetero_dataset import load_dataset, perpare_dataloader

def str_to_tuple(s):
    st = s.strip('()')
    return tuple(item.strip().strip("'") for item in st.split(','))

def getRawGraph(filename, suffix="json"):
    if os.path.exists(filename):
        if suffix == 'json':
            with open(filename) as f:
                example_graph = json.load(f)
            f.close()
        elif suffix == 'pt':
            with open(filename, 'rb') as f:
                example_graph = torch.load(f)
                # example_graph = torch.load(filename)
            f.close()
        return example_graph
    return None

task_ids = {
    (0, 'graph_query'),
    (1, 'api'),
    (2, 'issue_fix'),
    (3, 'unit_test'),
    (4, 'readme_summary'),
}

task_to_id = {task: idx for idx, task in task_ids}

def collate_cgm(graph_dir, encoder, qa_type='mft', seq_l=8192, use_chat=True):
    def collate(batches):
        result_batches = []
        for batch in batches:
            result_batch = {}
            graph = getRawGraph(batch['repo'], suffix='json')

            if graph is not None:
                graph['reponame'] = batch['repo'].split('/')[-1].split('.')[0]
                graph['language'] = batch['language']
                result_batch['graph'] = graph
                if use_chat:
                    features = encoder.dataToInput(batch)
                    input_ids = features['input_ids']
                    loss_mask = features['loss_mask']
                    qa_mask = features['qa_mask']
                else:
                    query_ids = encoder.tokenizer.encode(batch['prompt'], add_special_tokens=False)
                    answer_ids = encoder.tokenizer.encode(batch['answer'], add_special_tokens=False) + [
                        encoder.tokenizer.eos_token_id]
                    qa_mask = [1] * len(query_ids) + [0] * len(answer_ids)
                    loss_mask = [0] * len(query_ids) + [1] * len(answer_ids)
                    input_ids = query_ids + answer_ids

                min_seq = min(seq_l, len(input_ids))
                result_batch['x'] = torch.tensor(input_ids, dtype=torch.int64)[:min_seq - 1].contiguous()
                result_batch['qa_mask'] = torch.tensor(qa_mask, dtype=torch.bool)[:min_seq - 1].contiguous()
                result_batch['y'] = torch.tensor(input_ids, dtype=torch.int64)[1:min_seq].contiguous()
                result_batch['loss_mask'] = torch.tensor(loss_mask, dtype=torch.bool)[1:min_seq].contiguous()

                if qa_type == 'mft':
                    result_batch['task'] = task_to_id[batch['task']]
            else:
                raise ValueError(f"graph none for {batch['repo']}")

            result_batches.append(result_batch)

        final_result_batch = {}
        for key in result_batches[0].keys():
            if key == 'task':
                final_result_batch[key] = torch.tensor([rb[key] for rb in result_batches])
            elif key == 'graph':
                final_result_batch[key] = [rb[key] for rb in result_batches]
            else:
                final_result_batch[key] = torch.stack([rb[key] for rb in result_batches])
        return final_result_batch

    return collate


def train(args):
    accelerator = Accelerator(gradient_accumulation_steps=args.gradient_accumulation_steps)

    print_args(args, accelerator)

    # prepare logger
    logger = get_logger(__name__)
    logging.basicConfig(
        format="[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        level=logging.INFO,
    )
    logger.info(accelerator.state, main_process_only=True)

    train_files = args.train_files
    valid_files = args.valid_files

    graph_dir = args.graph_dir

    dataset = load_dataset('json', data_files={'train': train_files, 'valid': valid_files})

    train_dataset = dataset['train']
    valid_dataset = dataset['valid']

    epoch_train = len(train_dataset)

    if args.peft:
        save_suffix = args.framework_type + '_' + str(
            args.pretrained_model_path.split('/')[-1]) + '_' + args.task + '_M' + args.mode + '_LR' + str(
            args.learning_rate) + '_GA' + str(args.gradient_accumulation_steps) + '_' + str(args.peft) + '_r' + str(
            args.lora_rank) + '_alpha' + str(args.lora_alpha) + '_d' + str(args.lora_dropout) + '_m' + str(
            args.lora_modules) + str(datetime.datetime.now().strftime('%Y%m%d%H')) + '/'
    else:
        save_suffix = args.framework_type + '_' + str(
            args.pretrained_model_path.split('/')[-1]) + '_' + args.task + '_M' + args.mode + '_LR' + str(
            args.learning_rate) + '_GA' + str(args.gradient_accumulation_steps) + '_' + str(
            datetime.datetime.now().strftime('%Y%m%d%H')) + '/'

    args.output_dir = args.output_dir + save_suffix
    args.tb_dir = args.tb_dir + save_suffix
    if 'l' not in args.mode and args.peft:
        args.mode = args.mode + 'l'
    if args.peft == "QLoRA":
        if not args.quantization:
            args.quantization = "4bit"

    if accelerator.is_main_process:
        if not os.path.exists(args.output_dir):
            os.makedirs(args.output_dir)
        if not os.path.exists(args.tb_dir):
            os.makedirs(args.tb_dir)

    tokenizer = AutoTokenizer.from_pretrained(args.pretrained_tokenizer_path, trust_remote_code=False)

    encoder = CGMEncoder(tokenizer=tokenizer, config_name=args.model_type)

    collate_fn = collate_cgm(
        graph_dir,
        encoder,
        qa_type=args.task,
        seq_l=8192,
        use_chat=args.use_chat,
    )

    train_unit_batch_size = accelerator.num_processes * args.gradient_accumulation_steps * args.per_device_train_batch_size
    total_train_samples = len(train_dataset)
    if args.max_train_samples:
        max_train_samples = args.max_train_samples
        if total_train_samples > max_train_samples:
            total_train_samples = max_train_samples

    max_divisible_samples = (total_train_samples // train_unit_batch_size) * train_unit_batch_size
    subset_indices = list(range(max_divisible_samples))
    train_subset = Subset(train_dataset, subset_indices)
    train_dataloader = DataLoader(train_subset, batch_size=args.per_device_train_batch_size, collate_fn=collate_fn,
                                  shuffle=True)

    if args.max_valid_samples:
        max_valid_samples = args.max_valid_samples
        valid_unit_batch_size = accelerator.num_processes * args.per_device_eval_batch_size
        total_valid_samples = len(valid_dataset)
        if total_valid_samples > max_valid_samples:
            indices = list(range(max_valid_samples))
            random.shuffle(indices)
            subset_indices = indices[:max_valid_samples]
            valid_subset = Subset(valid_dataset, subset_indices)
        else:
            max_divisible_samples = (total_valid_samples // valid_unit_batch_size) * valid_unit_batch_size
            subset_indices = list(range(max_divisible_samples))
            valid_subset = Subset(valid_dataset, subset_indices)
        valid_dataloader = DataLoader(valid_subset, batch_size=args.per_device_eval_batch_size, collate_fn=collate_fn,
                                      shuffle=True)
    else:
        valid_dataloader = DataLoader(valid_dataset, batch_size=args.per_device_eval_batch_size, collate_fn=collate_fn,
                                      shuffle=True)

    logger.info(f"Train Samples: {len(train_dataloader)}", main_process_only=True)
    logger.info(f"Valid Samples: {len(valid_dataloader)}", main_process_only=True)

    model = CGM(args)
    # Please disable checkpointing and re-enable use-cache for inference
    model.lm.gradient_checkpointing_enable()
    model.lm.config.use_cache = False

    if args.peft == "QLoRA":
        model.lm = prepare_model_for_kbit_training(model.lm)  # use_gradient_checkpointing default is True
    else:
        model.lm.gradient_checkpointing_enable()

    if args.peft:
        peft_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=args.lora_rank,
            lora_alpha=args.lora_alpha,
            lora_dropout=args.lora_dropout,
            target_modules=args.lora_modules,
            bias="lora_only",
        )
        model.lm = get_peft_model(model.lm, peft_config)

        encoder_peft_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=args.enc_lora_rank,
            lora_alpha=args.enc_lora_alpha,
            lora_dropout=args.enc_lora_dropout,
            target_modules=args.enc_lora_modules,
            bias="lora_only",
        )
        model.encoder = get_peft_model(model.encoder, encoder_peft_config)

    if args.adapter_warmup:
        if 'l' in args.mode:
            for param in model.lm.parameters():
                param.requires_grad = False

    encoder_params = list(model.encoder.parameters()) if 'e' in args.mode else []
    pma_params = list(model.pma.parameters()) if 'p' in args.mode else []
    adapter_params = list(model.adapter.parameters()) if 'a' in args.mode else []
    lm_params = list(model.lm.parameters()) if 'l' in args.mode else []

    trained_params = encoder_params + pma_params + adapter_params + lm_params
    # trained_params = adapter_params + lm_params
    if not trained_params:
        raise ValueError("No parameters to train. Please check the mode argument.")

    optimizer = AdamW(
        trained_params,
        weight_decay=args.weight_decay,
        lr=args.learning_rate,
        betas=(0.9, 0.95),
    )
    overrode_max_train_steps = False
    num_update_steps_per_epoch = math.ceil(epoch_train / args.gradient_accumulation_steps)
    if args.max_train_steps is None:
        args.max_train_steps = args.num_train_epochs * num_update_steps_per_epoch
        overrode_max_train_steps = True

    if args.lr_scheduler_type == "reduce_lr_on_plateau":
        lr_scheduler = ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.75,
            patience=3,
            threshold=0.0001,
            threshold_mode='rel',
            cooldown=0,
            min_lr=args.min_lr,
            eps=1e-08,
        )
    else:
        lr_scheduler = get_scheduler(
            name=args.lr_scheduler_type,
            optimizer=optimizer,
            num_warmup_steps=args.num_warmup_steps * accelerator.num_processes,
            num_training_steps=args.max_train_steps,
        )

    logger.info(
        f"{'==' * 100}\nbefore accelerator preparation: [dataloader: {epoch_train}][epochs: {args.num_train_epochs}][total steps: {args.max_train_steps}]\n{'==' * 100}")
    if torch.cuda.is_available():
        model, train_dataloader, valid_dataloader, optimizer, lr_scheduler = accelerator.prepare(
            model, train_dataloader, valid_dataloader, optimizer, lr_scheduler
        )

    epoch_train = epoch_train / accelerator.num_processes
    num_update_steps_per_epoch = math.ceil(epoch_train / args.gradient_accumulation_steps)
    if overrode_max_train_steps:
        args.max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    args.num_train_epochs = math.ceil(args.max_train_steps / num_update_steps_per_epoch)
    logger.info(
        f"{'==' * 100}\nafter accelerator preparation: [dataloader: {epoch_train}][epochs: {args.num_train_epochs}][total steps: {args.max_train_steps}]\n{'==' * 100}")

    logger.info(f"{'==' * 100}Training...")

    accelerate_train_CGM(accelerator,
                         model,
                         train_dataloader,
                         valid_dataloader,
                         optimizer,
                         lr_scheduler,
                         tokenizer,
                         epoch_train,
                         args)

if __name__ == "__main__":
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    args = prepare_args()
    set_seed(args.seed)
    train(args)


================================================
FILE: cgm/utils/__init__.py
================================================



================================================
FILE: cgm/utils/arguments.py
================================================
from dataclasses import dataclass, asdict
import argparse, json
from typing import List, Union
import torch


@dataclass
class TrainArgs:
    graph_dir: Union[str, List[str]]
    train_files: Union[str, List[str]]
    valid_files: Union[str, List[str]]
    output_dir: str
    tb_dir: str

    embedding_dim: int = 2304

    load_pretrained_encoder: bool = False
    pretrained_encoder_path: Union[None, str] = None
    load_pretrained_adapter: bool = False
    pretrained_adapter_path: Union[None, str] = None
    adapter_hidden_dim: int = 4096
    adapter_num_layers: int = 1
    adapter_num_heads: int = 8

    self_defined: bool = False
    pretrained_model_path: Union[None, str] = None
    lm_hidden_dim: int = 4096
    quantization: Union[None, str] = None
    framework_type: Union[None, str] = "default"
    model_type: Union[None, str] = None

    load_pretrained_tokenizer: bool = True
    pretrained_tokenizer_path: Union[None, str] = None

    # for evaluation
    pretrained_lora_path: Union[None, str] = None

    # training mode:
    #  "e" 1, "a" 2, "l" 3
    mode: str = "a"
    task: str = "align"
    use_chat: bool = True
    use_adj: bool = False

    # lora rank, the bigger, the more trainalbe parameters
    peft: Union[None, str] = None
    lora_rank: int = 32
    lora_alpha: int = 32
    lora_dropout: float = 0.05
    lora_modules: Union[str, List[str]] = "all-linear"

    enc_peft: Union[None, str] = None
    enc_lora_rank: int = 32
    enc_lora_alpha: int = 32
    enc_lora_dropout: float = 0.05
    enc_lora_modules: Union[str, List[str]] = "all-linear"

    graph_pad_token: str = "<｜graph_pad｜>"
    graph_pad_id: int = 32022
    graph_token_num: int = 512

    learning_rate: float = 5e-5
    min_lr: float = 5e-6
    weight_decay: float = 0.1
    lr_scheduler_type: str = "cosine"

    gradient_accumulation_steps: int = 1
    num_warmup_steps: int = 300
    adapter_warmup: bool = False
    adapter_warmup_steps: int = 500
    num_train_epochs: int = 2

    # train/valid split
    data_split: str = "0.98,0.02"
    max_train_samples: Union[None, int] = None
    max_valid_samples: Union[None, int] = None

    per_device_train_batch_size: int = 1
    per_device_eval_batch_size: int = 1

    seed: int = 42

    seq_length: int = 4096
    log_interval: int = 10
    step_checkpointing: bool = False
    checkpointing_steps: int = 100

    step_evaluation: bool = False
    evaluation_steps: int = 100

    # max train steps, if None, depends on num_train_epochs
    max_train_steps: Union[None, int] = None

    # if checkpointing every epoch, maybe True in sst
    epoch_checkpointing: bool = False
    epoch_evaluation: bool = False

    early_stopping: bool = False
    early_stopping_stall_num: int = 5

    attn_implementation: str = "flash_attention_2"

    def dict(self):
        return {k: str(v) for k, v in asdict(self).items()}


def prepare_args(args_type="Train"):
    parser = argparse.ArgumentParser()
    parser.add_argument("--c", type=str, default=None)
    parsed = parser.parse_args()
    with open(parsed.c, 'r') as f:
        c = json.load(f)
    if args_type == "Train":
        args = TrainArgs(**c)
    else:
        raise ValueError("args_type must be Train")
    if not torch.cuda.is_available():
        args.attn_implementation = 'eager'

    return args



================================================
FILE: cgm/utils/common_utils.py
================================================
import torch

# print out arguments in a nice way
def print_args(args, accelerator):
    # 计算所有键的最大字符串长度
    max_key_length = max(len(str(key)) for key in vars(args).keys())
    
    message = ""
    message += "====" * 40 + "\n"
    message += '\n'.join([f'{k:<{max_key_length}} : {v}' for k, v in vars(args).items()]) + "\n"
    message += "====" * 40 + "\n"
    accelerator.print(message)

def count_parameters(model):
    return sum(p.numel() for p in model.parameters())

def print_with_rank(accelerator, msg):
    print(accelerator.process_index, msg)

def print_rank_0(*message):
    """If distributed is initialized print only on rank 0."""
    if torch.distributed.is_initialized():
        if torch.distributed.get_rank() == 0:
            print(*message, flush=True)
    else:
        print(*message, flush=True)

def print_rank_0_highlight(*message):
    """If distributed is initialized print only on rank 0."""
    if torch.distributed.is_initialized():
        if torch.distributed.get_rank() == 0:
            print('=='*100)
            print(*message, flush=True)
            print('=='*100)
    else:
        print('=='*100)
        print(*message, flush=True)
        print('=='*100)

def print_highlight(*message):
    print('=='*100)
    print(*message)
    print('=='*100)

def get_computation_speed(batch_size_per_device, seq_len, step_time):
    return batch_size_per_device * seq_len / (step_time + 1e-12)

def touch_print(accelerator, batch, num_tokens=10):
    """touch first and last tokens and labels for debugging usage"""
    accelerator.print(f"step 1 batch shape: {batch['input_ids'].shape},\n"
                      f"last {num_tokens} labels: {batch['labels'][:, -num_tokens:]}"
                      f"last {num_tokens} loss mask: {batch['loss_mask'][:, -num_tokens:]}")
    accelerator.print(f"first {num_tokens} input_ids and loss_mask")
    for pt in range(1):
        accelerator.print(f"{batch['input_ids'][:, num_tokens * pt: num_tokens * pt + num_tokens]}")
        accelerator.print(f"{batch['loss_mask'][:, num_tokens * pt: num_tokens * pt + num_tokens]}")


================================================
FILE: cgm/utils/loss.py
================================================
import torch
from torch.nn import CrossEntropyLoss, MSELoss, BCEWithLogitsLoss

def loss_CGM(output_logits, labels, loss_mask):

    lm_logits = output_logits.contiguous()
    labels = labels.to(device=lm_logits.device).contiguous()
    loss_mask = loss_mask.to(device=lm_logits.device)
    # logits: (bs, l, v); labels, loss_mask: (bs, l)

    # lm loss
    bsz = labels.shape[0]
    loss_func = CrossEntropyLoss(reduction='none')
    losses = loss_func(lm_logits.view(-1, lm_logits.shape[-1]), labels.view(-1))  # logits: (bs * l, v); labels: (bs * l,)
    # losses -> (bs, l)
    losses = losses.contiguous().view(bsz, -1)

    loss_mask = loss_mask.view(-1)
    losses = losses.view(-1)
    if loss_mask.sum() < 1:
        loss_lm = torch.sum(losses * loss_mask)
    else:
        loss_lm = torch.sum(losses * loss_mask) / loss_mask.sum()

    return loss_lm

def acc_lp(logits,labels):
    predictions = torch.sigmoid(logits)
    acc = ((predictions > 0.5) == labels.bool()).float().mean()
    return acc.item()

def loss_lp(outputs, edge_label_dict):
    loss_func = BCEWithLogitsLoss(reduction='mean')
    losses = []
    edge_loss = {}
    edge_acc = {}
    total_acc = 0
    total_edges = 0
    for edge_type in edge_label_dict.keys():
        lm_logits = outputs[edge_type].view(-1)
        labels = edge_label_dict[edge_type].to(device=lm_logits.device).view(-1)
        loss = loss_func(lm_logits,labels)
        losses.append(loss)
        acc = acc_lp(lm_logits, labels)
        edge_loss[edge_type] = loss.item()
        edge_acc[edge_type] = acc
        total_acc += len(labels) * acc
        total_edges += len(labels)
        # del lm_logits, labels, loss
    loss1 = torch.sum(torch.stack(losses))
    total_acc = total_acc / total_edges
    # del losses, loss_func
    return loss1, edge_loss, edge_acc, total_acc

def loss_ng(outputs, y_dict, mask_dict):
    loss_func = MSELoss(reduction='sum')
    loss2 = loss_func(outputs,y_dict['Method'])
    return loss2

def loss_lpng(lp_outputs, ng_outputs, edge_label_dict, y_dict, mask_dict):
    loss1, edge_loss, edge_acc, total_acc = loss_lp(lp_outputs, edge_label_dict)
    loss2 = loss_ng(ng_outputs, y_dict, mask_dict)
    loss = loss1 + loss2
    return loss, loss1.item(), loss2.item(), edge_loss, edge_acc, total_acc

    




================================================
FILE: cgm/utils/metrics.py
================================================
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

def calculate_metrics(y_true, y_pred, average='binary'):

    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, average=average)
    recall = recall_score(y_true, y_pred, average=average)
    f1 = f1_score(y_true, y_pred, average=average)

    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

    return metrics


================================================
FILE: cgm/utils/train_utils.py
================================================
import os
import sys
import math
import torch
import torch.nn as nn
from torch.nn.functional import one_hot
from tqdm.auto import tqdm
import time
import datetime
from collections import OrderedDict

sys.path.append("..")
from utils.common_utils import touch_print, print_rank_0
from utils.loss import loss_CGM
from torch.utils.tensorboard import SummaryWriter
from accelerate.logging import get_logger
from torch.cuda.amp import autocast

logger = get_logger(__name__)

task_ids = {
    (0, 'graph_query'),
    (1, 'api'),
    (2, 'issue_fix'),
    (3, 'unit_test'),
    (4, 'readme_summary'),
}

task_to_id = {task: idx for idx, task in task_ids}
id_to_task = {idx: task for idx, task in task_ids}

def check_weight_dtype(model):
    for name, param in model.named_parameters():
        print_rank_0(f"Layer {name}: {param.dtype}")

def write_tensorboard(summary_writer: SummaryWriter, log_dict: dict, completed_steps):
    for key, value in log_dict.items():
        summary_writer.add_scalar(f'{key}', value, completed_steps)

def accelerate_saving_checkpoint_CGM(accelerator, model, tokenizer, output_dir: str, completed_steps: int, args):
    accelerator.wait_for_everyone()

    accelerator.print(f"[CHECKPOINT] Saving checkpoint")
    unwrapped_model = accelerator.unwrap_model(model)

    save_encoder = False
    save_adapter = False
    save_lm = False
    if 'e' in args.mode: 
        save_encoder = True
    if 'a' in args.mode: 
        save_adapter = True
    if 'l' in args.mode: 
        save_lm = True

    if accelerator.is_main_process:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        tokenizer.save_pretrained(output_dir)

        if save_adapter: 
            torch.save(accelerator.get_state_dict(model.adapter), f"{output_dir}/adapter.pth")
        
    
    if save_encoder:
        unwrapped_model.encoder.save_pretrained(
            f"{output_dir}/encoder",
            is_main_process=accelerator.is_main_process,
            save_function=accelerator.save,
            state_dict=accelerator.get_state_dict(model.encoder)
        )

    if save_lm:
        unwrapped_model.lm.save_pretrained(
            output_dir,
            is_main_process=accelerator.is_main_process,
            save_function=accelerator.save,
            state_dict=accelerator.get_state_dict(model.lm)
        )

    accelerator.print(
        f"[CHECKPOINT][complete_steps={completed_steps}], checkpoint {output_dir} saved"
    )

    accelerator.wait_for_everyone()

def accelerate_evaluate_CGM(accelerator, model, tokenizer, valid_dataloader, args, completed_steps, step, min_eval_loss, stall_num,
                        best_step, summary_writer):

    losses = []
    end_eval = False
    eval_step = 0
    for batch in valid_dataloader:
        with torch.no_grad():
            if args.task == 'mft':
                task = batch['task']

            x = batch['x']
            y = batch['y']
            loss_mask = batch['loss_mask']
            qa_mask = batch['qa_mask']
            embeddings = batch['embeddings']

            len_y = y.shape[1]

            outputs = model(
                graph_embeddings = embeddings,
                qa_embeddings = x,
                qa_mask = qa_mask 
            )
            output_logits = outputs['logits'][:,-len_y:,:]

            if args.task == 'mft':
                loss_dict = {task_id: torch.tensor(0.0, device=output_logits.device) for task_id, _ in task_ids}

                task_id = task.item()
                loss_dict[task_id] += loss_CGM(
                    output_logits = output_logits,
                    labels = y,
                    loss_mask = loss_mask,
                )

                loss = sum(loss_dict.values())
            else:
                loss = loss_CGM(
                    output_logits = output_logits,
                    labels = y,
                    loss_mask = loss_mask,
                )
                
            eval_step += 1

            losses.append(accelerator.gather(loss.repeat(args.per_device_eval_batch_size)))
    
    accelerator.wait_for_everyone()
    valid_batch_num = len(losses)
    gathered_size = losses[0].shape
    losses = torch.cat(losses)

    try:
        eval_loss = torch.mean(losses)
        if eval_loss <= min_eval_loss:
            min_eval_loss = eval_loss
            stall_num = 0
            best_step = completed_steps
        else:
            stall_num += 1
        perplexity = math.exp(eval_loss)
    except OverflowError:
        perplexity = float("inf")

    logger.info(f"[EVAL][global_steps={step + 1}][completed_steps={completed_steps}]"
                f"[valid_batch_num={valid_batch_num}], [gather_size={gathered_size}]"
                f"[perplexity={perplexity:.4f}][eval_loss={eval_loss:.6f}]")
    eval_log_dict = {
        "valid/valid_loss": eval_loss.float(),
        "valid/perplexity": perplexity
    }

    if accelerator.is_main_process:
        write_tensorboard(summary_writer, eval_log_dict, completed_steps)

    return eval_loss, min_eval_loss, stall_num, best_step

def accelerate_evaluate_CGM_mft(accelerator, model, tokenizer, valid_dataloader, args, completed_steps, step, min_eval_loss, stall_num,
                        best_step, summary_writer):

    losses = []
    task_eval_counts = {}
    task_losses = {}
    eval_step = 0

    for batch in valid_dataloader:
        with torch.no_grad():
            if args.task == 'mft':
                task = batch['task']
                task_id = task.item()
                if task_id not in task_eval_counts:
                    task_eval_counts[task_id] = 0
                    task_losses[task_id] = []
                if task_eval_counts[task_id] >= 50:
                    continue

            x = batch['x']
            y = batch['y']
            loss_mask = batch['loss_mask']
            qa_mask = batch['qa_mask']
            embeddings = batch['embeddings']

            len_y = y.shape[1]

            outputs = model(
                graph_embeddings = embeddings,
                qa_embeddings = x,
                qa_mask = qa_mask 
            )
            output_logits = outputs['logits'][:,-len_y:,:]

            if args.task == 'mft':
                loss = loss_CGM(
                    output_logits = output_logits,
                    labels = y,
                    loss_mask = loss_mask,
                )
                task_losses[task_id].append(loss.item())
            else:
                loss = loss_CGM(
                    output_logits = output_logits,
                    labels = y,
                    loss_mask = loss_mask,
                )
                
            eval_step += 1
            losses.append(accelerator.gather(loss.repeat(args.per_device_eval_batch_size)))
            task_eval_counts[task_id] += 1

    accelerator.wait_for_everyone()
    valid_batch_num = len(losses)
    gathered_size = losses[0].shape
    losses = torch.cat(losses)

    try:
        eval_loss = torch.mean(losses)
        if eval_loss <= min_eval_loss:
            min_eval_loss = eval_loss
            stall_num = 0
            best_step = completed_steps
        else:
            stall_num += 1
        perplexity = math.exp(eval_loss)
    except OverflowError:
        perplexity = float("inf")

    logger.info(f"[EVAL][global_steps={step + 1}][completed_steps={completed_steps}]"
                f"[valid_batch_num={valid_batch_num}], [gather_size={gathered_size}]"
                f"[perplexity={perplexity:.4f}][eval_loss={eval_loss:.6f}]")
    
    for task_id, task_loss_list in task_losses.items():
        task_eval_loss = sum(task_loss_list) / len(task_loss_list) if task_loss_list else 0.0
        logger.info(f"[EVAL][task_id={task_id}][task_loss={task_eval_loss:.6f}]")
        eval_log_dict = {
            "valid/valid_loss": eval_loss.float(),
            "valid/perplexity": perplexity,
            f"valid/{id_to_task[task_id]}": task_eval_loss
        }

    if accelerator.is_main_process:
        write_tensorboard(summary_writer, eval_log_dict, completed_steps)

    return eval_loss, min_eval_loss, stall_num, best_step

def accelerate_monitor_CGM_mft(accelerator, reduce_loss_dict, args, completed_steps,
                       lr_scheduler, optimizer, summary_writer):

    """
    gather reduce_loss from all N devices.
    train logging and tensorboarding.
    """
    # gathered_loss_dict = {task_id: accelerator.gather(reduce_loss) for task_id, reduce_loss in reduce_loss_dict.items()}
    gathered_loss_dict = {task_id: reduce_loss for task_id, reduce_loss in reduce_loss_dict.items()}
    print_rank_0(f"*******************gathered_loss_dict*******************")
    print_rank_0(gathered_loss_dict)

    train_log_dict = {
        f"train/{id_to_task[task_id]}": torch.mean(gathered_loss) / max(reduce_loss_count_dict[task_id], 1)
        for task_id, gathered_loss in gathered_loss_dict.items()
    }
    train_log_dict["train/lr"] = optimizer.param_groups[0]['lr']

    logger.info(
        f"[TRAIN][completed_steps={completed_steps}]"
        f"[lr={optimizer.param_groups[0]['lr']:.4e}]",
    )
    for task_id, train_loss in train_log_dict.items():
        if task_id != "train/lr":
            logger.info(f"{task_id}={train_loss:.6f}")

    if accelerator.is_main_process:
        write_tensorboard(summary_writer, train_log_dict, completed_steps)


def accelerate_monitor_CGM(accelerator, reduce_loss, args, completed_steps,
                       lr_scheduler, optimizer, summary_writer):

    """
    gather reduce_loss from all N devices.
    train logging and tensorboarding.
    """
    reduce_losses = accelerator.gather(reduce_loss)
    # reduce_losses = reduce_loss

    train_loss = torch.mean(reduce_losses) / (args.log_interval * args.gradient_accumulation_steps)


    logger.info(
        f"[TRAIN][complete_steps={completed_steps}][train_loss={train_loss:.6f}]"
        f"[gather shape={reduce_losses.shape}][lr={optimizer.param_groups[0]['lr']:.4e}]",
    )

    train_log_dict = {
        "train/train_loss": train_loss,
        "train/lr": optimizer.param_groups[0]['lr']
    }

    if accelerator.is_main_process:
        write_tensorboard(summary_writer, train_log_dict, completed_steps)


def accelerate_train_CGM(accelerator, model, train_dataloader, valid_dataloader, optimizer, lr_scheduler, tokenizer,
                     total_train_dataset_size, args):

    summary_writer = SummaryWriter(log_dir=args.tb_dir, filename_suffix=args.tb_dir.split('/')[-1]) if accelerator.is_main_process else None
    # Train!
    total_batch_size = args.per_device_train_batch_size * accelerator.num_processes * args.gradient_accumulation_steps
    logger.info("**************************************** Running training ****************************************")
    logger.info(f"  Num examples = {total_train_dataset_size}")
    logger.info(f"  Num Epochs = {args.num_train_epochs}")
    logger.info(f"  Instantaneous batch size per device = {args.per_device_train_batch_size}")
    logger.info(f"  Total global train batch size (w. parallel, distributed & accumulation) = {total_batch_size}")
    logger.info(f"  Gradient Accumulation steps = {args.gradient_accumulation_steps}")
    logger.info(f"  Total optimization(update/completed) steps = {args.max_train_steps}")
    logger.info(f"  Complete/Optimization steps per Epoch = {args.max_train_steps // args.num_train_epochs}")
    logger.info("***************************************************************************************************")

    # Only show the progress bar once on each machine.
    progress_bar = tqdm(range(args.max_train_steps), disable=not accelerator.is_local_main_process)
    # check_weight_dtype(model.lm)
    # exit()
    # set starting_epoch, completed_steps and resume_step of train_dataloader
    completed_steps = 0
    starting_epoch = 0

    # monitor minimum eval_loss, stalling num, and best_step
    min_eval_loss = float('inf')
    eval_loss = 100.0
    checkpoint_eval_loss = float('inf')
    checkpoint_stall_num = 0
    stall_num = 0
    best_step = None
    
    reduce_loss = 0
    reduce_loss_dict = OrderedDict((task_id, 0) for task_id, _ in task_ids)
    reduce_loss_count_dict = OrderedDict((task_id, 0) for task_id, _ in task_ids)
    for epoch in range(starting_epoch, args.num_train_epochs):
        model.train()

        for step, batch in enumerate(train_dataloader):

            with accelerator.accumulate(model):

                graph = batch['graph']
                x = batch['x']
                y = batch['y'].unsqueeze(0)
                loss_mask = batch['loss_mask'].unsqueeze(0)
                qa_mask = batch['qa_mask'].unsqueeze(0)

                if args.task == 'mft':
                    task = batch['task']

                outputs = model(
                    graph = graph,
                    qa_ids = x,
                    qa_mask = qa_mask,
                )

                len_y = y.shape[1]
                output_logits = outputs['logits'][:,-len_y:,:]

                if args.task == 'mft':
                    loss_dict = {task_id: torch.tensor(0.0, device=output_logits.device) for task_id, _ in task_ids}
                    
                    task_id = task.item()
                    loss_dict[task_id] += loss_CGM(
                        output_logits = output_logits,
                        labels = y,
                        loss_mask = loss_mask,
                    )

                    loss = sum(loss_dict.values())
                else:
                    loss = loss_CGM(
                        output_logits = output_logits,
                        labels = y,
                        loss_mask = loss_mask,
                    )

                accelerator.backward(loss)

                optimizer.step()
                if not args.lr_scheduler_type == "reduce_lr_on_plateau":
                    lr_scheduler.step()
                optimizer.zero_grad()

                if optimizer.param_groups[0]['lr'] <= args.min_lr:
                    optimizer.param_groups[0]['lr'] = args.min_lr

                if args.task == 'mft':
                    for task_id in reduce_loss_dict.keys():
                        if not torch.isnan(loss_dict[task_id]):
                            reduce_loss_dict[task_id] += loss_dict[task_id].detach().float()
                            reduce_loss_count_dict[task_id] += 1
                else:

                    if not torch.isnan(loss):
                        reduce_loss += loss.detach().float()
                    else:
                        logger.info("loss nan")

                if accelerator.sync_gradients:
                    completed_steps += 1
                    if args.task == 'mft':
                        reduce_loss = sum(reduce_loss_dict.values())
                    logger.info(f"accelerator step (accumulate) {completed_steps}, loss: {reduce_loss}")
                        
                    if completed_steps % args.log_interval == 0:
                        if args.task == 'mft':
                            progress_bar.update(args.log_interval)
                            accelerate_monitor_CGM_mft(
                                accelerator, reduce_loss_dict, reduce_loss_count_dict, args, completed_steps, 
                                lr_scheduler, optimizer, summary_writer
                            )
                            reduce_loss_dict = OrderedDict((task_id, 0) for task_id, _ in task_ids)
                            reduce_loss_count_dict = OrderedDict((task_id, 0) for task_id, _ in task_ids)
                        else:
                            if isinstance(reduce_loss, torch.Tensor):
                                progress_bar.update(args.log_interval)
                                accelerate_monitor_CGM(
                                    accelerator, reduce_loss, args, completed_steps, 
                                    lr_scheduler, optimizer, summary_writer
                                )
                            reduce_loss = 0

                    # steps checkpointing
                    if args.step_checkpointing and completed_steps % args.checkpointing_steps == 0:
                        output_dir = f"step_{completed_steps}"
                        if args.output_dir is not None:
                            output_dir = os.path.join(args.output_dir, output_dir)
                        accelerate_saving_checkpoint_CGM(accelerator, model, tokenizer, output_dir, completed_steps, args)

                    if args.step_evaluation and completed_steps % args.evaluation_steps == 0:
                        logger.info(f"start evaluation...")
                        model.eval()
                        model.lm.gradient_checkpointing_disable()
                        model.lm.config.use_cache = True
                        if args.task == 'mft':
                            eval_loss, min_eval_loss, stall_num, best_step = accelerate_evaluate_CGM_mft(
                                accelerator, model, tokenizer, valid_dataloader, args, completed_steps, step,
                                min_eval_loss, stall_num, best_step, summary_writer
                            )
                        else:
                            eval_loss, min_eval_loss, stall_num, best_step = accelerate_evaluate_CGM(
                                accelerator, model, tokenizer, valid_dataloader, args, completed_steps, step,
                                min_eval_loss, stall_num, best_step, summary_writer
                            )
                        model.train()
                        model.lm.gradient_checkpointing_enable()
                        model.lm.config.use_cache = False

                        if args.lr_scheduler_type == "reduce_lr_on_plateau":
                            lr_scheduler.step(eval_loss)

                        if eval_loss < checkpoint_eval_loss:
                            checkpoint_eval_loss = eval_loss
                            output_dir = f"step_{completed_steps}_stall_{checkpoint_stall_num}"
                            if args.output_dir is not None:
                                output_dir = os.path.join(args.output_dir, output_dir)
                            accelerate_saving_checkpoint_CGM(accelerator, model, tokenizer, output_dir, completed_steps, args)
                            checkpoint_stall_num = 0
                        else:
                            if checkpoint_stall_num < 2:
                                output_dir = f"step_{completed_steps}_stall_{checkpoint_stall_num}"
                                if args.output_dir is not None:
                                    output_dir = os.path.join(args.output_dir, output_dir)
                                accelerate_saving_checkpoint_CGM(accelerator, model, tokenizer, output_dir, completed_steps, args)
                                checkpoint_stall_num += 1
                            
                            if args.lr_scheduler_type == "reduce_lr_on_plateau":
                                pass
                            elif args.lr_scheduler_type == 'cosine':
                                optimizer.param_groups[0]['lr'] = optimizer.param_groups[0]['lr'] * 0.33
                                lr_scheduler.base_lrs = optimizer.param_groups[0]['lr']
                                lr_scheduler.step()
                            else:
                                optimizer.param_groups[0]['lr'] = optimizer.param_groups[0]['lr'] * 0.33
                                lr_scheduler.step()

                    # adapter warmup
                    if args.adapter_warmup and completed_steps >= args.adapter_warmup_steps:
                        if 'l' in args.mode:
                            for param in model.lm.parameters():
                                param.requires_grad = True
                        args.adapter_warmup = False

                    accelerator.wait_for_everyone()

            load_t = time.time()

        if args.epoch_evaluation:
            model.eval()
            model.lm.gradient_checkpointing_disable()
            model.lm.config.use_cache = True
            if args.task == 'mft':
                eval_loss, min_eval_loss, stall_num, best_step = accelerate_evaluate_CGM_mft(
                accelerator, model, tokenizer, valid_dataloader, args, completed_steps, step,
                min_eval_loss, stall_num, best_step, summary_writer
                )
            else:
                eval_loss, min_eval_loss, stall_num, best_step = accelerate_evaluate_CGM(
                    accelerator, model, tokenizer, valid_dataloader, args, completed_steps, step,
                    min_eval_loss, stall_num, best_step, summary_writer
                )
            model.train()
            model.lm.gradient_checkpointing_enable()
            model.lm.config.use_cache = False 

            if args.lr_scheduler_type == "reduce_lr_on_plateau":
                lr_scheduler.step(eval_loss)

            if eval_loss < checkpoint_eval_loss:
                checkpoint_eval_loss = eval_loss
                output_dir = f"epoch_{epoch}"
                ckpt_tag = output_dir
                if args.output_dir is not None:
                    output_dir = os.path.join(args.output_dir, output_dir)
                accelerate_saving_checkpoint_CGM(accelerator, model, tokenizer, output_dir, completed_steps, args)
            else:
                if args.lr_scheduler_type == "reduce_lr_on_plateau":
                    pass
                    # lr_scheduler.step(eval_loss)
                elif args.lr_scheduler_type == 'cosine':
                    optimizer.param_groups[0]['lr'] = optimizer.param_groups[0]['lr'] * 0.33
                    lr_scheduler.base_lrs = optimizer.param_groups[0]['lr']
                    lr_scheduler.step()
                else:
                    optimizer.param_groups[0]['lr'] = optimizer.param_groups[0]['lr'] * 0.33
                    lr_scheduler.step()

        # epoch checkpointing
        if args.epoch_checkpointing:
            output_dir = f"epoch_{epoch}"
            if args.output_dir is not None:
                output_dir = os.path.join(args.output_dir, output_dir)
            accelerate_saving_checkpoint_CGM(accelerator, model, tokenizer, output_dir, completed_steps, args)

    if summary_writer:
        summary_writer.close()

    output_dir = f"final_step_{completed_steps}"
    if args.output_dir is not None:
        output_dir = os.path.join(args.output_dir, output_dir)
    accelerate_saving_checkpoint_CGM(accelerator, model, tokenizer, output_dir, completed_steps, args)



================================================
FILE: reranker/prompt.py
================================================
"""
Prompt Template for Reranker
"""

reranker_stage_1_system_prompt = """
You are an experienced software developer who specializes in extracting the most relevant files for solving issues from many reference files.

Task:
Based on the information received about the issue from a repository, find the most likely few files from among those that may be able to resolve the issue.

Instructions:
1. Analysis:
- Analyze the provided issue description and files, and pay attention to the relevance of the provided files with the given issue, especially those might be modified during fixing the issue.
- Determine the specific problem or error mentioned in the issue and note any clues that could help your judgment.
2. Extraction:
- Based on your analysis, choose the Top **1** relevant files which might be used in fixing the issue.
- You should choose files from the provided files, and should not modify their name in any way.

Respond in the following format:
[start_of_analysis]
<detailed_analysis> 
[end_of_analysis] 

[start_of_relevant_files] 
1. <file_with_its_path>
2. <file_with_its_path>
3. ...
[end_of_relevant_files] 

Notes:
- You can refer to to the information in the error logs (if exists).
- The relevant file usually exists in the project described in the issue (e.g., django, sklearn). File need modification is usually not in the tests files or external packages.
- The file you choose should be contained in the provided files.
- Provide the file path with files. Do not include redundant suffix like '/home/<USER>/', '/etc/service/' or '/tree/master'.
- Do not include any additional information such as line numbers or explanations in your extraction result.
- Files for initialization and configuration might be modified during changing the code.

Preferred extraction Examples of Related Files:
1. src/utils/file_handler.py
2. core/services/service_manager.py
3. ...
""".strip()

reranker_stage_1_user_prompt_template = """
<repository>
{}
</repository>

<issue>
{}
</issue>
 
<reference_python_file_list>
{}
</reference_python_file_list>

<other_reference_file_list>
{}
</other_reference_file_list>
"""

reranker_stage_2_system_prompt = """
You are an experienced software developer who specializes in assessing the relevance of the file for solving the issue in software repositories.

Task:
For a file provided, evaluate the likelihood that modifying this file would resolve the given issue, and assign a score based on specific criteria.

Instructions:
1. Analysis:
- Analyze the provided issue description and the content of the single relevant file, pay attention to any keywords, error messages, or specific functionalities mentioned that relate to the file.
- Determine how closely the contents and functionality of the file are tied to the problem or error described in the issue.
- Consider the role of the file in the overall project structure (e.g., configuration files, core logic files versus test files, or utility scripts).
2. Scoring:
- Based on your analysis, assign a score from 1 to 5 that represents the relevance of modifying the given file in order to solve the issue.

Score Specifications:
1. **Score 1**: The file is almost certainly unrelated to the issue, with no apparent connection to the functionality or error described in the issue.
2. **Score 2**: The file may be tangentially related, but modifying it is unlikely to resolve the issue directly; possible in rare edge cases.
3. **Score 3**: The file has some relevance to the issue; it might interact with the affected functionality indirectly and tweaking it could be part of a broader fix.
4. **Score 4**: The file is likely related to the issue; it includes code that interacts directly with the functionality in question and could plausibly contain bugs that lead to the issue.
5. **Score 5**: The file is very likely the root cause or heavily involved in the issue and modifying it should directly address the error or problem mentioned.

Respond in the following format:
[start_of_analysis]
<detailed_analysis>
[end_of_analysis]

[start_of_score]
Score <number>
[end_of_score]

Notes:
- The content of the file shows only the structure of this file, including the names of the classes and functions defined in this file.
- You can refer to to the information in the error logs (if exists).
""".strip()

reranker_stage_2_user_prompt_template = """
<repository>
{}
</repository>

<issue>
{}
</issue>

<file_name>
{}
</file_name>

<file_content>
{}
</file_content>
"""

def generate_prompt_for_reranker_stage_1(problem_statement, repo_name, py_file, other_file):
  """
  problem_statement: issue内容
  repo_name: repo名
  py_file: 可能相关的py文件名list
  other_file: 其他可能相关的文件名list
  """
  return reranker_stage_1_system_prompt, reranker_stage_1_user_prompt_template.format(repo_name, problem_statement, py_file, other_file)

def generate_prompt_for_reranker_stage_2(problem_statement, repo_name, file_name, file_content):
  """
  problem_statement: issue内容
  repo_name: repo名
  file_name: 文件名
  file_content: 文件内容，只包含类和函数的声明（class xxx和def xxx）
  """
  return reranker_stage_2_system_prompt, reranker_stage_2_user_prompt_template.format(repo_name, problem_statement, file_name, file_content)



================================================
FILE: retriever/locate.py
================================================
"""
基于 rapidfuzz + faiss 进行 anchor node 定位
"""

from rapidfuzz import process, fuzz
import pandas as pd
import json
import tqdm
import sys
import pickle
import numpy as np
import faiss


from codegraph_parser.python.codegraph_python_local import parse, NodeType, EdgeType
from utils import codegraph_to_nxgraph

def extract_info(item):
    """
    抽取需要匹配的字符部分
    """
    return item[1]

################################# Extractor #################################
def get_extractor_anchor(graph, entity_query, keywords_query):
    """
    获取 关键词匹配结果
    """

    all_nodes = graph.get_nodes()

    cand_name_list = []
    cand_path_name_list = []

    for node in all_nodes:
        node_type = node.get_type()
        if node_type in [NodeType.REPO, NodeType.PACKAGE]:
            continue
        
        try:
            node.name
        except:
            continue
        
        cand_name_list.append((node.node_id, node.name))
        
        if node_type == NodeType.FILE:
            if node.path:
                name_with_path = node.path + "/" + node.name
            else:
                name_with_path = node.name
            cand_path_name_list.append((node.node_id, name_with_path))

    cand_name_all = []
    cand_path_name_all = []

    for query in entity_query + keywords_query:
        
        if "/" in query:
            cand_path_name = process.extract((-1, query), cand_path_name_list, scorer=fuzz.WRatio, limit=3, processor=extract_info)
            cand_path_name_all.append(cand_path_name)

        query_wo_path = query.split('/')[-1]
        cand_name = process.extract((-1, query_wo_path), cand_name_list, scorer=fuzz.WRatio, limit=3, processor=extract_info)
        cand_name_all.append(cand_name)
            

    res = set()
    for query in cand_name_all:
        for item in query:
            res.add(item[0][0])
    for query in cand_path_name_all:
        for item in query:
            res.add(item[0][0])

    return res

################################# Extractor #################################

################################# Inferer #################################
def get_inferer_anchor(query_emb, node_embedding, k=15):
    """
    根据 embedding 进行语义检索
    """
    
    node2id_dict = {}
    id2node_dict = {}
    cand_vec = []

    raw_node_embedding = node_embedding["code"]
    for i, node_id in enumerate(raw_node_embedding):
        node2id_dict[node_id] = i
        id2node_dict[i] = node_id
        cand_vec.append(raw_node_embedding[node_id])

    cand_vec_np = np.array(cand_vec)

    ######### search #########
    d = 1024
    nb = len(cand_vec_np)
    nq = 5

    index = faiss.IndexFlatL2(d)
    index.add(cand_vec_np)
    D, I = index.search(cand_vec_np[:5], k)
    D, I = index.search(query_emb, k)

    anchor_node = []
    for query in I:
        tmp_node_list = []
        for trans_id in query:
            tmp_node_list.append(int(id2node_dict[trans_id]))
        anchor_node.append(tmp_node_list)

    return anchor_node

    
################################# Inferer #################################

################################# 辅助函数 #################################
def get_graph_file_name(item):
    """
    生成 graph_file_name
    """
    repo = item.repo
    repo = repo.replace("/", "#", 1)
    base_commit = item.base_commit
    return repo + "#" + base_commit + ".graph.json"
################################# 辅助函数 #################################


if __name__ == "__main__":

    # 数据变量定义
    test_basic_df = pd.read_json("test_lite_basic_info.json")
    test_basic_df["graph_file"] = test_basic_df.apply(lambda item: get_graph_file_name(item), axis=1)
    
    graph_data_path = "/swe-bench-lite/"

    # 读入 rewriter 提取结果 和 node embedding
    rewriter_output_path = "/rewriter_output.json"
    query_embedding_path = "/rewriter_embedding.pkl"
    node_embedding_path = "/node_embedding/"
    with open(rewriter_output_path, "r", encoding="utf-8") as file:
        rewriter_output = json.load(file)
        file.close()
    
    with open(query_embedding_path, "rb") as file:
        query_embedding = pickle.load(file)
        file.close()
    
    # save path
    anchor_node_dict = {}

    for idx, item in tqdm.tqdm(test_basic_df.iterrows()):

        instance_id = item.instance_id
        graph_file = item.graph_file
        tmp_graph_data_path = graph_data_path + graph_file
        query_emb = query_embedding[instance_id]

        # 解析图数据
        graph = parse(tmp_graph_data_path)
        graph_nx = codegraph_to_nxgraph(graph)

        # 获取 rewriter 输出
        entity_query = rewriter_output[instance_id]["code_entity"]
        keyword_query = rewriter_output[instance_id]["keyword"]

        # 读入 node_embedding
        tmp_node_embedding = node_embedding_path + "{}.pkl".format(instance_id)
        with open(tmp_node_embedding, "rb") as file:
            tmp_node_embedding = pickle.load(file)
            file.close()

        # 定位 anchor nodes
        res_extractor = get_extractor_anchor(graph, entity_query, keyword_query)
        res_inferer = get_inferer_anchor(query_emb, tmp_node_embedding)

        anchor_node = {
            "extractor_anchor_nodes": list(res_extractor),
            "inferer_anchor_nodes": list(res_inferer),
        }

        anchor_node_dict[instance_id] = anchor_node
        
        # TODO: 自定义保存方式


================================================
FILE: retriever/subgraph.py
================================================
"""
启发式搜索逻辑
- 对于 anchor node 进行一跳扩展
- 对于扩展后的结果进行 连通
"""
import sys
import json
import os
import tqdm
import pandas as pd

from codegraph_parser.python.codegraph_python_local import parse, NodeType, EdgeType
from utils import codegraph_to_nxgraph

################################# 子图重构代码 #################################
def get_path_to_repo(node, pre_node_dict, graph_nx):
    """获取该节点到 repo 的路径
    :param node -> CodeGraph Node 采样出的子图节点
    :param pre_node_dict -> list(Node) 每个节点
    :return
    """
    if node.get_type() == NodeType.REPO:
        return [node]
    
    pre_nodes = list()
    if node.node_id in pre_node_dict:
        pre_nodes = pre_node_dict[node.node_id]
    else:
        for pre_node in graph_nx.predecessors(node):
            # 判断 Edge 类型 - contains
            if graph_nx[pre_node][node][0]['type'] == EdgeType.CONTAINS:
                pre_nodes.append(pre_node)
                if pre_node.get_type() != NodeType.REPO:
                    pre_nodes.extend(get_path_to_repo(pre_node, pre_node_dict, graph_nx))
                pre_node_dict[node.node_id] = pre_nodes
                break

    return pre_nodes

def reconstruct_graph(subgraph_nodes, graph_nx, pre_node_dict):
    """
    根据所给节点重构 连通 的 CodeGraph
    pre_node_dict 全局复用
    """
    
    nodes = subgraph_nodes
    all_nodes = set(nodes)
    for node in nodes:
        pre_nodes = get_path_to_repo(node, pre_node_dict, graph_nx)
        all_nodes |= set(pre_nodes)

    # 根据节点裁剪子图
    subgraph = graph_nx.subgraph(list(all_nodes))
    
    return subgraph

################################# 子图重构代码 #################################

################################# BFS代码 #################################
def bfs_expand(graph_nx, subgraph_nodes, hops=1):
    """
    通过 bfs 扩展
    - 最笼统的版本：不区分方向的1-hop
    :param graph_nx nx格式的原图
    :param subgraph_nodes 需要扩展的节点
    :param hops 需要扩展的跳数
    """

    seed_node = subgraph_nodes
    visited_node = set()
    # 记录所有被 nhop 覆盖的节点
    nhops_neighbors = set([node.node_id for node in seed_node])
    
    for hop_idx in range(hops):
        tmp_seed_node = []
        for node in seed_node:
            if node.node_id in visited_node:
                continue
            visited_node.add(node.node_id)
            suc_nodes = graph_nx.successors(node)
            pre_nodes = graph_nx.predecessors(node)
            for node in suc_nodes:
                tmp_seed_node.append(node)
                nhops_neighbors.add(node.node_id)
            
            for node in pre_nodes:
                tmp_seed_node.append(node)
                nhops_neighbors.add(node.node_id)
        
        seed_node = tmp_seed_node
    return nhops_neighbors

def bfs_expand_file(graph_nx, subgraph_nodes, hops=1):
    """
    通过 bfs 扩展
    - 限制 File 遍历2跳
    :param graph_nx nx格式的原图
    :param subgraph_nodes 需要扩展的节点
    :param hops 需要扩展的跳数
    """

    seed_node = subgraph_nodes
    visited_node = set()
    nhops_neighbors = set([node.node_id for node in seed_node])
    
    for hop_idx in range(hops):
        tmp_seed_node = []
        for node in seed_node:
            if node.node_id in visited_node:
                continue
            visited_node.add(node.node_id)
            suc_nodes = graph_nx.successors(node)
            pre_nodes = graph_nx.predecessors(node)
            for node in suc_nodes:
                if node.get_type() == NodeType.FILE:
                    tmp_seed_node.append(node)
                nhops_neighbors.add(node.node_id)
            
            for node in pre_nodes:
                if node.get_type() == NodeType.FILE:
                    tmp_seed_node.append(node)
                nhops_neighbors.add(node.node_id)
        
        seed_node = tmp_seed_node
    return nhops_neighbors
################################# BFS代码 #################################

################################# 辅助函数 #################################
def get_graph_file_name(item):
    """
    生成 graph_file_name
    """
    repo = item.repo
    repo = repo.replace("/", "#", 1)
    base_commit = item.base_commit
    return repo + "#" + base_commit + ".graph.json"
################################# 辅助函数 #################################

if __name__ == "__main__":

    # 数据变量定义
    test_basic_df = pd.read_json("/test_lite_basic_info.json")
    test_basic_df["graph_file"] = test_basic_df.apply(lambda item: get_graph_file_name(item), axis=1)
    
    graph_data_path = "/swe-bench-lite3/"
    anchor_node_path = "/anchor_nodes.json"

    with open(anchor_node_path, "r", encoding="utf-8") as file:
        anchor_node_dict = json.load(file)

    subgraph_id_dict = {}

    for idx, item in tqdm.tqdm(test_basic_df.iterrows()):

        instance_id = item.instance_id
        graph_file = item.graph_file
        tmp_graph_data_path = graph_data_path + graph_file

        # 解析图数据
        graph = parse(tmp_graph_data_path)
        graph_nx = codegraph_to_nxgraph(graph)

        # 获取 anchor_nodes
        anchor_nodes_raw = anchor_node_dict[instance_id]
        extractor_anchors = anchor_nodes_raw["extractor_anchor_nodes"]
        inferer_anchors = [node for node_list in anchor_nodes_raw["inferer_anchor_nodes"] for node in node_list]
        anchor_nodes = list(set(extractor_anchors + inferer_anchors))

        # 先 bfs 再 reconstruct
        anchor_nodes = [graph.get_node_by_id(node_id) for node_id in anchor_nodes]
        expanded_nodes = bfs_expand_file(graph_nx, anchor_nodes, hops=2)

        expanded_nodes = [graph.get_node_by_id(node_id) for node_id in expanded_nodes]
        
        pre_node_dict = {}
        subgraph = reconstruct_graph(expanded_nodes, graph_nx, pre_node_dict)

        result_nodes = subgraph.nodes()
        
        # 获取子图的节点id
        result_nodes = [node.node_id for node in result_nodes if node.get_type() == NodeType.FILE]

        subgraph_id_dict[instance_id] = list(result_nodes)
        
        # TODO: 自定义保存方式


================================================
FILE: retriever/utils.py
================================================
import networkx as nx

def codegraph_to_nxgraph(graph):
    """
    将 CodeGraph 对象 转为 networkx 图对象
    :param graph: CodeGraph 对象
    :return graph_nx: nx.MultiDiGraph 对象
    """

    # 创建图 有向且允许两个节点之间有多条边
    G = nx.MultiDiGraph()

    # 增加点
    for node in graph.nodes:
        G.add_node(graph.nodes[node])

    # 增加边
    for edge in graph.edges:
        
        src_id = edge.source
        tgt_id = edge.target
        # 当前的数据中 会出现 不存在 node list 里的节点
        # 这样的 边 和 点 都先移除
        try:
            G.add_edge(graph.nodes[src_id], graph.nodes[tgt_id], type=edge.edge_type)
        except:
            pass
        
    print(f"nx Graph data parsed, nodes: {G.number_of_nodes():,}, edges: {G.number_of_edges():,}")
        
    return G

def codegraph_to_nxgraph_lite(graph):
    """
    轻量版 将 CodeGraph 对象 转为 networkx 图对象
    节点和边类型都脱离parser定义
    :param graph: CodeGraph 对象
    :return graph_nx: nx.MultiDiGraph 对象
    """
    
    # 创建图 有向且允许两个节点之间有多条边
    G = nx.MultiDiGraph()

    # 增加点
    for node in graph.nodes:
        G.add_node(node.node_id)

    # 增加边
    for edge in graph.edges:
        
        src_id = edge.source
        tgt_id = edge.target
        try:
            # 当前的数据中 会出现 不存在 node list 里的节点
            # 这样的 边 和 点 都先移除
            graph.nodes[src_id]
            graph.nodes[tgt_id]
            # 确保 源节点目标节点 都存在后，再加入
            G.add_edge(src_id, tgt_id, type=edge.edge_type.name)
        except:
            pass
        
    print(f"nx Graph lite data parsed, nodes: {G.number_of_nodes():,}, edges: {G.number_of_edges():,}")
        
    return G

def codegraph_to_nxgraph_analysis(graph):
    """
    分析版 将 CodeGraph 对象 转为 networkx 图对象
    专门用于路径分析的版本，移除 Repo 和 Package 节点；
    返回 有向图 和 无向图 两个 版本
    - 有向图：用于分析节点之间的转移概率；两个点之间可能存在多条边
    - 无向图：用于分析节点(File)之间的最短路径
    :param graph: CodeGraph 对象
    :return graph_nx: nx.MultiDiGraph 对象
    """
    
    # 创建图 有向且允许两个节点之间有多条边
    G_d = nx.MultiDiGraph()
    G_u = nx.Graph()

    # 增加点
    for node_id in graph.nodes:
        node = graph.get_node_by_id(node_id)
        node_type = node.get_type().name
        if node_type in ['REPO', 'PACKAGE']:
            continue
        G_d.add_node(node.node_id)
        G_u.add_node(node.node_id)

    # 增加边
    for edge in graph.edges:
        
        src_id = edge.source
        tgt_id = edge.target
        
        if G_d.has_node(src_id) and G_d.has_node(tgt_id):
            G_d.add_edge(src_id, tgt_id, type=edge.edge_type.name)
            G_u.add_edge(src_id, tgt_id, type=edge.edge_type.name)
        
    print(f"nx Graph analysis data parsed, nodes: {G_d.number_of_nodes():,}, edges: {G_d.number_of_edges():,}")
        
    return G_d, G_u


================================================
FILE: retriever/codegraph_parser/java/codegraph_java_local.py
================================================
import json
import random
from enum import Enum, auto
from typing import Any


class NodeType(Enum):
    """
    节点类型
    """
    REPO = auto(),
    PACKAGE = auto(),
    FILE = auto(),
    TEXTFILE = auto(),
    CLASS = auto(),
    FIELD = auto(),
    METHOD = auto()


class EdgeType(Enum):
    """
    边类型
    """
    CONTAINS = auto(),
    IMPORTS = auto(),
    EXTENDS = auto(),
    IMPLEMENTS = auto(),
    CALLS = auto(),
    REFERENCES = auto(),


class Direction(Enum):
    """
    关系的方向
    """
    IN = auto(),
    OUT = auto()


class CodeGraph:
    """
    CodeGraph表示了由若干类型节点和边组成的程序代码图。
    """

    def __init__(self, nodes, out_edges, in_edges):
        self.nodes = nodes
        self.out_edges = out_edges
        self.in_edges = in_edges
        self.edges = set()

    def get_nodes(self):
        """
        返回所有节点的集合
        """
        return self.nodes.values()

    def get_nodes_by_type(self, node_type: NodeType) -> list:
        """
        获取特定类型的所有节点
        """
        nodes = []
        for node in self.nodes.values():
            if node.get_type() == node_type:
                nodes.append(node)
        return nodes

    def get_nodes_by_type_and_name(self, node_type: NodeType, node_name) -> list:
        """
        通过node_type和node_name获取节点，如果不存在返回空list
        """
        nodes = []
        for node in self.nodes.values():
            if node.get_type() == node_type and node.name == node_name:
                nodes.append(node)
        return nodes

    def get_node_by_id(self, node_id) -> Any:
        """
        通过node_id获取节点，如果不存在返回None
        :param node_id:
        :return:
        """
        if self.nodes.get(node_id) is None:
            return None
        return self.nodes[node_id]

    # def get_random_node(self, node_type: NodeType) -> list | None:
    def get_random_node(self, node_type: NodeType):
        """
        返回指定类型的1个随机节点
        """
        nodes = self.get_nodes_by_type(node_type)
        if len(nodes) == 0:
            return None
        return random.choice(nodes)

    def get_random_nodes(self, node_type: NodeType, k: int) -> list:
        """
        返回最多指定类型的k个随机节点
        """
        nodes = self.get_nodes_by_type(node_type)
        return random.sample(nodes, min(k, len(nodes)))

    def get_out_nodes(self, node_id, edge_type: EdgeType = None) -> list:
        """
        获取node_id对应节点的出边的直接可达的节点
        :param node_id: 目标节点id
        :param edge_type: 目标节点类型
        :return:
        """
        return self.get_related_nodes(node_id, Direction.OUT, edge_type)

    def get_in_nodes(self, node_id, edge_type: EdgeType = None) -> list:
        """
        获取node_id对应节点的入边的直接可达的节点
        :param node_id: 目标节点id
        :param edge_type: 目标节点类型
        :return:
        """
        return self.get_related_nodes(node_id, Direction.IN, edge_type)

    def get_related_nodes(self, node_id, direction: Direction, edge_type: EdgeType = None) -> list:
        """
        返回目标节点相关的节点
        :param node_id: 节点ID
        :param direction: 关联方向
        :param edge_type: 边类型
        :return:
        """
        if self.get_node_by_id(node_id) is None:
            return []

        # 除非指定in，默认返回out
        if direction == Direction.IN:
            edges = self.in_edges.get(node_id)
        else:
            edges = self.out_edges.get(node_id)

        if edges is None:
            return []

        nodes = set()
        for edge in edges:
            if edge_type is None or edge_type == edge.edge_type:
                if direction == Direction.IN:
                    nodes.add(edge.source)
                else:
                    nodes.add(edge.target)

        return list(nodes)

    def nodes_to_dict(self):
        return [node.to_dict() for node in self.nodes.values()]

    def edges_to_dict(self):
        return [edge.to_dict() for edge in self.edges]

    def to_dict(self):
        return {"nodes": self.nodes_to_dict(), "edges": self.edges_to_dict()}


current_repo = ""


class Repo:
    """
    代码仓节点
    - path: 代码仓路径
    """

    def __init__(self, node_id, path, codegraph):
        self.node_id = node_id
        self.repo_name = None
        self.path = path
        if path is not None:
            # self.path = path[path.rfind("/") + 1:].replace('#', '/')
            if "#" in path:
                self.repo_name = path.split("/")[-1]
            else:
                self.repo_name = path
        else:
            self.path = current_repo
            self.repo_name = current_repo # 空的仓库名
        self.codegraph = codegraph

    @staticmethod
    def get_type() -> NodeType:
        return NodeType.REPO

    def node_size(self):
        return len(self.codegraph.nodes)

    def edge_size(self):
        return len(self.codegraph.edges)

    def node_type_size(self):
        return len(set(map(lambda n: n.get_type(), self.codegraph.nodes.values())))

    def edge_type_size(self):
        return len(set(map(lambda e: e.edge_type, self.codegraph.edges)))

    def query_modules(self):
        modules = self.get_packages()
        s = '\n'.join(list(map(lambda x: x.name, modules)))
        return f'仓库{self.path}包含以下模块:\n{s}'

    def get_packages(self):
        contained_ids = self.codegraph.get_out_nodes(self.node_id, EdgeType.CONTAINS)
        contained = list(map(self.codegraph.get_node_by_id, contained_ids))
        return list(filter(lambda n: n.get_type() == NodeType.PACKAGE, contained))

    def query_files(self):
        files = self.get_files()
        s = '\n'.join(list(map(lambda x: x.name, files)))
        return f'仓库{self.path}包含以下文件:\n{s}'

    def get_files(self):
        files = []
        modules = self.get_packages()
        for module in modules:
            files += module.get_files()
        return files

    def query_classes(self):
        classes = self.get_classes()
        s = '\n'.join(list(map(lambda x: x.name, classes)))
        return f'仓库{self.path}包含了以下类:\n{s}'

    def get_classes(self):
        classes = []
        files = self.get_files()
        for file in files:
            classes += file.get_classes()
        return classes

    def __str__(self):
        return self.path

    def __repr__(self):
        return self.path

    def to_dict(self):
        return {"nodeType": NodeType.REPO.name.capitalize(), "nodeId": self.node_id, "path": self.path}
    
    def get_content(self):
        return self.repo_name


class Package:
    """
    模块节点
    - name: 模块名
    """

    def __init__(self, node_id, name, codegraph):
        self.node_id = node_id
        self.name = name
        self.codegraph = codegraph

    @staticmethod
    def get_type():
        return NodeType.PACKAGE

    def query_files(self):
        files = self.get_files()
        s = '\n'.join(list(map(lambda x: x.name, files)))
        return f'模块{self.name}中包含以下文件:\n{s}'

    def get_files(self):
        return list(map(self.codegraph.get_node_by_id, self.codegraph.get_out_nodes(self.node_id, EdgeType.CONTAINS)))

    def query_classes(self):
        classes = self.get_classes()
        s = '\n'.join(list(map(lambda x: x.name, classes)))
        return f'模块{self.name}中包含了以下类:\n{s}'

    def get_classes(self):
        classes = []
        files = self.get_files()
        for file in files:
            classes += file.get_classes()
        return classes

    def __str__(self):
        return self.name

    def __repr__(self):
        return self.name

    def to_dict(self):
        return {"nodeType": NodeType.PACKAGE.name.capitalize(), "nodeId": self.node_id, "name": self.name}
    
    def get_content(self):
        return self.name


class File:
    """
    文件节点
    - name: 文件名
    """

    def __init__(self, node_id, name, path, text, codegraph, clean_text):
        self.node_id = node_id
        self.name = name
        self.path = path
        self.text = text
        self.codegraph = codegraph
        self.clean_text = clean_text

    @staticmethod
    def get_type():
        return NodeType.FILE

    def query_path(self):
        return f'文件的路径是{self.get_path()}'

    def get_path(self):
        module = self.codegraph.get_node_by_id(self.codegraph.get_in_nodes(self.node_id, EdgeType.CONTAINS)[0])
        path = module.name.replace('.', '/')
        return path + "/" + self.name

    def query_imports(self):
        imports = self.get_imports()
        s = '\n'.join(list(map(lambda x: x.name, imports)))
        return f"文件{self.name}引入以下类:\n{s}"

    def get_imports(self):
        return list(map(self.codegraph.get_node_by_id, self.codegraph.get_out_nodes(self.node_id, EdgeType.IMPORTS)))

    def query_classes(self):
        classes = self.get_classes()
        s = '\n'.join(list(map(lambda x: x.name, classes)))
        return f'文件{self.name}包含了以下类:\n{s}'

    def get_classes(self):
        return list(map(self.codegraph.get_node_by_id, self.get_classes_ids()))

    def get_classes_ids(self):
        return self.codegraph.get_out_nodes(self.node_id, EdgeType.CONTAINS)

    def query_methods(self):
        methods = self.get_methods()
        s = '\n'.join(list(map(lambda x: x.signature, methods)))
        return f'文件{self.name}中包含以下方法:\n{s}'

    def get_methods(self):
        methods = []
        for clazz in self.get_classes():
            methods += clazz.get_methods()
        return methods

    def query_dependent_files(self):
        dependent_files = self.get_dependent_files()
        if len(dependent_files) == 0:
            return f'文件{self.name}不依赖其它文件'
        s = '\n'.join(list(map(lambda x: x.name, dependent_files)))
        return f'文件{self.name}依赖了以下文件:\n{s}'

    def get_dependent_files(self):
        imports = self.get_imports()
        imported_classes = []
        for i in imports:
            imported_classes += self.codegraph.get_nodes_by_type_and_name(NodeType.CLASS, i.name)
        files = list(filter(lambda f: f is not None, map(lambda c: c.get_containing_file(), imported_classes)))
        return files

    def query_dependent_by_files(self):
        dependent_files = self.get_dependent_by_files()
        if len(dependent_files) == 0:
            return f'文件{self.name}没有被其它文件依赖'
        s = '\n'.join(list(map(lambda x: x.name, dependent_files)))
        return f'文件{self.name}被以下文件依赖了:\n{s}'

    def get_dependent_by_files(self):
        contained_classes = self.get_classes_ids()
        files = []
        for c in contained_classes:
            files += list(map(self.codegraph.get_node_by_id, self.codegraph.get_in_nodes(c, EdgeType.IMPORTS)))
        return files

    def __str__(self):
        return self.name

    def __repr__(self):
        return self.name

    def to_dict(self):
        return {"nodeType": NodeType.FILE.name.capitalize(), "nodeId": self.node_id,
                "name": self.name, "path": self.path, "text": self.text, "clean_text": self.clean_text}
    
    def get_content(self):
        filepath = self.path if self.path else ''
        filename = "# Filename: " + filepath + self.name + "\n"
        if self.clean_text:
            return filename + self.clean_text
        elif self.text:
            return filename + self.text # 为了兼容没有经过 clean text 处理的数据
        else:
            return filename

class TextFile:
    """
    文本文件节点，目前包含MD和XML
    - name: 文件名
    - text: 文本内容
    """

    def __init__(self, node_id, name, text, path, codegraph):
        self.node_id = node_id
        self.name = name
        self.text = text
        self.path = path
        self.codegraph = codegraph

    @staticmethod
    def get_type():
        return NodeType.TEXTFILE

    def __str__(self):
        return self.name

    def __repr__(self):
        return self.name

    def to_dict(self):
        return {"nodeType": NodeType.TEXTFILE.name.capitalize(), "nodeId": self.node_id, "name": self.name,
                "text": self.text, "path": self.path}
    
    def get_content(self):
        if self.text:
            return self.name + self.text
        else:
            return self.name

class Class:
    """
    类节点
    - name: 文件名
    - modifiers: 修饰符
    - comment: 注释
    """

    def __init__(self, node_id, name, class_type, modifiers, comment, text, start_loc, end_loc, codegraph, clean_text):
        self.node_id = node_id
        self.name = name
        self.class_type = class_type
        self.modifiers = modifiers
        self.comment = comment
        self.text = text
        self.start_loc = start_loc
        self.end_loc = end_loc
        self.codegraph = codegraph
        self.clean_text = clean_text

    @staticmethod
    def get_type():
        return NodeType.CLASS

    def query_all_superclasses(self):
        s = self.get_all_superclasses()
        if len(s) == 0:
            return f"{self.name}没有继承或实现的类或接口"
        a = '\n'.join(list(map(lambda x: x.name, s)))
        return f'类{self.name}实现或继承了以下类或接口:\n{a}'

    def get_all_superclasses(self):
        superclasses = set(self.get_superclasses()) | set(self.get_interfaces())
        tmp = set()
        for superclass in superclasses:
            tmp |= superclass.get_all_superclasses()
        return superclasses | tmp

    def get_superclass_and_interfaces(self):
        superclass_or_interface_ids = self.get_superclass_and_interface_ids()
        return list(map(self.codegraph.get_node_by_id, superclass_or_interface_ids))

    def get_superclass_and_interface_ids(self):
        return (set(self.codegraph.get_out_nodes(self.node_id, EdgeType.EXTENDS))
                | set(self.codegraph.get_out_nodes(self.node_id, EdgeType.IMPLEMENTS)))

    def get_superclasses(self):
        out = self.codegraph.get_out_nodes(self.node_id, EdgeType.EXTENDS)
        return list(map(self.codegraph.get_node_by_id, out))

    def get_superclass(self):
        out = self.codegraph.get_out_nodes(self.node_id, EdgeType.EXTENDS)
        if len(out) > 0:
            return self.codegraph.get_node_by_id(out[0])
        return None

    def get_superclass_list(self):
        superclass = self.get_superclass()
        if superclass is None:
            return []
        return [superclass] + superclass.get_superclass_list()

    def get_interfaces(self):
        return list(map(self.codegraph.get_node_by_id, self.codegraph.get_out_nodes(self.node_id, EdgeType.IMPLEMENTS)))

    def query_all_subclasses(self):
        s = self.get_all_subclasses()
        if len(s) == 0:
            return f"没有类或接口实现或继承了类{self.name}"
        a = '\n'.join(list(map(lambda x: x.name, s)))
        return f'类{self.name}被以下类实现或继承了:\n{a}'

    def get_all_subclasses(self):
        subclasses = set(self.get_subclasses())
        tmp = set()
        for subclass in subclasses:
            tmp |= subclass.get_all_subclasses()
        return subclasses | tmp

    def get_subclasses(self):
        subclass_ids = self.get_subclass_ids()
        return list(map(self.codegraph.get_node_by_id, subclass_ids))

    def get_subclass_ids(self):
        return (set(self.codegraph.get_in_nodes(self.node_id, EdgeType.EXTENDS))
                | set(self.codegraph.get_in_nodes(self.node_id, EdgeType.IMPLEMENTS)))

    def query_methods(self):
        methods = self.get_methods()
        if len(methods) == 0:
            return f'类{self.name}中没有定义方法'
        a = '\n'.join(list(map(lambda m: m.get_simple_signature(), methods)))
        return f"类{self.name}定义了以下方法:\n{a}"

    def get_methods(self):
        return list(filter(lambda n: n.get_type() == NodeType.METHOD.METHOD,
                           list(map(self.codegraph.get_node_by_id,
                                    self.codegraph.get_out_nodes(self.node_id, EdgeType.CONTAINS)))))

    def query_all_methods(self):
        methods = self.get_all_methods()
        if len(methods) == 0:
            return f'类{self.name}中不包含方法'
        a = '\n'.join(list(map(lambda m: m.signature, methods)))
        return f"类{self.name}包含了以下方法:\n{a}"

    def get_all_methods(self):
        methods = self.get_methods()
        for superclass in self.get_superclass_list():
            methods += superclass.get_all_methods()
        return methods

    def query_fields(self):
        fields = self.get_fields()
        if len(fields) == 0:
            return f'类{self.name}中没有定义字段'
        a = '\n'.join(list(map(lambda f: f'{f.name}:{f.field_type}', fields)))
        return f"类{self.name}定义了以下字段:\n{a}"

    def get_fields(self):
        return list(filter(lambda n: n.get_type() == NodeType.FIELD,
                           list(map(self.codegraph.get_node_by_id,
                                    self.codegraph.get_out_nodes(self.node_id, EdgeType.CONTAINS)))))

    def query_containing_file(self):
        file = self.get_containing_file()
        if file is not None:
            return f'类{self.name}所在的文件路径是{file.get_path()}'
        return f'类{self.name}所在的文件无法找到'

    def get_containing_file(self):
        file_ids = self.codegraph.get_in_nodes(self.node_id, EdgeType.CONTAINS)
        if len(file_ids) == 0:
            return None
        file = self.codegraph.get_node_by_id(file_ids[0])
        return file

    def __str__(self):
        return self.name

    def __repr__(self):
        return self.name

    def to_dict(self):
        return {"nodeType": NodeType.CLASS.name.capitalize(), "nodeId": self.node_id, "name": self.name,
                "classType": self.class_type, "comment": self.comment, "text": self.text, "startLoc": self.start_loc,
                "endLoc": self.end_loc, "modifiers": self.modifiers, "clean_text": self.clean_text}
        
    def get_content(self):

        if self.clean_text:
            return self.name + self.clean_text
        elif self.text:
            return self.name + self.text # 为了兼容没有经过 clean text 处理的数据
        else:
            return self.name


class Field:
    """
    字段节点
    - name: 字段名
    - field_type: 字段名类型
    """

    def __init__(self, node_id, name, field_type, intializer, modifiers, comment, arguments, start_loc, end_loc,
                 codegraph):
        self.node_id = node_id
        self.name = name
        self.field_type = field_type
        self.intializer = intializer
        self.modifiers = modifiers
        self.comment = comment
        self.arguments = arguments
        self.start_loc = start_loc
        self.end_loc = end_loc
        self.codegraph = codegraph

    @staticmethod
    def get_type():
        return NodeType.FIELD

    def __str__(self):
        return self.name + ":" + self.field_type

    def __repr__(self):
        return self.__str__()

    def to_dict(self):
        return {"nodeType": NodeType.FIELD.name.capitalize(), "nodeId": self.node_id, "name": self.name,
                "fieldType": self.field_type, "intializer": self.intializer, "comment": self.comment,
                "arguments": self.arguments, "startLoc": self.start_loc, "col": self.end_loc, "modifiers": self.modifiers}
    
    def get_content(self):
        
        comment = self.comment if self.comment else ""
        modifiers = self.modifiers if self.modifiers else ""
        field_type = self.field_type if self.field_type else ""
        
        if self.comment:
            return comment + "\n" + modifiers +\
            " " + field_type + " " + self.name
        else:
            return modifiers + " " +\
                field_type + " " + self.name


class Method:
    """
    方法节点
    - signature: 方法签名，格式为<class_name>#<method_name>(<params_type>,...)<return_type>
    - text: 方法文本,包含注释、方法签名和方法体等
    """

    def __init__(self, node_id, signature, modifiers, text, comment, class_name, method_name, method_sig, start_loc,
                 end_loc, codegraph):
        self.node_id = node_id
        self.signature = signature
        self.modifiers = modifiers
        self.text = text
        self.comment = comment
        self.class_name = class_name
        self.method_name = method_name
        self.method_sig = method_sig
        self.start_loc = start_loc
        self.end_loc = end_loc
        self.codegraph = codegraph

    @staticmethod
    def get_type():
        return NodeType.METHOD

    def get_simple_signature(self):
        return self.signature[self.signature.index("#") + 1:]

    def query_containing_file(self):
        return f'方法{self.signature}包含在文件{self.get_containing_file().get_path()}中'

    def get_containing_file(self):
        class_ids = self.codegraph.get_in_nodes(self.node_id, EdgeType.CONTAINS)
        if len(class_ids) == 0:
            return None
        class_node_id = class_ids[0]
        return self.codegraph.get_node_by_id(class_node_id).get_containing_file()

    def query_callees(self):
        callees = self.get_callees()
        s = '\n'.join(list(map(lambda x: x.signature, callees)))
        return f'方法{self.signature}直接调用了以下方法:\n{s}'

    def get_callees(self):
        return list(map(self.codegraph.get_node_by_id, self.codegraph.get_out_nodes(self.node_id, EdgeType.CALLS)))

    def get_callee_ids(self):
        return self.codegraph.get_out_nodes(self.node_id, EdgeType.CALLS)

    def query_callers(self):
        callers = self.get_callers()
        s = '\n'.join(list(map(lambda x: x.signature, callers)))
        return f'方法{self.signature}被以下方法直接调用了:\n{s}'

    def get_callers(self):
        return list(map(self.codegraph.get_node_by_id, self.codegraph.get_in_nodes(self.node_id, EdgeType.CALLS)))

    def get_caller_ids(self):
        return self.codegraph.get_in_nodes(self.node_id, EdgeType.CALLS)

    def query_common_callees(self, method1):
        common = self.get_common_callee_ids(method1)
        if len(common) == 0:
            return f"方法{self.signature}和方法{method1.signature}没有调用同一个方法"
        s = '\n'.join(list(map(lambda x: x.signature, common)))
        return f"方法{self.signature}和方法{method1.signature}调用了{len(common)}个共同的方法，分别是:\n{s}"

    def get_common_callee_ids(self, method1):
        return set(self.get_callee_ids()) & set(method1.get_callee_ids())

    def query_common_callers(self, method1):
        common = self.get_common_caller_ids(method1)
        if len(common) == 0:
            return f"方法{self.signature}和方法{method1.signature}没有被同一个方法共同调用"
        s = '\n'.join(list(map(lambda x: x.signature, common)))
        return f"方法{self.signature}和方法{method1.signature}被{len(common)}个方法共同调用，分别是:\n{s}"

    def get_common_caller_ids(self, method1):
        return set(self.get_caller_ids()) & set(method1.get_caller_ids())

    def __str__(self):
        return self.signature

    def __repr__(self):
        return self.signature

    def to_dict(self):
        return {"nodeType": NodeType.METHOD.name.capitalize(), "nodeId": self.node_id, "signature": self.signature,
                "method_name": self.method_name, "method_sig": self.method_sig, "comment": self.comment,
                "text": self.text, "startLoc": self.start_loc, "endLoc": self.end_loc, "modifiers": self.modifiers,
                "className": self.class_name}
    
    def get_content(self):
        if self.comment:
            return self.comment + "\n" + self.text
        else:
            return self.text
        


class Edge:
    """
    边。当前支持的边类型：
    - contains: 不同层级之间的包含关系，比如Repo包含Module，Module包含File，File包含Class，Class包含Field和Method
    - imports: File对Class的引入依赖关系，对应与Java中的import
    - extends: Class之间的继承关系
    - implements: Class之间的实现关系
    - calls: Method之间的调用关系
    """

    def __init__(self, edge_type, source, target):
        self.edge_type = edge_type
        self.source = source
        self.target = target

    def __str__(self):
        return f"{self.source} --[{self.edge_type.name.lower()}]--> {self.target}"

    def to_dict(self):
        return {"edgeType": self.edge_type.name.lower(), "source": self.source, "target": self.target}


def parse(filename):
    """
    从文件解析CodeGraph
    :param filename: 文件名
    :return: CodeGraph对象
    """
    codegraph = CodeGraph({}, {}, {})

    try:
        with open(filename, 'r') as file:
            content = file.read()
            print(f"Graph file opened: {filename}")
    except json.JSONDecodeError as e:
        print(e.msg)
        return

    try:
        data = json.loads(content)
        print(f"Graph data loaded, size: {sizeof_fmt(len(content))}")
    except json.decoder.JSONDecodeError as e:
        print(e.msg)
        return

    for node in data['nodes']:
        node_type = node['nodeType']
        
        # TODO
        # 兼容不同 json 文件格式
        try:
            node_id = node['nodeId']
        except:
            node_id = node['id']
        
        if node_type.upper() == NodeType.REPO.name:
            codegraph.nodes[node_id] = Repo(node_id, node.get('path'), codegraph)
        elif node_type.upper() == NodeType.PACKAGE.name:
            codegraph.nodes[node_id] = Package(node_id, node['name'], codegraph)
        # Handle old graph format
        elif node_type.upper() == "MODULE":
            codegraph.nodes[node_id] = Package(node_id, node['name'], codegraph)
        elif node_type.upper() == NodeType.FILE.name:
            codegraph.nodes[node_id] = File(node_id, node['name'], node.get('path'), node.get('text'), codegraph, node.get('clean_text'))
        elif node_type.upper() == NodeType.TEXTFILE.name:
            codegraph.nodes[node_id] = TextFile(node_id, node['name'], node['text'], node.get('path'), codegraph)
        elif node_type.upper() == NodeType.CLASS.name:

            # Patch：由于 Lib 不连通，考虑不读入 Lib 节点
            ########################################
            if node.get('classType') == 'Lib':
                continue
            ########################################

            codegraph.nodes[node_id] = Class(node_id, node['name'], node.get('classType'), node.get('modifiers'),
                                             node.get('comment'), node.get('text'), node.get('startLoc'),
                                             node.get('endLoc'), codegraph, node.get('clean_text'))
        elif node_type.upper() == NodeType.FIELD.name:
            codegraph.nodes[node_id] = Field(node_id, node['name'], node['fieldType'], node.get('initializer'),
                                             node.get('modifiers'), node.get('comment'), node.get('arguments'),
                                             node.get('startLoc'), node.get('endLoc'), codegraph)
        elif node_type.upper() == NodeType.METHOD.name:
            codegraph.nodes[node_id] = Method(node_id, node['signature'], node['modifiers'], node['text'],
                                              node.get('comment'), node.get('className'), node.get('methodName'),
                                              node.get('methodSig'), node.get('startLoc'), node.get('endLoc'),
                                              codegraph)
        else:
            print("Unkonwn node type: {0}", node_type)

    for edge in data['edges']:
        edge_type = EdgeType[edge['edgeType'].upper()]
        source = edge['source']
        target = edge['target']

        edge = Edge(edge_type, source, target)
        codegraph.edges.add(edge)
        codegraph.out_edges.setdefault(source, set()).add(edge)
        codegraph.in_edges.setdefault(target, set()).add(edge)

    print(f"Graph data parsed, nodes: {len(codegraph.nodes):,}, edges: {len(codegraph.edges):,}")

    return codegraph


def sizeof_fmt(num, suffix='B'):
    for unit in ['', 'Ki', 'Mi', 'Gi', 'Ti', 'Pi', 'Ei', 'Zi']:
        if abs(num) < 1024.0:
            return f"{num:3.1f}{unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f}Yi{suffix}"



================================================
FILE: retriever/codegraph_parser/python/codegraph_python_local.py
================================================
"""
这个 parser 将兼容 CGM 项目格式
即 所有节点的内容 将裁剪掉其子节点内容（保证内容不重复）
为 File 和 Class 节点新增 clean_text 字段
get_content 函数也将返回 clean_text 字段组成的内容
"""
import json
import random
from enum import Enum, auto
from typing import Any


class NodeType(Enum):
    """
    节点类型
    """
    REPO = auto(),
    PACKAGE = auto(),
    FILE = auto(),
    TEXTFILE = auto(),
    CLASS = auto(),
    ATTRIBUTE = auto(),
    FUNCTION = auto()
    LAMBDA = auto()


class EdgeType(Enum):
    """
    边类型
    """
    CONTAINS = auto(),
    IMPORTS = auto(),
    EXTENDS = auto(),
    IMPLEMENTS = auto(),
    CALLS = auto(),
    REFERENCES = auto(),


class Direction(Enum):
    """
    关系的方向
    """
    IN = auto(),
    OUT = auto()


class CodeGraph:
    """
    CodeGraph表示了由若干类型节点和边组成的程序代码图。
    """

    def __init__(self, nodes, out_edges, in_edges):
        self.nodes = nodes
        self.out_edges = out_edges
        self.in_edges = in_edges
        self.edges = set()

    def get_nodes(self):
        """
        返回所有节点的集合
        """
        return self.nodes.values()

    def get_nodes_by_type(self, node_type: NodeType) -> list:
        """
        获取特定类型的所有节点
        """
        nodes = []
        for node in self.nodes.values():
            if node.get_type() == node_type:
                nodes.append(node)
        return nodes

    def get_nodes_by_type_and_name(self, node_type: NodeType, node_name) -> list:
        """
        通过node_type和node_name获取节点，如果不存在返回空list
        """
        nodes = []
        for node in self.nodes.values():
            if node.get_type() == node_type and node.name == node_name:
                nodes.append(node)
        return nodes

    def get_node_by_id(self, node_id) -> Any:
        """
        通过node_id获取节点，如果不存在返回None
        :param node_id:
        :return:
        """
        if self.nodes.get(node_id) is None:
            return None
        return self.nodes[node_id]

    # def get_random_node(self, node_type: NodeType) -> list | None:
    def get_random_node(self, node_type: NodeType):
        """
        返回指定类型的1个随机节点
        """
        nodes = self.get_nodes_by_type(node_type)
        if len(nodes) == 0:
            return None
        return random.choice(nodes)

    def get_random_nodes(self, node_type: NodeType, k: int) -> list:
        """
        返回最多指定类型的k个随机节点
        """
        nodes = self.get_nodes_by_type(node_type)
        return random.sample(nodes, min(k, len(nodes)))

    def get_out_nodes(self, node_id, edge_type: EdgeType = None) -> list:
        """
        获取node_id对应节点的出边的直接可达的节点
        :param node_id: 目标节点id
        :param edge_type: 目标节点类型
        :return:
        """
        return self.get_related_nodes(node_id, Direction.OUT, edge_type)

    def get_in_nodes(self, node_id, edge_type: EdgeType = None) -> list:
        """
        获取node_id对应节点的入边的直接可达的节点
        :param node_id: 目标节点id
        :param edge_type: 目标节点类型
        :return:
        """
        return self.get_related_nodes(node_id, Direction.IN, edge_type)

    def get_related_nodes(self, node_id, direction: Direction, edge_type: EdgeType = None) -> list:
        """
        返回目标节点相关的节点
        :param node_id: 节点ID
        :param direction: 关联方向
        :param edge_type: 边类型
        :return:
        """
        if self.get_node_by_id(node_id) is None:
            return []

        # 除非指定in，默认返回out
        if direction == Direction.IN:
            edges = self.in_edges.get(node_id)
        else:
            edges = self.out_edges.get(node_id)

        if edges is None:
            return []

        nodes = set()
        for edge in edges:
            if edge_type is None or edge_type == edge.edge_type:
                if direction == Direction.IN:
                    nodes.add(edge.source)
                else:
                    nodes.add(edge.target)

        return list(nodes)

    def nodes_to_dict(self):
        return [node.to_dict() for node in self.nodes.values()]

    def edges_to_dict(self):
        return [edge.to_dict() for edge in self.edges]

    def to_dict(self):
        return {"nodes": self.nodes_to_dict(), "edges": self.edges_to_dict()}

class Repo:
    """
    代码仓节点
    - repo_name: 代码名
    - group_name: 组名
    """

    def __init__(self, node_id, repo_name, codegraph):
        self.node_id = node_id
        # index = repo_name.index('#') # index函数在找不到 # 时会报错，swebench仓库没有这个信息
        # 这个 reponame 是想去掉 base commit 的信息
        if "#" in repo_name:
            repo_name = repo_name.replace('#', '/', 1)
            try: # 针对不统一的命名方式
                self.repo_name, _ = repo_name.split("#")
            except:
                self.repo_name = repo_name
        else:
            self.repo_name = repo_name
        # index = repo_name.find('#')
        # if index != -1:
        #     self.repo_name = repo_name[index + 1:]
        #     self.group_name = repo_name[:index]
        # else:
        #     self.repo_name = repo_name
        self.group_name = ""
        self.codegraph = codegraph

    @staticmethod
    def get_type() -> NodeType:
        return NodeType.REPO

    def node_size(self):
        return len(self.codegraph.nodes)

    def edge_size(self):
        return len(self.codegraph.edges)

    def node_type_size(self):
        return len(set(map(lambda n: n.get_type(), self.codegraph.nodes.values())))

    def edge_type_size(self):
        return len(set(map(lambda e: e.edge_type, self.codegraph.edges)))

    def query_modules(self):
        modules = self.get_modules()
        s = '\n'.join(list(map(lambda x: x.name, modules)))
        return f'仓库{self.repo_name}包含以下模块:\n{s}'

    def get_modules(self):
        contained_ids = self.codegraph.get_out_nodes(self.node_id, EdgeType.CONTAINS)
        contained = list(map(self.codegraph.get_node_by_id, contained_ids))
        return list(filter(lambda n: n.get_type() == NodeType.PACKAGE, contained))

    def query_files(self):
        files = self.get_files()
        s = '\n'.join(list(map(lambda x: x.name, files)))
        return f'仓库{self.repo_name}包含以下文件:\n{s}'

    def get_files(self):
        files = []
        modules = self.get_modules()
        for module in modules:
            files += module.get_files()
        return files

    def query_classes(self):
        classes = self.get_classes()
        s = '\n'.join(list(map(lambda x: x.name, classes)))
        return f'仓库{self.repo_name}包含了以下类:\n{s}'

    def get_classes(self):
        classes = []
        files = self.get_files()
        for file in files:
            classes += file.get_classes()
        return classes

    def __str__(self):
        return self.repo_name

    def __repr__(self):
        return self.repo_name

    def to_dict(self):
        return {"nodeType": NodeType.REPO.name.capitalize(), "id": self.node_id, "repoName": self.repo_name, "groupName": self.group_name}
    
    def get_content(self):
        return self.repo_name


class Package:
    """
    模块节点
    - name: 模块名
    """

    def __init__(self, node_id, name, codegraph):
        self.node_id = node_id
        self.name = name
        self.codegraph = codegraph

    @staticmethod
    def get_type():
        return NodeType.PACKAGE

    def query_files(self):
        files = self.get_files()
        s = '\n'.join(list(map(lambda x: x.name, files)))
        return f'模块{self.name}中包含以下文件:\n{s}'

    def get_files(self):
        return list(map(self.codegraph.get_node_by_id, self.codegraph.get_out_nodes(self.node_id, EdgeType.CONTAINS)))

    def query_classes(self):
        classes = self.get_classes()
        s = '\n'.join(list(map(lambda x: x.name, classes)))
        return f'模块{self.name}中包含了以下类:\n{s}'

    def get_classes(self):
        classes = []
        files = self.get_files()
        for file in files:
            classes += file.get_classes()
        return classes

    def __str__(self):
        return self.name

    def __repr__(self):
        return self.name

    def to_dict(self):
        return {"nodeType": NodeType.PACKAGE.name.capitalize(), "id": self.node_id, "name": self.name}

    def get_content(self):
        return self.name

class File:
    """
    文件节点
    - name: 文件名
    """

    def __init__(self, node_id, name, path, text, codegraph, clean_text):
        self.node_id = node_id
        self.name = name
        self.path = path
        self.text = text
        self.codegraph = codegraph
        self.clean_text = clean_text

    @staticmethod
    def get_type():
        return NodeType.FILE

    def query_path(self):
        return f'文件的路径是{self.get_path()}'

    def get_path(self):
        module = self.codegraph.get_node_by_id(self.codegraph.get_in_nodes(self.node_id, EdgeType.CONTAINS)[0])
        path = module.name.replace('.', '/')
        return path + "/" + self.name

    def query_imports(self):
        imports = self.get_imports()
        s = '\n'.join(list(map(lambda x: x.name, imports)))
        return f"文件{self.name}引入以下类:\n{s}"

    def get_imports(self):
        return list(map(self.codegraph.get_node_by_id, self.codegraph.get_out_nodes(self.node_id, EdgeType.IMPORTS)))

    def query_classes(self):
        classes = self.get_classes()
        s = '\n'.join(list(map(lambda x: x.name, classes)))
        return f'文件{self.name}包含了以下类:\n{s}'

    def get_classes(self):
        return list(map(self.codegraph.get_node_by_id, self.get_classes_ids()))

    def get_classes_ids(self):
        return self.codegraph.get_out_nodes(self.node_id, EdgeType.CONTAINS)

    def query_functions(self):
        functions = self.get_functions()
        s = '\n'.join(list(map(lambda x: x.header, functions)))
        return f'文件{self.name}中包含以下方法:\n{s}'

    def get_functions(self):
        functions = []
        for clazz in self.get_classes():
            functions += clazz.get_functions()
        return functions

    def query_dependent_files(self):
        dependent_files = self.get_dependent_files()
        if len(dependent_files) == 0:
            return f'文件{self.name}不依赖其它文件'
        s = '\n'.join(list(map(lambda x: x.name, dependent_files)))
        return f'文件{self.name}依赖了以下文件:\n{s}'

    def get_dependent_files(self):
        imports = self.get_imports()
        imported_classes = []
        for i in imports:
            imported_classes += self.codegraph.get_nodes_by_type_and_name(NodeType.CLASS, i.name)
        files = list(filter(lambda f: f is not None, map(lambda c: c.get_containing_file(), imported_classes)))
        return files

    def query_dependent_by_files(self):
        dependent_files = self.get_dependent_by_files()
        if len(dependent_files) == 0:
            return f'文件{self.name}没有被其它文件依赖'
        s = '\n'.join(list(map(lambda x: x.name, dependent_files)))
        return f'文件{self.name}被以下文件依赖了:\n{s}'

    def get_dependent_by_files(self):
        contained_classes = self.get_classes_ids()
        files = []
        for c in contained_classes:
            files += list(map(self.codegraph.get_node_by_id, self.codegraph.get_in_nodes(c, EdgeType.IMPORTS)))
        return files

    def __str__(self):
        return self.name

    def __repr__(self):
        return self.name

    def to_dict(self):
        return {"nodeType": NodeType.FILE.name.capitalize(), "id": self.node_id,
                "fileName": self.name, "filePath": self.path, "text": self.text,
                "clean_text": self.clean_text}

    def get_content(self):
        filepath = self.path if self.path else ''
        filename = "# Filename: " + filepath + self.name + "\n"
        if self.clean_text:
            return filename + self.clean_text
        elif self.text:
            return filename + self.text # 为了兼容没有经过 clean text 处理的数据
        else:
            return filename

class TextFile:
    """
    文本文件节点，目前包含MD和XML
    - name: 文件名
    - text: 文本内容
    """

    def __init__(self, node_id, name, text, path, codegraph):
        self.node_id = node_id
        self.name = name
        self.text = text
        self.path = path
        self.codegraph = codegraph

    @staticmethod
    def get_type():
        return NodeType.TEXTFILE

    def __str__(self):
        return self.name

    def __repr__(self):
        return self.name

    def to_dict(self):
        return {"nodeType": NodeType.TEXTFILE.name.capitalize(), "id": self.node_id, "name": self.name, "text": self.text, "path": self.path}
    
    def get_content(self):
        if self.text:
            return self.name + self.text
        else:
            return self.name

class Class:
    """
    类节点
    - name: 文件名
    - modifiers: 修饰符
    - comment: 注释
    """

    def __init__(self, node_id, name, class_type, comment, text, start_loc, end_loc, col, codegraph, clean_text):
        self.node_id = node_id
        self.name = name
        self.class_type = class_type
        self.comment = comment
        self.text = text
        self.start_loc = start_loc
        self.end_loc = end_loc
        self.col = col
        self.codegraph = codegraph
        self.clean_text = clean_text

    @staticmethod
    def get_type():
        return NodeType.CLASS

    def query_all_superclasses(self):
        s = self.get_all_superclasses()
        if len(s) == 0:
            return f"{self.name}没有继承或实现的类或接口"
        a = '\n'.join(list(map(lambda x: x.name, s)))
        return f'类{self.name}实现或继承了以下类或接口:\n{a}'

    def get_all_superclasses(self):
        superclasses = set(self.get_superclasses()) | set(self.get_interfaces())
        tmp = set()
        for superclass in superclasses:
            tmp |= superclass.get_all_superclasses()
        return superclasses | tmp

    def get_superclass_and_interfaces(self):
        superclass_or_interface_ids = self.get_superclass_and_interface_ids()
        return list(map(self.codegraph.get_node_by_id, superclass_or_interface_ids))

    def get_superclass_and_interface_ids(self):
      