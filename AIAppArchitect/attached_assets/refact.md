Directory structure:
└── smallcloudai-refact/
    ├── README.md
    ├── CONTRIBUTING.md
    ├── LICENSE
    ├── docs/
    │   ├── README.md
    │   ├── astro.config.mjs
    │   ├── Dockerfile
    │   ├── nginx.conf
    │   ├── package-lock.json
    │   ├── package.json
    │   ├── tsconfig.json
    │   ├── public/
    │   │   ├── docker-compose.yml
    │   │   ├── .keep
    │   │   ├── assets/
    │   │   └── videos/
    │   ├── src/
    │   │   ├── env.d.ts
    │   │   ├── assets/
    │   │   ├── components/
    │   │   │   ├── Head.astro
    │   │   │   ├── Navigation.astro
    │   │   │   ├── PageTransitions.astro
    │   │   │   └── Search.astro
    │   │   ├── content/
    │   │   │   ├── config.ts
    │   │   │   └── docs/
    │   │   │       ├── byok.md
    │   │   │       ├── contributing.md
    │   │   │       ├── faq.md
    │   │   │       ├── index.mdx
    │   │   │       ├── privacy.md
    │   │   │       ├── supported-models.md
    │   │   │       ├── features/
    │   │   │       │   ├── agent-integrations.md
    │   │   │       │   ├── ai-chat.md
    │   │   │       │   ├── ai-toolbox.md
    │   │   │       │   ├── code-completion.md
    │   │   │       │   ├── context.md
    │   │   │       │   ├── finetuning.md
    │   │   │       │   ├── ai-toolbox/
    │   │   │       │   │   ├── comments.md
    │   │   │       │   │   ├── debug.md
    │   │   │       │   │   ├── explain-code.md
    │   │   │       │   │   ├── fix-bugs.md
    │   │   │       │   │   ├── improve-code.md
    │   │   │       │   │   └── naming.md
    │   │   │       │   └── autonomous-agent/
    │   │   │       │       ├── getting-started.md
    │   │   │       │       ├── overview.md
    │   │   │       │       ├── rollback.md
    │   │   │       │       ├── tools.md
    │   │   │       │       └── integrations/
    │   │   │       │           ├── chrome.md
    │   │   │       │           ├── command-line-service.md
    │   │   │       │           ├── command-line-tool.md
    │   │   │       │           ├── docker.md
    │   │   │       │           ├── github.md
    │   │   │       │           ├── gitlab.md
    │   │   │       │           ├── index.md
    │   │   │       │           ├── mcp.md
    │   │   │       │           ├── mysql.md
    │   │   │       │           ├── pdb.md
    │   │   │       │           ├── postgresql.md
    │   │   │       │           └── shell-commands.md
    │   │   │       ├── guides/
    │   │   │       │   ├── reverse-proxy.md
    │   │   │       │   ├── authentication/
    │   │   │       │   │   └── keycloak.md
    │   │   │       │   ├── deployment/
    │   │   │       │   │   ├── runpod.md
    │   │   │       │   │   └── aws/
    │   │   │       │   │       ├── ec2.md
    │   │   │       │   │       ├── getting-started.md
    │   │   │       │   │       ├── marketplace.md
    │   │   │       │   │       └── usage.md
    │   │   │       │   ├── plugins/
    │   │   │       │   │   └── jetbrains/
    │   │   │       │   │       └── troubleshooting.md
    │   │   │       │   └── version-specific/
    │   │   │       │       ├── self-hosted.md
    │   │   │       │       ├── teams.md
    │   │   │       │       └── enterprise/
    │   │   │       │           ├── getting-started.md
    │   │   │       │           ├── license.md
    │   │   │       │           ├── model-hosting.md
    │   │   │       │           ├── plugins.md
    │   │   │       │           └── users.md
    │   │   │       ├── installation/
    │   │   │       │   ├── installation-hub.mdx
    │   │   │       │   ├── jetbrains.md
    │   │   │       │   └── vs-code.md
    │   │   │       ├── introduction/
    │   │   │       │   ├── quickstart.md
    │   │   │       │   └── usage-based-pricing.md
    │   │   │       └── supported-models/
    │   │   │           ├── code-llama.md
    │   │   │           ├── llama2.md
    │   │   │           ├── refact-llm.md
    │   │   │           ├── starcoder.md
    │   │   │           └── wizard-coder.md
    │   │   └── styles/
    │   │       ├── components.css
    │   │       ├── custom.css
    │   │       ├── index.css
    │   │       ├── layout.css
    │   │       ├── search.css
    │   │       ├── typography.css
    │   │       └── variables.css
    │   └── .astro/
    │       └── types.d.ts
    ├── refact-agent/
    │   ├── engine/
    │   │   ├── README.md
    │   │   ├── build.rs
    │   │   ├── Cargo.toml
    │   │   ├── CODE_OF_CONDUCT.md
    │   │   ├── CONTRIBUTING.md
    │   │   ├── Cross.toml
    │   │   ├── INTEGRATIONS.md
    │   │   ├── LICENSE
    │   │   ├── rustfmt.toml
    │   │   ├── .dockerignore
    │   │   ├── assets/
    │   │   │   └── integrations/
    │   │   ├── docker/
    │   │   │   ├── fix_sqlite_vec.h
    │   │   │   ├── lsp-debug.Dockerfile
    │   │   │   ├── lsp-release.Dockerfile
    │   │   │   └── chrome/
    │   │   │       ├── Dockerfile
    │   │   │       ├── nginx.conf
    │   │   │       └── supervisord.conf
    │   │   ├── examples/
    │   │   │   ├── anthropic_thinking.py
    │   │   │   ├── ast_definition.sh
    │   │   │   ├── ast_references.sh
    │   │   │   ├── chat_with_at_command.py
    │   │   │   ├── http_caps.sh
    │   │   │   ├── http_chat.sh
    │   │   │   ├── http_chat_passthrough.sh
    │   │   │   ├── http_commit_message.sh
    │   │   │   ├── http_completion.sh
    │   │   │   ├── http_rag_status.sh
    │   │   │   ├── http_subchat.sh
    │   │   │   ├── http_vecdb_search.sh
    │   │   │   ├── links.sh
    │   │   │   ├── lsp_runner.py
    │   │   │   ├── note3.py
    │   │   │   └── rag_skeletonize_video.py
    │   │   ├── python_binding_and_cmdline/
    │   │   │   ├── __init__.py
    │   │   │   ├── setup.py
    │   │   │   └── refact/
    │   │   │       ├── __init__.py
    │   │   │       ├── chat_client.py
    │   │   │       ├── cli_app_switcher.py
    │   │   │       ├── cli_export.py
    │   │   │       ├── cli_inspect.py
    │   │   │       ├── cli_main.py
    │   │   │       ├── cli_markdown.py
    │   │   │       ├── cli_printing.py
    │   │   │       ├── cli_settings.py
    │   │   │       ├── cli_statusbar.py
    │   │   │       ├── cli_streaming.py
    │   │   │       ├── lsp_runner.py
    │   │   │       └── traj_compressor.py
    │   │   ├── src/
    │   │   │   ├── background_tasks.rs
    │   │   │   ├── call_validation.rs
    │   │   │   ├── completion_cache.rs
    │   │   │   ├── constants.rs
    │   │   │   ├── custom_error.rs
    │   │   │   ├── fetch_embedding.rs
    │   │   │   ├── file_filter.rs
    │   │   │   ├── files_blocklist.rs
    │   │   │   ├── files_correction.rs
    │   │   │   ├── files_correction_cache.rs
    │   │   │   ├── files_in_jsonl.rs
    │   │   │   ├── files_in_workspace.rs
    │   │   │   ├── forward_to_hf_endpoint.rs
    │   │   │   ├── forward_to_openai_endpoint.rs
    │   │   │   ├── fuzzy_search.rs
    │   │   │   ├── global_context.rs
    │   │   │   ├── http.rs
    │   │   │   ├── indexing_utils.rs
    │   │   │   ├── json_utils.rs
    │   │   │   ├── known_models.json
    │   │   │   ├── lsp.rs
    │   │   │   ├── main.rs
    │   │   │   ├── memories.rs
    │   │   │   ├── nicer_logs.rs
    │   │   │   ├── privacy.rs
    │   │   │   ├── restream.rs
    │   │   │   ├── scratchpad_abstract.rs
    │   │   │   ├── subchat.rs
    │   │   │   ├── tokens.rs
    │   │   │   ├── version.rs
    │   │   │   ├── agentic/
    │   │   │   │   ├── compress_trajectory.rs
    │   │   │   │   ├── generate_commit_message.rs
    │   │   │   │   ├── generate_follow_up_message.rs
    │   │   │   │   └── mod.rs
    │   │   │   ├── ast/
    │   │   │   │   ├── ast_db.rs
    │   │   │   │   ├── ast_indexer_thread.rs
    │   │   │   │   ├── ast_parse_anything.rs
    │   │   │   │   ├── ast_structs.rs
    │   │   │   │   ├── chunk_utils.rs
    │   │   │   │   ├── dummy_tokenizer.json
    │   │   │   │   ├── file_splitter.rs
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── parse_common.rs
    │   │   │   │   ├── parse_python.rs
    │   │   │   │   ├── alt_testsuite/
    │   │   │   │   │   ├── cpp_goat_library.correct
    │   │   │   │   │   ├── cpp_goat_library.h
    │   │   │   │   │   ├── cpp_goat_main.correct
    │   │   │   │   │   ├── cpp_goat_main.cpp
    │   │   │   │   │   ├── jump_to_conclusions_annotated.py
    │   │   │   │   │   ├── py_goat_library.correct
    │   │   │   │   │   ├── py_goat_library.py
    │   │   │   │   │   ├── py_goat_library_annotated.py
    │   │   │   │   │   ├── py_goat_main.py
    │   │   │   │   │   ├── py_goat_main_annotated.py
    │   │   │   │   │   ├── py_torture1_attr.py
    │   │   │   │   │   ├── py_torture1_attr_annotated.py
    │   │   │   │   │   ├── py_torture2_resolving.py
    │   │   │   │   │   └── py_torture2_resolving_annotated.py
    │   │   │   │   └── treesitter/
    │   │   │   │       ├── ast_instance_structs.rs
    │   │   │   │       ├── file_ast_markup.rs
    │   │   │   │       ├── language_id.rs
    │   │   │   │       ├── mod.rs
    │   │   │   │       ├── parsers.rs
    │   │   │   │       ├── skeletonizer.rs
    │   │   │   │       ├── structs.rs
    │   │   │   │       └── parsers/
    │   │   │   │           ├── cpp.rs
    │   │   │   │           ├── java.rs
    │   │   │   │           ├── js.rs
    │   │   │   │           ├── python.rs
    │   │   │   │           ├── rust.rs
    │   │   │   │           ├── tests.rs
    │   │   │   │           ├── ts.rs
    │   │   │   │           ├── utils.rs
    │   │   │   │           └── tests/
    │   │   │   │               ├── cpp.rs
    │   │   │   │               ├── java.rs
    │   │   │   │               ├── js.rs
    │   │   │   │               ├── python.rs
    │   │   │   │               ├── rust.rs
    │   │   │   │               ├── ts.rs
    │   │   │   │               └── cases/
    │   │   │   │                   ├── cpp/
    │   │   │   │                   │   ├── circle.cpp
    │   │   │   │                   │   ├── circle.cpp.decl_json
    │   │   │   │                   │   ├── circle.cpp.skeleton
    │   │   │   │                   │   ├── main.cpp
    │   │   │   │                   │   └── main.cpp.json
    │   │   │   │                   ├── java/
    │   │   │   │                   │   ├── main.java
    │   │   │   │                   │   ├── main.java.json
    │   │   │   │                   │   ├── person.java
    │   │   │   │                   │   ├── person.java.decl_json
    │   │   │   │                   │   └── person.java.skeleton
    │   │   │   │                   ├── js/
    │   │   │   │                   │   ├── car.js
    │   │   │   │                   │   ├── car.js.decl_json
    │   │   │   │                   │   ├── car.js.skeleton
    │   │   │   │                   │   ├── main.js
    │   │   │   │                   │   └── main.js.json
    │   │   │   │                   ├── python/
    │   │   │   │                   │   ├── calculator.py
    │   │   │   │                   │   ├── calculator.py.decl_json
    │   │   │   │                   │   ├── calculator.py.skeleton
    │   │   │   │                   │   ├── main.py
    │   │   │   │                   │   └── main.py.json
    │   │   │   │                   ├── rust/
    │   │   │   │                   │   ├── main.rs
    │   │   │   │                   │   ├── main.rs.json
    │   │   │   │                   │   ├── point.rs
    │   │   │   │                   │   ├── point.rs.decl_json
    │   │   │   │                   │   └── point.rs.skeleton
    │   │   │   │                   └── ts/
    │   │   │   │                       ├── main.ts
    │   │   │   │                       ├── main.ts.json
    │   │   │   │                       ├── person.ts
    │   │   │   │                       ├── person.ts.decl_json
    │   │   │   │                       └── person.ts.skeleton
    │   │   │   ├── at_commands/
    │   │   │   │   ├── at_ast_definition.rs
    │   │   │   │   ├── at_ast_reference.rs
    │   │   │   │   ├── at_commands.rs
    │   │   │   │   ├── at_file.rs
    │   │   │   │   ├── at_knowledge.rs
    │   │   │   │   ├── at_search.rs
    │   │   │   │   ├── at_tree.rs
    │   │   │   │   ├── at_web.rs
    │   │   │   │   ├── execute_at.rs
    │   │   │   │   └── mod.rs
    │   │   │   ├── caps/
    │   │   │   │   ├── caps.rs
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── providers.rs
    │   │   │   │   └── self_hosted.rs
    │   │   │   ├── cloud/
    │   │   │   │   ├── experts_req.rs
    │   │   │   │   ├── messages_req.rs
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── threads_req.rs
    │   │   │   │   └── threads_sub.rs
    │   │   │   ├── dashboard/
    │   │   │   │   ├── dashboard.rs
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── structs.rs
    │   │   │   │   └── utils.rs
    │   │   │   ├── git/
    │   │   │   │   ├── checkpoints.rs
    │   │   │   │   ├── commit_info.rs
    │   │   │   │   ├── mod.rs
    │   │   │   │   └── operations.rs
    │   │   │   ├── http/
    │   │   │   │   ├── routers.rs
    │   │   │   │   ├── utils.rs
    │   │   │   │   └── routers/
    │   │   │   │       ├── info.rs
    │   │   │   │       ├── v1.rs
    │   │   │   │       └── v1/
    │   │   │   │           ├── ast.rs
    │   │   │   │           ├── at_commands.rs
    │   │   │   │           ├── at_tools.rs
    │   │   │   │           ├── caps.rs
    │   │   │   │           ├── chat.rs
    │   │   │   │           ├── chat_based_handlers.rs
    │   │   │   │           ├── code_completion.rs
    │   │   │   │           ├── code_lens.rs
    │   │   │   │           ├── customization.rs
    │   │   │   │           ├── dashboard.rs
    │   │   │   │           ├── docker.rs
    │   │   │   │           ├── file_edit_tools.rs
    │   │   │   │           ├── git.rs
    │   │   │   │           ├── graceful_shutdown.rs
    │   │   │   │           ├── gui_help_handlers.rs
    │   │   │   │           ├── links.rs
    │   │   │   │           ├── lsp_like_handlers.rs
    │   │   │   │           ├── providers.rs
    │   │   │   │           ├── snippet_accepted.rs
    │   │   │   │           ├── status.rs
    │   │   │   │           ├── subchat.rs
    │   │   │   │           ├── sync_files.rs
    │   │   │   │           ├── system_prompt.rs
    │   │   │   │           ├── telemetry_chat.rs
    │   │   │   │           ├── telemetry_network.rs
    │   │   │   │           ├── v1_integrations.rs
    │   │   │   │           ├── vecdb.rs
    │   │   │   │           └── workspace.rs
    │   │   │   ├── integrations/
    │   │   │   │   ├── config_chat.rs
    │   │   │   │   ├── integr_abstract.rs
    │   │   │   │   ├── integr_chrome.rs
    │   │   │   │   ├── integr_cmdline.rs
    │   │   │   │   ├── integr_cmdline_service.rs
    │   │   │   │   ├── integr_github.rs
    │   │   │   │   ├── integr_gitlab.rs
    │   │   │   │   ├── integr_mysql.rs
    │   │   │   │   ├── integr_pdb.rs
    │   │   │   │   ├── integr_postgres.rs
    │   │   │   │   ├── integr_shell.rs
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── process_io_utils.rs
    │   │   │   │   ├── project_summary_chat.rs
    │   │   │   │   ├── running_integrations.rs
    │   │   │   │   ├── sessions.rs
    │   │   │   │   ├── setting_up_integrations.rs
    │   │   │   │   ├── utils.rs
    │   │   │   │   ├── yaml_schema.rs
    │   │   │   │   ├── docker/
    │   │   │   │   │   ├── docker_container_manager.rs
    │   │   │   │   │   ├── docker_ssh_tunnel_utils.rs
    │   │   │   │   │   ├── integr_docker.rs
    │   │   │   │   │   ├── integr_isolation.rs
    │   │   │   │   │   └── mod.rs
    │   │   │   │   └── mcp/
    │   │   │   │       ├── integr_mcp_common.rs
    │   │   │   │       ├── integr_mcp_sse.rs
    │   │   │   │       ├── integr_mcp_stdio.rs
    │   │   │   │       ├── mcp_sse_schema.yaml
    │   │   │   │       ├── mcp_stdio_schema.yaml
    │   │   │   │       ├── mod.rs
    │   │   │   │       ├── session_mcp.rs
    │   │   │   │       └── tool_mcp.rs
    │   │   │   ├── postprocessing/
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── pp_command_output.rs
    │   │   │   │   ├── pp_context_files.rs
    │   │   │   │   ├── pp_plain_text.rs
    │   │   │   │   └── pp_utils.rs
    │   │   │   ├── scratchpads/
    │   │   │   │   ├── chat_generic.rs
    │   │   │   │   ├── chat_passthrough.rs
    │   │   │   │   ├── chat_utils_deltadelta.rs
    │   │   │   │   ├── chat_utils_limit_history.rs
    │   │   │   │   ├── chat_utils_prompts.rs
    │   │   │   │   ├── code_completion_fim.rs
    │   │   │   │   ├── code_completion_replace.rs
    │   │   │   │   ├── comments_parser.rs
    │   │   │   │   ├── completon_rag.rs
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── multimodality.rs
    │   │   │   │   ├── passthrough_convert_messages.rs
    │   │   │   │   ├── scratchpad_utils.rs
    │   │   │   │   └── token_count_cache.rs
    │   │   │   ├── telemetry/
    │   │   │   │   ├── basic_chat.rs
    │   │   │   │   ├── basic_comp_counters.rs
    │   │   │   │   ├── basic_network.rs
    │   │   │   │   ├── basic_robot_human.rs
    │   │   │   │   ├── basic_transmit.rs
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── snippets_collection.rs
    │   │   │   │   ├── snippets_transmit.rs
    │   │   │   │   ├── telemetry_structs.rs
    │   │   │   │   └── utils.rs
    │   │   │   ├── tools/
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── scope_utils.rs
    │   │   │   │   ├── tool_ast_definition.rs
    │   │   │   │   ├── tool_ast_reference.rs
    │   │   │   │   ├── tool_cat.rs
    │   │   │   │   ├── tool_create_knowledge.rs
    │   │   │   │   ├── tool_create_memory_bank.rs
    │   │   │   │   ├── tool_knowledge.rs
    │   │   │   │   ├── tool_locate_search.rs
    │   │   │   │   ├── tool_mv.rs
    │   │   │   │   ├── tool_regex_search.rs
    │   │   │   │   ├── tool_rm.rs
    │   │   │   │   ├── tool_search.rs
    │   │   │   │   ├── tool_strategic_planning.rs
    │   │   │   │   ├── tool_tree.rs
    │   │   │   │   ├── tool_web.rs
    │   │   │   │   ├── tools_description.rs
    │   │   │   │   ├── tools_execute.rs
    │   │   │   │   ├── tools_list.rs
    │   │   │   │   └── file_edit/
    │   │   │   │       ├── auxiliary.rs
    │   │   │   │       ├── mod.rs
    │   │   │   │       ├── tool_create_textdoc.rs
    │   │   │   │       ├── tool_update_textdoc.rs
    │   │   │   │       └── tool_update_textdoc_regex.rs
    │   │   │   ├── vecdb/
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── vdb_emb_aux.rs
    │   │   │   │   ├── vdb_error.rs
    │   │   │   │   ├── vdb_file_splitter.rs
    │   │   │   │   ├── vdb_highlev.rs
    │   │   │   │   ├── vdb_init.rs
    │   │   │   │   ├── vdb_remote.rs
    │   │   │   │   ├── vdb_sqlite.rs
    │   │   │   │   ├── vdb_structs.rs
    │   │   │   │   └── vdb_thread.rs
    │   │   │   └── yaml_configs/
    │   │   │       ├── create_configs.rs
    │   │   │       ├── customization_compiled_in.yaml
    │   │   │       ├── customization_loader.rs
    │   │   │       ├── default_builtin_tools.yaml
    │   │   │       ├── default_customization.yaml
    │   │   │       ├── default_indexing.yaml
    │   │   │       ├── default_privacy.yaml
    │   │   │       ├── default_shell.yaml
    │   │   │       ├── mod.rs
    │   │   │       └── default_providers/
    │   │   │           ├── anthropic.yaml
    │   │   │           ├── custom.yaml
    │   │   │           ├── deepseek.yaml
    │   │   │           ├── google_gemini.yaml
    │   │   │           ├── groq.yaml
    │   │   │           ├── lmstudio.yaml
    │   │   │           ├── ollama.yaml
    │   │   │           ├── openai.yaml
    │   │   │           ├── openrouter.yaml
    │   │   │           └── xai.yaml
    │   │   ├── tests/
    │   │   │   ├── lsp_connect.py
    │   │   │   ├── test01_completion_edge_cases.py
    │   │   │   ├── test02_completion_with_rag.py
    │   │   │   ├── test03_at_commands_completion.py
    │   │   │   ├── test04_completion_lsp.py
    │   │   │   ├── test05_is_openai_compatible.py
    │   │   │   ├── test06_tool_not_tool.py
    │   │   │   ├── test08_post_processing.py
    │   │   │   ├── test09_ast_pick_up_changes.py
    │   │   │   ├── test10_locate.py
    │   │   │   ├── test12_tools_authorize_calls.py
    │   │   │   ├── test13_vision.py
    │   │   │   ├── test_diff_handlers.py
    │   │   │   ├── emergency_frog_situation/
    │   │   │   │   ├── frog.py
    │   │   │   │   ├── holiday.py
    │   │   │   │   ├── jump_to_conclusions.py
    │   │   │   │   ├── set_as_avatar.py
    │   │   │   │   └── work_day.py
    │   │   │   └── test13_data/
    │   │   └── .cargo/
    │   │       └── config.toml
    │   └── gui/
    │       ├── README.md
    │       ├── codegen.ts
    │       ├── index.html
    │       ├── LICENSE.txt
    │       ├── package-lock.json
    │       ├── package.json
    │       ├── tsconfig.json
    │       ├── tsconfig.node.json
    │       ├── urqlProvider.tsx
    │       ├── vite.config.ts
    │       ├── vite.node.config.ts
    │       ├── .eslintrc.cjs
    │       ├── .prettierignore
    │       ├── .prettierrc.js
    │       ├── generated/
    │       │   ├── documents.ts
    │       │   ├── schema.graphql
    │       │   └── graphql/
    │       │       ├── fragment-masking.ts
    │       │       ├── gql.ts
    │       │       ├── graphql.ts
    │       │       └── index.ts
    │       ├── patches/
    │       │   └── happy-dom+17.4.6.patch
    │       ├── public/
    │       │   └── mockServiceWorker.js
    │       ├── src/
    │       │   ├── debugConfig.ts
    │       │   ├── main.tsx
    │       │   ├── vite-env.d.ts
    │       │   ├── __fixtures__/
    │       │   │   ├── caps.ts
    │       │   │   ├── chat.ts
    │       │   │   ├── chat_config_thread.ts
    │       │   │   ├── chat_diff.ts
    │       │   │   ├── chat_links_response.ts
    │       │   │   ├── chat_textdoc.ts
    │       │   │   ├── checkpoints.ts
    │       │   │   ├── confirmation.ts
    │       │   │   ├── context_files.ts
    │       │   │   ├── fim.ts
    │       │   │   ├── history.ts
    │       │   │   ├── index.ts
    │       │   │   ├── integrations.ts
    │       │   │   ├── knowledge.ts
    │       │   │   ├── markdown-issue.ts
    │       │   │   ├── markdown.ts
    │       │   │   ├── msw.ts
    │       │   │   ├── prompts.ts
    │       │   │   ├── some_chrome_screenshots.ts
    │       │   │   ├── survey_questions.ts
    │       │   │   ├── table.ts
    │       │   │   └── tools_response.ts
    │       │   ├── __tests__/
    │       │   │   ├── ChatCapsFetchError.test.tsx
    │       │   │   ├── DeleteChat.test.tsx
    │       │   │   ├── isAbsolutePath.test.ts
    │       │   │   ├── RestoreChat.test.tsx
    │       │   │   ├── StartNewChat.test.tsx
    │       │   │   └── UserSurvey.test.tsx
    │       │   ├── app/
    │       │   │   ├── middleware.ts
    │       │   │   ├── storage.ts
    │       │   │   └── store.ts
    │       │   ├── components/
    │       │   │   ├── Accordion/
    │       │   │   │   ├── Accordion.module.css
    │       │   │   │   ├── Accordion.stories.tsx
    │       │   │   │   ├── Accordion.tsx
    │       │   │   │   └── index.ts
    │       │   │   ├── Buttons/
    │       │   │   │   ├── button.module.css
    │       │   │   │   ├── Buttons.tsx
    │       │   │   │   ├── FadedButton.tsx
    │       │   │   │   ├── index.tsx
    │       │   │   │   └── ThinkingButton.tsx
    │       │   │   ├── Callout/
    │       │   │   │   ├── Callout.module.css
    │       │   │   │   ├── Callout.stories.tsx
    │       │   │   │   ├── Callout.tsx
    │       │   │   │   ├── ErrorCallout.stories.tsx
    │       │   │   │   └── index.ts
    │       │   │   ├── Chart/
    │       │   │   │   ├── Chart.stories.tsx
    │       │   │   │   └── Chart.tsx
    │       │   │   ├── Chat/
    │       │   │   │   ├── Chat.stories.tsx
    │       │   │   │   ├── Chat.tsx
    │       │   │   │   └── index.tsx
    │       │   │   ├── ChatContent/
    │       │   │   │   ├── AssistantInput.tsx
    │       │   │   │   ├── ChatContent.module.css
    │       │   │   │   ├── ChatContent.stories.tsx
    │       │   │   │   ├── ChatContent.tsx
    │       │   │   │   ├── ContextFiles.tsx
    │       │   │   │   ├── DiffContent.test.tsx
    │       │   │   │   ├── DiffContent.tsx
    │       │   │   │   ├── index.tsx
    │       │   │   │   ├── LikeButton.module.css
    │       │   │   │   ├── LikeButton.tsx
    │       │   │   │   ├── PlaceHolderText.tsx
    │       │   │   │   ├── PlainText.tsx
    │       │   │   │   ├── SystemInput.tsx
    │       │   │   │   ├── ToolsContent.tsx
    │       │   │   │   ├── UserInput.tsx
    │       │   │   │   └── ReasoningContent/
    │       │   │   │       ├── index.ts
    │       │   │   │       ├── ReasoningContent.module.css
    │       │   │   │       └── ReasoningContent.tsx
    │       │   │   ├── ChatForm/
    │       │   │   │   ├── actions.ts
    │       │   │   │   ├── ChatControls.tsx
    │       │   │   │   ├── ChatForm.module.css
    │       │   │   │   ├── ChatForm.stories.tsx
    │       │   │   │   ├── ChatForm.test.tsx
    │       │   │   │   ├── ChatForm.tsx
    │       │   │   │   ├── FilesPreview.tsx
    │       │   │   │   ├── Form.tsx
    │       │   │   │   ├── index.tsx
    │       │   │   │   ├── PromptSelect.tsx
    │       │   │   │   ├── RetryForm.tsx
    │       │   │   │   ├── TokensPreview.tsx
    │       │   │   │   ├── ToolConfirmation.module.css
    │       │   │   │   ├── ToolConfirmation.stories.tsx
    │       │   │   │   ├── ToolConfirmation.tsx
    │       │   │   │   ├── ToolUseSwitch.tsx
    │       │   │   │   ├── useCheckBoxes.ts
    │       │   │   │   ├── useCommandCompletionAndPreviewFiles.ts
    │       │   │   │   ├── useInputValue.ts
    │       │   │   │   ├── utils.ts
    │       │   │   │   ├── AgentCapabilities/
    │       │   │   │   │   ├── AgentCapabilities.tsx
    │       │   │   │   │   ├── index.ts
    │       │   │   │   │   ├── ToolGroup.module.css
    │       │   │   │   │   ├── ToolGroup.tsx
    │       │   │   │   │   ├── ToolGroupList.tsx
    │       │   │   │   │   ├── ToolGroups.tsx
    │       │   │   │   │   ├── ToolsList.tsx
    │       │   │   │   │   └── useToolGroups.ts
    │       │   │   │   └── SuggestNewChat/
    │       │   │   │       ├── index.ts
    │       │   │   │       ├── SuggestNewChat.module.css
    │       │   │   │       └── SuggestNewChat.tsx
    │       │   │   ├── ChatHistory/
    │       │   │   │   ├── ChatHistory.tsx
    │       │   │   │   ├── HistoryItem.tsx
    │       │   │   │   └── index.tsx
    │       │   │   ├── ChatLinks/
    │       │   │   │   ├── ChatLinks.module.css
    │       │   │   │   ├── ChatLinks.stories.tsx
    │       │   │   │   ├── ChatLinks.tsx
    │       │   │   │   ├── index.ts
    │       │   │   │   └── UncommittedChangesWarning.tsx
    │       │   │   ├── ChatRawJSON/
    │       │   │   │   ├── ChatRawJSON.tsx
    │       │   │   │   └── index.tsx
    │       │   │   ├── Checkbox/
    │       │   │   │   ├── Checkbox.stories.tsx
    │       │   │   │   ├── Checkbox.tsx
    │       │   │   │   └── index.ts
    │       │   │   ├── Collapsible/
    │       │   │   │   ├── Chevron.module.css
    │       │   │   │   ├── Chevron.tsx
    │       │   │   │   ├── collapsible.module.css
    │       │   │   │   ├── Collapsible.stories.tsx
    │       │   │   │   ├── Collapsible.tsx
    │       │   │   │   └── index.tsx
    │       │   │   ├── ComboBox/
    │       │   │   │   ├── ComboBox.module.css
    │       │   │   │   ├── ComboBox.stories.tsx
    │       │   │   │   ├── ComboBox.test.tsx
    │       │   │   │   ├── ComboBox.tsx
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── Item.tsx
    │       │   │   │   ├── Popover.tsx
    │       │   │   │   ├── utils.test.ts
    │       │   │   │   └── utils.ts
    │       │   │   ├── Command/
    │       │   │   │   ├── Command.module.css
    │       │   │   │   ├── index.tsx
    │       │   │   │   └── Markdown.tsx
    │       │   │   ├── DeletePopover/
    │       │   │   │   ├── DeletePopover.module.css
    │       │   │   │   ├── DeletePopover.tsx
    │       │   │   │   └── index.ts
    │       │   │   ├── DialogImage/
    │       │   │   │   ├── DialogImage.tsx
    │       │   │   │   └── index.ts
    │       │   │   ├── DocumentationSettings/
    │       │   │   │   ├── DocumentationActions.tsx
    │       │   │   │   ├── DocumentationSettings.stories.tsx
    │       │   │   │   ├── DocumentationSettings.tsx
    │       │   │   │   └── index.ts
    │       │   │   ├── Dropzone/
    │       │   │   │   ├── Dropzone.tsx
    │       │   │   │   └── index.tsx
    │       │   │   ├── FileList/
    │       │   │   │   ├── file-list.module.css
    │       │   │   │   ├── FileList.tsx
    │       │   │   │   └── index.ts
    │       │   │   ├── FileUpload/
    │       │   │   │   ├── FileUpload.tsx
    │       │   │   │   └── index.tsx
    │       │   │   ├── FIMDebug/
    │       │   │   │   ├── fim.module.css
    │       │   │   │   ├── FIMDebug.stories.tsx
    │       │   │   │   ├── FIMDebug.tsx
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── SearchContext.tsx
    │       │   │   │   └── SymoblList.tsx
    │       │   │   ├── IntegrationsView/
    │       │   │   │   ├── CustomFieldsAndWidgets.tsx
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── IntegrationsView.module.css
    │       │   │   │   ├── IntegrationsView.tsx
    │       │   │   │   ├── Confirmation/
    │       │   │   │   │   ├── Confirmation.tsx
    │       │   │   │   │   └── index.ts
    │       │   │   │   ├── DisplayIntegrations/
    │       │   │   │   │   ├── GlobalIntegrations.tsx
    │       │   │   │   │   ├── IntegrationCard.module.css
    │       │   │   │   │   ├── IntegrationCard.tsx
    │       │   │   │   │   ├── IntegrationsList.tsx
    │       │   │   │   │   ├── NewIntegrations.tsx
    │       │   │   │   │   ├── ProjectIntegrations.tsx
    │       │   │   │   │   └── useUpdateIntegration.ts
    │       │   │   │   ├── Header/
    │       │   │   │   │   ├── IntegrationsHeader.module.css
    │       │   │   │   │   └── IntegrationsHeader.tsx
    │       │   │   │   ├── hooks/
    │       │   │   │   │   ├── useFormAvailability.ts
    │       │   │   │   │   ├── useFormFields.ts
    │       │   │   │   │   └── useIntegrations.ts
    │       │   │   │   ├── IntegrationDocker/
    │       │   │   │   │   ├── DockerContainerCard.tsx
    │       │   │   │   │   ├── index.ts
    │       │   │   │   │   ├── IntegrationDocker.module.css
    │       │   │   │   │   └── IntegrationDocker.tsx
    │       │   │   │   ├── IntegrationForm/
    │       │   │   │   │   ├── ErrorState.tsx
    │       │   │   │   │   ├── FormAvailabilityAndDelete.tsx
    │       │   │   │   │   ├── FormFields.tsx
    │       │   │   │   │   ├── FormSmartlinks.tsx
    │       │   │   │   │   ├── index.ts
    │       │   │   │   │   ├── IntegrationAvailability.tsx
    │       │   │   │   │   ├── IntegrationForm.module.css
    │       │   │   │   │   ├── IntegrationForm.tsx
    │       │   │   │   │   ├── MCPLogs.tsx
    │       │   │   │   │   └── useGetMCPLogs.ts
    │       │   │   │   ├── IntegrationsTable/
    │       │   │   │   │   ├── ArgumentsTable.tsx
    │       │   │   │   │   ├── ConfirmationTable.module.css
    │       │   │   │   │   ├── ConfirmationTable.tsx
    │       │   │   │   │   ├── DefaultCell.tsx
    │       │   │   │   │   ├── KeyValueTable.tsx
    │       │   │   │   │   └── ParametersTable.tsx
    │       │   │   │   └── IntermediateIntegration/
    │       │   │   │       ├── index.ts
    │       │   │   │       ├── IntegrationPathField.tsx
    │       │   │   │       ├── IntermediateIntegration.module.css
    │       │   │   │       └── IntermediateIntegration.tsx
    │       │   │   ├── Link/
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── Link.module.css
    │       │   │   │   └── Link.tsx
    │       │   │   ├── LogoAnimation/
    │       │   │   │   ├── animationData.json
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── LogoAnimation.stories.tsx
    │       │   │   │   ├── LogoAnimation.tsx
    │       │   │   │   └── types.ts
    │       │   │   ├── Markdown/
    │       │   │   │   ├── CodeBlock.tsx
    │       │   │   │   ├── highlightjs.css
    │       │   │   │   ├── index.tsx
    │       │   │   │   ├── Markdown.module.css
    │       │   │   │   ├── Markdown.tsx
    │       │   │   │   └── Pre.tsx
    │       │   │   ├── OnOffSwitch/
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── OnOffSwitch.module.css
    │       │   │   │   └── OnOffSwitch.tsx
    │       │   │   ├── PageWrapper/
    │       │   │   │   ├── index.tsx
    │       │   │   │   ├── PageWrapper.module.css
    │       │   │   │   └── PageWrapper.tsx
    │       │   │   ├── Portal/
    │       │   │   │   ├── index.ts
    │       │   │   │   └── Portal.tsx
    │       │   │   ├── Reveal/
    │       │   │   │   ├── index.tsx
    │       │   │   │   ├── reveal.module.css
    │       │   │   │   ├── Reveal.stories.tsx
    │       │   │   │   └── Reveal.tsx
    │       │   │   ├── ScrollArea/
    │       │   │   │   ├── FollowButton.tsx
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── ScrollArea.module.css
    │       │   │   │   ├── ScrollArea.stories.tsx
    │       │   │   │   ├── ScrollArea.tsx
    │       │   │   │   ├── ScrollAreaWithAnchor.stories.tsx
    │       │   │   │   ├── ScrollAreaWithAnchor.tsx
    │       │   │   │   ├── ScrollToBottomButton.tsx
    │       │   │   │   ├── todo.md
    │       │   │   │   ├── useIsIntersecting.ts
    │       │   │   │   ├── useSapceCalculator.ts
    │       │   │   │   ├── useScrollContext.ts
    │       │   │   │   └── utils.ts
    │       │   │   ├── Select/
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── select.module.css
    │       │   │   │   ├── Select.stories.tsx
    │       │   │   │   └── Select.tsx
    │       │   │   ├── Sidebar/
    │       │   │   │   ├── index.tsx
    │       │   │   │   ├── Sidebar.module.css
    │       │   │   │   ├── Sidebar.stories.tsx
    │       │   │   │   ├── Sidebar.tsx
    │       │   │   │   └── GroupTree/
    │       │   │   │       ├── CustomTreeNode.module.css
    │       │   │   │       ├── CustomTreeNode.tsx
    │       │   │   │       ├── FolderIcon.tsx
    │       │   │   │       ├── GroupTree.module.css
    │       │   │   │       ├── GroupTree.tsx
    │       │   │   │       ├── index.ts
    │       │   │   │       ├── NavTreeSubs.graphql
    │       │   │   │       ├── NavTreeWantWorkspaces.graphql
    │       │   │   │       ├── useGroupTree.ts
    │       │   │   │       ├── utils.ts
    │       │   │   │       └── ConfirmGroupSelection/
    │       │   │   │           ├── ConfirmGroupSelection.graphql
    │       │   │   │           ├── ConfirmGroupSelection.module.css
    │       │   │   │           ├── ConfirmGroupSelection.tsx
    │       │   │   │           └── index.ts
    │       │   │   ├── SmartLink/
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── SmartLink.module.css
    │       │   │   │   └── SmartLink.tsx
    │       │   │   ├── Spinner/
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── Spinner.module.css
    │       │   │   │   ├── Spinner.stories.tsx
    │       │   │   │   └── Spinner.tsx
    │       │   │   ├── StatisticView/
    │       │   │   │   ├── StatisticView.stories.tsx
    │       │   │   │   └── StatisticView.tsx
    │       │   │   ├── Table/
    │       │   │   │   ├── formatTableCell.test.tsx
    │       │   │   │   ├── formatTableCell.ts
    │       │   │   │   ├── Table.module.css
    │       │   │   │   ├── Table.stories.tsx
    │       │   │   │   ├── Table.tsx
    │       │   │   │   ├── TableCell.tsx
    │       │   │   │   └── TableRow.tsx
    │       │   │   ├── Text/
    │       │   │   │   ├── AnimatedText.module.css
    │       │   │   │   ├── AnimatedText.stories.tsx
    │       │   │   │   ├── AnimatedText.tsx
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── Text.module.css
    │       │   │   │   ├── Text.tsx
    │       │   │   │   ├── TruncateLeft.tsx
    │       │   │   │   └── TruncateRight.tsx
    │       │   │   ├── TextArea/
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── TextArea.module.css
    │       │   │   │   ├── TextArea.stories.tsx
    │       │   │   │   ├── TextArea.test.tsx
    │       │   │   │   └── TextArea.tsx
    │       │   │   ├── Theme/
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── theme-config.css
    │       │   │   │   └── Theme.tsx
    │       │   │   ├── Toolbar/
    │       │   │   │   ├── Dropdown.tsx
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── Toolbar.module.css
    │       │   │   │   └── Toolbar.tsx
    │       │   │   ├── Tools/
    │       │   │   │   ├── Texdoc.module.css
    │       │   │   │   ├── Textdoc.tsx
    │       │   │   │   └── types.ts
    │       │   │   ├── Tour/
    │       │   │   │   ├── index.tsx
    │       │   │   │   ├── Tour.tsx
    │       │   │   │   ├── TourBox.tsx
    │       │   │   │   ├── TourBubble.tsx
    │       │   │   │   ├── TourButton.tsx
    │       │   │   │   ├── TourEnd.tsx
    │       │   │   │   ├── TourTitle.tsx
    │       │   │   │   └── Welcome.tsx
    │       │   │   └── UsageCounter/
    │       │   │       ├── index.ts
    │       │   │       ├── UsageCounter.fixtures.ts
    │       │   │       ├── UsageCounter.module.css
    │       │   │       ├── UsageCounter.stories.tsx
    │       │   │       ├── UsageCounter.tsx
    │       │   │       └── useUsageCounter.ts
    │       │   ├── contexts/
    │       │   │   └── AbortControllers.tsx
    │       │   ├── events/
    │       │   │   ├── index.ts
    │       │   │   └── setup.ts
    │       │   ├── features/
    │       │   │   ├── App.module.css
    │       │   │   ├── App.tsx
    │       │   │   ├── Documentation.tsx
    │       │   │   ├── TipOfTheDay.tsx
    │       │   │   ├── Tour.tsx
    │       │   │   ├── AttachedImages/
    │       │   │   │   ├── imagesSlice.ts
    │       │   │   │   └── index.ts
    │       │   │   ├── Chat/
    │       │   │   │   ├── activeFile.ts
    │       │   │   │   ├── Chat.test.tsx
    │       │   │   │   ├── Chat.tsx
    │       │   │   │   ├── currentProject.ts
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── selectedSnippet.ts
    │       │   │   │   └── Thread/
    │       │   │   │       ├── actions.ts
    │       │   │   │       ├── index.ts
    │       │   │   │       ├── reducer.test.ts
    │       │   │   │       ├── reducer.ts
    │       │   │   │       ├── selectors.ts
    │       │   │   │       ├── types.ts
    │       │   │   │       ├── utils.test.ts
    │       │   │   │       └── utils.ts
    │       │   │   ├── Checkpoints/
    │       │   │   │   ├── CheckpointButton.tsx
    │       │   │   │   ├── Checkpoints.module.css
    │       │   │   │   ├── Checkpoints.stories.tsx
    │       │   │   │   ├── Checkpoints.tsx
    │       │   │   │   ├── checkpointsSlice.ts
    │       │   │   │   ├── CheckpointsStatusIndicator.tsx
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── types.ts
    │       │   │   │   └── utils.ts
    │       │   │   ├── CoinBalance/
    │       │   │   │   ├── coinBalanceSlice.ts
    │       │   │   │   └── index.ts
    │       │   │   ├── Config/
    │       │   │   │   ├── configSlice.ts
    │       │   │   │   └── FeatureMenu.tsx
    │       │   │   ├── Errors/
    │       │   │   │   ├── errorsSlice.ts
    │       │   │   │   └── informationSlice.ts
    │       │   │   ├── FIM/
    │       │   │   │   ├── actions.ts
    │       │   │   │   ├── FIMDebug.test.tsx
    │       │   │   │   ├── FIMDebug.tsx
    │       │   │   │   ├── index.ts
    │       │   │   │   └── reducer.ts
    │       │   │   ├── History/
    │       │   │   │   └── historySlice.ts
    │       │   │   ├── Integrations/
    │       │   │   │   ├── convertRawIntegrationFormValues.ts
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── IntegrationFormField.module.css
    │       │   │   │   ├── IntegrationFormField.tsx
    │       │   │   │   ├── Integrations.tsx
    │       │   │   │   └── integrationsSlice.tsx
    │       │   │   ├── Knowledge/
    │       │   │   │   └── knowledgeSlice.ts
    │       │   │   ├── Login/
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── LoginPage.stories.tsx
    │       │   │   │   └── LoginPage.tsx
    │       │   │   ├── Pages/
    │       │   │   │   └── pagesSlice.ts
    │       │   │   ├── PatchesAndDiffsTracker/
    │       │   │   │   └── patchesAndDiffsTrackerSlice.ts
    │       │   │   ├── Providers/
    │       │   │   │   ├── constants.ts
    │       │   │   │   ├── getProviderName.ts
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── Providers.tsx
    │       │   │   │   ├── ProviderUpdateContext.tsx
    │       │   │   │   ├── useUpdateProvider.ts
    │       │   │   │   ├── icons/
    │       │   │   │   │   ├── Anthropic.tsx
    │       │   │   │   │   ├── Custom.tsx
    │       │   │   │   │   ├── DeepSeek.tsx
    │       │   │   │   │   ├── Gemini.tsx
    │       │   │   │   │   ├── Groq.tsx
    │       │   │   │   │   ├── iconsMap.tsx
    │       │   │   │   │   ├── LMStudio.tsx
    │       │   │   │   │   ├── Ollama.tsx
    │       │   │   │   │   ├── OpenAI.tsx
    │       │   │   │   │   ├── OpenRouter.tsx
    │       │   │   │   │   ├── Refact.tsx
    │       │   │   │   │   └── Xai.tsx
    │       │   │   │   ├── ProviderCard/
    │       │   │   │   │   ├── index.ts
    │       │   │   │   │   ├── ProviderCard.module.css
    │       │   │   │   │   ├── ProviderCard.tsx
    │       │   │   │   │   └── useProviderCard.ts
    │       │   │   │   ├── ProviderForm/
    │       │   │   │   │   ├── FormFields.tsx
    │       │   │   │   │   ├── index.ts
    │       │   │   │   │   ├── ProviderForm.module.css
    │       │   │   │   │   ├── ProviderForm.tsx
    │       │   │   │   │   ├── useProviderForm.ts
    │       │   │   │   │   ├── utils.ts
    │       │   │   │   │   └── ProviderModelsList/
    │       │   │   │   │       ├── index.ts
    │       │   │   │   │       ├── ModelCard.module.css
    │       │   │   │   │       ├── ModelCard.tsx
    │       │   │   │   │       ├── ProviderModelsList.tsx
    │       │   │   │   │       ├── components/
    │       │   │   │   │       │   ├── AddModelButton.tsx
    │       │   │   │   │       │   ├── CapabilityBadge.tsx
    │       │   │   │   │       │   ├── FormField.tsx
    │       │   │   │   │       │   ├── FormSelect.tsx
    │       │   │   │   │       │   ├── index.ts
    │       │   │   │   │       │   └── ModelCardPopup.tsx
    │       │   │   │   │       ├── hooks/
    │       │   │   │   │       │   ├── index.ts
    │       │   │   │   │       │   └── useModelDialogState.ts
    │       │   │   │   │       └── utils/
    │       │   │   │   │           ├── extractHumanReadableReasoningType.ts
    │       │   │   │   │           └── index.ts
    │       │   │   │   ├── ProviderPreview/
    │       │   │   │   │   ├── index.ts
    │       │   │   │   │   ├── ProviderPreview.tsx
    │       │   │   │   │   └── useProviderPreview.ts
    │       │   │   │   └── ProvidersView/
    │       │   │   │       ├── ConfiguredProvidersView.tsx
    │       │   │   │       ├── index.ts
    │       │   │   │       ├── ProvidersView.module.css
    │       │   │   │       ├── ProvidersView.tsx
    │       │   │   │       └── useConfiguredProvidersView.tsx
    │       │   │   ├── Statistics/
    │       │   │   │   ├── index.ts
    │       │   │   │   └── Statistics.tsx
    │       │   │   ├── Teams/
    │       │   │   │   ├── index.ts
    │       │   │   │   └── teamsSlice.ts
    │       │   │   ├── ThreadHistory/
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── ThreadHistory.module.css
    │       │   │   │   └── ThreadHistory.tsx
    │       │   │   ├── ToolConfirmation/
    │       │   │   │   └── confirmationSlice.ts
    │       │   │   └── UserSurvey/
    │       │   │       ├── index.ts
    │       │   │       ├── UserSurvey.stories.tsx
    │       │   │       ├── UserSurvey.tsx
    │       │   │       └── userSurveySlice.ts
    │       │   ├── hooks/
    │       │   │   ├── index.ts
    │       │   │   ├── useAbortControllers.ts
    │       │   │   ├── useActiveTeamsGroup.ts
    │       │   │   ├── useApiKey.ts
    │       │   │   ├── useAppDispatch.ts
    │       │   │   ├── useAppearance.ts
    │       │   │   ├── useAppSelector.ts
    │       │   │   ├── useAttachedImages.ts
    │       │   │   ├── useAutoFocusOnce.ts
    │       │   │   ├── useCanUseTools.ts
    │       │   │   ├── useCapsForToolUse.ts
    │       │   │   ├── useCheckpoints.ts
    │       │   │   ├── useCoinBalance.ts
    │       │   │   ├── useCompressChat.ts
    │       │   │   ├── useCompressionStop.ts
    │       │   │   ├── useConfig.ts
    │       │   │   ├── useCopyToClipboard.ts
    │       │   │   ├── useDeleteIntegrationByPath.ts
    │       │   │   ├── useDiffFileReload.ts
    │       │   │   ├── useEffectOnce.ts
    │       │   │   ├── useEventBusForApp.ts
    │       │   │   ├── useEventBusForFIMDebug.ts
    │       │   │   ├── useEventBusForIDE.ts
    │       │   │   ├── useEventBusForWeb.ts
    │       │   │   ├── useExecuteActionForDockerContainer.ts
    │       │   │   ├── useGetCapsQuery.ts
    │       │   │   ├── useGetDockerContainersQuery.ts
    │       │   │   ├── useGetIntegrationDataByPathQuery.ts
    │       │   │   ├── useGetIntegrationsDataQuery.ts
    │       │   │   ├── useGetPing.ts
    │       │   │   ├── useGetPromptsQuery.ts
    │       │   │   ├── useGetStatisticDataQuery.ts
    │       │   │   ├── useGetToolGroupsQuery.ts
    │       │   │   ├── useGetUser.ts
    │       │   │   ├── useGetUserSurvey.ts
    │       │   │   ├── useGoToLink.ts
    │       │   │   ├── useHasCaps.ts
    │       │   │   ├── useHideScroll.ts
    │       │   │   ├── useIsOnline.ts
    │       │   │   ├── useLinksFromLsp.ts
    │       │   │   ├── useLogin.ts
    │       │   │   ├── useLogout.ts
    │       │   │   ├── useModelsQuery.ts
    │       │   │   ├── useMutationObserver.ts
    │       │   │   ├── useOnPressedEnter.ts
    │       │   │   ├── useOpenUrl.ts
    │       │   │   ├── usePatchesAndDiffEventsForIDE.ts
    │       │   │   ├── usePostMessage.ts
    │       │   │   ├── usePreviewCheckpoints.ts
    │       │   │   ├── useProvidersQuery.ts
    │       │   │   ├── useResizeObserver.ts
    │       │   │   ├── useRestoreCheckpoints.ts
    │       │   │   ├── useSaveIntegrationData.ts
    │       │   │   ├── useSendChatRequest.ts
    │       │   │   ├── useSmartLinks.ts
    │       │   │   ├── useSmartSubscription.ts
    │       │   │   ├── useStartPollingForUser.ts
    │       │   │   ├── useThinking.ts
    │       │   │   ├── useTotalCostForChat.ts
    │       │   │   ├── useUndoRedo.ts
    │       │   │   ├── useUpdateToolGroupsMutation.ts
    │       │   │   └── useWindowDimensions.ts
    │       │   ├── images/
    │       │   │   ├── coin.module.css
    │       │   │   ├── coin.tsx
    │       │   │   ├── GoogleIcon.tsx
    │       │   │   ├── index.ts
    │       │   │   ├── linkIcon.module.css
    │       │   │   ├── linkIcon.tsx
    │       │   │   └── PuzzleIcon.tsx
    │       │   ├── lib/
    │       │   │   ├── index.ts
    │       │   │   └── render/
    │       │   │       ├── index.tsx
    │       │   │       ├── RenderApp.tsx
    │       │   │       └── web.css
    │       │   ├── services/
    │       │   │   ├── refact/
    │       │   │   │   ├── caps.ts
    │       │   │   │   ├── chat.ts
    │       │   │   │   ├── checkpoints.ts
    │       │   │   │   ├── commands.ts
    │       │   │   │   ├── consts.ts
    │       │   │   │   ├── diffs.ts
    │       │   │   │   ├── docker.ts
    │       │   │   │   ├── fim.ts
    │       │   │   │   ├── index.ts
    │       │   │   │   ├── integrations.ts
    │       │   │   │   ├── knowledge.ts
    │       │   │   │   ├── links.ts
    │       │   │   │   ├── models.ts
    │       │   │   │   ├── path.ts
    │       │   │   │   ├── ping.ts
    │       │   │   │   ├── prompts.ts
    │       │   │   │   ├── providers.ts
    │       │   │   │   ├── statistics.ts
    │       │   │   │   ├── teams.ts
    │       │   │   │   ├── telemetry.ts
    │       │   │   │   ├── tools.ts
    │       │   │   │   ├── types.test.tsx
    │       │   │   │   └── types.ts
    │       │   │   └── smallcloud/
    │       │   │       ├── index.ts
    │       │   │       └── types.ts
    │       │   └── utils/
    │       │       ├── ApiKey.ts
    │       │       ├── calculateUsageInputTokens.test.ts
    │       │       ├── calculateUsageInputTokens.ts
    │       │       ├── copyChatHistoryToClipboard.ts
    │       │       ├── createProjectLabelsWithConflictMarkers.test.ts
    │       │       ├── createProjectLabelsWithConflictMarkers.ts
    │       │       ├── createSyntheticEvent.ts
    │       │       ├── extractLinkFromPuzzle.test.ts
    │       │       ├── extractLinkFromPuzzle.ts
    │       │       ├── fallbackCopying.ts
    │       │       ├── fencedBackticks.ts
    │       │       ├── filename.ts
    │       │       ├── formatDateToHumanReadable.ts
    │       │       ├── formatIntegrationIconPath.ts
    │       │       ├── formatNumberToFixed.ts
    │       │       ├── formatPathName.ts
    │       │       ├── formatProjectName.ts
    │       │       ├── getIntegrationInfo.ts
    │       │       ├── getMetering.ts
    │       │       ├── hasProperty.ts
    │       │       ├── index.ts
    │       │       ├── isAbsolutePath.ts
    │       │       ├── isDetailMessage.ts
    │       │       ├── mockServer.ts
    │       │       ├── parseOrElse.ts
    │       │       ├── partition.ts
    │       │       ├── scanForDuplicates.ts
    │       │       ├── takeFromEndWhile.ts
    │       │       ├── takeFromLast.ts
    │       │       ├── takeWhile.ts
    │       │       ├── test-setup.ts
    │       │       ├── test-utils.tsx
    │       │       ├── toPascalCase.ts
    │       │       ├── trimIndent.ts
    │       │       ├── utils.test.tsx
    │       │       └── validateSnakeCase.ts
    │       ├── .github/
    │       │   └── pull_request_template.md
    │       ├── .husky/
    │       │   ├── pre-commit
    │       │   └── .gitignore
    │       ├── .refact/
    │       │   └── integrations.d/
    │       │       └── service_webserver.yaml
    │       └── .storybook/
    │           ├── main.ts
    │           └── preview.tsx
    ├── refact-server/
    │   ├── README.md
    │   ├── CONTRIBUTING.md
    │   ├── database-start.sh
    │   ├── docker-entrypoint-proxy.sh
    │   ├── docker-entrypoint.sh
    │   ├── Dockerfile
    │   ├── Dockerfile.base
    │   ├── Dockerfile.proxy
    │   ├── LICENSE
    │   ├── setup.py
    │   ├── code_contrast/
    │   │   ├── __init__.py
    │   │   └── format_2023q2/
    │   │       ├── __init__.py
    │   │       ├── el_chunk.py
    │   │       ├── el_file.py
    │   │       ├── el_msg.py
    │   │       ├── element.py
    │   │       ├── format.py
    │   │       ├── from_orig_dest_message.py
    │   │       ├── packing.py
    │   │       ├── test_2023q2.py
    │   │       └── unpacking.py
    │   ├── metrics/
    │   │   ├── measure_humaneval_continue.py
    │   │   └── measure_humaneval_fim.py
    │   ├── refact_data_pipeline/
    │   │   ├── __init__.py
    │   │   ├── code_filter.py
    │   │   ├── datadef.py
    │   │   ├── datautils.py
    │   │   ├── filters_chat.py
    │   │   ├── filters_diff2023q2.py
    │   │   ├── filters_fim.py
    │   │   ├── filters_fim_v2.py
    │   │   ├── filters_hdfs.py
    │   │   ├── filters_human_eval.py
    │   │   ├── filters_human_eval_x.py
    │   │   ├── filters_packing.py
    │   │   ├── finetune_datasource.py
    │   │   ├── git_command.exp
    │   │   ├── pipeline_pieces.py
    │   │   └── utils/
    │   │       ├── __init__.py
    │   │       └── text_extraction.py
    │   ├── refact_known_models/
    │   │   ├── __init__.py
    │   │   ├── huggingface.py
    │   │   └── passthrough.py
    │   ├── refact_proxy/
    │   │   ├── __init__.py
    │   │   └── webgui/
    │   │       ├── __init__.py
    │   │       ├── selfhost_fastapi_completions.py
    │   │       ├── selfhost_model_assigner.py
    │   │       └── webgui.py
    │   ├── refact_utils/
    │   │   ├── __init__.py
    │   │   ├── finetune/
    │   │   │   ├── __init__.py
    │   │   │   ├── train_defaults.py
    │   │   │   └── utils.py
    │   │   ├── huggingface/
    │   │   │   ├── __init__.py
    │   │   │   └── utils.py
    │   │   ├── scripts/
    │   │   │   ├── __init__.py
    │   │   │   ├── best_lora.py
    │   │   │   ├── env.py
    │   │   │   └── merge_lora.py
    │   │   └── third_party/
    │   │       ├── __init__.py
    │   │       ├── tokenizers/
    │   │       │   ├── claude.json
    │   │       │   ├── deepseek-r1.json
    │   │       │   ├── deepseek-v3.json
    │   │       │   ├── gemma2.json
    │   │       │   ├── gpt-4o.json
    │   │       │   ├── grok-1.json
    │   │       │   └── llama-3-1.json
    │   │       └── utils/
    │   │           ├── __init__.py
    │   │           ├── configs.py
    │   │           ├── migration.py
    │   │           ├── models.py
    │   │           └── tokenizers.py
    │   ├── refact_webgui/
    │   │   ├── __init__.py
    │   │   ├── dashboards/
    │   │   │   ├── __init__.py
    │   │   │   ├── dash_prime.py
    │   │   │   ├── dash_teams.py
    │   │   │   ├── utils.py
    │   │   │   └── tests/
    │   │   │       └── insert_mock_tele_data.py
    │   │   └── webgui/
    │   │       ├── __init__.py
    │   │       ├── nginx.conf
    │   │       ├── selfhost_database.py
    │   │       ├── selfhost_fastapi_completions.py
    │   │       ├── selfhost_fastapi_gpu.py
    │   │       ├── selfhost_login.py
    │   │       ├── selfhost_model_assigner.py
    │   │       ├── selfhost_model_resolve.py
    │   │       ├── selfhost_plugins.py
    │   │       ├── selfhost_queue.py
    │   │       ├── selfhost_static.py
    │   │       ├── selfhost_statistics.py
    │   │       ├── selfhost_webutils.py
    │   │       ├── tab_about.py
    │   │       ├── tab_finetune.py
    │   │       ├── tab_loras.py
    │   │       ├── tab_models_host.py
    │   │       ├── tab_server_logs.py
    │   │       ├── tab_settings.py
    │   │       ├── tab_third_party_apis.py
    │   │       ├── tab_upload.py
    │   │       ├── webgui.py
    │   │       ├── middleware/
    │   │       │   ├── __init__.py
    │   │       │   ├── login_middleware.py
    │   │       │   ├── no_cache_middleware.py
    │   │       │   └── stats_middleware.py
    │   │       └── static/
    │   │           ├── admin-style.css
    │   │           ├── admin.html
    │   │           ├── admin.js
    │   │           ├── error.js
    │   │           ├── index.html
    │   │           ├── index.js
    │   │           ├── style.css
    │   │           ├── tab-about.html
    │   │           ├── tab-about.js
    │   │           ├── tab-finetune.html
    │   │           ├── tab-finetune.js
    │   │           ├── tab-model-hosting.html
    │   │           ├── tab-model-hosting.js
    │   │           ├── tab-server-logs.html
    │   │           ├── tab-server-logs.js
    │   │           ├── tab-settings.html
    │   │           ├── tab-settings.js
    │   │           ├── tab-stats.html
    │   │           ├── tab-stats.js
    │   │           ├── tab-third-party-apis.css
    │   │           ├── tab-third-party-apis.html
    │   │           ├── tab-third-party-apis.js
    │   │           ├── tab-upload.html
    │   │           ├── tab-upload.js
    │   │           ├── assets/
    │   │           │   ├── bootstrap-icons.css
    │   │           │   ├── bootstrap-icons.woff
    │   │           │   ├── bootstrap-icons.woff2
    │   │           │   ├── handlebars.js
    │   │           │   ├── style.css
    │   │           │   ├── theme-config.css
    │   │           │   └── fonts/
    │   │           │       ├── AzeretMono-Bold.woff2
    │   │           │       ├── AzeretMono-Regular.woff2
    │   │           │       ├── Manrope-Bold.woff2
    │   │           │       └── Manrope-Regular.woff2
    │   │           ├── components/
    │   │           │   └── modals/
    │   │           │       └── modal-upload-files.js
    │   │           ├── dashboards/
    │   │           │   ├── dash_prime.js
    │   │           │   ├── dash_users.js
    │   │           │   ├── plots.js
    │   │           │   └── utils.js
    │   │           └── utils/
    │   │               ├── tab-model-hosting-utils.js
    │   │               └── utils.js
    │   ├── self_hosting_machinery/
    │   │   ├── __init__.py
    │   │   ├── finetune/
    │   │   │   ├── __init__.py
    │   │   │   ├── configuration/
    │   │   │   │   ├── __init__.py
    │   │   │   │   ├── finetune_config.py
    │   │   │   │   └── supported_models.py
    │   │   │   ├── modelling/
    │   │   │   │   ├── __init__.py
    │   │   │   │   ├── flash_sa.py
    │   │   │   │   ├── lora.py
    │   │   │   │   ├── loss.py
    │   │   │   │   └── utils.py
    │   │   │   ├── scripts/
    │   │   │   │   ├── __init__.py
    │   │   │   │   ├── finetune_filter.py
    │   │   │   │   ├── finetune_sequence.py
    │   │   │   │   ├── finetune_train.py
    │   │   │   │   ├── process_uploaded_files.py
    │   │   │   │   └── auxiliary/
    │   │   │   │       ├── __init__.py
    │   │   │   │       ├── dataset.py
    │   │   │   │       ├── early_stopper.py
    │   │   │   │       ├── file_sets_context.py
    │   │   │   │       ├── file_status_context.py
    │   │   │   │       ├── finetune_filter_status_tracker.py
    │   │   │   │       ├── finetune_status_tracker.py
    │   │   │   │       └── model.py
    │   │   │   └── utils/
    │   │   │       ├── __init__.py
    │   │   │       ├── eta.py
    │   │   │       ├── timer.py
    │   │   │       ├── traces.py
    │   │   │       └── traces_plot.py
    │   │   ├── inference/
    │   │   │   ├── __init__.py
    │   │   │   ├── inference_base.py
    │   │   │   ├── inference_embeddings.py
    │   │   │   ├── inference_hf.py
    │   │   │   ├── inference_worker.py
    │   │   │   ├── lora_loader_mixin.py
    │   │   │   ├── scratchpad_hf.py
    │   │   │   ├── stream_results.py
    │   │   │   └── stream_results_async.py
    │   │   ├── scripts/
    │   │   │   ├── __init__.py
    │   │   │   ├── enum_devices.py
    │   │   │   ├── first_run.py
    │   │   │   └── hf_hub_available.py
    │   │   └── watchdog/
    │   │       ├── __init__.py
    │   │       ├── docker_watchdog.py
    │   │       └── watchdog.d/
    │   │           ├── enum_gpus.cfg
    │   │           ├── filetune.cfg
    │   │           ├── hf_hub_available.cfg
    │   │           ├── model.cfg
    │   │           ├── process_uploaded.cfg
    │   │           └── webgui.cfg
    │   └── .github/
    │       └── scripts/
    │           ├── README.md
    │           ├── build_base_docker.sh
    │           └── cicl_docker_injection.sh
    ├── .github/
    │   └── workflows/
    │       ├── agent_engine_build.yml
    │       ├── agent_engine_release.yml
    │       ├── agent_gui_build.yml
    │       ├── docs_build.yml
    │       ├── server_build.yml
    │       ├── server_build_and_push_custom.yml
    │       ├── server_nightly.yml
    │       └── server_release.yml
    └── .refact/
        ├── project_summary.yaml
        └── integrations.d/
            └── cmdline_cargo_check.yaml


Files Content:

(Files content cropped to 300k characters, download full ingest to see more)
================================================
FILE: README.md
================================================
<a name="readme-top"></a>

<div align="center">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://docs.refact.ai/_astro/logo-dark.CCzD55EA.svg">
    <source media="(prefers-color-scheme: light)" srcset="https://docs.refact.ai/_astro/logo-light.CblxRz3x.svg">
    <!-- Fallback if neither preference is set -->
    <img alt="Refact.ai logo" src="https://docs.refact.ai/_astro/logo-dark.CCzD55EA.svg" width="200">
  </picture>
  <h1 align="center">Refact - Open Sourced AI Software Development Agent</h1>
</div>

<div align="center">
  <a href="https://github.com/smallcloudai/refact/stargazers"><img src="https://img.shields.io/github/stars/smallcloudai/refact?style=for-the-badge&color=blue" alt="Stargazers"></a>
  <a href="https://discord.gg/Kts7CYg99R"><img src="https://img.shields.io/badge/Discord-Join%20Us-purple?logo=discord&logoColor=white&style=for-the-badge" alt="Join our Discord community"></a>
  <a href="https://docs.refact.ai"><img src="https://img.shields.io/badge/documentation-blue?logo=googledocs&logoColor=FFE165&style=for-the-badge" alt="Check out the documentation"></a>
  
</div>



Refact Agent is a free, open-source AI Agent that handles engineering tasks end-to-end. It deeply understands your codebases and integrates with your tools, databases, and browsers to automate complex, multi-step tasks.


## 🚀 Seamless Integration with Your Workflow  

Refact Agent works effortlessly with the tools and databases you already use:  


- **📁 Version Control:** GitHub, GitLab  
- **🗄️ Databases:** PostgreSQL, MySQL  
- **🛠️ Debugging:** Pdb  
- **🐳 Containerization:** Docker  

### ⚡ Why Choose Refact Agent?  

- ✅ **Deploy On-Premise:** Maintain **100% control** over your codebase.  
- 🧠 **Access State-of-the-Art Models:** Supports Claude 3.7 Sonnet, GPT-4o, o3-mini, and more.  
- 🔑 **Bring Your Own Key (BYOK):** Use your own API keys for external LLMs.  
- 💬 **Integrated IDE Chat:** Stay in your workflow, no need to switch between tools!  
- ⚡ **Free, Unlimited, Context-Aware Auto-Completion:** Code faster with smart AI suggestions.  
- 🛠️ **Supports 25+ Programming Languages:** Python, JavaScript, Java, Rust, TypeScript, PHP, C++, C#, Go, and many more!  

### 🎉  Hear from our Community

Our Ambassadors shared remarkable stories of how they transform weeks of coding into minutes with Refact.ai Agent!

1️⃣ How Refact.ai built 99.9% of an IoT cloud app [Read](https://www.linkedin.com/posts/refactai_refactai-agent-activity-7308103386451578881-FO23/)

2️⃣ How AI-coded prototypes help UX teams deliver real value [Read](https://medium.com/@siarheimardovich/how-ai-coded-prototypes-help-ux-teams-deliver-real-value-b51eb2ea5167)

3️⃣ 80 hours of building from scratch — instead done in 30 minutes [Read more](https://www.linkedin.com/posts/refactai_refactai-users-stories-activity-7310996174864289793-692-/)

4️⃣ 3 weeks of waiting for a functional GUI? Built in just 14 minutes [Read more](https://www.linkedin.com/posts/refactai_refactai-users-stories-activity-7310996174864289793-692-/)


📜 [View Full List of Supported Models](https://docs.refact.ai/supported-models/) 

> 📢  **Using AI for work? Let’s bring it to your company!** 
> 
> [Fill out this form](https://refact.ai/contact/?utm_source=github&utm_medium=readme&utm_campaign=enterprise) — Our AI Agent will be tailored to your company’s data, learning from feedback, and helping organize knowledge for **better collaboration** with your team.


## 📚 Table of Contents

- 🚀 [Core Features and Functionality](#-core-features-and-functionality)
- 🤖 [Which Tasks Can Refact Help You With?](#-which-tasks-can-refact-help-you-with)
- ⚙️ [QuickStart](#%EF%B8%8F-quickstart)
- 🐳 [Running Refact Self-Hosted in a Docker Container](#-running-refact-self-hosted-in-a-docker-container)
- 🔌 [Getting Started with Plugins](#-getting-started-with-plugins)
- 📖 [Documentation](#-documentation)
- 🥇 [Contribution](#-contribution)
- 🎉 [Join the Community](#-join-the-community)

## 🚀 Core Features and Functionality

 ✅ **Unlimited accurate auto-completion** with context awareness – Powered by Qwen2.5-Coder-1.5B, utilizing Retrieval-Augmented Generation (RAG).  

![auto-completion](https://lh7-rt.googleusercontent.com/docsz/AD_4nXfClhl11Ul0YQjDTZJvrfhsj3bqK_VIz6bFfbTRc62dsMOz4LK4u72i9-gLTQDIgm0yChmFe57hvUxSoI2fQ5DSntna7_Ch0qbGx5zcB-othfwKnoYkbt3M3YgGFlrqFszuDEBhUw?key=zllGjEBckkx13bRZ6JIqX6qr)

 ✅ **Integrated in-IDE Chat** – AI deeply understands your code and provides relevant, intelligent answers.  

 ✅ **Integrated with Tools** – Works with GitHub, GitLab, PostgreSQL, MySQL, Pdb, Docker, and shell commands.  

![integrations](https://lh7-rt.googleusercontent.com/docsz/AD_4nXc4DWYXF73AgPWAaFFGLTqEprWwA0im8R_A1QMo4QW4pTnSi1MCoP9L8udMZb5FPyN-CdgefaxJFGpX2ndn5nkjGBF2b_hZBNHogM7IM6SPvUIvUd9iE1lYIq7q-TB2qKzSGLk00A?key=zllGjEBckkx13bRZ6JIqX6qr)

 ✅ **State-of-the-Art Models** – Supports Claude 3.7 Sonnet, GPT-4o, o3-mini, and more.  

 ✅ **Bring Your Own Key (BYOK)** – Use your own API keys for external LLMs.  

![BYOK](https://lh7-rt.googleusercontent.com/docsz/AD_4nXe1UDsuaER6WMxAnKEwz15T3OPslkpSo2vNGMGaNoEiZOJvAptY8yEvND_rI23q_5Sof1DceexyrW5x6oUwcpVr5KQvWUByrN_TnLGVY2HG_0sg8uWnRb14jKAes2MBDPM37EQO?key=zllGjEBckkx13bRZ6JIqX6qr)


## 🤖 Which Tasks Can Refact Help You With?

- 🏗 **Generate code** from natural language prompts (even with typos).  

- 🔄 **Refactor code** for better quality and readability.  

- 📖 **Explain code** to quickly understand unfamiliar code.  

- 🐞 **Debug code** to detect and fix errors faster.  

- 🧪 **Generate unit tests** for reliable code.  

- 📌 **Code Review** with AI-assisted suggestions.  

- 📜 **Create Documentation** to keep knowledge up to date. 
 
- 🏷 **Generate Docstrings** for structured documentation.  



## ⚙️ QuickStart

You can install the Refact repository without Docker:
```bash
pip install .
```

For GPU with CUDA capability >= 8.0 and flash-attention v2 support:
```bash
FLASH_ATTENTION_FORCE_BUILD=TRUE MAX_JOBS=4 INSTALL_OPTIONAL=TRUE pip install .
```



## 🐳 Running Refact Self-Hosted in a Docker Container

The easiest way to run the self-hosted server is using a pre-built Docker image.  
See `CONTRIBUTING.md` for installation without a Docker container.


### 🔌 Getting Started with Plugins

1. **Download Refact** for VS Code or JetBrains.  
2. **Set up a custom inference URL:**  
   ```
   http://127.0.0.1:8008
   ```
3. **Configure the plugin settings:**  
   - **JetBrains:** Settings > Tools > Refact.ai > Advanced > Inference URL  
   - **VSCode:** Extensions > Refact.ai Assistant > Settings > Address URL  



## 📖 Documentation

For detailed guidance and best practices, check out our [documentation.](https://docs.refact.ai/)


## 🥇 Contribution

Want to contribute to our project? We're always open to new ideas and features!  
- **Check out GitHub Issues** – See what we're working on or suggest your own ideas.  
- **Read our Contributing Guide** – Check out `Contributing.md` to get started.  

Your contributions help shape the future of Refact Agent! 🚀



### 🎉 Join the Community

We're all about open-source and empowering developers with AI tools. Our vision is to build the future of programming. Join us and be part of the journey!

📢 **[Join our Discord server](https://refact.ai/community/)** – A community-run space for discussion, questions, and feedback.



**Made with ❤️ by developers who automate the boring, so you can focus on building the future.**





================================================
FILE: CONTRIBUTING.md
================================================
# 🌟 Contribute to Refact.ai Agent

Welcome to the Refact.ai project! We're excited to have you join our community. Whether you're a first-time contributor or a seasoned developer, here are some impactful ways to get involved.


## 📚 Table of Contents
- [❤️ Ways to Contribute](#%EF%B8%8F-ways-to-contribute)
  - [🐛 Report Bugs](#-report-bugs)
  - [Contributing To Code](#contributing-to-code)
    - [ What Can I Do](#what-can-i-do)
    - [Coding Standards](#coding-standards)
    - [Testing](#testing)
    - [Contact](#contact)


### ❤️ Ways to Contribute

* Fork the repository
* Create a feature branch
* Do the work
* Create a pull request
* Maintainers will review it

### 🐛 Report Bugs
Encountered an issue? Help us improve Refact.ai by reporting bugs in issue section, make sure you label the issue with correct tag [here](https://github.com/smallcloudai/refact-lsp/issues)! 

### 📖 Improving Documentation
Help us make Refact.ai more accessible by contributing to our documentation, make sure you label the issue with correct tag! Create issues [here](https://github.com/smallcloudai/web_docs_refact_ai/issues).

### Contributing To Code

#### What Can I Do?

Before you start, create an issue with a title that begins with `[idea] ...`. The field of AI and agents is vast,
and not every idea will benefit the project, even if it is a good idea in itself.

Another rule of thumb: Only implement a feature you can test thoroughly.


#### Coding Standards

Good practices for Rust are generally applicable to this project. There are a few points however:

1. Naming. Use "Find in files..." to check if a name you give to your structs, fields, functions is too
generic. If a name is already all over the project, be more specific. For example "IntegrationGitHub" is a good
name, but "Integration" is not, even if it's in `github.rs` and files work as namespaces in Rust. It's
still hard to navigate the project if you can't use search.

2. Locks. For some reason, it's still hard for most people, and for current AI models, too. Refact is
multi-threaded, locks are necessary. But locks need to be activated for the shortest time possible, this
is how you use `Arc<AMutex<>>` to do it:

```rust
struct BigStruct {
    ...
    pub small_struct: Arc<AMutex<SmallStruct>>;
}

fn some_code(big_struct: Arc<AMutex<BigStruct>>)
{
    let small_struct = {
        let big_struct_locked = big_struct.lock().await;
        big_struct_locked.small_struct.clone()  // cloning Arc is cheap
        // big_struct_locked is destroyed here because it goes out of scope
    };
    // use small_struct without holding big_struct_locked
}
```

Another multi-threaded trick, move a member function outside of a class:

```rust
struct MyStruct {
    pub data1: i32,
    pub data2: i32,
}

impl MyStruct {
    pub fn lengthy_function1(&mut self)  {  }
}

fn some_code(my_struct: Arc<AMutex<SmallStruct>>)
{
    my_struct.lock().await.lengthy_function1();
    // Whoops, lengthy_function has the whole structure locked for a long time,
    // and Rust won't not let you unlock it
}

pub fn lengthy_function2(s: Arc<AMutex<SmallStruct>>)
{
    let (data1, data2) = {
        let s_locked = s.lock().await;
        (s_locked.data1.clone(), s_locked.data2.clone())
    }
    // Do lengthy stuff here without locks!
}
```

Avoid nested locks, avoid RwLock unless you know what you are doing.


#### Testing

It's a good idea to have tests in source files, and run them using `cargo test`, and we
have CI in place to run it automatically.
But not everything can be tested solely within Rust tests, for example a Rust test cannot run
an AI model inside.

So we have `tests/*.py` scripts that expect the `refact-lsp` process to be running on port 8001,
and the project itself as a workspace dir:


```bash
cargo build && target/debug/refact-lsp --http-port 8001 --reset-memory --experimental --workspace-folder . --logs-stderr --vecdb --ast
```

Running those tests is still manual. To make sure your work didn't break other features,
run tests for things you might have broken.


#### Contact

If you have any questions or concerns, please contact the project maintainers on Discord:
https://www.smallcloud.ai/discord


================================================
FILE: LICENSE
================================================
BSD 3-Clause License

Copyright (c) 2023, Small Magellanic Cloud AI Ltd.
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.



================================================
FILE: docs/README.md
================================================
# Starlight Starter Kit: Basics

```
npm create astro@latest -- --template starlight
```

[![Open in StackBlitz](https://developer.stackblitz.com/img/open_in_stackblitz.svg)](https://stackblitz.com/github/withastro/starlight/tree/main/examples/basics)
[![Open with CodeSandbox](https://assets.codesandbox.io/github/button-edit-lime.svg)](https://codesandbox.io/p/sandbox/github/withastro/starlight/tree/main/examples/basics)

> 🧑‍🚀 **Seasoned astronaut?** Delete this file. Have fun!

## 🚀 Project Structure

Inside of your Astro + Starlight project, you'll see the following folders and files:

```
.
├── public/
├── src/
│   ├── assets/
│   ├── content/
│   │   ├── docs/
│   │   └── config.ts
│   └── env.d.ts
├── astro.config.mjs
├── package.json
└── tsconfig.json
```

Starlight looks for `.md` or `.mdx` files in the `src/content/docs/` directory. Each file is exposed as a route based on its file name.

Images can be added to `src/assets/` and embedded in Markdown with a relative link.

Static assets, like favicons, can be placed in the `public/` directory.

## 🧞 Commands

All commands are run from the root of the project, from a terminal:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`      |
| `npm run build`           | Build your production site to `./dist/`          |
| `npm run preview`         | Preview your build locally, before deploying     |
| `npm run astro ...`       | Run CLI commands like `astro add`, `astro check` |
| `npm run astro -- --help` | Get help using the Astro CLI                     |

## 👀 Want to learn more?

Check out [Starlight’s docs](https://starlight.astro.build/), read [the Astro documentation](https://docs.astro.build), or jump into the [Astro Discord server](https://astro.build/chat).



================================================
FILE: docs/astro.config.mjs
================================================
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

const site = 'https://docs.refact.ai/';

// https://astro.build/config
export default defineConfig({
  integrations: [
    starlight({
      title: 'Refact Documentation',
      components: {
        Search: './src/components/Search.astro',
        Head: './src/components/Head.astro'
      },
      logo: {
        light: '/src/assets/logo-light.svg',
        dark: '/src/assets/logo-dark.svg',
        replacesTitle: true,
      },
      social: {
        github: 'https://github.com/smallcloudai',
        discord: 'https://smallcloud.ai/discord'
      },
      head: [
        {
          tag: 'meta',
          attrs: { property: 'og:image', content: site + 'og.jpg' }
        },
        {
          tag: 'meta',
          attrs: { property: 'twitter:image', content: site + 'og.jpg' }
        },
        {
          tag: 'script',
          attrs: {
            async: true,
            src: 'https://www.googletagmanager.com/gtag/js?id=G-76LB6JQLMK',
          },
        },
        {
          tag: 'script',
          content: `
						window.dataLayer = window.dataLayer || [];
						function gtag() {
							dataLayer.push(arguments);
						}
						gtag('js', new Date());

						gtag('config', 'G-76LB6JQLMK');
					`,
        }
      ],
      sidebar: [
        {
          label: 'Introduction',
          collapsed: true,
          items: [
            { 
              label: 'Quickstart', 
              link: '/introduction/quickstart/',
              attrs: {
                'aria-label': 'Get started with Refact'
              }
            },
            {
              label: 'Usage Based Pricing',
              link: '/introduction/usage-based-pricing/',
              attrs: {
                'aria-label': 'Learn about Usage Based Pricing'
              }
            },
            {
              label: 'Installation',
              collapsed: true,
              items: [
                { 
                  label: 'Installation Hub', 
                  link: '/installation/installation-hub/',
                  attrs: {
                    'aria-label': 'Browse Installation Options'
                  }
                },
                { 
                  label: 'VS Code', 
                  link: '/installation/vs-code/',
                  attrs: {
                    'aria-label': 'Install Refact for VS Code'
                  }
                },
                { 
                  label: 'JetBrains IDEs', 
                  link: '/installation/jetbrains/',
                  attrs: {
                    'aria-label': 'Install Refact for JetBrains IDEs'
                  }
                },
              ] 
            },
            {
              label: 'Features',
              collapsed: true,
              items: [
                { 
                  label: 'AI Chat', 
                  link: '/features/ai-chat/',
                  attrs: {
                    'aria-label': 'Learn about AI Chat Feature'
                  }
                },
                { 
                  label: 'AI Toolbox', 
                  link: '/features/ai-toolbox/',
                  attrs: {
                    'aria-label': 'Explore AI Toolbox Features'
                  }
                },
                { 
                  label: 'Code Completion', 
                  link: '/features/code-completion/',
                  attrs: {
                    'aria-label': 'Learn about Code Completion'
                  }
                },
                { 
                  label: 'Context', 
                  link: '/features/context/',
                  attrs: {
                    'aria-label': 'Understanding Context Features'
                  }
                },
                { 
                  label: 'Fine-tuning', 
                  link: '/features/finetuning/',
                  attrs: {
                    'aria-label': 'Learn about Model Fine-tuning'
                  }
                },
              ]
            },
          ],
        },
        {
          label: 'Autonomous Agent',
          collapsed: true,
          items: [
            { label: 'Getting Started', link: '/features/autonomous-agent/getting-started/' },
            { label: 'Overview', link: '/features/autonomous-agent/overview/' },
            { 
              label: 'Tools', 
              link: '/features/autonomous-agent/tools/',
              attrs: {
                'aria-label': 'Learn about Agent Tools'
              }
            },
            { 
              label: 'Rollback', 
              link: '/features/autonomous-agent/rollback/',
              attrs: {
                'aria-label': 'Learn about Agent Rollback Feature'
              }
            },
            { 
              label: 'Integrations', 
              collapsed: true,
              items: [
                { label: 'Overview', link: '/features/autonomous-agent/integrations/' },
                // Development Tools
    		{ label: 'Chrome', link: '/features/autonomous-agent/integrations/chrome/' },
                { label: 'Shell Commands', link: '/features/autonomous-agent/integrations/shell-commands/' },
                { label: 'Command Line Tool', link: '/features/autonomous-agent/integrations/command-line-tool/' },
                { label: 'Command Line Service', link: '/features/autonomous-agent/integrations/command-line-service/' },
                { label: 'MCP Server', link: '/features/autonomous-agent/integrations/mcp/', attrs: { 'aria-label': 'Connect to Model Context Protocol servers' } },
                // Version Control
                { label: 'GitHub', link: '/features/autonomous-agent/integrations/github/' },
                { label: 'GitLab', link: '/features/autonomous-agent/integrations/gitlab/' },
                // Container Management
                { label: 'Docker', link: '/features/autonomous-agent/integrations/docker/' },
                // Databases
                { label: 'PostgreSQL', link: '/features/autonomous-agent/integrations/postgresql/' },
                { label: 'MySQL', link: '/features/autonomous-agent/integrations/mysql/' },
                // Debugging
                { label: 'PDB', link: '/features/autonomous-agent/integrations/pdb/' },
              ] 
            },
          ]
        },
        {
          label: 'Guides',
          collapsed: true,
          items: [
            { 
              label: 'Deployment',
              collapsed: true,
              items: [
                { 
                  label: 'Runpod Deployment', 
                  link: '/guides/deployment/runpod/',
                  attrs: {
                    'aria-label': 'Learn about Runpod Deployment'
                  }
                },
                { 
                  label: 'AWS Deployment', 
                  collapsed: true,
                  items: [
                    { 
                      label: 'Getting Started', 
                      link: '/guides/deployment/aws/getting-started/',
                      attrs: {
                        'aria-label': 'Getting Started with AWS Deployment'
                      }
                    },
                    { 
                      label: 'Launch from EC2', 
                      link: '/guides/deployment/aws/ec2/',
                      attrs: {
                        'aria-label': 'Launch Refact from EC2'
                      }
                    },
                    { 
                      label: 'Launch from Website', 
                      link: '/guides/deployment/aws/marketplace/',
                      attrs: {
                        'aria-label': 'Launch Refact from AWS Marketplace'
                      }
                    },
                    { 
                      label: 'Usage', 
                      link: '/guides/deployment/aws/usage/',
                      attrs: {
                        'aria-label': 'AWS Deployment Usage Guide'
                      }
                    },
                  ] 
                },
              ] 
            },
            {
              label: 'Plugins',
              collapsed: true,
              items: [
                { 
                  label: 'JetBrains IDEs', 
                  collapsed: true,
                  items: [
                    { 
                      label: 'Troubleshooting', 
                      link: '/guides/plugins/jetbrains/troubleshooting/',
                      attrs: {
                        'aria-label': 'JetBrains IDEs Troubleshooting Guide'
                      }
                    },
                  ]
                },
              ]
            },
            { 
              label: 'Authentication', 
              collapsed: true,
              items: [
                { 
                  label: 'Keycloak Integration', 
                  link: '/guides/authentication/keycloak/',
                  attrs: {
                    'aria-label': 'Learn about Keycloak Integration'
                  }
                },
              ]
            },
            { 
              label: 'Version-specific Usage',
              collapsed: true,
              items: [
                { 
                  label: 'Self-hosted Refact',
                  collapsed: true,
                  items: [
                    { 
                      label: 'Self-hosted Refact', 
                      link: '/guides/version-specific/self-hosted/',
                      attrs: {
                        'aria-label': 'Self-hosted Refact Guide'
                      }
                    }
                  ]
                },
                { 
                  label: 'Enterprise Refact', 
                  collapsed: true,
                  items: [
                    { 
                      label: 'Getting Started', 
                      link: '/guides/version-specific/enterprise/getting-started/',
                      attrs: {
                        'aria-label': 'Getting Started with Enterprise Refact'
                      }
                    },
                    { 
                      label: 'License', 
                      link: '/guides/version-specific/enterprise/license/',
                      attrs: {
                        'aria-label': 'Enterprise Refact License Information'
                      }
                    },
                    { 
                      label: 'Users', 
                      link: '/guides/version-specific/enterprise/users/',
                      attrs: {
                        'aria-label': 'Enterprise Refact User Management'
                      }
                    },
                    { 
                      label: 'Model Hosting', 
                      link: '/guides/version-specific/enterprise/model-hosting/',
                      attrs: {
                        'aria-label': 'Enterprise Refact Model Hosting Guide'
                      }
                    },
                    { 
                      label: 'Plugins', 
                      link: '/guides/version-specific/enterprise/plugins/',
                      attrs: {
                        'aria-label': 'Enterprise Refact Plugins Guide'
                      }
                    },
                  ] 
                },
                { 
                  label: 'Refact Teams', 
                  link: '/guides/version-specific/teams/',
                  attrs: {
                    'aria-label': 'Learn about Refact Teams'
                  }
                },
              ]
            },
            { 
              label: 'Reverse Proxy', 
              link: '/guides/reverse-proxy/',
              attrs: {
                'aria-label': 'Learn about Reverse Proxy Setup'
              }
            },
          ]
        },
        {
          label: 'Supported Models',
          link: '/supported-models/',
          attrs: {
            'aria-label': 'View Supported AI Models'
          }
        },
        {
          label: 'BYOK',
          link: '/byok/',
          attrs: {
            'aria-label': 'Learn about Bring Your Own Key (BYOK)'
          }
        },
        {
          label: 'FAQ',
          link: '/faq/',
          attrs: {
            'aria-label': 'Frequently Asked Questions'
          }
        },
        {
          label: 'Contributing',
          link: '/contributing/',
          attrs: {
            'aria-label': 'Learn how to contribute to Refact'
          }
        },
      ],
      customCss: [
        // Main CSS entry point that imports all other CSS files
        './src/styles/index.css',
      ],
      editLink: {
        baseUrl: 'https://github.com/smallcloudai/refact/edit/main/docs/',
      },
      lastUpdated: true,
    }),
  ],
});



================================================
FILE: docs/Dockerfile
================================================
FROM nginx
COPY dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

# docker build -t europe-west4-docker.pkg.dev/small-storage1/databases-and-such/refact-ai:20230825 .



================================================
FILE: docs/nginx.conf
================================================
server {
    listen       80;
    # server_name  one-movies.local;
    root   /usr/share/nginx/html;
    location / {
        index  index.html index.htm;
    }
}



================================================
FILE: docs/package-lock.json
================================================
{
  "name": "web_docs_refact_ai",
  "version": "0.0.1",
  "lockfileVersion": 3,
  "requires": true,
  "packages": {
    "": {
      "name": "web_docs_refact_ai",
      "version": "0.0.1",
      "dependencies": {
        "@astrojs/starlight": "^0.21.5",
        "@docsearch/css": "^3.5.2",
        "@docsearch/js": "^3.5.2",
        "astro": "^4.6.4",
        "sharp": "^0.32.6"
      }
    },
    "node_modules/@algolia/autocomplete-core": {
      "version": "1.9.3",
      "resolved": "https://registry.npmjs.org/@algolia/autocomplete-core/-/autocomplete-core-1.9.3.tgz",
      "integrity": "sha512-009HdfugtGCdC4JdXUbVJClA0q0zh24yyePn+KUGk3rP7j8FEe/m5Yo/z65gn6nP/cM39PxpzqKrL7A6fP6PPw==",
      "dependencies": {
        "@algolia/autocomplete-plugin-algolia-insights": "1.9.3",
        "@algolia/autocomplete-shared": "1.9.3"
      }
    },
    "node_modules/@algolia/autocomplete-plugin-algolia-insights": {
      "version": "1.9.3",
      "resolved": "https://registry.npmjs.org/@algolia/autocomplete-plugin-algolia-insights/-/autocomplete-plugin-algolia-insights-1.9.3.tgz",
      "integrity": "sha512-a/yTUkcO/Vyy+JffmAnTWbr4/90cLzw+CC3bRbhnULr/EM0fGNvM13oQQ14f2moLMcVDyAx/leczLlAOovhSZg==",
      "dependencies": {
        "@algolia/autocomplete-shared": "1.9.3"
      },
      "peerDependencies": {
        "search-insights": ">= 1 < 3"
      }
    },
    "node_modules/@algolia/autocomplete-preset-algolia": {
      "version": "1.9.3",
      "resolved": "https://registry.npmjs.org/@algolia/autocomplete-preset-algolia/-/autocomplete-preset-algolia-1.9.3.tgz",
      "integrity": "sha512-d4qlt6YmrLMYy95n5TB52wtNDr6EgAIPH81dvvvW8UmuWRgxEtY0NJiPwl/h95JtG2vmRM804M0DSwMCNZlzRA==",
      "dependencies": {
        "@algolia/autocomplete-shared": "1.9.3"
      },
      "peerDependencies": {
        "@algolia/client-search": ">= 4.9.1 < 6",
        "algoliasearch": ">= 4.9.1 < 6"
      }
    },
    "node_modules/@algolia/autocomplete-shared": {
      "version": "1.9.3",
      "resolved": "https://registry.npmjs.org/@algolia/autocomplete-shared/-/autocomplete-shared-1.9.3.tgz",
      "integrity": "sha512-Wnm9E4Ye6Rl6sTTqjoymD+l8DjSTHsHboVRYrKgEt8Q7UHm9nYbqhN/i0fhUYA3OAEH7WA8x3jfpnmJm3rKvaQ==",
      "peerDependencies": {
        "@algolia/client-search": ">= 4.9.1 < 6",
        "algoliasearch": ">= 4.9.1 < 6"
      }
    },
    "node_modules/@algolia/cache-browser-local-storage": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/cache-browser-local-storage/-/cache-browser-local-storage-4.20.0.tgz",
      "integrity": "sha512-uujahcBt4DxduBTvYdwO3sBfHuJvJokiC3BP1+O70fglmE1ShkH8lpXqZBac1rrU3FnNYSUs4pL9lBdTKeRPOQ==",
      "dependencies": {
        "@algolia/cache-common": "4.20.0"
      }
    },
    "node_modules/@algolia/cache-common": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/cache-common/-/cache-common-4.20.0.tgz",
      "integrity": "sha512-vCfxauaZutL3NImzB2G9LjLt36vKAckc6DhMp05An14kVo8F1Yofb6SIl6U3SaEz8pG2QOB9ptwM5c+zGevwIQ=="
    },
    "node_modules/@algolia/cache-in-memory": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/cache-in-memory/-/cache-in-memory-4.20.0.tgz",
      "integrity": "sha512-Wm9ak/IaacAZXS4mB3+qF/KCoVSBV6aLgIGFEtQtJwjv64g4ePMapORGmCyulCFwfePaRAtcaTbMcJF+voc/bg==",
      "dependencies": {
        "@algolia/cache-common": "4.20.0"
      }
    },
    "node_modules/@algolia/client-account": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/client-account/-/client-account-4.20.0.tgz",
      "integrity": "sha512-GGToLQvrwo7am4zVkZTnKa72pheQeez/16sURDWm7Seyz+HUxKi3BM6fthVVPUEBhtJ0reyVtuK9ArmnaKl10Q==",
      "dependencies": {
        "@algolia/client-common": "4.20.0",
        "@algolia/client-search": "4.20.0",
        "@algolia/transporter": "4.20.0"
      }
    },
    "node_modules/@algolia/client-analytics": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/client-analytics/-/client-analytics-4.20.0.tgz",
      "integrity": "sha512-EIr+PdFMOallRdBTHHdKI3CstslgLORQG7844Mq84ib5oVFRVASuuPmG4bXBgiDbcsMLUeOC6zRVJhv1KWI0ug==",
      "dependencies": {
        "@algolia/client-common": "4.20.0",
        "@algolia/client-search": "4.20.0",
        "@algolia/requester-common": "4.20.0",
        "@algolia/transporter": "4.20.0"
      }
    },
    "node_modules/@algolia/client-common": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/client-common/-/client-common-4.20.0.tgz",
      "integrity": "sha512-P3WgMdEss915p+knMMSd/fwiHRHKvDu4DYRrCRaBrsfFw7EQHon+EbRSm4QisS9NYdxbS04kcvNoavVGthyfqQ==",
      "dependencies": {
        "@algolia/requester-common": "4.20.0",
        "@algolia/transporter": "4.20.0"
      }
    },
    "node_modules/@algolia/client-personalization": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/client-personalization/-/client-personalization-4.20.0.tgz",
      "integrity": "sha512-N9+zx0tWOQsLc3K4PVRDV8GUeOLAY0i445En79Pr3zWB+m67V+n/8w4Kw1C5LlbHDDJcyhMMIlqezh6BEk7xAQ==",
      "dependencies": {
        "@algolia/client-common": "4.20.0",
        "@algolia/requester-common": "4.20.0",
        "@algolia/transporter": "4.20.0"
      }
    },
    "node_modules/@algolia/client-search": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/client-search/-/client-search-4.20.0.tgz",
      "integrity": "sha512-zgwqnMvhWLdpzKTpd3sGmMlr4c+iS7eyyLGiaO51zDZWGMkpgoNVmltkzdBwxOVXz0RsFMznIxB9zuarUv4TZg==",
      "dependencies": {
        "@algolia/client-common": "4.20.0",
        "@algolia/requester-common": "4.20.0",
        "@algolia/transporter": "4.20.0"
      }
    },
    "node_modules/@algolia/logger-common": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/logger-common/-/logger-common-4.20.0.tgz",
      "integrity": "sha512-xouigCMB5WJYEwvoWW5XDv7Z9f0A8VoXJc3VKwlHJw/je+3p2RcDXfksLI4G4lIVncFUYMZx30tP/rsdlvvzHQ=="
    },
    "node_modules/@algolia/logger-console": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/logger-console/-/logger-console-4.20.0.tgz",
      "integrity": "sha512-THlIGG1g/FS63z0StQqDhT6bprUczBI8wnLT3JWvfAQDZX5P6fCg7dG+pIrUBpDIHGszgkqYEqECaKKsdNKOUA==",
      "dependencies": {
        "@algolia/logger-common": "4.20.0"
      }
    },
    "node_modules/@algolia/requester-browser-xhr": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/requester-browser-xhr/-/requester-browser-xhr-4.20.0.tgz",
      "integrity": "sha512-HbzoSjcjuUmYOkcHECkVTwAelmvTlgs48N6Owt4FnTOQdwn0b8pdht9eMgishvk8+F8bal354nhx/xOoTfwiAw==",
      "dependencies": {
        "@algolia/requester-common": "4.20.0"
      }
    },
    "node_modules/@algolia/requester-common": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/requester-common/-/requester-common-4.20.0.tgz",
      "integrity": "sha512-9h6ye6RY/BkfmeJp7Z8gyyeMrmmWsMOCRBXQDs4mZKKsyVlfIVICpcSibbeYcuUdurLhIlrOUkH3rQEgZzonng=="
    },
    "node_modules/@algolia/requester-node-http": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/requester-node-http/-/requester-node-http-4.20.0.tgz",
      "integrity": "sha512-ocJ66L60ABSSTRFnCHIEZpNHv6qTxsBwJEPfYaSBsLQodm0F9ptvalFkHMpvj5DfE22oZrcrLbOYM2bdPJRHng==",
      "dependencies": {
        "@algolia/requester-common": "4.20.0"
      }
    },
    "node_modules/@algolia/transporter": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/@algolia/transporter/-/transporter-4.20.0.tgz",
      "integrity": "sha512-Lsii1pGWOAISbzeyuf+r/GPhvHMPHSPrTDWNcIzOE1SG1inlJHICaVe2ikuoRjcpgxZNU54Jl+if15SUCsaTUg==",
      "dependencies": {
        "@algolia/cache-common": "4.20.0",
        "@algolia/logger-common": "4.20.0",
        "@algolia/requester-common": "4.20.0"
      }
    },
    "node_modules/@ampproject/remapping": {
      "version": "2.3.0",
      "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz",
      "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==",
      "dependencies": {
        "@jridgewell/gen-mapping": "^0.3.5",
        "@jridgewell/trace-mapping": "^0.3.24"
      },
      "engines": {
        "node": ">=6.0.0"
      }
    },
    "node_modules/@astrojs/compiler": {
      "version": "2.7.1",
      "resolved": "https://registry.npmjs.org/@astrojs/compiler/-/compiler-2.7.1.tgz",
      "integrity": "sha512-/POejAYuj8WEw7ZI0J8JBvevjfp9jQ9Wmu/Bg52RiNwGXkMV7JnYpsenVfHvvf1G7R5sXHGKlTcxlQWhoUTiGQ=="
    },
    "node_modules/@astrojs/internal-helpers": {
      "version": "0.4.0",
      "resolved": "https://registry.npmjs.org/@astrojs/internal-helpers/-/internal-helpers-0.4.0.tgz",
      "integrity": "sha512-6B13lz5n6BrbTqCTwhXjJXuR1sqiX/H6rTxzlXx+lN1NnV4jgnq/KJldCQaUWJzPL5SiWahQyinxAbxQtwgPHA=="
    },
    "node_modules/@astrojs/markdown-remark": {
      "version": "5.1.0",
      "resolved": "https://registry.npmjs.org/@astrojs/markdown-remark/-/markdown-remark-5.1.0.tgz",
      "integrity": "sha512-S6Z3K2hOB7MfjeDoHsotnP/q2UsnEDB8NlNAaCjMDsGBZfTUbWxyLW3CaphEWw08f6KLZi2ibK9yC3BaMhh2NQ==",
      "dependencies": {
        "@astrojs/prism": "^3.1.0",
        "github-slugger": "^2.0.0",
        "hast-util-from-html": "^2.0.0",
        "hast-util-to-text": "^4.0.0",
        "import-meta-resolve": "^4.0.0",
        "mdast-util-definitions": "^6.0.0",
        "rehype-raw": "^7.0.0",
        "rehype-stringify": "^10.0.0",
        "remark-gfm": "^4.0.0",
        "remark-parse": "^11.0.0",
        "remark-rehype": "^11.0.0",
        "remark-smartypants": "^2.0.0",
        "shiki": "^1.1.2",
        "unified": "^11.0.4",
        "unist-util-remove-position": "^5.0.0",
        "unist-util-visit": "^5.0.0",
        "unist-util-visit-parents": "^6.0.0",
        "vfile": "^6.0.1"
      }
    },
    "node_modules/@astrojs/markdown-remark/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/@astrojs/markdown-remark/node_modules/unified": {
      "version": "11.0.4",
      "resolved": "https://registry.npmjs.org/unified/-/unified-11.0.4.tgz",
      "integrity": "sha512-apMPnyLjAX+ty4OrNap7yumyVAMlKx5IWU2wlzzUdYJO9A8f1p9m/gywF/GM2ZDFcjQPrx59Mc90KwmxsoklxQ==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "bail": "^2.0.0",
        "devlop": "^1.0.0",
        "extend": "^3.0.0",
        "is-plain-obj": "^4.0.0",
        "trough": "^2.0.0",
        "vfile": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/markdown-remark/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/markdown-remark/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/markdown-remark/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/markdown-remark/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/markdown-remark/node_modules/vfile": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.1.tgz",
      "integrity": "sha512-1bYqc7pt6NIADBJ98UiG0Bn/CHIVOoZ/IyEkqIruLg0mE1BKzkOXY2D6CSqQIcKqgadppE5lrxgWXJmXd7zZJw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/markdown-remark/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/mdx": {
      "version": "2.3.1",
      "resolved": "https://registry.npmjs.org/@astrojs/mdx/-/mdx-2.3.1.tgz",
      "integrity": "sha512-BOQFKD2Pi9cRntNQJlpF2fh4xV8doNpmVy9NKI95r4jsitrY4X5aTOhAowi+fkQgP/zW1A4HwCyQ6Pdam6z8zQ==",
      "dependencies": {
        "@astrojs/markdown-remark": "5.1.0",
        "@mdx-js/mdx": "^3.0.0",
        "acorn": "^8.11.2",
        "es-module-lexer": "^1.4.1",
        "estree-util-visit": "^2.0.0",
        "github-slugger": "^2.0.0",
        "gray-matter": "^4.0.3",
        "hast-util-to-html": "^9.0.0",
        "kleur": "^4.1.4",
        "rehype-raw": "^7.0.0",
        "remark-gfm": "^4.0.0",
        "remark-smartypants": "^2.0.0",
        "source-map": "^0.7.4",
        "unist-util-visit": "^5.0.0",
        "vfile": "^6.0.1"
      },
      "engines": {
        "node": "^18.17.1 || ^20.3.0 || >=21.0.0"
      },
      "peerDependencies": {
        "astro": "^4.0.0"
      }
    },
    "node_modules/@astrojs/mdx/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/@astrojs/mdx/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/mdx/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/mdx/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/mdx/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/mdx/node_modules/vfile": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.1.tgz",
      "integrity": "sha512-1bYqc7pt6NIADBJ98UiG0Bn/CHIVOoZ/IyEkqIruLg0mE1BKzkOXY2D6CSqQIcKqgadppE5lrxgWXJmXd7zZJw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/mdx/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/prism": {
      "version": "3.1.0",
      "resolved": "https://registry.npmjs.org/@astrojs/prism/-/prism-3.1.0.tgz",
      "integrity": "sha512-Z9IYjuXSArkAUx3N6xj6+Bnvx8OdUSHA8YoOgyepp3+zJmtVYJIl/I18GozdJVW1p5u/CNpl3Km7/gwTJK85cw==",
      "dependencies": {
        "prismjs": "^1.29.0"
      },
      "engines": {
        "node": "^18.17.1 || ^20.3.0 || >=21.0.0"
      }
    },
    "node_modules/@astrojs/sitemap": {
      "version": "3.1.4",
      "resolved": "https://registry.npmjs.org/@astrojs/sitemap/-/sitemap-3.1.4.tgz",
      "integrity": "sha512-po8CqDCK14O6phU1mB5C8SyVLyQEa+7pJM8oXxs1mVh8DgvxxaA5E7lak1vzOmBcyyyHBW32jakGqNYc66sBRw==",
      "dependencies": {
        "sitemap": "^7.1.1",
        "stream-replace-string": "^2.0.0",
        "zod": "^3.22.4"
      }
    },
    "node_modules/@astrojs/starlight": {
      "version": "0.21.5",
      "resolved": "https://registry.npmjs.org/@astrojs/starlight/-/starlight-0.21.5.tgz",
      "integrity": "sha512-cvftxu7DM4C25KGSxqyIk81DiQGX0zx9s5sfmprd1kKQK1h/MQXaRVDCpJrK4SjrgWtpG1UoKLJZBgD5w4k9kw==",
      "dependencies": {
        "@astrojs/mdx": "^2.1.1",
        "@astrojs/sitemap": "^3.0.5",
        "@pagefind/default-ui": "^1.0.3",
        "@types/hast": "^3.0.3",
        "@types/mdast": "^4.0.3",
        "astro-expressive-code": "^0.33.4",
        "bcp-47": "^2.1.0",
        "hast-util-from-html": "^2.0.1",
        "hast-util-select": "^6.0.2",
        "hast-util-to-string": "^3.0.0",
        "hastscript": "^8.0.0",
        "mdast-util-directive": "^3.0.0",
        "mdast-util-to-markdown": "^2.1.0",
        "pagefind": "^1.0.3",
        "rehype": "^13.0.1",
        "remark-directive": "^3.0.0",
        "unified": "^11.0.4",
        "unist-util-visit": "^5.0.0",
        "vfile": "^6.0.1"
      },
      "peerDependencies": {
        "astro": "^4.2.7"
      }
    },
    "node_modules/@astrojs/starlight/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/@astrojs/starlight/node_modules/unified": {
      "version": "11.0.4",
      "resolved": "https://registry.npmjs.org/unified/-/unified-11.0.4.tgz",
      "integrity": "sha512-apMPnyLjAX+ty4OrNap7yumyVAMlKx5IWU2wlzzUdYJO9A8f1p9m/gywF/GM2ZDFcjQPrx59Mc90KwmxsoklxQ==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "bail": "^2.0.0",
        "devlop": "^1.0.0",
        "extend": "^3.0.0",
        "is-plain-obj": "^4.0.0",
        "trough": "^2.0.0",
        "vfile": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/starlight/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/starlight/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/starlight/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/starlight/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/starlight/node_modules/vfile": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.1.tgz",
      "integrity": "sha512-1bYqc7pt6NIADBJ98UiG0Bn/CHIVOoZ/IyEkqIruLg0mE1BKzkOXY2D6CSqQIcKqgadppE5lrxgWXJmXd7zZJw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/starlight/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@astrojs/telemetry": {
      "version": "3.1.0",
      "resolved": "https://registry.npmjs.org/@astrojs/telemetry/-/telemetry-3.1.0.tgz",
      "integrity": "sha512-/ca/+D8MIKEC8/A9cSaPUqQNZm+Es/ZinRv0ZAzvu2ios7POQSsVD+VOj7/hypWNsNM3T7RpfgNq7H2TU1KEHA==",
      "dependencies": {
        "ci-info": "^4.0.0",
        "debug": "^4.3.4",
        "dlv": "^1.1.3",
        "dset": "^3.1.3",
        "is-docker": "^3.0.0",
        "is-wsl": "^3.0.0",
        "which-pm-runs": "^1.1.0"
      },
      "engines": {
        "node": "^18.17.1 || ^20.3.0 || >=21.0.0"
      }
    },
    "node_modules/@babel/code-frame": {
      "version": "7.24.2",
      "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.24.2.tgz",
      "integrity": "sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ==",
      "dependencies": {
        "@babel/highlight": "^7.24.2",
        "picocolors": "^1.0.0"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/compat-data": {
      "version": "7.24.4",
      "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.24.4.tgz",
      "integrity": "sha512-vg8Gih2MLK+kOkHJp4gBEIkyaIi00jgWot2D9QOmmfLC8jINSOzmCLta6Bvz/JSBCqnegV0L80jhxkol5GWNfQ==",
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/core": {
      "version": "7.24.4",
      "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.24.4.tgz",
      "integrity": "sha512-MBVlMXP+kkl5394RBLSxxk/iLTeVGuXTV3cIDXavPpMMqnSnt6apKgan/U8O3USWZCWZT/TbgfEpKa4uMgN4Dg==",
      "dependencies": {
        "@ampproject/remapping": "^2.2.0",
        "@babel/code-frame": "^7.24.2",
        "@babel/generator": "^7.24.4",
        "@babel/helper-compilation-targets": "^7.23.6",
        "@babel/helper-module-transforms": "^7.23.3",
        "@babel/helpers": "^7.24.4",
        "@babel/parser": "^7.24.4",
        "@babel/template": "^7.24.0",
        "@babel/traverse": "^7.24.1",
        "@babel/types": "^7.24.0",
        "convert-source-map": "^2.0.0",
        "debug": "^4.1.0",
        "gensync": "^1.0.0-beta.2",
        "json5": "^2.2.3",
        "semver": "^6.3.1"
      },
      "engines": {
        "node": ">=6.9.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/babel"
      }
    },
    "node_modules/@babel/core/node_modules/semver": {
      "version": "6.3.1",
      "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz",
      "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==",
      "bin": {
        "semver": "bin/semver.js"
      }
    },
    "node_modules/@babel/generator": {
      "version": "7.24.4",
      "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.24.4.tgz",
      "integrity": "sha512-Xd6+v6SnjWVx/nus+y0l1sxMOTOMBkyL4+BIdbALyatQnAe/SRVjANeDPSCYaX+i1iJmuGSKf3Z+E+V/va1Hvw==",
      "dependencies": {
        "@babel/types": "^7.24.0",
        "@jridgewell/gen-mapping": "^0.3.5",
        "@jridgewell/trace-mapping": "^0.3.25",
        "jsesc": "^2.5.1"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-annotate-as-pure": {
      "version": "7.22.5",
      "resolved": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.22.5.tgz",
      "integrity": "sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==",
      "dependencies": {
        "@babel/types": "^7.22.5"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-compilation-targets": {
      "version": "7.23.6",
      "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.23.6.tgz",
      "integrity": "sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==",
      "dependencies": {
        "@babel/compat-data": "^7.23.5",
        "@babel/helper-validator-option": "^7.23.5",
        "browserslist": "^4.22.2",
        "lru-cache": "^5.1.1",
        "semver": "^6.3.1"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-compilation-targets/node_modules/semver": {
      "version": "6.3.1",
      "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz",
      "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==",
      "bin": {
        "semver": "bin/semver.js"
      }
    },
    "node_modules/@babel/helper-environment-visitor": {
      "version": "7.22.20",
      "resolved": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.20.tgz",
      "integrity": "sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==",
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-function-name": {
      "version": "7.23.0",
      "resolved": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.23.0.tgz",
      "integrity": "sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==",
      "dependencies": {
        "@babel/template": "^7.22.15",
        "@babel/types": "^7.23.0"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-hoist-variables": {
      "version": "7.22.5",
      "resolved": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz",
      "integrity": "sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==",
      "dependencies": {
        "@babel/types": "^7.22.5"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-module-imports": {
      "version": "7.24.3",
      "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.24.3.tgz",
      "integrity": "sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg==",
      "dependencies": {
        "@babel/types": "^7.24.0"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-module-transforms": {
      "version": "7.23.3",
      "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.23.3.tgz",
      "integrity": "sha512-7bBs4ED9OmswdfDzpz4MpWgSrV7FXlc3zIagvLFjS5H+Mk7Snr21vQ6QwrsoCGMfNC4e4LQPdoULEt4ykz0SRQ==",
      "dependencies": {
        "@babel/helper-environment-visitor": "^7.22.20",
        "@babel/helper-module-imports": "^7.22.15",
        "@babel/helper-simple-access": "^7.22.5",
        "@babel/helper-split-export-declaration": "^7.22.6",
        "@babel/helper-validator-identifier": "^7.22.20"
      },
      "engines": {
        "node": ">=6.9.0"
      },
      "peerDependencies": {
        "@babel/core": "^7.0.0"
      }
    },
    "node_modules/@babel/helper-plugin-utils": {
      "version": "7.24.0",
      "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.0.tgz",
      "integrity": "sha512-9cUznXMG0+FxRuJfvL82QlTqIzhVW9sL0KjMPHhAOOvpQGL8QtdxnBKILjBqxlHyliz0yCa1G903ZXI/FuHy2w==",
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-simple-access": {
      "version": "7.22.5",
      "resolved": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz",
      "integrity": "sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==",
      "dependencies": {
        "@babel/types": "^7.22.5"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-split-export-declaration": {
      "version": "7.22.6",
      "resolved": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz",
      "integrity": "sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==",
      "dependencies": {
        "@babel/types": "^7.22.5"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-string-parser": {
      "version": "7.24.1",
      "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.24.1.tgz",
      "integrity": "sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ==",
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-validator-identifier": {
      "version": "7.22.20",
      "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz",
      "integrity": "sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==",
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helper-validator-option": {
      "version": "7.23.5",
      "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.23.5.tgz",
      "integrity": "sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==",
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/helpers": {
      "version": "7.24.4",
      "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.24.4.tgz",
      "integrity": "sha512-FewdlZbSiwaVGlgT1DPANDuCHaDMiOo+D/IDYRFYjHOuv66xMSJ7fQwwODwRNAPkADIO/z1EoF/l2BCWlWABDw==",
      "dependencies": {
        "@babel/template": "^7.24.0",
        "@babel/traverse": "^7.24.1",
        "@babel/types": "^7.24.0"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/highlight": {
      "version": "7.24.2",
      "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.24.2.tgz",
      "integrity": "sha512-Yac1ao4flkTxTteCDZLEvdxg2fZfz1v8M4QpaGypq/WPDqg3ijHYbDfs+LG5hvzSoqaSZ9/Z9lKSP3CjZjv+pA==",
      "dependencies": {
        "@babel/helper-validator-identifier": "^7.22.20",
        "chalk": "^2.4.2",
        "js-tokens": "^4.0.0",
        "picocolors": "^1.0.0"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/parser": {
      "version": "7.24.4",
      "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.4.tgz",
      "integrity": "sha512-zTvEBcghmeBma9QIGunWevvBAp4/Qu9Bdq+2k0Ot4fVMD6v3dsC9WOcRSKk7tRRyBM/53yKMJko9xOatGQAwSg==",
      "bin": {
        "parser": "bin/babel-parser.js"
      },
      "engines": {
        "node": ">=6.0.0"
      }
    },
    "node_modules/@babel/plugin-syntax-jsx": {
      "version": "7.24.1",
      "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.1.tgz",
      "integrity": "sha512-2eCtxZXf+kbkMIsXS4poTvT4Yu5rXiRa+9xGVT56raghjmBTKMpFNc9R4IDiB4emao9eO22Ox7CxuJG7BgExqA==",
      "dependencies": {
        "@babel/helper-plugin-utils": "^7.24.0"
      },
      "engines": {
        "node": ">=6.9.0"
      },
      "peerDependencies": {
        "@babel/core": "^7.0.0-0"
      }
    },
    "node_modules/@babel/plugin-transform-react-jsx": {
      "version": "7.23.4",
      "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.23.4.tgz",
      "integrity": "sha512-5xOpoPguCZCRbo/JeHlloSkTA8Bld1J/E1/kLfD1nsuiW1m8tduTA1ERCgIZokDflX/IBzKcqR3l7VlRgiIfHA==",
      "dependencies": {
        "@babel/helper-annotate-as-pure": "^7.22.5",
        "@babel/helper-module-imports": "^7.22.15",
        "@babel/helper-plugin-utils": "^7.22.5",
        "@babel/plugin-syntax-jsx": "^7.23.3",
        "@babel/types": "^7.23.4"
      },
      "engines": {
        "node": ">=6.9.0"
      },
      "peerDependencies": {
        "@babel/core": "^7.0.0-0"
      }
    },
    "node_modules/@babel/template": {
      "version": "7.24.0",
      "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.24.0.tgz",
      "integrity": "sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA==",
      "dependencies": {
        "@babel/code-frame": "^7.23.5",
        "@babel/parser": "^7.24.0",
        "@babel/types": "^7.24.0"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/traverse": {
      "version": "7.24.1",
      "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.24.1.tgz",
      "integrity": "sha512-xuU6o9m68KeqZbQuDt2TcKSxUw/mrsvavlEqQ1leZ/B+C9tk6E4sRWy97WaXgvq5E+nU3cXMxv3WKOCanVMCmQ==",
      "dependencies": {
        "@babel/code-frame": "^7.24.1",
        "@babel/generator": "^7.24.1",
        "@babel/helper-environment-visitor": "^7.22.20",
        "@babel/helper-function-name": "^7.23.0",
        "@babel/helper-hoist-variables": "^7.22.5",
        "@babel/helper-split-export-declaration": "^7.22.6",
        "@babel/parser": "^7.24.1",
        "@babel/types": "^7.24.0",
        "debug": "^4.3.1",
        "globals": "^11.1.0"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@babel/types": {
      "version": "7.24.0",
      "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.24.0.tgz",
      "integrity": "sha512-+j7a5c253RfKh8iABBhywc8NSfP5LURe7Uh4qpsh6jc+aLJguvmIUBdjSdEMQv2bENrCR5MfRdjGo7vzS/ob7w==",
      "dependencies": {
        "@babel/helper-string-parser": "^7.23.4",
        "@babel/helper-validator-identifier": "^7.22.20",
        "to-fast-properties": "^2.0.0"
      },
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/@ctrl/tinycolor": {
      "version": "3.6.1",
      "resolved": "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz",
      "integrity": "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==",
      "engines": {
        "node": ">=10"
      }
    },
    "node_modules/@docsearch/css": {
      "version": "3.5.2",
      "resolved": "https://registry.npmjs.org/@docsearch/css/-/css-3.5.2.tgz",
      "integrity": "sha512-SPiDHaWKQZpwR2siD0KQUwlStvIAnEyK6tAE2h2Wuoq8ue9skzhlyVQ1ddzOxX6khULnAALDiR/isSF3bnuciA=="
    },
    "node_modules/@docsearch/js": {
      "version": "3.5.2",
      "resolved": "https://registry.npmjs.org/@docsearch/js/-/js-3.5.2.tgz",
      "integrity": "sha512-p1YFTCDflk8ieHgFJYfmyHBki1D61+U9idwrLh+GQQMrBSP3DLGKpy0XUJtPjAOPltcVbqsTjiPFfH7JImjUNg==",
      "dependencies": {
        "@docsearch/react": "3.5.2",
        "preact": "^10.0.0"
      }
    },
    "node_modules/@docsearch/react": {
      "version": "3.5.2",
      "resolved": "https://registry.npmjs.org/@docsearch/react/-/react-3.5.2.tgz",
      "integrity": "sha512-9Ahcrs5z2jq/DcAvYtvlqEBHImbm4YJI8M9y0x6Tqg598P40HTEkX7hsMcIuThI+hTFxRGZ9hll0Wygm2yEjng==",
      "dependencies": {
        "@algolia/autocomplete-core": "1.9.3",
        "@algolia/autocomplete-preset-algolia": "1.9.3",
        "@docsearch/css": "3.5.2",
        "algoliasearch": "^4.19.1"
      },
      "peerDependencies": {
        "@types/react": ">= 16.8.0 < 19.0.0",
        "react": ">= 16.8.0 < 19.0.0",
        "react-dom": ">= 16.8.0 < 19.0.0",
        "search-insights": ">= 1 < 3"
      },
      "peerDependenciesMeta": {
        "@types/react": {
          "optional": true
        },
        "react": {
          "optional": true
        },
        "react-dom": {
          "optional": true
        },
        "search-insights": {
          "optional": true
        }
      }
    },
    "node_modules/@esbuild/aix-ppc64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.20.2.tgz",
      "integrity": "sha512-D+EBOJHXdNZcLJRBkhENNG8Wji2kgc9AZ9KiPr1JuZjsNtyHzrsfLRrY0tk2H2aoFu6RANO1y1iPPUCDYWkb5g==",
      "cpu": [
        "ppc64"
      ],
      "optional": true,
      "os": [
        "aix"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/android-arm": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.20.2.tgz",
      "integrity": "sha512-t98Ra6pw2VaDhqNWO2Oph2LXbz/EJcnLmKLGBJwEwXX/JAN83Fym1rU8l0JUWK6HkIbWONCSSatf4sf2NBRx/w==",
      "cpu": [
        "arm"
      ],
      "optional": true,
      "os": [
        "android"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/android-arm64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.20.2.tgz",
      "integrity": "sha512-mRzjLacRtl/tWU0SvD8lUEwb61yP9cqQo6noDZP/O8VkwafSYwZ4yWy24kan8jE/IMERpYncRt2dw438LP3Xmg==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "android"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/android-x64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.20.2.tgz",
      "integrity": "sha512-btzExgV+/lMGDDa194CcUQm53ncxzeBrWJcncOBxuC6ndBkKxnHdFJn86mCIgTELsooUmwUm9FkhSp5HYu00Rg==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "android"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/darwin-arm64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.20.2.tgz",
      "integrity": "sha512-4J6IRT+10J3aJH3l1yzEg9y3wkTDgDk7TSDFX+wKFiWjqWp/iCfLIYzGyasx9l0SAFPT1HwSCR+0w/h1ES/MjA==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "darwin"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/darwin-x64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.20.2.tgz",
      "integrity": "sha512-tBcXp9KNphnNH0dfhv8KYkZhjc+H3XBkF5DKtswJblV7KlT9EI2+jeA8DgBjp908WEuYll6pF+UStUCfEpdysA==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "darwin"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/freebsd-arm64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.20.2.tgz",
      "integrity": "sha512-d3qI41G4SuLiCGCFGUrKsSeTXyWG6yem1KcGZVS+3FYlYhtNoNgYrWcvkOoaqMhwXSMrZRl69ArHsGJ9mYdbbw==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "freebsd"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/freebsd-x64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.20.2.tgz",
      "integrity": "sha512-d+DipyvHRuqEeM5zDivKV1KuXn9WeRX6vqSqIDgwIfPQtwMP4jaDsQsDncjTDDsExT4lR/91OLjRo8bmC1e+Cw==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "freebsd"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/linux-arm": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.20.2.tgz",
      "integrity": "sha512-VhLPeR8HTMPccbuWWcEUD1Az68TqaTYyj6nfE4QByZIQEQVWBB8vup8PpR7y1QHL3CpcF6xd5WVBU/+SBEvGTg==",
      "cpu": [
        "arm"
      ],
      "optional": true,
      "os": [
        "linux"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/linux-arm64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.20.2.tgz",
      "integrity": "sha512-9pb6rBjGvTFNira2FLIWqDk/uaf42sSyLE8j1rnUpuzsODBq7FvpwHYZxQ/It/8b+QOS1RYfqgGFNLRI+qlq2A==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "linux"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/linux-ia32": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.20.2.tgz",
      "integrity": "sha512-o10utieEkNPFDZFQm9CoP7Tvb33UutoJqg3qKf1PWVeeJhJw0Q347PxMvBgVVFgouYLGIhFYG0UGdBumROyiig==",
      "cpu": [
        "ia32"
      ],
      "optional": true,
      "os": [
        "linux"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/linux-loong64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.20.2.tgz",
      "integrity": "sha512-PR7sp6R/UC4CFVomVINKJ80pMFlfDfMQMYynX7t1tNTeivQ6XdX5r2XovMmha/VjR1YN/HgHWsVcTRIMkymrgQ==",
      "cpu": [
        "loong64"
      ],
      "optional": true,
      "os": [
        "linux"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/linux-mips64el": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.20.2.tgz",
      "integrity": "sha512-4BlTqeutE/KnOiTG5Y6Sb/Hw6hsBOZapOVF6njAESHInhlQAghVVZL1ZpIctBOoTFbQyGW+LsVYZ8lSSB3wkjA==",
      "cpu": [
        "mips64el"
      ],
      "optional": true,
      "os": [
        "linux"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/linux-ppc64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.20.2.tgz",
      "integrity": "sha512-rD3KsaDprDcfajSKdn25ooz5J5/fWBylaaXkuotBDGnMnDP1Uv5DLAN/45qfnf3JDYyJv/ytGHQaziHUdyzaAg==",
      "cpu": [
        "ppc64"
      ],
      "optional": true,
      "os": [
        "linux"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/linux-riscv64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.20.2.tgz",
      "integrity": "sha512-snwmBKacKmwTMmhLlz/3aH1Q9T8v45bKYGE3j26TsaOVtjIag4wLfWSiZykXzXuE1kbCE+zJRmwp+ZbIHinnVg==",
      "cpu": [
        "riscv64"
      ],
      "optional": true,
      "os": [
        "linux"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/linux-s390x": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.20.2.tgz",
      "integrity": "sha512-wcWISOobRWNm3cezm5HOZcYz1sKoHLd8VL1dl309DiixxVFoFe/o8HnwuIwn6sXre88Nwj+VwZUvJf4AFxkyrQ==",
      "cpu": [
        "s390x"
      ],
      "optional": true,
      "os": [
        "linux"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/linux-x64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.20.2.tgz",
      "integrity": "sha512-1MdwI6OOTsfQfek8sLwgyjOXAu+wKhLEoaOLTjbijk6E2WONYpH9ZU2mNtR+lZ2B4uwr+usqGuVfFT9tMtGvGw==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "linux"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/netbsd-x64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.20.2.tgz",
      "integrity": "sha512-K8/DhBxcVQkzYc43yJXDSyjlFeHQJBiowJ0uVL6Tor3jGQfSGHNNJcWxNbOI8v5k82prYqzPuwkzHt3J1T1iZQ==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "netbsd"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/openbsd-x64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.20.2.tgz",
      "integrity": "sha512-eMpKlV0SThJmmJgiVyN9jTPJ2VBPquf6Kt/nAoo6DgHAoN57K15ZghiHaMvqjCye/uU4X5u3YSMgVBI1h3vKrQ==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "openbsd"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/sunos-x64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.20.2.tgz",
      "integrity": "sha512-2UyFtRC6cXLyejf/YEld4Hajo7UHILetzE1vsRcGL3earZEW77JxrFjH4Ez2qaTiEfMgAXxfAZCm1fvM/G/o8w==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "sunos"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/win32-arm64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.20.2.tgz",
      "integrity": "sha512-GRibxoawM9ZCnDxnP3usoUDO9vUkpAxIIZ6GQI+IlVmr5kP3zUq+l17xELTHMWTWzjxa2guPNyrpq1GWmPvcGQ==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "win32"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/win32-ia32": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.20.2.tgz",
      "integrity": "sha512-HfLOfn9YWmkSKRQqovpnITazdtquEW8/SoHW7pWpuEeguaZI4QnCRW6b+oZTztdBnZOS2hqJ6im/D5cPzBTTlQ==",
      "cpu": [
        "ia32"
      ],
      "optional": true,
      "os": [
        "win32"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@esbuild/win32-x64": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.20.2.tgz",
      "integrity": "sha512-N49X4lJX27+l9jbLKSqZ6bKNjzQvHaT8IIFUy+YIqmXQdjYCToGWwOItDrfby14c78aDd5NHQl29xingXfCdLQ==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "win32"
      ],
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/@expressive-code/core": {
      "version": "0.33.5",
      "resolved": "https://registry.npmjs.org/@expressive-code/core/-/core-0.33.5.tgz",
      "integrity": "sha512-KL0EkKAvd7SSIQL3ZIP19xqe4xNjBaQYNvcJC6RmoBUnQpvxaJNFwRxCBEF/X0ftJEMaSG7WTrabZ9c/zFeqmA==",
      "dependencies": {
        "@ctrl/tinycolor": "^3.6.0",
        "hast-util-to-html": "^8.0.4",
        "hastscript": "^7.2.0",
        "postcss": "^8.4.21",
        "postcss-nested": "^6.0.1"
      }
    },
    "node_modules/@expressive-code/core/node_modules/@types/hast": {
      "version": "2.3.10",
      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-2.3.10.tgz",
      "integrity": "sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==",
      "dependencies": {
        "@types/unist": "^2"
      }
    },
    "node_modules/@expressive-code/core/node_modules/hast-util-from-parse5": {
      "version": "7.1.2",
      "resolved": "https://registry.npmjs.org/hast-util-from-parse5/-/hast-util-from-parse5-7.1.2.tgz",
      "integrity": "sha512-Nz7FfPBuljzsN3tCQ4kCBKqdNhQE2l0Tn+X1ubgKBPRoiDIu1mL08Cfw4k7q71+Duyaw7DXDN+VTAp4Vh3oCOw==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "@types/unist": "^2.0.0",
        "hastscript": "^7.0.0",
        "property-information": "^6.0.0",
        "vfile": "^5.0.0",
        "vfile-location": "^4.0.0",
        "web-namespaces": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/core/node_modules/hast-util-parse-selector": {
      "version": "3.1.1",
      "resolved": "https://registry.npmjs.org/hast-util-parse-selector/-/hast-util-parse-selector-3.1.1.tgz",
      "integrity": "sha512-jdlwBjEexy1oGz0aJ2f4GKMaVKkA9jwjr4MjAAI22E5fM/TXVZHuS5OpONtdeIkRKqAaryQ2E9xNQxijoThSZA==",
      "dependencies": {
        "@types/hast": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/core/node_modules/hast-util-raw": {
      "version": "7.2.3",
      "resolved": "https://registry.npmjs.org/hast-util-raw/-/hast-util-raw-7.2.3.tgz",
      "integrity": "sha512-RujVQfVsOrxzPOPSzZFiwofMArbQke6DJjnFfceiEbFh7S05CbPt0cYN+A5YeD3pso0JQk6O1aHBnx9+Pm2uqg==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "@types/parse5": "^6.0.0",
        "hast-util-from-parse5": "^7.0.0",
        "hast-util-to-parse5": "^7.0.0",
        "html-void-elements": "^2.0.0",
        "parse5": "^6.0.0",
        "unist-util-position": "^4.0.0",
        "unist-util-visit": "^4.0.0",
        "vfile": "^5.0.0",
        "web-namespaces": "^2.0.0",
        "zwitch": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/core/node_modules/hast-util-to-html": {
      "version": "8.0.4",
      "resolved": "https://registry.npmjs.org/hast-util-to-html/-/hast-util-to-html-8.0.4.tgz",
      "integrity": "sha512-4tpQTUOr9BMjtYyNlt0P50mH7xj0Ks2xpo8M943Vykljf99HW6EzulIoJP1N3eKOSScEHzyzi9dm7/cn0RfGwA==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "@types/unist": "^2.0.0",
        "ccount": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "hast-util-raw": "^7.0.0",
        "hast-util-whitespace": "^2.0.0",
        "html-void-elements": "^2.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0",
        "stringify-entities": "^4.0.0",
        "zwitch": "^2.0.4"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/core/node_modules/hast-util-to-parse5": {
      "version": "7.1.0",
      "resolved": "https://registry.npmjs.org/hast-util-to-parse5/-/hast-util-to-parse5-7.1.0.tgz",
      "integrity": "sha512-YNRgAJkH2Jky5ySkIqFXTQiaqcAtJyVE+D5lkN6CdtOqrnkLfGYYrEcKuHOJZlp+MwjSwuD3fZuawI+sic/RBw==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0",
        "web-namespaces": "^2.0.0",
        "zwitch": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/core/node_modules/hast-util-whitespace": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-2.0.1.tgz",
      "integrity": "sha512-nAxA0v8+vXSBDt3AnRUNjyRIQ0rD+ntpbAp4LnPkumc5M9yUbSMa4XDU9Q6etY4f1Wp4bNgvc1yjiZtsTTrSng==",
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/core/node_modules/hastscript": {
      "version": "7.2.0",
      "resolved": "https://registry.npmjs.org/hastscript/-/hastscript-7.2.0.tgz",
      "integrity": "sha512-TtYPq24IldU8iKoJQqvZOuhi5CyCQRAbvDOX0x1eW6rsHSxa/1i2CCiptNTotGHJ3VoHRGmqiv6/D3q113ikkw==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "hast-util-parse-selector": "^3.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/core/node_modules/html-void-elements": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/html-void-elements/-/html-void-elements-2.0.1.tgz",
      "integrity": "sha512-0quDb7s97CfemeJAnW9wC0hw78MtW7NU3hqtCD75g2vFlDLt36llsYD7uB7SUzojLMP24N5IatXf7ylGXiGG9A==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/@expressive-code/core/node_modules/parse5": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz",
      "integrity": "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw=="
    },
    "node_modules/@expressive-code/core/node_modules/unist-util-position": {
      "version": "4.0.4",
      "resolved": "https://registry.npmjs.org/unist-util-position/-/unist-util-position-4.0.4.tgz",
      "integrity": "sha512-kUBE91efOWfIVBo8xzh/uZQ7p9ffYRtUbMRZBNFYwf0RK8koUMx6dGUfwylLOKmaT2cs4wSW96QoYUSXAyEtpg==",
      "dependencies": {
        "@types/unist": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/core/node_modules/vfile-location": {
      "version": "4.1.0",
      "resolved": "https://registry.npmjs.org/vfile-location/-/vfile-location-4.1.0.tgz",
      "integrity": "sha512-YF23YMyASIIJXpktBa4vIGLJ5Gs88UB/XePgqPmTa7cDA+JeO3yclbpheQYCHjVHBn/yePzrXuygIL+xbvRYHw==",
      "dependencies": {
        "@types/unist": "^2.0.0",
        "vfile": "^5.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/plugin-frames": {
      "version": "0.33.5",
      "resolved": "https://registry.npmjs.org/@expressive-code/plugin-frames/-/plugin-frames-0.33.5.tgz",
      "integrity": "sha512-lFt/gbnZscBSxHovg4XiWohp5nrxk4McS6RGABdj6+0gJcX8/YrFTM23GKBIkaDePxdDidVY0jQYGYDL/RrQHw==",
      "dependencies": {
        "@expressive-code/core": "^0.33.5",
        "hastscript": "^7.2.0"
      }
    },
    "node_modules/@expressive-code/plugin-frames/node_modules/@types/hast": {
      "version": "2.3.10",
      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-2.3.10.tgz",
      "integrity": "sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==",
      "dependencies": {
        "@types/unist": "^2"
      }
    },
    "node_modules/@expressive-code/plugin-frames/node_modules/hast-util-parse-selector": {
      "version": "3.1.1",
      "resolved": "https://registry.npmjs.org/hast-util-parse-selector/-/hast-util-parse-selector-3.1.1.tgz",
      "integrity": "sha512-jdlwBjEexy1oGz0aJ2f4GKMaVKkA9jwjr4MjAAI22E5fM/TXVZHuS5OpONtdeIkRKqAaryQ2E9xNQxijoThSZA==",
      "dependencies": {
        "@types/hast": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/plugin-frames/node_modules/hastscript": {
      "version": "7.2.0",
      "resolved": "https://registry.npmjs.org/hastscript/-/hastscript-7.2.0.tgz",
      "integrity": "sha512-TtYPq24IldU8iKoJQqvZOuhi5CyCQRAbvDOX0x1eW6rsHSxa/1i2CCiptNTotGHJ3VoHRGmqiv6/D3q113ikkw==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "hast-util-parse-selector": "^3.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/plugin-shiki": {
      "version": "0.33.5",
      "resolved": "https://registry.npmjs.org/@expressive-code/plugin-shiki/-/plugin-shiki-0.33.5.tgz",
      "integrity": "sha512-LWgttQTUrIPE1X+Lya1qFWiX47tH2AS2hkbj/cZoWkdiSjn6zUvtTypK/2Xn6Rgn6z6ClzpgHvkXRqFn7nAB4A==",
      "dependencies": {
        "@expressive-code/core": "^0.33.5",
        "shiki": "^1.1.7"
      }
    },
    "node_modules/@expressive-code/plugin-text-markers": {
      "version": "0.33.5",
      "resolved": "https://registry.npmjs.org/@expressive-code/plugin-text-markers/-/plugin-text-markers-0.33.5.tgz",
      "integrity": "sha512-JxSHL1MGrJAPNaUMjFXex3K+9NJDbfew9H6PmX8LQ+fm9VNQdtBYTAz/x7nqOk7bkTrtAZK5RfDqUfb8U5M+2A==",
      "dependencies": {
        "@expressive-code/core": "^0.33.5",
        "hastscript": "^7.2.0",
        "unist-util-visit-parents": "^5.1.3"
      }
    },
    "node_modules/@expressive-code/plugin-text-markers/node_modules/@types/hast": {
      "version": "2.3.10",
      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-2.3.10.tgz",
      "integrity": "sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==",
      "dependencies": {
        "@types/unist": "^2"
      }
    },
    "node_modules/@expressive-code/plugin-text-markers/node_modules/hast-util-parse-selector": {
      "version": "3.1.1",
      "resolved": "https://registry.npmjs.org/hast-util-parse-selector/-/hast-util-parse-selector-3.1.1.tgz",
      "integrity": "sha512-jdlwBjEexy1oGz0aJ2f4GKMaVKkA9jwjr4MjAAI22E5fM/TXVZHuS5OpONtdeIkRKqAaryQ2E9xNQxijoThSZA==",
      "dependencies": {
        "@types/hast": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@expressive-code/plugin-text-markers/node_modules/hastscript": {
      "version": "7.2.0",
      "resolved": "https://registry.npmjs.org/hastscript/-/hastscript-7.2.0.tgz",
      "integrity": "sha512-TtYPq24IldU8iKoJQqvZOuhi5CyCQRAbvDOX0x1eW6rsHSxa/1i2CCiptNTotGHJ3VoHRGmqiv6/D3q113ikkw==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "hast-util-parse-selector": "^3.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@jridgewell/gen-mapping": {
      "version": "0.3.5",
      "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz",
      "integrity": "sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==",
      "dependencies": {
        "@jridgewell/set-array": "^1.2.1",
        "@jridgewell/sourcemap-codec": "^1.4.10",
        "@jridgewell/trace-mapping": "^0.3.24"
      },
      "engines": {
        "node": ">=6.0.0"
      }
    },
    "node_modules/@jridgewell/resolve-uri": {
      "version": "3.1.2",
      "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz",
      "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==",
      "engines": {
        "node": ">=6.0.0"
      }
    },
    "node_modules/@jridgewell/set-array": {
      "version": "1.2.1",
      "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz",
      "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==",
      "engines": {
        "node": ">=6.0.0"
      }
    },
    "node_modules/@jridgewell/sourcemap-codec": {
      "version": "1.4.15",
      "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz",
      "integrity": "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg=="
    },
    "node_modules/@jridgewell/trace-mapping": {
      "version": "0.3.25",
      "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz",
      "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==",
      "dependencies": {
        "@jridgewell/resolve-uri": "^3.1.0",
        "@jridgewell/sourcemap-codec": "^1.4.14"
      }
    },
    "node_modules/@mdx-js/mdx": {
      "version": "3.0.1",
      "resolved": "https://registry.npmjs.org/@mdx-js/mdx/-/mdx-3.0.1.tgz",
      "integrity": "sha512-eIQ4QTrOWyL3LWEe/bu6Taqzq2HQvHcyTMaOrI95P2/LmJE7AsfPfgJGuFLPVqBUE1BC1rik3VIhU+s9u72arA==",
      "dependencies": {
        "@types/estree": "^1.0.0",
        "@types/estree-jsx": "^1.0.0",
        "@types/hast": "^3.0.0",
        "@types/mdx": "^2.0.0",
        "collapse-white-space": "^2.0.0",
        "devlop": "^1.0.0",
        "estree-util-build-jsx": "^3.0.0",
        "estree-util-is-identifier-name": "^3.0.0",
        "estree-util-to-js": "^2.0.0",
        "estree-walker": "^3.0.0",
        "hast-util-to-estree": "^3.0.0",
        "hast-util-to-jsx-runtime": "^2.0.0",
        "markdown-extensions": "^2.0.0",
        "periscopic": "^3.0.0",
        "remark-mdx": "^3.0.0",
        "remark-parse": "^11.0.0",
        "remark-rehype": "^11.0.0",
        "source-map": "^0.7.0",
        "unified": "^11.0.0",
        "unist-util-position-from-estree": "^2.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "unist-util-visit": "^5.0.0",
        "vfile": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@mdx-js/mdx/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/@mdx-js/mdx/node_modules/unified": {
      "version": "11.0.4",
      "resolved": "https://registry.npmjs.org/unified/-/unified-11.0.4.tgz",
      "integrity": "sha512-apMPnyLjAX+ty4OrNap7yumyVAMlKx5IWU2wlzzUdYJO9A8f1p9m/gywF/GM2ZDFcjQPrx59Mc90KwmxsoklxQ==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "bail": "^2.0.0",
        "devlop": "^1.0.0",
        "extend": "^3.0.0",
        "is-plain-obj": "^4.0.0",
        "trough": "^2.0.0",
        "vfile": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@mdx-js/mdx/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@mdx-js/mdx/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@mdx-js/mdx/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@mdx-js/mdx/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@mdx-js/mdx/node_modules/vfile": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.1.tgz",
      "integrity": "sha512-1bYqc7pt6NIADBJ98UiG0Bn/CHIVOoZ/IyEkqIruLg0mE1BKzkOXY2D6CSqQIcKqgadppE5lrxgWXJmXd7zZJw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@mdx-js/mdx/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/@nodelib/fs.scandir": {
      "version": "2.1.5",
      "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz",
      "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==",
      "dependencies": {
        "@nodelib/fs.stat": "2.0.5",
        "run-parallel": "^1.1.9"
      },
      "engines": {
        "node": ">= 8"
      }
    },
    "node_modules/@nodelib/fs.stat": {
      "version": "2.0.5",
      "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz",
      "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==",
      "engines": {
        "node": ">= 8"
      }
    },
    "node_modules/@nodelib/fs.walk": {
      "version": "1.2.8",
      "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz",
      "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==",
      "dependencies": {
        "@nodelib/fs.scandir": "2.1.5",
        "fastq": "^1.6.0"
      },
      "engines": {
        "node": ">= 8"
      }
    },
    "node_modules/@pagefind/darwin-arm64": {
      "version": "1.0.3",
      "resolved": "https://registry.npmjs.org/@pagefind/darwin-arm64/-/darwin-arm64-1.0.3.tgz",
      "integrity": "sha512-vsHDtvao3W4iFCxVc4S0BVhpj3E2MAoIVM7RmuQfGp1Ng22nGLRaMP6FguLO8TMabRJdvp4SVr227hL4WGKOHA==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "darwin"
      ]
    },
    "node_modules/@pagefind/darwin-x64": {
      "version": "1.0.3",
      "resolved": "https://registry.npmjs.org/@pagefind/darwin-x64/-/darwin-x64-1.0.3.tgz",
      "integrity": "sha512-NhEXHHYmB/hT6lx5rCcmnVTxH+uIkMAd43bzEqMwHQosqTZEIQfwihmV39H+m8yo7jFvz3zRbJNzhAh7G4PiwA==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "darwin"
      ]
    },
    "node_modules/@pagefind/default-ui": {
      "version": "1.0.3",
      "resolved": "https://registry.npmjs.org/@pagefind/default-ui/-/default-ui-1.0.3.tgz",
      "integrity": "sha512-WieFJXvezyvjZh49I8j7a7Kz3LsXYY2Uep3IWvG5NG05mmiurURXjXc+KyrpIp/iAycSnjrC1TDJ8CdES/ee3A=="
    },
    "node_modules/@pagefind/linux-arm64": {
      "version": "1.0.3",
      "resolved": "https://registry.npmjs.org/@pagefind/linux-arm64/-/linux-arm64-1.0.3.tgz",
      "integrity": "sha512-RGsMt4AmGT8WxCSeP09arU7Za6Vf/We4TWHVSbY7vDMuwWql9Ngoib/q1cP9dIAIMdkXh9ePG/S3mGnJYsdzuQ==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@pagefind/linux-x64": {
      "version": "1.0.3",
      "resolved": "https://registry.npmjs.org/@pagefind/linux-x64/-/linux-x64-1.0.3.tgz",
      "integrity": "sha512-o+VCKaqImL42scSH1n5gUfppYSNyu3BuGTvtKKgWHmycbL+A3fkFH+ZOFbaLeN7LVTvJqJIOYbk4j2yaq9784Q==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@pagefind/windows-x64": {
      "version": "1.0.3",
      "resolved": "https://registry.npmjs.org/@pagefind/windows-x64/-/windows-x64-1.0.3.tgz",
      "integrity": "sha512-S+Yq4FyvXJm4F+iN/wRiLvEEF8Xs9lTKGtQGaRHXJslQyl65dytDDPIULXJXIadrDbnMrnTt4C2YHmEUIyUIHg==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "win32"
      ]
    },
    "node_modules/@rollup/rollup-android-arm-eabi": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.16.4.tgz",
      "integrity": "sha512-GkhjAaQ8oUTOKE4g4gsZ0u8K/IHU1+2WQSgS1TwTcYvL+sjbaQjNHFXbOJ6kgqGHIO1DfUhI/Sphi9GkRT9K+Q==",
      "cpu": [
        "arm"
      ],
      "optional": true,
      "os": [
        "android"
      ]
    },
    "node_modules/@rollup/rollup-android-arm64": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.4.tgz",
      "integrity": "sha512-Bvm6D+NPbGMQOcxvS1zUl8H7DWlywSXsphAeOnVeiZLQ+0J6Is8T7SrjGTH29KtYkiY9vld8ZnpV3G2EPbom+w==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "android"
      ]
    },
    "node_modules/@rollup/rollup-darwin-arm64": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.16.4.tgz",
      "integrity": "sha512-i5d64MlnYBO9EkCOGe5vPR/EeDwjnKOGGdd7zKFhU5y8haKhQZTN2DgVtpODDMxUr4t2K90wTUJg7ilgND6bXw==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "darwin"
      ]
    },
    "node_modules/@rollup/rollup-darwin-x64": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.16.4.tgz",
      "integrity": "sha512-WZupV1+CdUYehaZqjaFTClJI72fjJEgTXdf4NbW69I9XyvdmztUExBtcI2yIIU6hJtYvtwS6pkTkHJz+k08mAQ==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "darwin"
      ]
    },
    "node_modules/@rollup/rollup-linux-arm-gnueabihf": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.16.4.tgz",
      "integrity": "sha512-ADm/xt86JUnmAfA9mBqFcRp//RVRt1ohGOYF6yL+IFCYqOBNwy5lbEK05xTsEoJq+/tJzg8ICUtS82WinJRuIw==",
      "cpu": [
        "arm"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@rollup/rollup-linux-arm-musleabihf": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.16.4.tgz",
      "integrity": "sha512-tJfJaXPiFAG+Jn3cutp7mCs1ePltuAgRqdDZrzb1aeE3TktWWJ+g7xK9SNlaSUFw6IU4QgOxAY4rA+wZUT5Wfg==",
      "cpu": [
        "arm"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@rollup/rollup-linux-arm64-gnu": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.16.4.tgz",
      "integrity": "sha512-7dy1BzQkgYlUTapDTvK997cgi0Orh5Iu7JlZVBy1MBURk7/HSbHkzRnXZa19ozy+wwD8/SlpJnOOckuNZtJR9w==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@rollup/rollup-linux-arm64-musl": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.16.4.tgz",
      "integrity": "sha512-zsFwdUw5XLD1gQe0aoU2HVceI6NEW7q7m05wA46eUAyrkeNYExObfRFQcvA6zw8lfRc5BHtan3tBpo+kqEOxmg==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.16.4.tgz",
      "integrity": "sha512-p8C3NnxXooRdNrdv6dBmRTddEapfESEUflpICDNKXpHvTjRRq1J82CbU5G3XfebIZyI3B0s074JHMWD36qOW6w==",
      "cpu": [
        "ppc64"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@rollup/rollup-linux-riscv64-gnu": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.16.4.tgz",
      "integrity": "sha512-Lh/8ckoar4s4Id2foY7jNgitTOUQczwMWNYi+Mjt0eQ9LKhr6sK477REqQkmy8YHY3Ca3A2JJVdXnfb3Rrwkng==",
      "cpu": [
        "riscv64"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@rollup/rollup-linux-s390x-gnu": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.16.4.tgz",
      "integrity": "sha512-1xwwn9ZCQYuqGmulGsTZoKrrn0z2fAur2ujE60QgyDpHmBbXbxLaQiEvzJWDrscRq43c8DnuHx3QorhMTZgisQ==",
      "cpu": [
        "s390x"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@rollup/rollup-linux-x64-gnu": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.16.4.tgz",
      "integrity": "sha512-LuOGGKAJ7dfRtxVnO1i3qWc6N9sh0Em/8aZ3CezixSTM+E9Oq3OvTsvC4sm6wWjzpsIlOCnZjdluINKESflJLA==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@rollup/rollup-linux-x64-musl": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.16.4.tgz",
      "integrity": "sha512-ch86i7KkJKkLybDP2AtySFTRi5fM3KXp0PnHocHuJMdZwu7BuyIKi35BE9guMlmTpwwBTB3ljHj9IQXnTCD0vA==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "linux"
      ]
    },
    "node_modules/@rollup/rollup-win32-arm64-msvc": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.16.4.tgz",
      "integrity": "sha512-Ma4PwyLfOWZWayfEsNQzTDBVW8PZ6TUUN1uFTBQbF2Chv/+sjenE86lpiEwj2FiviSmSZ4Ap4MaAfl1ciF4aSA==",
      "cpu": [
        "arm64"
      ],
      "optional": true,
      "os": [
        "win32"
      ]
    },
    "node_modules/@rollup/rollup-win32-ia32-msvc": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.16.4.tgz",
      "integrity": "sha512-9m/ZDrQsdo/c06uOlP3W9G2ENRVzgzbSXmXHT4hwVaDQhYcRpi9bgBT0FTG9OhESxwK0WjQxYOSfv40cU+T69w==",
      "cpu": [
        "ia32"
      ],
      "optional": true,
      "os": [
        "win32"
      ]
    },
    "node_modules/@rollup/rollup-win32-x64-msvc": {
      "version": "4.16.4",
      "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.16.4.tgz",
      "integrity": "sha512-YunpoOAyGLDseanENHmbFvQSfVL5BxW3k7hhy0eN4rb3gS/ct75dVD0EXOWIqFT/nE8XYW6LP6vz6ctKRi0k9A==",
      "cpu": [
        "x64"
      ],
      "optional": true,
      "os": [
        "win32"
      ]
    },
    "node_modules/@shikijs/core": {
      "version": "1.3.0",
      "resolved": "https://registry.npmjs.org/@shikijs/core/-/core-1.3.0.tgz",
      "integrity": "sha512-7fedsBfuILDTBmrYZNFI8B6ATTxhQAasUHllHmjvSZPnoq4bULWoTpHwmuQvZ8Aq03/tAa2IGo6RXqWtHdWaCA=="
    },
    "node_modules/@types/acorn": {
      "version": "4.0.6",
      "resolved": "https://registry.npmjs.org/@types/acorn/-/acorn-4.0.6.tgz",
      "integrity": "sha512-veQTnWP+1D/xbxVrPC3zHnCZRjSrKfhbMUlEA43iMZLu7EsnTtkJklIuwrCPbOi8YkvDQAiW05VQQFvvz9oieQ==",
      "dependencies": {
        "@types/estree": "*"
      }
    },
    "node_modules/@types/babel__core": {
      "version": "7.20.5",
      "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz",
      "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==",
      "dependencies": {
        "@babel/parser": "^7.20.7",
        "@babel/types": "^7.20.7",
        "@types/babel__generator": "*",
        "@types/babel__template": "*",
        "@types/babel__traverse": "*"
      }
    },
    "node_modules/@types/babel__generator": {
      "version": "7.6.8",
      "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.8.tgz",
      "integrity": "sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==",
      "dependencies": {
        "@babel/types": "^7.0.0"
      }
    },
    "node_modules/@types/babel__template": {
      "version": "7.4.4",
      "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz",
      "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==",
      "dependencies": {
        "@babel/parser": "^7.1.0",
        "@babel/types": "^7.0.0"
      }
    },
    "node_modules/@types/babel__traverse": {
      "version": "7.20.5",
      "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.5.tgz",
      "integrity": "sha512-WXCyOcRtH37HAUkpXhUduaxdm82b4GSlyTqajXviN4EfiuPgNYR109xMCKvpl6zPIpua0DGlMEDCq+g8EdoheQ==",
      "dependencies": {
        "@babel/types": "^7.20.7"
      }
    },
    "node_modules/@types/cookie": {
      "version": "0.6.0",
      "resolved": "https://registry.npmjs.org/@types/cookie/-/cookie-0.6.0.tgz",
      "integrity": "sha512-4Kh9a6B2bQciAhf7FSuMRRkUWecJgJu9nPnx3yzpsfXX/c50REIqpHY4C82bXP90qrLtXtkDxTZosYO3UpOwlA=="
    },
    "node_modules/@types/debug": {
      "version": "4.1.12",
      "resolved": "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz",
      "integrity": "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==",
      "dependencies": {
        "@types/ms": "*"
      }
    },
    "node_modules/@types/estree": {
      "version": "1.0.5",
      "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.5.tgz",
      "integrity": "sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw=="
    },
    "node_modules/@types/estree-jsx": {
      "version": "1.0.5",
      "resolved": "https://registry.npmjs.org/@types/estree-jsx/-/estree-jsx-1.0.5.tgz",
      "integrity": "sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==",
      "dependencies": {
        "@types/estree": "*"
      }
    },
    "node_modules/@types/hast": {
      "version": "3.0.4",
      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-3.0.4.tgz",
      "integrity": "sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==",
      "dependencies": {
        "@types/unist": "*"
      }
    },
    "node_modules/@types/mdast": {
      "version": "4.0.3",
      "resolved": "https://registry.npmjs.org/@types/mdast/-/mdast-4.0.3.tgz",
      "integrity": "sha512-LsjtqsyF+d2/yFOYaN22dHZI1Cpwkrj+g06G8+qtUKlhovPW89YhqSnfKtMbkgmEtYpH2gydRNULd6y8mciAFg==",
      "dependencies": {
        "@types/unist": "*"
      }
    },
    "node_modules/@types/mdx": {
      "version": "2.0.13",
      "resolved": "https://registry.npmjs.org/@types/mdx/-/mdx-2.0.13.tgz",
      "integrity": "sha512-+OWZQfAYyio6YkJb3HLxDrvnx6SWWDbC0zVPfBRzUk0/nqoDyf6dNxQi3eArPe8rJ473nobTMQ/8Zk+LxJ+Yuw=="
    },
    "node_modules/@types/ms": {
      "version": "0.7.34",
      "resolved": "https://registry.npmjs.org/@types/ms/-/ms-0.7.34.tgz",
      "integrity": "sha512-nG96G3Wp6acyAgJqGasjODb+acrI7KltPiRxzHPXnP3NgI28bpQDRv53olbqGXbfcgF5aiiHmO3xpwEpS5Ld9g=="
    },
    "node_modules/@types/nlcst": {
      "version": "1.0.4",
      "resolved": "https://registry.npmjs.org/@types/nlcst/-/nlcst-1.0.4.tgz",
      "integrity": "sha512-ABoYdNQ/kBSsLvZAekMhIPMQ3YUZvavStpKYs7BjLLuKVmIMA0LUgZ7b54zzuWJRbHF80v1cNf4r90Vd6eMQDg==",
      "dependencies": {
        "@types/unist": "^2"
      }
    },
    "node_modules/@types/node": {
      "version": "17.0.45",
      "resolved": "https://registry.npmjs.org/@types/node/-/node-17.0.45.tgz",
      "integrity": "sha512-w+tIMs3rq2afQdsPJlODhoUEKzFP1ayaoyl1CcnwtIlsVe7K7bA1NGm4s3PraqTLlXnbIN84zuBlxBWo1u9BLw=="
    },
    "node_modules/@types/parse5": {
      "version": "6.0.3",
      "resolved": "https://registry.npmjs.org/@types/parse5/-/parse5-6.0.3.tgz",
      "integrity": "sha512-SuT16Q1K51EAVPz1K29DJ/sXjhSQ0zjvsypYJ6tlwVsRV9jwW5Adq2ch8Dq8kDBCkYnELS7N7VNCSB5nC56t/g=="
    },
    "node_modules/@types/sax": {
      "version": "1.2.7",
      "resolved": "https://registry.npmjs.org/@types/sax/-/sax-1.2.7.tgz",
      "integrity": "sha512-rO73L89PJxeYM3s3pPPjiPgVVcymqU490g0YO5n5By0k2Erzj6tay/4lr1CHAAU4JyOWd1rpQ8bCf6cZfHU96A==",
      "dependencies": {
        "@types/node": "*"
      }
    },
    "node_modules/@types/unist": {
      "version": "2.0.8",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-2.0.8.tgz",
      "integrity": "sha512-d0XxK3YTObnWVp6rZuev3c49+j4Lo8g4L1ZRm9z5L0xpoZycUPshHgczK5gsUMaZOstjVYYi09p5gYvUtfChYw=="
    },
    "node_modules/@ungap/structured-clone": {
      "version": "1.2.0",
      "resolved": "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.2.0.tgz",
      "integrity": "sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ=="
    },
    "node_modules/acorn": {
      "version": "8.11.3",
      "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.11.3.tgz",
      "integrity": "sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==",
      "bin": {
        "acorn": "bin/acorn"
      },
      "engines": {
        "node": ">=0.4.0"
      }
    },
    "node_modules/acorn-jsx": {
      "version": "5.3.2",
      "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz",
      "integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==",
      "peerDependencies": {
        "acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"
      }
    },
    "node_modules/algoliasearch": {
      "version": "4.20.0",
      "resolved": "https://registry.npmjs.org/algoliasearch/-/algoliasearch-4.20.0.tgz",
      "integrity": "sha512-y+UHEjnOItoNy0bYO+WWmLWBlPwDjKHW6mNHrPi0NkuhpQOOEbrkwQH/wgKFDLh7qlKjzoKeiRtlpewDPDG23g==",
      "dependencies": {
        "@algolia/cache-browser-local-storage": "4.20.0",
        "@algolia/cache-common": "4.20.0",
        "@algolia/cache-in-memory": "4.20.0",
        "@algolia/client-account": "4.20.0",
        "@algolia/client-analytics": "4.20.0",
        "@algolia/client-common": "4.20.0",
        "@algolia/client-personalization": "4.20.0",
        "@algolia/client-search": "4.20.0",
        "@algolia/logger-common": "4.20.0",
        "@algolia/logger-console": "4.20.0",
        "@algolia/requester-browser-xhr": "4.20.0",
        "@algolia/requester-common": "4.20.0",
        "@algolia/requester-node-http": "4.20.0",
        "@algolia/transporter": "4.20.0"
      }
    },
    "node_modules/ansi-align": {
      "version": "3.0.1",
      "resolved": "https://registry.npmjs.org/ansi-align/-/ansi-align-3.0.1.tgz",
      "integrity": "sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==",
      "dependencies": {
        "string-width": "^4.1.0"
      }
    },
    "node_modules/ansi-align/node_modules/ansi-regex": {
      "version": "5.0.1",
      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
      "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/ansi-align/node_modules/emoji-regex": {
      "version": "8.0.0",
      "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",
      "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
    },
    "node_modules/ansi-align/node_modules/string-width": {
      "version": "4.2.3",
      "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",
      "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
      "dependencies": {
        "emoji-regex": "^8.0.0",
        "is-fullwidth-code-point": "^3.0.0",
        "strip-ansi": "^6.0.1"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/ansi-align/node_modules/strip-ansi": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
      "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
      "dependencies": {
        "ansi-regex": "^5.0.1"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/ansi-regex": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.1.tgz",
      "integrity": "sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==",
      "engines": {
        "node": ">=12"
      },
      "funding": {
        "url": "https://github.com/chalk/ansi-regex?sponsor=1"
      }
    },
    "node_modules/ansi-styles": {
      "version": "3.2.1",
      "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz",
      "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==",
      "dependencies": {
        "color-convert": "^1.9.0"
      },
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/anymatch": {
      "version": "3.1.3",
      "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz",
      "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==",
      "dependencies": {
        "normalize-path": "^3.0.0",
        "picomatch": "^2.0.4"
      },
      "engines": {
        "node": ">= 8"
      }
    },
    "node_modules/arg": {
      "version": "5.0.2",
      "resolved": "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz",
      "integrity": "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg=="
    },
    "node_modules/argparse": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz",
      "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
    },
    "node_modules/aria-query": {
      "version": "5.3.0",
      "resolved": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.0.tgz",
      "integrity": "sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==",
      "dependencies": {
        "dequal": "^2.0.3"
      }
    },
    "node_modules/array-iterate": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/array-iterate/-/array-iterate-2.0.1.tgz",
      "integrity": "sha512-I1jXZMjAgCMmxT4qxXfPXa6SthSoE8h6gkSI9BGGNv8mP8G/v0blc+qFnZu6K42vTOiuME596QaLO0TP3Lk0xg==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/astring": {
      "version": "1.8.6",
      "resolved": "https://registry.npmjs.org/astring/-/astring-1.8.6.tgz",
      "integrity": "sha512-ISvCdHdlTDlH5IpxQJIex7BWBywFWgjJSVdwst+/iQCoEYnyOaQ95+X1JGshuBjGp6nxKUy1jMgE3zPqN7fQdg==",
      "bin": {
        "astring": "bin/astring"
      }
    },
    "node_modules/astro": {
      "version": "4.6.4",
      "resolved": "https://registry.npmjs.org/astro/-/astro-4.6.4.tgz",
      "integrity": "sha512-y2IsR2ASDtu/AeKzLm48XtaqlA+xTUT5vmlaNtEBVEYdSk1qUDxewx/K3VpTjZcKIGL/sZA2CuFlrqFJhM/A5g==",
      "dependencies": {
        "@astrojs/compiler": "^2.7.1",
        "@astrojs/internal-helpers": "0.4.0",
        "@astrojs/markdown-remark": "5.1.0",
        "@astrojs/telemetry": "3.1.0",
        "@babel/core": "^7.24.4",
        "@babel/generator": "^7.24.4",
        "@babel/parser": "^7.24.4",
        "@babel/plugin-transform-react-jsx": "^7.23.4",
        "@babel/traverse": "^7.24.1",
        "@babel/types": "^7.24.0",
        "@types/babel__core": "^7.20.5",
        "@types/cookie": "^0.6.0",
        "acorn": "^8.11.3",
        "aria-query": "^5.3.0",
        "axobject-query": "^4.0.0",
        "boxen": "^7.1.1",
        "chokidar": "^3.6.0",
        "ci-info": "^4.0.0",
        "clsx": "^2.1.0",
        "common-ancestor-path": "^1.0.1",
        "cookie": "^0.6.0",
        "cssesc": "^3.0.0",
        "debug": "^4.3.4",
        "deterministic-object-hash": "^2.0.2",
        "devalue": "^5.0.0",
        "diff": "^5.2.0",
        "dlv": "^1.1.3",
        "dset": "^3.1.3",
        "es-module-lexer": "^1.5.0",
        "esbuild": "^0.20.2",
        "estree-walker": "^3.0.3",
        "execa": "^8.0.1",
        "fast-glob": "^3.3.2",
        "flattie": "^1.1.1",
        "github-slugger": "^2.0.0",
        "gray-matter": "^4.0.3",
        "html-escaper": "^3.0.3",
        "http-cache-semantics": "^4.1.1",
        "js-yaml": "^4.1.0",
        "kleur": "^4.1.5",
        "magic-string": "^0.30.10",
        "mrmime": "^2.0.0",
        "ora": "^8.0.1",
        "p-limit": "^5.0.0",
        "p-queue": "^8.0.1",
        "path-to-regexp": "^6.2.2",
        "preferred-pm": "^3.1.3",
        "prompts": "^2.4.2",
        "rehype": "^13.0.1",
        "resolve": "^1.22.8",
        "semver": "^7.6.0",
        "shiki": "^1.3.0",
        "string-width": "^7.1.0",
        "strip-ansi": "^7.1.0",
        "tsconfck": "^3.0.3",
        "unist-util-visit": "^5.0.0",
        "vfile": "^6.0.1",
        "vite": "^5.2.10",
        "vitefu": "^0.2.5",
        "which-pm": "^2.1.1",
        "yargs-parser": "^21.1.1",
        "zod": "^3.23.0",
        "zod-to-json-schema": "^3.22.5"
      },
      "bin": {
        "astro": "astro.js"
      },
      "engines": {
        "node": "^18.17.1 || ^20.3.0 || >=21.0.0",
        "npm": ">=9.6.5",
        "pnpm": ">=7.1.0"
      },
      "optionalDependencies": {
        "sharp": "^0.32.6"
      }
    },
    "node_modules/astro-expressive-code": {
      "version": "0.33.5",
      "resolved": "https://registry.npmjs.org/astro-expressive-code/-/astro-expressive-code-0.33.5.tgz",
      "integrity": "sha512-9JAyllueMUN8JTl/h/yTdbKinNmfalEWcV11s3lSf/UJQbAZfWJuy+IlGcArZDI/CmD21GXhFHLqYthpdY33ug==",
      "dependencies": {
        "hast-util-to-html": "^8.0.4",
        "remark-expressive-code": "^0.33.5"
      },
      "peerDependencies": {
        "astro": "^4.0.0-beta || ^3.3.0"
      }
    },
    "node_modules/astro-expressive-code/node_modules/@types/hast": {
      "version": "2.3.10",
      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-2.3.10.tgz",
      "integrity": "sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==",
      "dependencies": {
        "@types/unist": "^2"
      }
    },
    "node_modules/astro-expressive-code/node_modules/hast-util-from-parse5": {
      "version": "7.1.2",
      "resolved": "https://registry.npmjs.org/hast-util-from-parse5/-/hast-util-from-parse5-7.1.2.tgz",
      "integrity": "sha512-Nz7FfPBuljzsN3tCQ4kCBKqdNhQE2l0Tn+X1ubgKBPRoiDIu1mL08Cfw4k7q71+Duyaw7DXDN+VTAp4Vh3oCOw==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "@types/unist": "^2.0.0",
        "hastscript": "^7.0.0",
        "property-information": "^6.0.0",
        "vfile": "^5.0.0",
        "vfile-location": "^4.0.0",
        "web-namespaces": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro-expressive-code/node_modules/hast-util-parse-selector": {
      "version": "3.1.1",
      "resolved": "https://registry.npmjs.org/hast-util-parse-selector/-/hast-util-parse-selector-3.1.1.tgz",
      "integrity": "sha512-jdlwBjEexy1oGz0aJ2f4GKMaVKkA9jwjr4MjAAI22E5fM/TXVZHuS5OpONtdeIkRKqAaryQ2E9xNQxijoThSZA==",
      "dependencies": {
        "@types/hast": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro-expressive-code/node_modules/hast-util-raw": {
      "version": "7.2.3",
      "resolved": "https://registry.npmjs.org/hast-util-raw/-/hast-util-raw-7.2.3.tgz",
      "integrity": "sha512-RujVQfVsOrxzPOPSzZFiwofMArbQke6DJjnFfceiEbFh7S05CbPt0cYN+A5YeD3pso0JQk6O1aHBnx9+Pm2uqg==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "@types/parse5": "^6.0.0",
        "hast-util-from-parse5": "^7.0.0",
        "hast-util-to-parse5": "^7.0.0",
        "html-void-elements": "^2.0.0",
        "parse5": "^6.0.0",
        "unist-util-position": "^4.0.0",
        "unist-util-visit": "^4.0.0",
        "vfile": "^5.0.0",
        "web-namespaces": "^2.0.0",
        "zwitch": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro-expressive-code/node_modules/hast-util-to-html": {
      "version": "8.0.4",
      "resolved": "https://registry.npmjs.org/hast-util-to-html/-/hast-util-to-html-8.0.4.tgz",
      "integrity": "sha512-4tpQTUOr9BMjtYyNlt0P50mH7xj0Ks2xpo8M943Vykljf99HW6EzulIoJP1N3eKOSScEHzyzi9dm7/cn0RfGwA==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "@types/unist": "^2.0.0",
        "ccount": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "hast-util-raw": "^7.0.0",
        "hast-util-whitespace": "^2.0.0",
        "html-void-elements": "^2.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0",
        "stringify-entities": "^4.0.0",
        "zwitch": "^2.0.4"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro-expressive-code/node_modules/hast-util-to-parse5": {
      "version": "7.1.0",
      "resolved": "https://registry.npmjs.org/hast-util-to-parse5/-/hast-util-to-parse5-7.1.0.tgz",
      "integrity": "sha512-YNRgAJkH2Jky5ySkIqFXTQiaqcAtJyVE+D5lkN6CdtOqrnkLfGYYrEcKuHOJZlp+MwjSwuD3fZuawI+sic/RBw==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0",
        "web-namespaces": "^2.0.0",
        "zwitch": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro-expressive-code/node_modules/hast-util-whitespace": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-2.0.1.tgz",
      "integrity": "sha512-nAxA0v8+vXSBDt3AnRUNjyRIQ0rD+ntpbAp4LnPkumc5M9yUbSMa4XDU9Q6etY4f1Wp4bNgvc1yjiZtsTTrSng==",
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro-expressive-code/node_modules/hastscript": {
      "version": "7.2.0",
      "resolved": "https://registry.npmjs.org/hastscript/-/hastscript-7.2.0.tgz",
      "integrity": "sha512-TtYPq24IldU8iKoJQqvZOuhi5CyCQRAbvDOX0x1eW6rsHSxa/1i2CCiptNTotGHJ3VoHRGmqiv6/D3q113ikkw==",
      "dependencies": {
        "@types/hast": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "hast-util-parse-selector": "^3.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro-expressive-code/node_modules/html-void-elements": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/html-void-elements/-/html-void-elements-2.0.1.tgz",
      "integrity": "sha512-0quDb7s97CfemeJAnW9wC0hw78MtW7NU3hqtCD75g2vFlDLt36llsYD7uB7SUzojLMP24N5IatXf7ylGXiGG9A==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/astro-expressive-code/node_modules/parse5": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz",
      "integrity": "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw=="
    },
    "node_modules/astro-expressive-code/node_modules/unist-util-position": {
      "version": "4.0.4",
      "resolved": "https://registry.npmjs.org/unist-util-position/-/unist-util-position-4.0.4.tgz",
      "integrity": "sha512-kUBE91efOWfIVBo8xzh/uZQ7p9ffYRtUbMRZBNFYwf0RK8koUMx6dGUfwylLOKmaT2cs4wSW96QoYUSXAyEtpg==",
      "dependencies": {
        "@types/unist": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro-expressive-code/node_modules/vfile-location": {
      "version": "4.1.0",
      "resolved": "https://registry.npmjs.org/vfile-location/-/vfile-location-4.1.0.tgz",
      "integrity": "sha512-YF23YMyASIIJXpktBa4vIGLJ5Gs88UB/XePgqPmTa7cDA+JeO3yclbpheQYCHjVHBn/yePzrXuygIL+xbvRYHw==",
      "dependencies": {
        "@types/unist": "^2.0.0",
        "vfile": "^5.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro/node_modules/@types/node": {
      "version": "20.12.7",
      "resolved": "https://registry.npmjs.org/@types/node/-/node-20.12.7.tgz",
      "integrity": "sha512-wq0cICSkRLVaf3UGLMGItu/PtdY7oaXaI/RVU+xliKVOtRna3PRY57ZDfztpDL0n11vfymMUnXv8QwYCO7L1wg==",
      "optional": true,
      "peer": true,
      "dependencies": {
        "undici-types": "~5.26.4"
      }
    },
    "node_modules/astro/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/astro/node_modules/lru-cache": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz",
      "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==",
      "dependencies": {
        "yallist": "^4.0.0"
      },
      "engines": {
        "node": ">=10"
      }
    },
    "node_modules/astro/node_modules/semver": {
      "version": "7.6.0",
      "resolved": "https://registry.npmjs.org/semver/-/semver-7.6.0.tgz",
      "integrity": "sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg==",
      "dependencies": {
        "lru-cache": "^6.0.0"
      },
      "bin": {
        "semver": "bin/semver.js"
      },
      "engines": {
        "node": ">=10"
      }
    },
    "node_modules/astro/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro/node_modules/vfile": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.1.tgz",
      "integrity": "sha512-1bYqc7pt6NIADBJ98UiG0Bn/CHIVOoZ/IyEkqIruLg0mE1BKzkOXY2D6CSqQIcKqgadppE5lrxgWXJmXd7zZJw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/astro/node_modules/vite": {
      "version": "5.2.10",
      "resolved": "https://registry.npmjs.org/vite/-/vite-5.2.10.tgz",
      "integrity": "sha512-PAzgUZbP7msvQvqdSD+ErD5qGnSFiGOoWmV5yAKUEI0kdhjbH6nMWVyZQC/hSc4aXwc0oJ9aEdIiF9Oje0JFCw==",
      "dependencies": {
        "esbuild": "^0.20.1",
        "postcss": "^8.4.38",
        "rollup": "^4.13.0"
      },
      "bin": {
        "vite": "bin/vite.js"
      },
      "engines": {
        "node": "^18.0.0 || >=20.0.0"
      },
      "funding": {
        "url": "https://github.com/vitejs/vite?sponsor=1"
      },
      "optionalDependencies": {
        "fsevents": "~2.3.3"
      },
      "peerDependencies": {
        "@types/node": "^18.0.0 || >=20.0.0",
        "less": "*",
        "lightningcss": "^1.21.0",
        "sass": "*",
        "stylus": "*",
        "sugarss": "*",
        "terser": "^5.4.0"
      },
      "peerDependenciesMeta": {
        "@types/node": {
          "optional": true
        },
        "less": {
          "optional": true
        },
        "lightningcss": {
          "optional": true
        },
        "sass": {
          "optional": true
        },
        "stylus": {
          "optional": true
        },
        "sugarss": {
          "optional": true
        },
        "terser": {
          "optional": true
        }
      }
    },
    "node_modules/astro/node_modules/vitefu": {
      "version": "0.2.5",
      "resolved": "https://registry.npmjs.org/vitefu/-/vitefu-0.2.5.tgz",
      "integrity": "sha512-SgHtMLoqaeeGnd2evZ849ZbACbnwQCIwRH57t18FxcXoZop0uQu0uzlIhJBlF/eWVzuce0sHeqPcDo+evVcg8Q==",
      "peerDependencies": {
        "vite": "^3.0.0 || ^4.0.0 || ^5.0.0"
      },
      "peerDependenciesMeta": {
        "vite": {
          "optional": true
        }
      }
    },
    "node_modules/astro/node_modules/yallist": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz",
      "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
    },
    "node_modules/astro/node_modules/zod-to-json-schema": {
      "version": "3.23.0",
      "resolved": "https://registry.npmjs.org/zod-to-json-schema/-/zod-to-json-schema-3.23.0.tgz",
      "integrity": "sha512-az0uJ243PxsRIa2x1WmNE/pnuA05gUq/JB8Lwe1EDCCL/Fz9MgjYQ0fPlyc2Tcv6aF2ZA7WM5TWaRZVEFaAIag==",
      "peerDependencies": {
        "zod": "^3.23.3"
      }
    },
    "node_modules/axobject-query": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/axobject-query/-/axobject-query-4.0.0.tgz",
      "integrity": "sha512-+60uv1hiVFhHZeO+Lz0RYzsVHy5Wr1ayX0mwda9KPDVLNJgZ1T9Ny7VmFbLDzxsH0D87I86vgj3gFrjTJUYznw==",
      "dependencies": {
        "dequal": "^2.0.3"
      }
    },
    "node_modules/b4a": {
      "version": "1.6.4",
      "resolved": "https://registry.npmjs.org/b4a/-/b4a-1.6.4.tgz",
      "integrity": "sha512-fpWrvyVHEKyeEvbKZTVOeZF3VSKKWtJxFIxX/jaVPf+cLbGUSitjb49pHLqPV2BUNNZ0LcoeEGfE/YCpyDYHIw=="
    },
    "node_modules/bail": {
      "version": "2.0.2",
      "resolved": "https://registry.npmjs.org/bail/-/bail-2.0.2.tgz",
      "integrity": "sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/base-64": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/base-64/-/base-64-1.0.0.tgz",
      "integrity": "sha512-kwDPIFCGx0NZHog36dj+tHiwP4QMzsZ3AgMViUBKI0+V5n4U0ufTCUMhnQ04diaRI8EX/QcPfql7zlhZ7j4zgg=="
    },
    "node_modules/base64-js": {
      "version": "1.5.1",
      "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz",
      "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==",
      "funding": [
        {
          "type": "github",
          "url": "https://github.com/sponsors/feross"
        },
        {
          "type": "patreon",
          "url": "https://www.patreon.com/feross"
        },
        {
          "type": "consulting",
          "url": "https://feross.org/support"
        }
      ]
    },
    "node_modules/bcp-47": {
      "version": "2.1.0",
      "resolved": "https://registry.npmjs.org/bcp-47/-/bcp-47-2.1.0.tgz",
      "integrity": "sha512-9IIS3UPrvIa1Ej+lVDdDwO7zLehjqsaByECw0bu2RRGP73jALm6FYbzI5gWbgHLvNdkvfXB5YrSbocZdOS0c0w==",
      "dependencies": {
        "is-alphabetical": "^2.0.0",
        "is-alphanumerical": "^2.0.0",
        "is-decimal": "^2.0.0"
      },
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/bcp-47-match": {
      "version": "2.0.3",
      "resolved": "https://registry.npmjs.org/bcp-47-match/-/bcp-47-match-2.0.3.tgz",
      "integrity": "sha512-JtTezzbAibu8G0R9op9zb3vcWZd9JF6M0xOYGPn0fNCd7wOpRB1mU2mH9T8gaBGbAAyIIVgB2G7xG0GP98zMAQ==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/binary-extensions": {
      "version": "2.3.0",
      "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz",
      "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==",
      "engines": {
        "node": ">=8"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/boolbase": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz",
      "integrity": "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww=="
    },
    "node_modules/boxen": {
      "version": "7.1.1",
      "resolved": "https://registry.npmjs.org/boxen/-/boxen-7.1.1.tgz",
      "integrity": "sha512-2hCgjEmP8YLWQ130n2FerGv7rYpfBmnmp9Uy2Le1vge6X3gZIfSmEzP5QTDElFxcvVcXlEn8Aq6MU/PZygIOog==",
      "dependencies": {
        "ansi-align": "^3.0.1",
        "camelcase": "^7.0.1",
        "chalk": "^5.2.0",
        "cli-boxes": "^3.0.0",
        "string-width": "^5.1.2",
        "type-fest": "^2.13.0",
        "widest-line": "^4.0.1",
        "wrap-ansi": "^8.1.0"
      },
      "engines": {
        "node": ">=14.16"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/boxen/node_modules/chalk": {
      "version": "5.3.0",
      "resolved": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz",
      "integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==",
      "engines": {
        "node": "^12.17.0 || ^14.13 || >=16.0.0"
      },
      "funding": {
        "url": "https://github.com/chalk/chalk?sponsor=1"
      }
    },
    "node_modules/boxen/node_modules/emoji-regex": {
      "version": "9.2.2",
      "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz",
      "integrity": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg=="
    },
    "node_modules/boxen/node_modules/string-width": {
      "version": "5.1.2",
      "resolved": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz",
      "integrity": "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==",
      "dependencies": {
        "eastasianwidth": "^0.2.0",
        "emoji-regex": "^9.2.2",
        "strip-ansi": "^7.0.1"
      },
      "engines": {
        "node": ">=12"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/braces": {
      "version": "3.0.3",
      "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz",
      "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==",
      "dependencies": {
        "fill-range": "^7.1.1"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/browserslist": {
      "version": "4.23.0",
      "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.23.0.tgz",
      "integrity": "sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ==",
      "funding": [
        {
          "type": "opencollective",
          "url": "https://opencollective.com/browserslist"
        },
        {
          "type": "tidelift",
          "url": "https://tidelift.com/funding/github/npm/browserslist"
        },
        {
          "type": "github",
          "url": "https://github.com/sponsors/ai"
        }
      ],
      "dependencies": {
        "caniuse-lite": "^1.0.30001587",
        "electron-to-chromium": "^1.4.668",
        "node-releases": "^2.0.14",
        "update-browserslist-db": "^1.0.13"
      },
      "bin": {
        "browserslist": "cli.js"
      },
      "engines": {
        "node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"
      }
    },
    "node_modules/camelcase": {
      "version": "7.0.1",
      "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-7.0.1.tgz",
      "integrity": "sha512-xlx1yCK2Oc1APsPXDL2LdlNP6+uu8OCDdhOBSVT279M/S+y75O30C2VuD8T2ogdePBBl7PfPF4504tnLgX3zfw==",
      "engines": {
        "node": ">=14.16"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/caniuse-lite": {
      "version": "1.0.30001612",
      "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001612.tgz",
      "integrity": "sha512-lFgnZ07UhaCcsSZgWW0K5j4e69dK1u/ltrL9lTUiFOwNHs12S3UMIEYgBV0Z6C6hRDev7iRnMzzYmKabYdXF9g==",
      "funding": [
        {
          "type": "opencollective",
          "url": "https://opencollective.com/browserslist"
        },
        {
          "type": "tidelift",
          "url": "https://tidelift.com/funding/github/npm/caniuse-lite"
        },
        {
          "type": "github",
          "url": "https://github.com/sponsors/ai"
        }
      ]
    },
    "node_modules/ccount": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/ccount/-/ccount-2.0.1.tgz",
      "integrity": "sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/chalk": {
      "version": "2.4.2",
      "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz",
      "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==",
      "dependencies": {
        "ansi-styles": "^3.2.1",
        "escape-string-regexp": "^1.0.5",
        "supports-color": "^5.3.0"
      },
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/character-entities": {
      "version": "2.0.2",
      "resolved": "https://registry.npmjs.org/character-entities/-/character-entities-2.0.2.tgz",
      "integrity": "sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/character-entities-html4": {
      "version": "2.1.0",
      "resolved": "https://registry.npmjs.org/character-entities-html4/-/character-entities-html4-2.1.0.tgz",
      "integrity": "sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/character-entities-legacy": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz",
      "integrity": "sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/character-reference-invalid": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz",
      "integrity": "sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/chokidar": {
      "version": "3.6.0",
      "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz",
      "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==",
      "dependencies": {
        "anymatch": "~3.1.2",
        "braces": "~3.0.2",
        "glob-parent": "~5.1.2",
        "is-binary-path": "~2.1.0",
        "is-glob": "~4.0.1",
        "normalize-path": "~3.0.0",
        "readdirp": "~3.6.0"
      },
      "engines": {
        "node": ">= 8.10.0"
      },
      "funding": {
        "url": "https://paulmillr.com/funding/"
      },
      "optionalDependencies": {
        "fsevents": "~2.3.2"
      }
    },
    "node_modules/chownr": {
      "version": "1.1.4",
      "resolved": "https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz",
      "integrity": "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg=="
    },
    "node_modules/ci-info": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/ci-info/-/ci-info-4.0.0.tgz",
      "integrity": "sha512-TdHqgGf9odd8SXNuxtUBVx8Nv+qZOejE6qyqiy5NtbYYQOeFa6zmHkxlPzmaLxWWHsU6nJmB7AETdVPi+2NBUg==",
      "funding": [
        {
          "type": "github",
          "url": "https://github.com/sponsors/sibiraj-s"
        }
      ],
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/cli-boxes": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/cli-boxes/-/cli-boxes-3.0.0.tgz",
      "integrity": "sha512-/lzGpEWL/8PfI0BmBOPRwp0c/wFNX1RdUML3jK/RcSBA9T8mZDdQpqYBKtCFTOfQbwPqWEOpjqW+Fnayc0969g==",
      "engines": {
        "node": ">=10"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/cli-cursor": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-4.0.0.tgz",
      "integrity": "sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==",
      "dependencies": {
        "restore-cursor": "^4.0.0"
      },
      "engines": {
        "node": "^12.20.0 || ^14.13.1 || >=16.0.0"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/cli-spinners": {
      "version": "2.9.2",
      "resolved": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz",
      "integrity": "sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==",
      "engines": {
        "node": ">=6"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/clsx": {
      "version": "2.1.1",
      "resolved": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz",
      "integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==",
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/collapse-white-space": {
      "version": "2.1.0",
      "resolved": "https://registry.npmjs.org/collapse-white-space/-/collapse-white-space-2.1.0.tgz",
      "integrity": "sha512-loKTxY1zCOuG4j9f6EPnuyyYkf58RnhhWTvRoZEokgB+WbdXehfjFviyOVYkqzEWz1Q5kRiZdBYS5SwxbQYwzw==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/color": {
      "version": "4.2.3",
      "resolved": "https://registry.npmjs.org/color/-/color-4.2.3.tgz",
      "integrity": "sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==",
      "dependencies": {
        "color-convert": "^2.0.1",
        "color-string": "^1.9.0"
      },
      "engines": {
        "node": ">=12.5.0"
      }
    },
    "node_modules/color-convert": {
      "version": "1.9.3",
      "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz",
      "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==",
      "dependencies": {
        "color-name": "1.1.3"
      }
    },
    "node_modules/color-name": {
      "version": "1.1.3",
      "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz",
      "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
    },
    "node_modules/color-string": {
      "version": "1.9.1",
      "resolved": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz",
      "integrity": "sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==",
      "dependencies": {
        "color-name": "^1.0.0",
        "simple-swizzle": "^0.2.2"
      }
    },
    "node_modules/color/node_modules/color-convert": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz",
      "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==",
      "dependencies": {
        "color-name": "~1.1.4"
      },
      "engines": {
        "node": ">=7.0.0"
      }
    },
    "node_modules/color/node_modules/color-name": {
      "version": "1.1.4",
      "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz",
      "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
    },
    "node_modules/comma-separated-tokens": {
      "version": "2.0.3",
      "resolved": "https://registry.npmjs.org/comma-separated-tokens/-/comma-separated-tokens-2.0.3.tgz",
      "integrity": "sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/common-ancestor-path": {
      "version": "1.0.1",
      "resolved": "https://registry.npmjs.org/common-ancestor-path/-/common-ancestor-path-1.0.1.tgz",
      "integrity": "sha512-L3sHRo1pXXEqX8VU28kfgUY+YGsk09hPqZiZmLacNib6XNTCM8ubYeT7ryXQw8asB1sKgcU5lkB7ONug08aB8w=="
    },
    "node_modules/convert-source-map": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz",
      "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg=="
    },
    "node_modules/cookie": {
      "version": "0.6.0",
      "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.6.0.tgz",
      "integrity": "sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==",
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/cross-spawn": {
      "version": "7.0.3",
      "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz",
      "integrity": "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==",
      "dependencies": {
        "path-key": "^3.1.0",
        "shebang-command": "^2.0.0",
        "which": "^2.0.1"
      },
      "engines": {
        "node": ">= 8"
      }
    },
    "node_modules/css-selector-parser": {
      "version": "3.0.5",
      "resolved": "https://registry.npmjs.org/css-selector-parser/-/css-selector-parser-3.0.5.tgz",
      "integrity": "sha512-3itoDFbKUNx1eKmVpYMFyqKX04Ww9osZ+dLgrk6GEv6KMVeXUhUnp4I5X+evw+u3ZxVU6RFXSSRxlTeMh8bA+g==",
      "funding": [
        {
          "type": "github",
          "url": "https://github.com/sponsors/mdevils"
        },
        {
          "type": "patreon",
          "url": "https://patreon.com/mdevils"
        }
      ]
    },
    "node_modules/cssesc": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz",
      "integrity": "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==",
      "bin": {
        "cssesc": "bin/cssesc"
      },
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/debug": {
      "version": "4.3.4",
      "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz",
      "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==",
      "dependencies": {
        "ms": "2.1.2"
      },
      "engines": {
        "node": ">=6.0"
      },
      "peerDependenciesMeta": {
        "supports-color": {
          "optional": true
        }
      }
    },
    "node_modules/decode-named-character-reference": {
      "version": "1.0.2",
      "resolved": "https://registry.npmjs.org/decode-named-character-reference/-/decode-named-character-reference-1.0.2.tgz",
      "integrity": "sha512-O8x12RzrUF8xyVcY0KJowWsmaJxQbmy0/EtnNtHRpsOcT7dFk5W598coHqBVpmWo1oQQfsCqfCmkZN5DJrZVdg==",
      "dependencies": {
        "character-entities": "^2.0.0"
      },
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/decompress-response": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz",
      "integrity": "sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==",
      "dependencies": {
        "mimic-response": "^3.1.0"
      },
      "engines": {
        "node": ">=10"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/deep-extend": {
      "version": "0.6.0",
      "resolved": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz",
      "integrity": "sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==",
      "engines": {
        "node": ">=4.0.0"
      }
    },
    "node_modules/dequal": {
      "version": "2.0.3",
      "resolved": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz",
      "integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==",
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/detect-libc": {
      "version": "2.0.2",
      "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.2.tgz",
      "integrity": "sha512-UX6sGumvvqSaXgdKGUsgZWqcUyIXZ/vZTrlRT/iobiKhGL0zL4d3osHj3uqllWJK+i+sixDS/3COVEOFbupFyw==",
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/deterministic-object-hash": {
      "version": "2.0.2",
      "resolved": "https://registry.npmjs.org/deterministic-object-hash/-/deterministic-object-hash-2.0.2.tgz",
      "integrity": "sha512-KxektNH63SrbfUyDiwXqRb1rLwKt33AmMv+5Nhsw1kqZ13SJBRTgZHtGbE+hH3a1mVW1cz+4pqSWVPAtLVXTzQ==",
      "dependencies": {
        "base-64": "^1.0.0"
      },
      "engines": {
        "node": ">=18"
      }
    },
    "node_modules/devalue": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/devalue/-/devalue-5.0.0.tgz",
      "integrity": "sha512-gO+/OMXF7488D+u3ue+G7Y4AA3ZmUnB3eHJXmBTgNHvr4ZNzl36A0ZtG+XCRNYCkYx/bFmw4qtkoFLa+wSrwAA=="
    },
    "node_modules/devlop": {
      "version": "1.1.0",
      "resolved": "https://registry.npmjs.org/devlop/-/devlop-1.1.0.tgz",
      "integrity": "sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==",
      "dependencies": {
        "dequal": "^2.0.0"
      },
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/diff": {
      "version": "5.2.0",
      "resolved": "https://registry.npmjs.org/diff/-/diff-5.2.0.tgz",
      "integrity": "sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==",
      "engines": {
        "node": ">=0.3.1"
      }
    },
    "node_modules/direction": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/direction/-/direction-2.0.1.tgz",
      "integrity": "sha512-9S6m9Sukh1cZNknO1CWAr2QAWsbKLafQiyM5gZ7VgXHeuaoUwffKN4q6NC4A/Mf9iiPlOXQEKW/Mv/mh9/3YFA==",
      "bin": {
        "direction": "cli.js"
      },
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/dlv": {
      "version": "1.1.3",
      "resolved": "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz",
      "integrity": "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA=="
    },
    "node_modules/dset": {
      "version": "3.1.3",
      "resolved": "https://registry.npmjs.org/dset/-/dset-3.1.3.tgz",
      "integrity": "sha512-20TuZZHCEZ2O71q9/+8BwKwZ0QtD9D8ObhrihJPr+vLLYlSuAU3/zL4cSlgbfeoGHTjCSJBa7NGcrF9/Bx/WJQ==",
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/eastasianwidth": {
      "version": "0.2.0",
      "resolved": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz",
      "integrity": "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA=="
    },
    "node_modules/electron-to-chromium": {
      "version": "1.4.747",
      "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.747.tgz",
      "integrity": "sha512-+FnSWZIAvFHbsNVmUxhEqWiaOiPMcfum1GQzlWCg/wLigVtshOsjXHyEFfmt6cFK6+HkS3QOJBv6/3OPumbBfw=="
    },
    "node_modules/emoji-regex": {
      "version": "10.3.0",
      "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.3.0.tgz",
      "integrity": "sha512-QpLs9D9v9kArv4lfDEgg1X/gN5XLnf/A6l9cs8SPZLRZR3ZkY9+kwIQTxm+fsSej5UMYGE8fdoaZVIBlqG0XTw=="
    },
    "node_modules/end-of-stream": {
      "version": "1.4.4",
      "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz",
      "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==",
      "dependencies": {
        "once": "^1.4.0"
      }
    },
    "node_modules/entities": {
      "version": "4.5.0",
      "resolved": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz",
      "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==",
      "engines": {
        "node": ">=0.12"
      },
      "funding": {
        "url": "https://github.com/fb55/entities?sponsor=1"
      }
    },
    "node_modules/es-module-lexer": {
      "version": "1.5.0",
      "resolved": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.5.0.tgz",
      "integrity": "sha512-pqrTKmwEIgafsYZAGw9kszYzmagcE/n4dbgwGWLEXg7J4QFJVQRBld8j3Q3GNez79jzxZshq0bcT962QHOghjw=="
    },
    "node_modules/esbuild": {
      "version": "0.20.2",
      "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.20.2.tgz",
      "integrity": "sha512-WdOOppmUNU+IbZ0PaDiTst80zjnrOkyJNHoKupIcVyU8Lvla3Ugx94VzkQ32Ijqd7UhHJy75gNWDMUekcrSJ6g==",
      "hasInstallScript": true,
      "bin": {
        "esbuild": "bin/esbuild"
      },
      "engines": {
        "node": ">=12"
      },
      "optionalDependencies": {
        "@esbuild/aix-ppc64": "0.20.2",
        "@esbuild/android-arm": "0.20.2",
        "@esbuild/android-arm64": "0.20.2",
        "@esbuild/android-x64": "0.20.2",
        "@esbuild/darwin-arm64": "0.20.2",
        "@esbuild/darwin-x64": "0.20.2",
        "@esbuild/freebsd-arm64": "0.20.2",
        "@esbuild/freebsd-x64": "0.20.2",
        "@esbuild/linux-arm": "0.20.2",
        "@esbuild/linux-arm64": "0.20.2",
        "@esbuild/linux-ia32": "0.20.2",
        "@esbuild/linux-loong64": "0.20.2",
        "@esbuild/linux-mips64el": "0.20.2",
        "@esbuild/linux-ppc64": "0.20.2",
        "@esbuild/linux-riscv64": "0.20.2",
        "@esbuild/linux-s390x": "0.20.2",
        "@esbuild/linux-x64": "0.20.2",
        "@esbuild/netbsd-x64": "0.20.2",
        "@esbuild/openbsd-x64": "0.20.2",
        "@esbuild/sunos-x64": "0.20.2",
        "@esbuild/win32-arm64": "0.20.2",
        "@esbuild/win32-ia32": "0.20.2",
        "@esbuild/win32-x64": "0.20.2"
      }
    },
    "node_modules/escalade": {
      "version": "3.1.2",
      "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.1.2.tgz",
      "integrity": "sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==",
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/escape-string-regexp": {
      "version": "1.0.5",
      "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz",
      "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==",
      "engines": {
        "node": ">=0.8.0"
      }
    },
    "node_modules/esprima": {
      "version": "4.0.1",
      "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz",
      "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==",
      "bin": {
        "esparse": "bin/esparse.js",
        "esvalidate": "bin/esvalidate.js"
      },
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/estree-util-attach-comments": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/estree-util-attach-comments/-/estree-util-attach-comments-3.0.0.tgz",
      "integrity": "sha512-cKUwm/HUcTDsYh/9FgnuFqpfquUbwIqwKM26BVCGDPVgvaCl/nDCCjUfiLlx6lsEZ3Z4RFxNbOQ60pkaEwFxGw==",
      "dependencies": {
        "@types/estree": "^1.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/estree-util-build-jsx": {
      "version": "3.0.1",
      "resolved": "https://registry.npmjs.org/estree-util-build-jsx/-/estree-util-build-jsx-3.0.1.tgz",
      "integrity": "sha512-8U5eiL6BTrPxp/CHbs2yMgP8ftMhR5ww1eIKoWRMlqvltHF8fZn5LRDvTKuxD3DUn+shRbLGqXemcP51oFCsGQ==",
      "dependencies": {
        "@types/estree-jsx": "^1.0.0",
        "devlop": "^1.0.0",
        "estree-util-is-identifier-name": "^3.0.0",
        "estree-walker": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/estree-util-is-identifier-name": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz",
      "integrity": "sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==",
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/estree-util-to-js": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/estree-util-to-js/-/estree-util-to-js-2.0.0.tgz",
      "integrity": "sha512-WDF+xj5rRWmD5tj6bIqRi6CkLIXbbNQUcxQHzGysQzvHmdYG2G7p/Tf0J0gpxGgkeMZNTIjT/AoSvC9Xehcgdg==",
      "dependencies": {
        "@types/estree-jsx": "^1.0.0",
        "astring": "^1.8.0",
        "source-map": "^0.7.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/estree-util-visit": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/estree-util-visit/-/estree-util-visit-2.0.0.tgz",
      "integrity": "sha512-m5KgiH85xAhhW8Wta0vShLcUvOsh3LLPI2YVwcbio1l7E09NTLL1EyMZFM1OyWowoH0skScNbhOPl4kcBgzTww==",
      "dependencies": {
        "@types/estree-jsx": "^1.0.0",
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/estree-util-visit/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/estree-walker": {
      "version": "3.0.3",
      "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-3.0.3.tgz",
      "integrity": "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==",
      "dependencies": {
        "@types/estree": "^1.0.0"
      }
    },
    "node_modules/eventemitter3": {
      "version": "5.0.1",
      "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz",
      "integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA=="
    },
    "node_modules/execa": {
      "version": "8.0.1",
      "resolved": "https://registry.npmjs.org/execa/-/execa-8.0.1.tgz",
      "integrity": "sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==",
      "dependencies": {
        "cross-spawn": "^7.0.3",
        "get-stream": "^8.0.1",
        "human-signals": "^5.0.0",
        "is-stream": "^3.0.0",
        "merge-stream": "^2.0.0",
        "npm-run-path": "^5.1.0",
        "onetime": "^6.0.0",
        "signal-exit": "^4.1.0",
        "strip-final-newline": "^3.0.0"
      },
      "engines": {
        "node": ">=16.17"
      },
      "funding": {
        "url": "https://github.com/sindresorhus/execa?sponsor=1"
      }
    },
    "node_modules/execa/node_modules/signal-exit": {
      "version": "4.1.0",
      "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz",
      "integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==",
      "engines": {
        "node": ">=14"
      },
      "funding": {
        "url": "https://github.com/sponsors/isaacs"
      }
    },
    "node_modules/expand-template": {
      "version": "2.0.3",
      "resolved": "https://registry.npmjs.org/expand-template/-/expand-template-2.0.3.tgz",
      "integrity": "sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==",
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/expressive-code": {
      "version": "0.33.5",
      "resolved": "https://registry.npmjs.org/expressive-code/-/expressive-code-0.33.5.tgz",
      "integrity": "sha512-UPg2jSvZEfXPiCa4MKtMoMQ5Hwiv7In5/LSCa/ukhjzZqPO48iVsCcEBgXWEUmEAQ02P0z00/xFfBmVnUKH+Zw==",
      "dependencies": {
        "@expressive-code/core": "^0.33.5",
        "@expressive-code/plugin-frames": "^0.33.5",
        "@expressive-code/plugin-shiki": "^0.33.5",
        "@expressive-code/plugin-text-markers": "^0.33.5"
      }
    },
    "node_modules/extend": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz",
      "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
    },
    "node_modules/extend-shallow": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz",
      "integrity": "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==",
      "dependencies": {
        "is-extendable": "^0.1.0"
      },
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/fast-fifo": {
      "version": "1.3.2",
      "resolved": "https://registry.npmjs.org/fast-fifo/-/fast-fifo-1.3.2.tgz",
      "integrity": "sha512-/d9sfos4yxzpwkDkuN7k2SqFKtYNmCTzgfEpz82x34IM9/zc8KGxQoXg1liNC/izpRM/MBdt44Nmx41ZWqk+FQ=="
    },
    "node_modules/fast-glob": {
      "version": "3.3.2",
      "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz",
      "integrity": "sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==",
      "dependencies": {
        "@nodelib/fs.stat": "^2.0.2",
        "@nodelib/fs.walk": "^1.2.3",
        "glob-parent": "^5.1.2",
        "merge2": "^1.3.0",
        "micromatch": "^4.0.4"
      },
      "engines": {
        "node": ">=8.6.0"
      }
    },
    "node_modules/fastq": {
      "version": "1.17.1",
      "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz",
      "integrity": "sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==",
      "dependencies": {
        "reusify": "^1.0.4"
      }
    },
    "node_modules/fill-range": {
      "version": "7.1.1",
      "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz",
      "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==",
      "dependencies": {
        "to-regex-range": "^5.0.1"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/find-up": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz",
      "integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==",
      "dependencies": {
        "locate-path": "^6.0.0",
        "path-exists": "^4.0.0"
      },
      "engines": {
        "node": ">=10"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/find-yarn-workspace-root2": {
      "version": "1.2.16",
      "resolved": "https://registry.npmjs.org/find-yarn-workspace-root2/-/find-yarn-workspace-root2-1.2.16.tgz",
      "integrity": "sha512-hr6hb1w8ePMpPVUK39S4RlwJzi+xPLuVuG8XlwXU3KD5Yn3qgBWVfy3AzNlDhWvE1EORCE65/Qm26rFQt3VLVA==",
      "dependencies": {
        "micromatch": "^4.0.2",
        "pkg-dir": "^4.2.0"
      }
    },
    "node_modules/flattie": {
      "version": "1.1.1",
      "resolved": "https://registry.npmjs.org/flattie/-/flattie-1.1.1.tgz",
      "integrity": "sha512-9UbaD6XdAL97+k/n+N7JwX46K/M6Zc6KcFYskrYL8wbBV/Uyk0CTAMY0VT+qiK5PM7AIc9aTWYtq65U7T+aCNQ==",
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/fs-constants": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz",
      "integrity": "sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow=="
    },
    "node_modules/fsevents": {
      "version": "2.3.3",
      "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz",
      "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==",
      "hasInstallScript": true,
      "optional": true,
      "os": [
        "darwin"
      ],
      "engines": {
        "node": "^8.16.0 || ^10.6.0 || >=11.0.0"
      }
    },
    "node_modules/function-bind": {
      "version": "1.1.2",
      "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz",
      "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==",
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/gensync": {
      "version": "1.0.0-beta.2",
      "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz",
      "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==",
      "engines": {
        "node": ">=6.9.0"
      }
    },
    "node_modules/get-east-asian-width": {
      "version": "1.2.0",
      "resolved": "https://registry.npmjs.org/get-east-asian-width/-/get-east-asian-width-1.2.0.tgz",
      "integrity": "sha512-2nk+7SIVb14QrgXFHcm84tD4bKQz0RxPuMT8Ag5KPOq7J5fEmAg0UbXdTOSHqNuHSU28k55qnceesxXRZGzKWA==",
      "engines": {
        "node": ">=18"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/get-stream": {
      "version": "8.0.1",
      "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-8.0.1.tgz",
      "integrity": "sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==",
      "engines": {
        "node": ">=16"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/github-from-package": {
      "version": "0.0.0",
      "resolved": "https://registry.npmjs.org/github-from-package/-/github-from-package-0.0.0.tgz",
      "integrity": "sha512-SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw=="
    },
    "node_modules/github-slugger": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/github-slugger/-/github-slugger-2.0.0.tgz",
      "integrity": "sha512-IaOQ9puYtjrkq7Y0Ygl9KDZnrf/aiUJYUpVf89y8kyaxbRG7Y1SrX/jaumrv81vc61+kiMempujsM3Yw7w5qcw=="
    },
    "node_modules/glob-parent": {
      "version": "5.1.2",
      "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",
      "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",
      "dependencies": {
        "is-glob": "^4.0.1"
      },
      "engines": {
        "node": ">= 6"
      }
    },
    "node_modules/globals": {
      "version": "11.12.0",
      "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz",
      "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==",
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/graceful-fs": {
      "version": "4.2.11",
      "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz",
      "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
    },
    "node_modules/gray-matter": {
      "version": "4.0.3",
      "resolved": "https://registry.npmjs.org/gray-matter/-/gray-matter-4.0.3.tgz",
      "integrity": "sha512-5v6yZd4JK3eMI3FqqCouswVqwugaA9r4dNZB1wwcmrD02QkV5H0y7XBQW8QwQqEaZY1pM9aqORSORhJRdNK44Q==",
      "dependencies": {
        "js-yaml": "^3.13.1",
        "kind-of": "^6.0.2",
        "section-matter": "^1.0.0",
        "strip-bom-string": "^1.0.0"
      },
      "engines": {
        "node": ">=6.0"
      }
    },
    "node_modules/gray-matter/node_modules/argparse": {
      "version": "1.0.10",
      "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz",
      "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==",
      "dependencies": {
        "sprintf-js": "~1.0.2"
      }
    },
    "node_modules/gray-matter/node_modules/js-yaml": {
      "version": "3.14.1",
      "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz",
      "integrity": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==",
      "dependencies": {
        "argparse": "^1.0.7",
        "esprima": "^4.0.0"
      },
      "bin": {
        "js-yaml": "bin/js-yaml.js"
      }
    },
    "node_modules/has-flag": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz",
      "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==",
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/hasown": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.0.tgz",
      "integrity": "sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA==",
      "dependencies": {
        "function-bind": "^1.1.2"
      },
      "engines": {
        "node": ">= 0.4"
      }
    },
    "node_modules/hast-util-from-html": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/hast-util-from-html/-/hast-util-from-html-2.0.1.tgz",
      "integrity": "sha512-RXQBLMl9kjKVNkJTIO6bZyb2n+cUH8LFaSSzo82jiLT6Tfc+Pt7VQCS+/h3YwG4jaNE2TA2sdJisGWR+aJrp0g==",
      "dependencies": {
        "@types/hast": "^3.0.0",
        "devlop": "^1.1.0",
        "hast-util-from-parse5": "^8.0.0",
        "parse5": "^7.0.0",
        "vfile": "^6.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-from-html/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/hast-util-from-html/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-from-html/node_modules/vfile": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.1.tgz",
      "integrity": "sha512-1bYqc7pt6NIADBJ98UiG0Bn/CHIVOoZ/IyEkqIruLg0mE1BKzkOXY2D6CSqQIcKqgadppE5lrxgWXJmXd7zZJw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-from-html/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-from-parse5": {
      "version": "8.0.1",
      "resolved": "https://registry.npmjs.org/hast-util-from-parse5/-/hast-util-from-parse5-8.0.1.tgz",
      "integrity": "sha512-Er/Iixbc7IEa7r/XLtuG52zoqn/b3Xng/w6aZQ0xGVxzhw5xUFxcRqdPzP6yFi/4HBYRaifaI5fQ1RH8n0ZeOQ==",
      "dependencies": {
        "@types/hast": "^3.0.0",
        "@types/unist": "^3.0.0",
        "devlop": "^1.0.0",
        "hastscript": "^8.0.0",
        "property-information": "^6.0.0",
        "vfile": "^6.0.0",
        "vfile-location": "^5.0.0",
        "web-namespaces": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-from-parse5/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/hast-util-from-parse5/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-from-parse5/node_modules/vfile": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.1.tgz",
      "integrity": "sha512-1bYqc7pt6NIADBJ98UiG0Bn/CHIVOoZ/IyEkqIruLg0mE1BKzkOXY2D6CSqQIcKqgadppE5lrxgWXJmXd7zZJw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-from-parse5/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-has-property": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/hast-util-has-property/-/hast-util-has-property-3.0.0.tgz",
      "integrity": "sha512-MNilsvEKLFpV604hwfhVStK0usFY/QmM5zX16bo7EjnAEGofr5YyI37kzopBlZJkHD4t887i+q/C8/tr5Q94cA==",
      "dependencies": {
        "@types/hast": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-is-element": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/hast-util-is-element/-/hast-util-is-element-3.0.0.tgz",
      "integrity": "sha512-Val9mnv2IWpLbNPqc/pUem+a7Ipj2aHacCwgNfTiK0vJKl0LF+4Ba4+v1oPHFpf3bLYmreq0/l3Gud9S5OH42g==",
      "dependencies": {
        "@types/hast": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-parse-selector": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/hast-util-parse-selector/-/hast-util-parse-selector-4.0.0.tgz",
      "integrity": "sha512-wkQCkSYoOGCRKERFWcxMVMOcYE2K1AaNLU8DXS9arxnLOUEWbOXKXiJUNzEpqZ3JOKpnha3jkFrumEjVliDe7A==",
      "dependencies": {
        "@types/hast": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-raw": {
      "version": "9.0.2",
      "resolved": "https://registry.npmjs.org/hast-util-raw/-/hast-util-raw-9.0.2.tgz",
      "integrity": "sha512-PldBy71wO9Uq1kyaMch9AHIghtQvIwxBUkv823pKmkTM3oV1JxtsTNYdevMxvUHqcnOAuO65JKU2+0NOxc2ksA==",
      "dependencies": {
        "@types/hast": "^3.0.0",
        "@types/unist": "^3.0.0",
        "@ungap/structured-clone": "^1.0.0",
        "hast-util-from-parse5": "^8.0.0",
        "hast-util-to-parse5": "^8.0.0",
        "html-void-elements": "^3.0.0",
        "mdast-util-to-hast": "^13.0.0",
        "parse5": "^7.0.0",
        "unist-util-position": "^5.0.0",
        "unist-util-visit": "^5.0.0",
        "vfile": "^6.0.0",
        "web-namespaces": "^2.0.0",
        "zwitch": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-raw/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/hast-util-raw/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-raw/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-raw/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-raw/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-raw/node_modules/vfile": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.1.tgz",
      "integrity": "sha512-1bYqc7pt6NIADBJ98UiG0Bn/CHIVOoZ/IyEkqIruLg0mE1BKzkOXY2D6CSqQIcKqgadppE5lrxgWXJmXd7zZJw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-raw/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-select": {
      "version": "6.0.2",
      "resolved": "https://registry.npmjs.org/hast-util-select/-/hast-util-select-6.0.2.tgz",
      "integrity": "sha512-hT/SD/d/Meu+iobvgkffo1QecV8WeKWxwsNMzcTJsKw1cKTQKSR/7ArJeURLNJF9HDjp9nVoORyNNJxrvBye8Q==",
      "dependencies": {
        "@types/hast": "^3.0.0",
        "@types/unist": "^3.0.0",
        "bcp-47-match": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "css-selector-parser": "^3.0.0",
        "devlop": "^1.0.0",
        "direction": "^2.0.0",
        "hast-util-has-property": "^3.0.0",
        "hast-util-to-string": "^3.0.0",
        "hast-util-whitespace": "^3.0.0",
        "not": "^0.1.0",
        "nth-check": "^2.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0",
        "unist-util-visit": "^5.0.0",
        "zwitch": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-select/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/hast-util-select/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-select/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-select/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-to-estree": {
      "version": "3.1.0",
      "resolved": "https://registry.npmjs.org/hast-util-to-estree/-/hast-util-to-estree-3.1.0.tgz",
      "integrity": "sha512-lfX5g6hqVh9kjS/B9E2gSkvHH4SZNiQFiqWS0x9fENzEl+8W12RqdRxX6d/Cwxi30tPQs3bIO+aolQJNp1bIyw==",
      "dependencies": {
        "@types/estree": "^1.0.0",
        "@types/estree-jsx": "^1.0.0",
        "@types/hast": "^3.0.0",
        "comma-separated-tokens": "^2.0.0",
        "devlop": "^1.0.0",
        "estree-util-attach-comments": "^3.0.0",
        "estree-util-is-identifier-name": "^3.0.0",
        "hast-util-whitespace": "^3.0.0",
        "mdast-util-mdx-expression": "^2.0.0",
        "mdast-util-mdx-jsx": "^3.0.0",
        "mdast-util-mdxjs-esm": "^2.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0",
        "style-to-object": "^0.4.0",
        "unist-util-position": "^5.0.0",
        "zwitch": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-to-html": {
      "version": "9.0.1",
      "resolved": "https://registry.npmjs.org/hast-util-to-html/-/hast-util-to-html-9.0.1.tgz",
      "integrity": "sha512-hZOofyZANbyWo+9RP75xIDV/gq+OUKx+T46IlwERnKmfpwp81XBFbT9mi26ws+SJchA4RVUQwIBJpqEOBhMzEQ==",
      "dependencies": {
        "@types/hast": "^3.0.0",
        "@types/unist": "^3.0.0",
        "ccount": "^2.0.0",
        "comma-separated-tokens": "^2.0.0",
        "hast-util-raw": "^9.0.0",
        "hast-util-whitespace": "^3.0.0",
        "html-void-elements": "^3.0.0",
        "mdast-util-to-hast": "^13.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0",
        "stringify-entities": "^4.0.0",
        "zwitch": "^2.0.4"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-to-html/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/hast-util-to-jsx-runtime": {
      "version": "2.3.0",
      "resolved": "https://registry.npmjs.org/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.0.tgz",
      "integrity": "sha512-H/y0+IWPdsLLS738P8tDnrQ8Z+dj12zQQ6WC11TIM21C8WFVoIxcqWXf2H3hiTVZjF1AWqoimGwrTWecWrnmRQ==",
      "dependencies": {
        "@types/estree": "^1.0.0",
        "@types/hast": "^3.0.0",
        "@types/unist": "^3.0.0",
        "comma-separated-tokens": "^2.0.0",
        "devlop": "^1.0.0",
        "estree-util-is-identifier-name": "^3.0.0",
        "hast-util-whitespace": "^3.0.0",
        "mdast-util-mdx-expression": "^2.0.0",
        "mdast-util-mdx-jsx": "^3.0.0",
        "mdast-util-mdxjs-esm": "^2.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0",
        "style-to-object": "^1.0.0",
        "unist-util-position": "^5.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-to-jsx-runtime/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/hast-util-to-jsx-runtime/node_modules/inline-style-parser": {
      "version": "0.2.3",
      "resolved": "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.2.3.tgz",
      "integrity": "sha512-qlD8YNDqyTKTyuITrDOffsl6Tdhv+UC4hcdAVuQsK4IMQ99nSgd1MIA/Q+jQYoh9r3hVUXhYh7urSRmXPkW04g=="
    },
    "node_modules/hast-util-to-jsx-runtime/node_modules/style-to-object": {
      "version": "1.0.6",
      "resolved": "https://registry.npmjs.org/style-to-object/-/style-to-object-1.0.6.tgz",
      "integrity": "sha512-khxq+Qm3xEyZfKd/y9L3oIWQimxuc4STrQKtQn8aSDRHb8mFgpukgX1hdzfrMEW6JCjyJ8p89x+IUMVnCBI1PA==",
      "dependencies": {
        "inline-style-parser": "0.2.3"
      }
    },
    "node_modules/hast-util-to-jsx-runtime/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-to-jsx-runtime/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-to-parse5": {
      "version": "8.0.0",
      "resolved": "https://registry.npmjs.org/hast-util-to-parse5/-/hast-util-to-parse5-8.0.0.tgz",
      "integrity": "sha512-3KKrV5ZVI8if87DVSi1vDeByYrkGzg4mEfeu4alwgmmIeARiBLKCZS2uw5Gb6nU9x9Yufyj3iudm6i7nl52PFw==",
      "dependencies": {
        "@types/hast": "^3.0.0",
        "comma-separated-tokens": "^2.0.0",
        "devlop": "^1.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0",
        "web-namespaces": "^2.0.0",
        "zwitch": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-to-string": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/hast-util-to-string/-/hast-util-to-string-3.0.0.tgz",
      "integrity": "sha512-OGkAxX1Ua3cbcW6EJ5pT/tslVb90uViVkcJ4ZZIMW/R33DX/AkcJcRrPebPwJkHYwlDHXz4aIwvAAaAdtrACFA==",
      "dependencies": {
        "@types/hast": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-to-text": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/hast-util-to-text/-/hast-util-to-text-4.0.2.tgz",
      "integrity": "sha512-KK6y/BN8lbaq654j7JgBydev7wuNMcID54lkRav1P0CaE1e47P72AWWPiGKXTJU271ooYzcvTAn/Zt0REnvc7A==",
      "dependencies": {
        "@types/hast": "^3.0.0",
        "@types/unist": "^3.0.0",
        "hast-util-is-element": "^3.0.0",
        "unist-util-find-after": "^5.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hast-util-to-text/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/hast-util-whitespace": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz",
      "integrity": "sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==",
      "dependencies": {
        "@types/hast": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/hastscript": {
      "version": "8.0.0",
      "resolved": "https://registry.npmjs.org/hastscript/-/hastscript-8.0.0.tgz",
      "integrity": "sha512-dMOtzCEd3ABUeSIISmrETiKuyydk1w0pa+gE/uormcTpSYuaNJPbX1NU3JLyscSLjwAQM8bWMhhIlnCqnRvDTw==",
      "dependencies": {
        "@types/hast": "^3.0.0",
        "comma-separated-tokens": "^2.0.0",
        "hast-util-parse-selector": "^4.0.0",
        "property-information": "^6.0.0",
        "space-separated-tokens": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/html-escaper": {
      "version": "3.0.3",
      "resolved": "https://registry.npmjs.org/html-escaper/-/html-escaper-3.0.3.tgz",
      "integrity": "sha512-RuMffC89BOWQoY0WKGpIhn5gX3iI54O6nRA0yC124NYVtzjmFWBIiFd8M0x+ZdX0P9R4lADg1mgP8C7PxGOWuQ=="
    },
    "node_modules/html-void-elements": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/html-void-elements/-/html-void-elements-3.0.0.tgz",
      "integrity": "sha512-bEqo66MRXsUGxWHV5IP0PUiAWwoEjba4VCzg0LjFJBpchPaTfyfCKTG6bc5F8ucKec3q5y6qOdGyYTSBEvhCrg==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/http-cache-semantics": {
      "version": "4.1.1",
      "resolved": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz",
      "integrity": "sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ=="
    },
    "node_modules/human-signals": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/human-signals/-/human-signals-5.0.0.tgz",
      "integrity": "sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==",
      "engines": {
        "node": ">=16.17.0"
      }
    },
    "node_modules/ieee754": {
      "version": "1.2.1",
      "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz",
      "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==",
      "funding": [
        {
          "type": "github",
          "url": "https://github.com/sponsors/feross"
        },
        {
          "type": "patreon",
          "url": "https://www.patreon.com/feross"
        },
        {
          "type": "consulting",
          "url": "https://feross.org/support"
        }
      ]
    },
    "node_modules/import-meta-resolve": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/import-meta-resolve/-/import-meta-resolve-4.0.0.tgz",
      "integrity": "sha512-okYUR7ZQPH+efeuMJGlq4f8ubUgO50kByRPyt/Cy1Io4PSRsPjxME+YlVaCOx+NIToW7hCsZNFJyTPFFKepRSA==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/inherits": {
      "version": "2.0.4",
      "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz",
      "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
    },
    "node_modules/ini": {
      "version": "1.3.8",
      "resolved": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz",
      "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="
    },
    "node_modules/inline-style-parser": {
      "version": "0.1.1",
      "resolved": "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.1.1.tgz",
      "integrity": "sha512-7NXolsK4CAS5+xvdj5OMMbI962hU/wvwoxk+LWR9Ek9bVtyuuYScDN6eS0rUm6TxApFpw7CX1o4uJzcd4AyD3Q=="
    },
    "node_modules/is-alphabetical": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/is-alphabetical/-/is-alphabetical-2.0.1.tgz",
      "integrity": "sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/is-alphanumerical": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz",
      "integrity": "sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==",
      "dependencies": {
        "is-alphabetical": "^2.0.0",
        "is-decimal": "^2.0.0"
      },
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/is-arrayish": {
      "version": "0.3.2",
      "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz",
      "integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ=="
    },
    "node_modules/is-binary-path": {
      "version": "2.1.0",
      "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz",
      "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==",
      "dependencies": {
        "binary-extensions": "^2.0.0"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/is-buffer": {
      "version": "2.0.5",
      "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.5.tgz",
      "integrity": "sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==",
      "funding": [
        {
          "type": "github",
          "url": "https://github.com/sponsors/feross"
        },
        {
          "type": "patreon",
          "url": "https://www.patreon.com/feross"
        },
        {
          "type": "consulting",
          "url": "https://feross.org/support"
        }
      ],
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/is-core-module": {
      "version": "2.13.1",
      "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.1.tgz",
      "integrity": "sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==",
      "dependencies": {
        "hasown": "^2.0.0"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/is-decimal": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/is-decimal/-/is-decimal-2.0.1.tgz",
      "integrity": "sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/is-docker": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/is-docker/-/is-docker-3.0.0.tgz",
      "integrity": "sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==",
      "bin": {
        "is-docker": "cli.js"
      },
      "engines": {
        "node": "^12.20.0 || ^14.13.1 || >=16.0.0"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/is-extendable": {
      "version": "0.1.1",
      "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz",
      "integrity": "sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==",
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/is-extglob": {
      "version": "2.1.1",
      "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz",
      "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==",
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/is-fullwidth-code-point": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz",
      "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==",
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/is-glob": {
      "version": "4.0.3",
      "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz",
      "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==",
      "dependencies": {
        "is-extglob": "^2.1.1"
      },
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/is-hexadecimal": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz",
      "integrity": "sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/is-inside-container": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/is-inside-container/-/is-inside-container-1.0.0.tgz",
      "integrity": "sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA==",
      "dependencies": {
        "is-docker": "^3.0.0"
      },
      "bin": {
        "is-inside-container": "cli.js"
      },
      "engines": {
        "node": ">=14.16"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/is-interactive": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/is-interactive/-/is-interactive-2.0.0.tgz",
      "integrity": "sha512-qP1vozQRI+BMOPcjFzrjXuQvdak2pHNUMZoeG2eRbiSqyvbEf/wQtEOTOX1guk6E3t36RkaqiSt8A/6YElNxLQ==",
      "engines": {
        "node": ">=12"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/is-number": {
      "version": "7.0.0",
      "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz",
      "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==",
      "engines": {
        "node": ">=0.12.0"
      }
    },
    "node_modules/is-plain-obj": {
      "version": "4.1.0",
      "resolved": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz",
      "integrity": "sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==",
      "engines": {
        "node": ">=12"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/is-reference": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/is-reference/-/is-reference-3.0.2.tgz",
      "integrity": "sha512-v3rht/LgVcsdZa3O2Nqs+NMowLOxeOm7Ay9+/ARQ2F+qEoANRcqrjAZKGN0v8ymUetZGgkp26LTnGT7H0Qo9Pg==",
      "dependencies": {
        "@types/estree": "*"
      }
    },
    "node_modules/is-stream": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-3.0.0.tgz",
      "integrity": "sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==",
      "engines": {
        "node": "^12.20.0 || ^14.13.1 || >=16.0.0"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/is-unicode-supported": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-2.0.0.tgz",
      "integrity": "sha512-FRdAyx5lusK1iHG0TWpVtk9+1i+GjrzRffhDg4ovQ7mcidMQ6mj+MhKPmvh7Xwyv5gIS06ns49CA7Sqg7lC22Q==",
      "engines": {
        "node": ">=18"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/is-wsl": {
      "version": "3.1.0",
      "resolved": "https://registry.npmjs.org/is-wsl/-/is-wsl-3.1.0.tgz",
      "integrity": "sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw==",
      "dependencies": {
        "is-inside-container": "^1.0.0"
      },
      "engines": {
        "node": ">=16"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/isexe": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz",
      "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
    },
    "node_modules/js-tokens": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz",
      "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
    },
    "node_modules/js-yaml": {
      "version": "4.1.0",
      "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz",
      "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==",
      "dependencies": {
        "argparse": "^2.0.1"
      },
      "bin": {
        "js-yaml": "bin/js-yaml.js"
      }
    },
    "node_modules/jsesc": {
      "version": "2.5.2",
      "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz",
      "integrity": "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==",
      "bin": {
        "jsesc": "bin/jsesc"
      },
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/json5": {
      "version": "2.2.3",
      "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz",
      "integrity": "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==",
      "bin": {
        "json5": "lib/cli.js"
      },
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/kind-of": {
      "version": "6.0.3",
      "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz",
      "integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==",
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/kleur": {
      "version": "4.1.5",
      "resolved": "https://registry.npmjs.org/kleur/-/kleur-4.1.5.tgz",
      "integrity": "sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==",
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/load-yaml-file": {
      "version": "0.2.0",
      "resolved": "https://registry.npmjs.org/load-yaml-file/-/load-yaml-file-0.2.0.tgz",
      "integrity": "sha512-OfCBkGEw4nN6JLtgRidPX6QxjBQGQf72q3si2uvqyFEMbycSFFHwAZeXx6cJgFM9wmLrf9zBwCP3Ivqa+LLZPw==",
      "dependencies": {
        "graceful-fs": "^4.1.5",
        "js-yaml": "^3.13.0",
        "pify": "^4.0.1",
        "strip-bom": "^3.0.0"
      },
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/load-yaml-file/node_modules/argparse": {
      "version": "1.0.10",
      "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz",
      "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==",
      "dependencies": {
        "sprintf-js": "~1.0.2"
      }
    },
    "node_modules/load-yaml-file/node_modules/js-yaml": {
      "version": "3.14.1",
      "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz",
      "integrity": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==",
      "dependencies": {
        "argparse": "^1.0.7",
        "esprima": "^4.0.0"
      },
      "bin": {
        "js-yaml": "bin/js-yaml.js"
      }
    },
    "node_modules/load-yaml-file/node_modules/strip-bom": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz",
      "integrity": "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==",
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/locate-path": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz",
      "integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==",
      "dependencies": {
        "p-locate": "^5.0.0"
      },
      "engines": {
        "node": ">=10"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/log-symbols": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/log-symbols/-/log-symbols-6.0.0.tgz",
      "integrity": "sha512-i24m8rpwhmPIS4zscNzK6MSEhk0DUWa/8iYQWxhffV8jkI4Phvs3F+quL5xvS0gdQR0FyTCMMH33Y78dDTzzIw==",
      "dependencies": {
        "chalk": "^5.3.0",
        "is-unicode-supported": "^1.3.0"
      },
      "engines": {
        "node": ">=18"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/log-symbols/node_modules/chalk": {
      "version": "5.3.0",
      "resolved": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz",
      "integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==",
      "engines": {
        "node": "^12.17.0 || ^14.13 || >=16.0.0"
      },
      "funding": {
        "url": "https://github.com/chalk/chalk?sponsor=1"
      }
    },
    "node_modules/log-symbols/node_modules/is-unicode-supported": {
      "version": "1.3.0",
      "resolved": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-1.3.0.tgz",
      "integrity": "sha512-43r2mRvz+8JRIKnWJ+3j8JtjRKZ6GmjzfaE/qiBJnikNnYv/6bagRJ1kUhNk8R5EX/GkobD+r+sfxCPJsiKBLQ==",
      "engines": {
        "node": ">=12"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/longest-streak": {
      "version": "3.1.0",
      "resolved": "https://registry.npmjs.org/longest-streak/-/longest-streak-3.1.0.tgz",
      "integrity": "sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/loose-envify": {
      "version": "1.4.0",
      "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz",
      "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==",
      "optional": true,
      "peer": true,
      "dependencies": {
        "js-tokens": "^3.0.0 || ^4.0.0"
      },
      "bin": {
        "loose-envify": "cli.js"
      }
    },
    "node_modules/lru-cache": {
      "version": "5.1.1",
      "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz",
      "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==",
      "dependencies": {
        "yallist": "^3.0.2"
      }
    },
    "node_modules/magic-string": {
      "version": "0.30.10",
      "resolved": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.10.tgz",
      "integrity": "sha512-iIRwTIf0QKV3UAnYK4PU8uiEc4SRh5jX0mwpIwETPpHdhVM4f53RSwS/vXvN1JhGX+Cs7B8qIq3d6AH49O5fAQ==",
      "dependencies": {
        "@jridgewell/sourcemap-codec": "^1.4.15"
      }
    },
    "node_modules/markdown-extensions": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/markdown-extensions/-/markdown-extensions-2.0.0.tgz",
      "integrity": "sha512-o5vL7aDWatOTX8LzaS1WMoaoxIiLRQJuIKKe2wAw6IeULDHaqbiqiggmx+pKvZDb1Sj+pE46Sn1T7lCqfFtg1Q==",
      "engines": {
        "node": ">=16"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/markdown-table": {
      "version": "3.0.3",
      "resolved": "https://registry.npmjs.org/markdown-table/-/markdown-table-3.0.3.tgz",
      "integrity": "sha512-Z1NL3Tb1M9wH4XESsCDEksWoKTdlUafKc4pt0GRwjUyXaCFZ+dc3g2erqB6zm3szA2IUSi7VnPI+o/9jnxh9hw==",
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/mdast-util-definitions": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-definitions/-/mdast-util-definitions-6.0.0.tgz",
      "integrity": "sha512-scTllyX6pnYNZH/AIp/0ePz6s4cZtARxImwoPJ7kS42n+MnVsI4XbnG6d4ibehRIldYMWM2LD7ImQblVhUejVQ==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "@types/unist": "^3.0.0",
        "unist-util-visit": "^5.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-definitions/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/mdast-util-definitions/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-definitions/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-definitions/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-directive": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-directive/-/mdast-util-directive-3.0.0.tgz",
      "integrity": "sha512-JUpYOqKI4mM3sZcNxmF/ox04XYFFkNwr0CFlrQIkCwbvH0xzMCqkMqAde9wRd80VAhaUrwFwKm2nxretdT1h7Q==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "@types/unist": "^3.0.0",
        "devlop": "^1.0.0",
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0",
        "parse-entities": "^4.0.0",
        "stringify-entities": "^4.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-directive/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/mdast-util-directive/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-directive/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-find-and-replace": {
      "version": "3.0.1",
      "resolved": "https://registry.npmjs.org/mdast-util-find-and-replace/-/mdast-util-find-and-replace-3.0.1.tgz",
      "integrity": "sha512-SG21kZHGC3XRTSUhtofZkBzZTJNM5ecCi0SK2IMKmSXR8vO3peL+kb1O0z7Zl83jKtutG4k5Wv/W7V3/YHvzPA==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "escape-string-regexp": "^5.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-find-and-replace/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz",
      "integrity": "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==",
      "engines": {
        "node": ">=12"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/mdast-util-find-and-replace/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-find-and-replace/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-from-markdown": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.0.tgz",
      "integrity": "sha512-n7MTOr/z+8NAX/wmhhDji8O3bRvPTV/U0oTCaZJkjhPSKTPhS3xufVhKGF8s1pJ7Ox4QgoIU7KHseh09S+9rTA==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "@types/unist": "^3.0.0",
        "decode-named-character-reference": "^1.0.0",
        "devlop": "^1.0.0",
        "mdast-util-to-string": "^4.0.0",
        "micromark": "^4.0.0",
        "micromark-util-decode-numeric-character-reference": "^2.0.0",
        "micromark-util-decode-string": "^2.0.0",
        "micromark-util-normalize-identifier": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-from-markdown/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/mdast-util-from-markdown/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-gfm": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-gfm/-/mdast-util-gfm-3.0.0.tgz",
      "integrity": "sha512-dgQEX5Amaq+DuUqf26jJqSK9qgixgd6rYDHAv4aTBuA92cTknZlKpPfa86Z/s8Dj8xsAQpFfBmPUHWJBWqS4Bw==",
      "dependencies": {
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-gfm-autolink-literal": "^2.0.0",
        "mdast-util-gfm-footnote": "^2.0.0",
        "mdast-util-gfm-strikethrough": "^2.0.0",
        "mdast-util-gfm-table": "^2.0.0",
        "mdast-util-gfm-task-list-item": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-gfm-autolink-literal": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-gfm-autolink-literal/-/mdast-util-gfm-autolink-literal-2.0.0.tgz",
      "integrity": "sha512-FyzMsduZZHSc3i0Px3PQcBT4WJY/X/RCtEJKuybiC6sjPqLv7h1yqAkmILZtuxMSsUyaLUWNp71+vQH2zqp5cg==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "ccount": "^2.0.0",
        "devlop": "^1.0.0",
        "mdast-util-find-and-replace": "^3.0.0",
        "micromark-util-character": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-gfm-footnote": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-gfm-footnote/-/mdast-util-gfm-footnote-2.0.0.tgz",
      "integrity": "sha512-5jOT2boTSVkMnQ7LTrd6n/18kqwjmuYqo7JUPe+tRCY6O7dAuTFMtTPauYYrMPpox9hlN0uOx/FL8XvEfG9/mQ==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "devlop": "^1.1.0",
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0",
        "micromark-util-normalize-identifier": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-gfm-strikethrough": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-gfm-strikethrough/-/mdast-util-gfm-strikethrough-2.0.0.tgz",
      "integrity": "sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-gfm-table": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-gfm-table/-/mdast-util-gfm-table-2.0.0.tgz",
      "integrity": "sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "devlop": "^1.0.0",
        "markdown-table": "^3.0.0",
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-gfm-task-list-item": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-gfm-task-list-item/-/mdast-util-gfm-task-list-item-2.0.0.tgz",
      "integrity": "sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "devlop": "^1.0.0",
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-mdx": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-mdx/-/mdast-util-mdx-3.0.0.tgz",
      "integrity": "sha512-JfbYLAW7XnYTTbUsmpu0kdBUVe+yKVJZBItEjwyYJiDJuZ9w4eeaqks4HQO+R7objWgS2ymV60GYpI14Ug554w==",
      "dependencies": {
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-mdx-expression": "^2.0.0",
        "mdast-util-mdx-jsx": "^3.0.0",
        "mdast-util-mdxjs-esm": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-mdx-expression": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.0.tgz",
      "integrity": "sha512-fGCu8eWdKUKNu5mohVGkhBXCXGnOTLuFqOvGMvdikr+J1w7lDJgxThOKpwRWzzbyXAU2hhSwsmssOY4yTokluw==",
      "dependencies": {
        "@types/estree-jsx": "^1.0.0",
        "@types/hast": "^3.0.0",
        "@types/mdast": "^4.0.0",
        "devlop": "^1.0.0",
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-mdx-jsx": {
      "version": "3.1.2",
      "resolved": "https://registry.npmjs.org/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.1.2.tgz",
      "integrity": "sha512-eKMQDeywY2wlHc97k5eD8VC+9ASMjN8ItEZQNGwJ6E0XWKiW/Z0V5/H8pvoXUf+y+Mj0VIgeRRbujBmFn4FTyA==",
      "dependencies": {
        "@types/estree-jsx": "^1.0.0",
        "@types/hast": "^3.0.0",
        "@types/mdast": "^4.0.0",
        "@types/unist": "^3.0.0",
        "ccount": "^2.0.0",
        "devlop": "^1.1.0",
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0",
        "parse-entities": "^4.0.0",
        "stringify-entities": "^4.0.0",
        "unist-util-remove-position": "^5.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-mdx-jsx/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/mdast-util-mdx-jsx/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-mdx-jsx/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-mdxjs-esm": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz",
      "integrity": "sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==",
      "dependencies": {
        "@types/estree-jsx": "^1.0.0",
        "@types/hast": "^3.0.0",
        "@types/mdast": "^4.0.0",
        "devlop": "^1.0.0",
        "mdast-util-from-markdown": "^2.0.0",
        "mdast-util-to-markdown": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-phrasing": {
      "version": "4.1.0",
      "resolved": "https://registry.npmjs.org/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz",
      "integrity": "sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-phrasing/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/mdast-util-phrasing/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-hast": {
      "version": "13.1.0",
      "resolved": "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-13.1.0.tgz",
      "integrity": "sha512-/e2l/6+OdGp/FB+ctrJ9Avz71AN/GRH3oi/3KAx/kMnoUsD6q0woXlDT8lLEeViVKE7oZxE7RXzvO3T8kF2/sA==",
      "dependencies": {
        "@types/hast": "^3.0.0",
        "@types/mdast": "^4.0.0",
        "@ungap/structured-clone": "^1.0.0",
        "devlop": "^1.0.0",
        "micromark-util-sanitize-uri": "^2.0.0",
        "trim-lines": "^3.0.0",
        "unist-util-position": "^5.0.0",
        "unist-util-visit": "^5.0.0",
        "vfile": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-hast/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/mdast-util-to-hast/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-hast/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-hast/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-hast/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-hast/node_modules/vfile": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.1.tgz",
      "integrity": "sha512-1bYqc7pt6NIADBJ98UiG0Bn/CHIVOoZ/IyEkqIruLg0mE1BKzkOXY2D6CSqQIcKqgadppE5lrxgWXJmXd7zZJw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-hast/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-markdown": {
      "version": "2.1.0",
      "resolved": "https://registry.npmjs.org/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.0.tgz",
      "integrity": "sha512-SR2VnIEdVNCJbP6y7kVTJgPLifdr8WEU440fQec7qHoHOUz/oJ2jmNRqdDQ3rbiStOXb2mCDGTuwsK5OPUgYlQ==",
      "dependencies": {
        "@types/mdast": "^4.0.0",
        "@types/unist": "^3.0.0",
        "longest-streak": "^3.0.0",
        "mdast-util-phrasing": "^4.0.0",
        "mdast-util-to-string": "^4.0.0",
        "micromark-util-decode-string": "^2.0.0",
        "unist-util-visit": "^5.0.0",
        "zwitch": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-markdown/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/mdast-util-to-markdown/node_modules/unist-util-is": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-markdown/node_modules/unist-util-visit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0",
        "unist-util-visit-parents": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-markdown/node_modules/unist-util-visit-parents": {
      "version": "6.0.1",
      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-is": "^6.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/mdast-util-to-string": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz",
      "integrity": "sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==",
      "dependencies": {
        "@types/mdast": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/merge-stream": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz",
      "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
    },
    "node_modules/merge2": {
      "version": "1.4.1",
      "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz",
      "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==",
      "engines": {
        "node": ">= 8"
      }
    },
    "node_modules/micromark": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/micromark/-/micromark-4.0.0.tgz",
      "integrity": "sha512-o/sd0nMof8kYff+TqcDx3VSrgBTcZpSvYcAHIfHhv5VAuNmisCxjhx6YmxS8PFEpb9z5WKWKPdzf0jM23ro3RQ==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "@types/debug": "^4.0.0",
        "debug": "^4.0.0",
        "decode-named-character-reference": "^1.0.0",
        "devlop": "^1.0.0",
        "micromark-core-commonmark": "^2.0.0",
        "micromark-factory-space": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-chunked": "^2.0.0",
        "micromark-util-combine-extensions": "^2.0.0",
        "micromark-util-decode-numeric-character-reference": "^2.0.0",
        "micromark-util-encode": "^2.0.0",
        "micromark-util-normalize-identifier": "^2.0.0",
        "micromark-util-resolve-all": "^2.0.0",
        "micromark-util-sanitize-uri": "^2.0.0",
        "micromark-util-subtokenize": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-core-commonmark": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-2.0.1.tgz",
      "integrity": "sha512-CUQyKr1e///ZODyD1U3xit6zXwy1a8q2a1S1HKtIlmgvurrEpaw/Y9y6KSIbF8P59cn/NjzHyO+Q2fAyYLQrAA==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "decode-named-character-reference": "^1.0.0",
        "devlop": "^1.0.0",
        "micromark-factory-destination": "^2.0.0",
        "micromark-factory-label": "^2.0.0",
        "micromark-factory-space": "^2.0.0",
        "micromark-factory-title": "^2.0.0",
        "micromark-factory-whitespace": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-chunked": "^2.0.0",
        "micromark-util-classify-character": "^2.0.0",
        "micromark-util-html-tag-name": "^2.0.0",
        "micromark-util-normalize-identifier": "^2.0.0",
        "micromark-util-resolve-all": "^2.0.0",
        "micromark-util-subtokenize": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-extension-directive": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-directive/-/micromark-extension-directive-3.0.0.tgz",
      "integrity": "sha512-61OI07qpQrERc+0wEysLHMvoiO3s2R56x5u7glHq2Yqq6EHbH4dW25G9GfDdGCDYqA21KE6DWgNSzxSwHc2hSg==",
      "dependencies": {
        "devlop": "^1.0.0",
        "micromark-factory-space": "^2.0.0",
        "micromark-factory-whitespace": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0",
        "parse-entities": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-gfm": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-gfm/-/micromark-extension-gfm-3.0.0.tgz",
      "integrity": "sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w==",
      "dependencies": {
        "micromark-extension-gfm-autolink-literal": "^2.0.0",
        "micromark-extension-gfm-footnote": "^2.0.0",
        "micromark-extension-gfm-strikethrough": "^2.0.0",
        "micromark-extension-gfm-table": "^2.0.0",
        "micromark-extension-gfm-tagfilter": "^2.0.0",
        "micromark-extension-gfm-task-list-item": "^2.0.0",
        "micromark-util-combine-extensions": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-gfm-autolink-literal": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-gfm-autolink-literal/-/micromark-extension-gfm-autolink-literal-2.0.0.tgz",
      "integrity": "sha512-rTHfnpt/Q7dEAK1Y5ii0W8bhfJlVJFnJMHIPisfPK3gpVNuOP0VnRl96+YJ3RYWV/P4gFeQoGKNlT3RhuvpqAg==",
      "dependencies": {
        "micromark-util-character": "^2.0.0",
        "micromark-util-sanitize-uri": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-gfm-footnote": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-gfm-footnote/-/micromark-extension-gfm-footnote-2.0.0.tgz",
      "integrity": "sha512-6Rzu0CYRKDv3BfLAUnZsSlzx3ak6HAoI85KTiijuKIz5UxZxbUI+pD6oHgw+6UtQuiRwnGRhzMmPRv4smcz0fg==",
      "dependencies": {
        "devlop": "^1.0.0",
        "micromark-core-commonmark": "^2.0.0",
        "micromark-factory-space": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-normalize-identifier": "^2.0.0",
        "micromark-util-sanitize-uri": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-gfm-strikethrough": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-gfm-strikethrough/-/micromark-extension-gfm-strikethrough-2.0.0.tgz",
      "integrity": "sha512-c3BR1ClMp5fxxmwP6AoOY2fXO9U8uFMKs4ADD66ahLTNcwzSCyRVU4k7LPV5Nxo/VJiR4TdzxRQY2v3qIUceCw==",
      "dependencies": {
        "devlop": "^1.0.0",
        "micromark-util-chunked": "^2.0.0",
        "micromark-util-classify-character": "^2.0.0",
        "micromark-util-resolve-all": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-gfm-table": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-gfm-table/-/micromark-extension-gfm-table-2.0.0.tgz",
      "integrity": "sha512-PoHlhypg1ItIucOaHmKE8fbin3vTLpDOUg8KAr8gRCF1MOZI9Nquq2i/44wFvviM4WuxJzc3demT8Y3dkfvYrw==",
      "dependencies": {
        "devlop": "^1.0.0",
        "micromark-factory-space": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-gfm-tagfilter": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-gfm-tagfilter/-/micromark-extension-gfm-tagfilter-2.0.0.tgz",
      "integrity": "sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg==",
      "dependencies": {
        "micromark-util-types": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-gfm-task-list-item": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/micromark-extension-gfm-task-list-item/-/micromark-extension-gfm-task-list-item-2.0.1.tgz",
      "integrity": "sha512-cY5PzGcnULaN5O7T+cOzfMoHjBW7j+T9D2sucA5d/KbsBTPcYdebm9zUd9zzdgJGCwahV+/W78Z3nbulBYVbTw==",
      "dependencies": {
        "devlop": "^1.0.0",
        "micromark-factory-space": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-mdx-expression": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-mdx-expression/-/micromark-extension-mdx-expression-3.0.0.tgz",
      "integrity": "sha512-sI0nwhUDz97xyzqJAbHQhp5TfaxEvZZZ2JDqUo+7NvyIYG6BZ5CPPqj2ogUoPJlmXHBnyZUzISg9+oUmU6tUjQ==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "@types/estree": "^1.0.0",
        "devlop": "^1.0.0",
        "micromark-factory-mdx-expression": "^2.0.0",
        "micromark-factory-space": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-events-to-acorn": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-extension-mdx-jsx": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-mdx-jsx/-/micromark-extension-mdx-jsx-3.0.0.tgz",
      "integrity": "sha512-uvhhss8OGuzR4/N17L1JwvmJIpPhAd8oByMawEKx6NVdBCbesjH4t+vjEp3ZXft9DwvlKSD07fCeI44/N0Vf2w==",
      "dependencies": {
        "@types/acorn": "^4.0.0",
        "@types/estree": "^1.0.0",
        "devlop": "^1.0.0",
        "estree-util-is-identifier-name": "^3.0.0",
        "micromark-factory-mdx-expression": "^2.0.0",
        "micromark-factory-space": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-mdx-jsx/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/micromark-extension-mdx-jsx/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-mdx-jsx/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-mdx-md": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-mdx-md/-/micromark-extension-mdx-md-2.0.0.tgz",
      "integrity": "sha512-EpAiszsB3blw4Rpba7xTOUptcFeBFi+6PY8VnJ2hhimH+vCQDirWgsMpz7w1XcZE7LVrSAUGb9VJpG9ghlYvYQ==",
      "dependencies": {
        "micromark-util-types": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-mdxjs": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-mdxjs/-/micromark-extension-mdxjs-3.0.0.tgz",
      "integrity": "sha512-A873fJfhnJ2siZyUrJ31l34Uqwy4xIFmvPY1oj+Ean5PHcPBYzEsvqvWGaWcfEIr11O5Dlw3p2y0tZWpKHDejQ==",
      "dependencies": {
        "acorn": "^8.0.0",
        "acorn-jsx": "^5.0.0",
        "micromark-extension-mdx-expression": "^3.0.0",
        "micromark-extension-mdx-jsx": "^3.0.0",
        "micromark-extension-mdx-md": "^2.0.0",
        "micromark-extension-mdxjs-esm": "^3.0.0",
        "micromark-util-combine-extensions": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-mdxjs-esm": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/micromark-extension-mdxjs-esm/-/micromark-extension-mdxjs-esm-3.0.0.tgz",
      "integrity": "sha512-DJFl4ZqkErRpq/dAPyeWp15tGrcrrJho1hKK5uBS70BCtfrIFg81sqcTVu3Ta+KD1Tk5vAtBNElWxtAa+m8K9A==",
      "dependencies": {
        "@types/estree": "^1.0.0",
        "devlop": "^1.0.0",
        "micromark-core-commonmark": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-events-to-acorn": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0",
        "unist-util-position-from-estree": "^2.0.0",
        "vfile-message": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-mdxjs-esm/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/micromark-extension-mdxjs-esm/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-extension-mdxjs-esm/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-factory-destination": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-2.0.0.tgz",
      "integrity": "sha512-j9DGrQLm/Uhl2tCzcbLhy5kXsgkHUrjJHg4fFAeoMRwJmJerT9aw4FEhIbZStWN8A3qMwOp1uzHr4UL8AInxtA==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-character": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-factory-label": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-2.0.0.tgz",
      "integrity": "sha512-RR3i96ohZGde//4WSe/dJsxOX6vxIg9TimLAS3i4EhBAFx8Sm5SmqVfR8E87DPSR31nEAjZfbt91OMZWcNgdZw==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "devlop": "^1.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-factory-mdx-expression": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/micromark-factory-mdx-expression/-/micromark-factory-mdx-expression-2.0.1.tgz",
      "integrity": "sha512-F0ccWIUHRLRrYp5TC9ZYXmZo+p2AM13ggbsW4T0b5CRKP8KHVRB8t4pwtBgTxtjRmwrK0Irwm7vs2JOZabHZfg==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "@types/estree": "^1.0.0",
        "devlop": "^1.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-events-to-acorn": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0",
        "unist-util-position-from-estree": "^2.0.0",
        "vfile-message": "^4.0.0"
      }
    },
    "node_modules/micromark-factory-mdx-expression/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/micromark-factory-mdx-expression/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-factory-mdx-expression/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-factory-space": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-2.0.0.tgz",
      "integrity": "sha512-TKr+LIDX2pkBJXFLzpyPyljzYK3MtmllMUMODTQJIUfDGncESaqB90db9IAUcz4AZAJFdd8U9zOp9ty1458rxg==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-character": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-factory-title": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-2.0.0.tgz",
      "integrity": "sha512-jY8CSxmpWLOxS+t8W+FG3Xigc0RDQA9bKMY/EwILvsesiRniiVMejYTE4wumNc2f4UbAa4WsHqe3J1QS1sli+A==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-factory-space": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-factory-whitespace": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.0.tgz",
      "integrity": "sha512-28kbwaBjc5yAI1XadbdPYHX/eDnqaUFVikLwrO7FDnKG7lpgxnvk/XGRhX/PN0mOZ+dBSZ+LgunHS+6tYQAzhA==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-factory-space": "^2.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-util-character": {
      "version": "2.1.0",
      "resolved": "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-2.1.0.tgz",
      "integrity": "sha512-KvOVV+X1yLBfs9dCBSopq/+G1PcgT3lAK07mC4BzXi5E7ahzMAF8oIupDDJ6mievI6F+lAATkbQQlQixJfT3aQ==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-util-chunked": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-2.0.0.tgz",
      "integrity": "sha512-anK8SWmNphkXdaKgz5hJvGa7l00qmcaUQoMYsBwDlSKFKjc6gjGXPDw3FNL3Nbwq5L8gE+RCbGqTw49FK5Qyvg==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-symbol": "^2.0.0"
      }
    },
    "node_modules/micromark-util-classify-character": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-2.0.0.tgz",
      "integrity": "sha512-S0ze2R9GH+fu41FA7pbSqNWObo/kzwf8rN/+IGlW/4tC6oACOs8B++bh+i9bVyNnwCcuksbFwsBme5OCKXCwIw==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-character": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-util-combine-extensions": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.0.tgz",
      "integrity": "sha512-vZZio48k7ON0fVS3CUgFatWHoKbbLTK/rT7pzpJ4Bjp5JjkZeasRfrS9wsBdDJK2cJLHMckXZdzPSSr1B8a4oQ==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-chunked": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-util-decode-numeric-character-reference": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.1.tgz",
      "integrity": "sha512-bmkNc7z8Wn6kgjZmVHOX3SowGmVdhYS7yBpMnuMnPzDq/6xwVA604DuOXMZTO1lvq01g+Adfa0pE2UKGlxL1XQ==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-symbol": "^2.0.0"
      }
    },
    "node_modules/micromark-util-decode-string": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-2.0.0.tgz",
      "integrity": "sha512-r4Sc6leeUTn3P6gk20aFMj2ntPwn6qpDZqWvYmAG6NgvFTIlj4WtrAudLi65qYoaGdXYViXYw2pkmn7QnIFasA==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "decode-named-character-reference": "^1.0.0",
        "micromark-util-character": "^2.0.0",
        "micromark-util-decode-numeric-character-reference": "^2.0.0",
        "micromark-util-symbol": "^2.0.0"
      }
    },
    "node_modules/micromark-util-encode": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-2.0.0.tgz",
      "integrity": "sha512-pS+ROfCXAGLWCOc8egcBvT0kf27GoWMqtdarNfDcjb6YLuV5cM3ioG45Ys2qOVqeqSbjaKg72vU+Wby3eddPsA==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ]
    },
    "node_modules/micromark-util-events-to-acorn": {
      "version": "2.0.2",
      "resolved": "https://registry.npmjs.org/micromark-util-events-to-acorn/-/micromark-util-events-to-acorn-2.0.2.tgz",
      "integrity": "sha512-Fk+xmBrOv9QZnEDguL9OI9/NQQp6Hz4FuQ4YmCb/5V7+9eAh1s6AYSvL20kHkD67YIg7EpE54TiSlcsf3vyZgA==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "@types/acorn": "^4.0.0",
        "@types/estree": "^1.0.0",
        "@types/unist": "^3.0.0",
        "devlop": "^1.0.0",
        "estree-util-visit": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0",
        "vfile-message": "^4.0.0"
      }
    },
    "node_modules/micromark-util-events-to-acorn/node_modules/@types/unist": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.2.tgz",
      "integrity": "sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ=="
    },
    "node_modules/micromark-util-events-to-acorn/node_modules/unist-util-stringify-position": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
      "dependencies": {
        "@types/unist": "^3.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-util-events-to-acorn/node_modules/vfile-message": {
      "version": "4.0.2",
      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
      "dependencies": {
        "@types/unist": "^3.0.0",
        "unist-util-stringify-position": "^4.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/micromark-util-html-tag-name": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.0.tgz",
      "integrity": "sha512-xNn4Pqkj2puRhKdKTm8t1YHC/BAjx6CEwRFXntTaRf/x16aqka6ouVoutm+QdkISTlT7e2zU7U4ZdlDLJd2Mcw==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ]
    },
    "node_modules/micromark-util-normalize-identifier": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.0.tgz",
      "integrity": "sha512-2xhYT0sfo85FMrUPtHcPo2rrp1lwbDEEzpx7jiH2xXJLqBuy4H0GgXk5ToU8IEwoROtXuL8ND0ttVa4rNqYK3w==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-symbol": "^2.0.0"
      }
    },
    "node_modules/micromark-util-resolve-all": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.0.tgz",
      "integrity": "sha512-6KU6qO7DZ7GJkaCgwBNtplXCvGkJToU86ybBAUdavvgsCiG8lSSvYxr9MhwmQ+udpzywHsl4RpGJsYWG1pDOcA==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-util-sanitize-uri": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.0.tgz",
      "integrity": "sha512-WhYv5UEcZrbAtlsnPuChHUAsu/iBPOVaEVsntLBIdpibO0ddy8OzavZz3iL2xVvBZOpolujSliP65Kq0/7KIYw==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "micromark-util-character": "^2.0.0",
        "micromark-util-encode": "^2.0.0",
        "micromark-util-symbol": "^2.0.0"
      }
    },
    "node_modules/micromark-util-subtokenize": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-2.0.1.tgz",
      "integrity": "sha512-jZNtiFl/1aY73yS3UGQkutD0UbhTt68qnRpw2Pifmz5wV9h8gOVsN70v+Lq/f1rKaU/W8pxRe8y8Q9FX1AOe1Q==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ],
      "dependencies": {
        "devlop": "^1.0.0",
        "micromark-util-chunked": "^2.0.0",
        "micromark-util-symbol": "^2.0.0",
        "micromark-util-types": "^2.0.0"
      }
    },
    "node_modules/micromark-util-symbol": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-2.0.0.tgz",
      "integrity": "sha512-8JZt9ElZ5kyTnO94muPxIGS8oyElRJaiJO8EzV6ZSyGQ1Is8xwl4Q45qU5UOg+bGH4AikWziz0iN4sFLWs8PGw==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ]
    },
    "node_modules/micromark-util-types": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-2.0.0.tgz",
      "integrity": "sha512-oNh6S2WMHWRZrmutsRmDDfkzKtxF+bc2VxLC9dvtrDIRFln627VsFP6fLMgTryGDljgLPjkrzQSDcPrjPyDJ5w==",
      "funding": [
        {
          "type": "GitHub Sponsors",
          "url": "https://github.com/sponsors/unifiedjs"
        },
        {
          "type": "OpenCollective",
          "url": "https://opencollective.com/unified"
        }
      ]
    },
    "node_modules/micromatch": {
      "version": "4.0.5",
      "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz",
      "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==",
      "dependencies": {
        "braces": "^3.0.2",
        "picomatch": "^2.3.1"
      },
      "engines": {
        "node": ">=8.6"
      }
    },
    "node_modules/mimic-fn": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-4.0.0.tgz",
      "integrity": "sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==",
      "engines": {
        "node": ">=12"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/mimic-response": {
      "version": "3.1.0",
      "resolved": "https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz",
      "integrity": "sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==",
      "engines": {
        "node": ">=10"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/minimist": {
      "version": "1.2.8",
      "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz",
      "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==",
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/mkdirp-classic": {
      "version": "0.5.3",
      "resolved": "https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz",
      "integrity": "sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A=="
    },
    "node_modules/mrmime": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/mrmime/-/mrmime-2.0.0.tgz",
      "integrity": "sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==",
      "engines": {
        "node": ">=10"
      }
    },
    "node_modules/ms": {
      "version": "2.1.2",
      "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz",
      "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
    },
    "node_modules/nanoid": {
      "version": "3.3.7",
      "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz",
      "integrity": "sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==",
      "funding": [
        {
          "type": "github",
          "url": "https://github.com/sponsors/ai"
        }
      ],
      "bin": {
        "nanoid": "bin/nanoid.cjs"
      },
      "engines": {
        "node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"
      }
    },
    "node_modules/napi-build-utils": {
      "version": "1.0.2",
      "resolved": "https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-1.0.2.tgz",
      "integrity": "sha512-ONmRUqK7zj7DWX0D9ADe03wbwOBZxNAfF20PlGfCWQcD3+/MakShIHrMqx9YwPTfxDdF1zLeL+RGZiR9kGMLdg=="
    },
    "node_modules/nlcst-to-string": {
      "version": "3.1.1",
      "resolved": "https://registry.npmjs.org/nlcst-to-string/-/nlcst-to-string-3.1.1.tgz",
      "integrity": "sha512-63mVyqaqt0cmn2VcI2aH6kxe1rLAmSROqHMA0i4qqg1tidkfExgpb0FGMikMCn86mw5dFtBtEANfmSSK7TjNHw==",
      "dependencies": {
        "@types/nlcst": "^1.0.0"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/unified"
      }
    },
    "node_modules/node-abi": {
      "version": "3.47.0",
      "resolved": "https://registry.npmjs.org/node-abi/-/node-abi-3.47.0.tgz",
      "integrity": "sha512-2s6B2CWZM//kPgwnuI0KrYwNjfdByE25zvAaEpq9IH4zcNsarH8Ihu/UuX6XMPEogDAxkuUFeZn60pXNHAqn3A==",
      "dependencies": {
        "semver": "^7.3.5"
      },
      "engines": {
        "node": ">=10"
      }
    },
    "node_modules/node-addon-api": {
      "version": "6.1.0",
      "resolved": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-6.1.0.tgz",
      "integrity": "sha512-+eawOlIgy680F0kBzPUNFhMZGtJ1YmqM6l4+Crf4IkImjYrO/mqPwRMh352g23uIaQKFItcQ64I7KMaJxHgAVA=="
    },
    "node_modules/node-releases": {
      "version": "2.0.14",
      "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.14.tgz",
      "integrity": "sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw=="
    },
    "node_modules/normalize-path": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz",
      "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==",
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/not": {
      "version": "0.1.0",
      "resolved": "https://registry.npmjs.org/not/-/not-0.1.0.tgz",
      "integrity": "sha512-5PDmaAsVfnWUgTUbJ3ERwn7u79Z0dYxN9ErxCpVJJqe2RK0PJ3z+iFUxuqjwtlDDegXvtWoxD/3Fzxox7tFGWA=="
    },
    "node_modules/npm-run-path": {
      "version": "5.1.0",
      "resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.1.0.tgz",
      "integrity": "sha512-sJOdmRGrY2sjNTRMbSvluQqg+8X7ZK61yvzBEIDhz4f8z1TZFYABsqjjCBd/0PUNE9M6QDgHJXQkGUEm7Q+l9Q==",
      "dependencies": {
        "path-key": "^4.0.0"
      },
      "engines": {
        "node": "^12.20.0 || ^14.13.1 || >=16.0.0"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/npm-run-path/node_modules/path-key": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz",
      "integrity": "sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==",
      "engines": {
        "node": ">=12"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/nth-check": {
      "version": "2.1.1",
      "resolved": "https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz",
      "integrity": "sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==",
      "dependencies": {
        "boolbase": "^1.0.0"
      },
      "funding": {
        "url": "https://github.com/fb55/nth-check?sponsor=1"
      }
    },
    "node_modules/once": {
      "version": "1.4.0",
      "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz",
      "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==",
      "dependencies": {
        "wrappy": "1"
      }
    },
    "node_modules/onetime": {
      "version": "6.0.0",
      "resolved": "https://registry.npmjs.org/onetime/-/onetime-6.0.0.tgz",
      "integrity": "sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==",
      "dependencies": {
        "mimic-fn": "^4.0.0"
      },
      "engines": {
        "node": ">=12"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/ora": {
      "version": "8.0.1",
      "resolved": "https://registry.npmjs.org/ora/-/ora-8.0.1.tgz",
      "integrity": "sha512-ANIvzobt1rls2BDny5fWZ3ZVKyD6nscLvfFRpQgfWsythlcsVUC9kL0zq6j2Z5z9wwp1kd7wpsD/T9qNPVLCaQ==",
      "dependencies": {
        "chalk": "^5.3.0",
        "cli-cursor": "^4.0.0",
        "cli-spinners": "^2.9.2",
        "is-interactive": "^2.0.0",
        "is-unicode-supported": "^2.0.0",
        "log-symbols": "^6.0.0",
        "stdin-discarder": "^0.2.1",
        "string-width": "^7.0.0",
        "strip-ansi": "^7.1.0"
      },
      "engines": {
        "node": ">=18"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/ora/node_modules/chalk": {
      "version": "5.3.0",
      "resolved": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz",
      "integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==",
      "engines": {
        "node": "^12.17.0 || ^14.13 || >=16.0.0"
      },
      "funding": {
        "url": "https://github.com/chalk/chalk?sponsor=1"
      }
    },
    "node_modules/p-limit": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-5.0.0.tgz",
      "integrity": "sha512-/Eaoq+QyLSiXQ4lyYV23f14mZRQcXnxfHrN0vCai+ak9G0pp9iEQukIIZq5NccEvwRB8PUnZT0KsOoDCINS1qQ==",
      "dependencies": {
        "yocto-queue": "^1.0.0"
      },
      "engines": {
        "node": ">=18"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/p-locate": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz",
      "integrity": "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==",
      "dependencies": {
        "p-limit": "^3.0.2"
      },
      "engines": {
        "node": ">=10"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/p-locate/node_modules/p-limit": {
      "version": "3.1.0",
      "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz",
      "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==",
      "dependencies": {
        "yocto-queue": "^0.1.0"
      },
      "engines": {
        "node": ">=10"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/p-locate/node_modules/yocto-queue": {
      "version": "0.1.0",
      "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz",
      "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==",
      "engines": {
        "node": ">=10"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/p-queue": {
      "version": "8.0.1",
      "resolved": "https://registry.npmjs.org/p-queue/-/p-queue-8.0.1.tgz",
      "integrity": "sha512-NXzu9aQJTAzbBqOt2hwsR63ea7yvxJc0PwN/zobNAudYfb1B7R08SzB4TsLeSbUCuG467NhnoT0oO6w1qRO+BA==",
      "dependencies": {
        "eventemitter3": "^5.0.1",
        "p-timeout": "^6.1.2"
      },
      "engines": {
        "node": ">=18"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/p-timeout": {
      "version": "6.1.2",
      "resolved": "https://registry.npmjs.org/p-timeout/-/p-timeout-6.1.2.tgz",
      "integrity": "sha512-UbD77BuZ9Bc9aABo74gfXhNvzC9Tx7SxtHSh1fxvx3jTLLYvmVhiQZZrJzqqU0jKbN32kb5VOKiLEQI/3bIjgQ==",
      "engines": {
        "node": ">=14.16"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/p-try": {
      "version": "2.2.0",
      "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz",
      "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==",
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/pagefind": {
      "version": "1.0.3",
      "resolved": "https://registry.npmjs.org/pagefind/-/pagefind-1.0.3.tgz",
      "integrity": "sha512-ws7kmMxW6OuxzsOjj3YAx6TYq/54MiE3wfyBM3J5CInbZyBBvM2Z8c8IYvnMkBcb5v2EoB9DewXEekOEiDRu5g==",
      "bin": {
        "pagefind": "lib/runner/bin.cjs"
      },
      "optionalDependencies": {
        "@pagefind/darwin-arm64": "1.0.3",
        "@pagefind/darwin-x64": "1.0.3",
        "@pagefind/linux-arm64": "1.0.3",
        "@pagefind/linux-x64": "1.0.3",
        "@pagefind/windows-x64": "1.0.3"
      }
    },
    "node_modules/parse-entities": {
      "version": "4.0.1",
      "resolved": "https://registry.npmjs.org/parse-entities/-/parse-entities-4.0.1.tgz",
      "integrity": "sha512-SWzvYcSJh4d/SGLIOQfZ/CoNv6BTlI6YEQ7Nj82oDVnRpwe/Z/F1EMx42x3JAOwGBlCjeCH0BRJQbQ/opHL17w==",
      "dependencies": {
        "@types/unist": "^2.0.0",
        "character-entities": "^2.0.0",
        "character-entities-legacy": "^3.0.0",
        "character-reference-invalid": "^2.0.0",
        "decode-named-character-reference": "^1.0.0",
        "is-alphanumerical": "^2.0.0",
        "is-decimal": "^2.0.0",
        "is-hexadecimal": "^2.0.0"
      },
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/parse-latin": {
      "version": "5.0.1",
      "resolved": "https://registry.npmjs.org/parse-latin/-/parse-latin-5.0.1.tgz",
      "integrity": "sha512-b/K8ExXaWC9t34kKeDV8kGXBkXZ1HCSAZRYE7HR14eA1GlXX5L8iWhs8USJNhQU9q5ci413jCKF0gOyovvyRBg==",
      "dependencies": {
        "nlcst-to-string": "^3.0.0",
        "unist-util-modify-children": "^3.0.0",
        "unist-util-visit-children": "^2.0.0"
      },
      "funding": {
        "type": "github",
        "url": "https://github.com/sponsors/wooorm"
      }
    },
    "node_modules/parse5": {
      "version": "7.1.2",
      "resolved": "https://registry.npmjs.org/parse5/-/parse5-7.1.2.tgz",
      "integrity": "sha512-Czj1WaSVpaoj0wbhMzLmWD69anp2WH7FXMB9n1Sy8/ZFF9jolSQVMu1Ij5WIyGmcBmhk7EOndpO4mIpihVqAXw==",
      "dependencies": {
        "entities": "^4.4.0"
      },
      "funding": {
        "url": "https://github.com/inikulin/parse5?sponsor=1"
      }
    },
    "node_modules/path-exists": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz",
      "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==",
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/path-key": {
      "version": "3.1.1",
      "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz",
      "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==",
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/path-parse": {
      "version": "1.0.7",
      "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz",
      "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
    },
    "node_modules/path-to-regexp": {
      "version": "6.2.2",
      "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.2.2.tgz",
      "integrity": "sha512-GQX3SSMokngb36+whdpRXE+3f9V8UzyAorlYvOGx87ufGHehNTn5lCxrKtLyZ4Yl/wEKnNnr98ZzOwwDZV5ogw=="
    },
    "node_modules/periscopic": {
      "version": "3.1.0",
      "resolved": "https://registry.npmjs.org/periscopic/-/periscopic-3.1.0.tgz",
      "integrity": "sha512-vKiQ8RRtkl9P+r/+oefh25C3fhybptkHKCZSPlcXiJux2tJF55GnEj3BVn4A5gKfq9NWWXXrxkHBwVPUfH0opw==",
      "dependencies": {
        "@types/estree": "^1.0.0",
        "estree-walker": "^3.0.0",
        "is-reference": "^3.0.0"
      }
    },
    "node_modules/picocolors": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz",
      "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="
    },
    "node_modules/picomatch": {
      "version": "2.3.1",
      "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz",
      "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",
      "engines": {
        "node": ">=8.6"
      },
      "funding": {
        "url": "https://github.com/sponsors/jonschlinkert"
      }
    },
    "node_modules/pify": {
      "version": "4.0.1",
      "resolved": "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz",
      "integrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==",
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/pkg-dir": {
      "version": "4.2.0",
      "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz",
      "integrity": "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==",
      "dependencies": {
        "find-up": "^4.0.0"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/pkg-dir/node_modules/find-up": {
      "version": "4.1.0",
      "resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz",
      "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==",
      "dependencies": {
        "locate-path": "^5.0.0",
        "path-exists": "^4.0.0"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/pkg-dir/node_modules/locate-path": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz",
      "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==",
      "dependencies": {
        "p-locate": "^4.1.0"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/pkg-dir/node_modules/p-limit": {
      "version": "2.3.0",
      "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz",
      "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2My