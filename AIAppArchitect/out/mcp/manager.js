"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPManager = void 0;
// MCP Manager - Orchestrates MCP servers and connections
const vscode = __importStar(require("vscode"));
const events_1 = require("events");
const client_1 = require("./client");
const stdio_1 = require("./transports/stdio");
const streamableHttp_1 = require("./transports/streamableHttp");
const types_1 = require("./types");
class MCPManager extends events_1.EventEmitter {
    constructor(context) {
        super();
        this.servers = new Map();
        this.clients = new Map();
        this.context = context;
        this.securityContext = {
            userId: 'default',
            permissions: [],
            requireExplicitConsent: true,
            allowedDomains: [],
            blockedDomains: []
        };
        this.loadConfiguration();
    }
    async initialize() {
        console.log('🔌 Initializing MCP Manager...');
        // Load server configurations from settings
        const configs = this.getServerConfigurations();
        for (const config of configs) {
            if (config.enabled && config.autoStart !== false) {
                try {
                    await this.addServer(config);
                }
                catch (error) {
                    console.error(`Failed to initialize MCP server ${config.name}:`, error);
                }
            }
        }
        console.log(`✅ MCP Manager initialized with ${this.servers.size} servers`);
    }
    async addServer(config) {
        const serverId = `${config.name}-${Date.now()}`;
        const serverInstance = {
            id: serverId,
            config,
            status: types_1.MCPConnectionStatus.Disconnected,
            tools: [],
            resources: [],
            prompts: []
        };
        this.servers.set(serverId, serverInstance);
        this.emit('serverAdded', serverInstance);
        if (config.enabled) {
            await this.connectServer(serverId);
        }
        return serverId;
    }
    async removeServer(serverId) {
        const server = this.servers.get(serverId);
        if (!server) {
            throw new Error(`Server ${serverId} not found`);
        }
        await this.disconnectServer(serverId);
        this.servers.delete(serverId);
        this.emit('serverRemoved', serverId);
    }
    async connectServer(serverId) {
        const server = this.servers.get(serverId);
        if (!server) {
            throw new Error(`Server ${serverId} not found`);
        }
        if (server.status === types_1.MCPConnectionStatus.Connected) {
            return;
        }
        try {
            server.status = types_1.MCPConnectionStatus.Connecting;
            this.updateServerStatus(serverId, server.status);
            const client = new client_1.MCPClient({ name: 'Aizen AI Extension', version: '2.0.0' }, {
                tools: { listChanged: true },
                resources: { subscribe: true, listChanged: true },
                prompts: { listChanged: true },
                sampling: {},
                elicitation: {}
            });
            // Set up client event handlers
            client.on('connected', async (info) => {
                server.capabilities = info.capabilities;
                server.connectedAt = new Date();
                server.status = types_1.MCPConnectionStatus.Connected;
                this.updateServerStatus(serverId, server.status);
                // Load available tools, resources, and prompts
                await this.refreshServerCapabilities(serverId);
            });
            client.on('error', (error) => {
                server.lastError = error.message;
                server.status = types_1.MCPConnectionStatus.Error;
                this.updateServerStatus(serverId, server.status);
            });
            client.on('disconnected', () => {
                server.status = types_1.MCPConnectionStatus.Disconnected;
                this.updateServerStatus(serverId, server.status);
            });
            client.on('toolsChanged', () => this.refreshServerTools(serverId));
            client.on('resourcesChanged', () => this.refreshServerResources(serverId));
            client.on('promptsChanged', () => this.refreshServerPrompts(serverId));
            // Create appropriate transport
            let transport;
            switch (server.config.transport) {
                case 'stdio':
                    if (!server.config.command) {
                        throw new Error('Command required for stdio transport');
                    }
                    transport = new stdio_1.StdioTransport({
                        command: server.config.command,
                        args: server.config.args,
                        env: server.config.env,
                        timeout: server.config.timeout
                    });
                    await transport.connect();
                    break;
                case 'streamable-http':
                    if (!server.config.url) {
                        throw new Error('URL required for streamable-http transport');
                    }
                    transport = new streamableHttp_1.StreamableHttpPollingTransport({
                        url: server.config.url,
                        headers: server.config.env,
                        timeout: server.config.timeout
                    });
                    break;
                default:
                    throw new Error(`Unsupported transport: ${server.config.transport}`);
            }
            await client.connect(transport);
            this.clients.set(serverId, client);
        }
        catch (error) {
            server.status = types_1.MCPConnectionStatus.Error;
            server.lastError = error instanceof Error ? error.message : String(error);
            this.updateServerStatus(serverId, server.status);
            throw error;
        }
    }
    async disconnectServer(serverId) {
        const client = this.clients.get(serverId);
        if (client) {
            await client.disconnect();
            this.clients.delete(serverId);
        }
        const server = this.servers.get(serverId);
        if (server) {
            server.status = types_1.MCPConnectionStatus.Disconnected;
            this.updateServerStatus(serverId, server.status);
        }
    }
    async callTool(serverId, toolName, arguments_) {
        // Check permissions
        if (!await this.checkToolPermission(serverId, toolName)) {
            throw new Error(`Permission denied for tool ${toolName} on server ${serverId}`);
        }
        const client = this.clients.get(serverId);
        if (!client || !client.isConnected) {
            throw new Error(`Server ${serverId} is not connected`);
        }
        try {
            const result = await client.callTool(toolName, arguments_);
            this.updateServerActivity(serverId);
            return result;
        }
        catch (error) {
            console.error(`Tool call failed: ${toolName}`, error);
            throw error;
        }
    }
    async readResource(serverId, uri) {
        // Check permissions
        if (!await this.checkResourcePermission(serverId, uri)) {
            throw new Error(`Permission denied for resource ${uri} on server ${serverId}`);
        }
        const client = this.clients.get(serverId);
        if (!client || !client.isConnected) {
            throw new Error(`Server ${serverId} is not connected`);
        }
        try {
            const result = await client.readResource(uri);
            this.updateServerActivity(serverId);
            return result;
        }
        catch (error) {
            console.error(`Resource read failed: ${uri}`, error);
            throw error;
        }
    }
    async getPrompt(serverId, promptName, arguments_) {
        // Check permissions
        if (!await this.checkPromptPermission(serverId, promptName)) {
            throw new Error(`Permission denied for prompt ${promptName} on server ${serverId}`);
        }
        const client = this.clients.get(serverId);
        if (!client || !client.isConnected) {
            throw new Error(`Server ${serverId} is not connected`);
        }
        try {
            const result = await client.getPrompt(promptName, arguments_);
            this.updateServerActivity(serverId);
            return result;
        }
        catch (error) {
            console.error(`Prompt get failed: ${promptName}`, error);
            throw error;
        }
    }
    // Permission management
    async requestPermission(serverId, scope, target) {
        if (!this.securityContext.requireExplicitConsent) {
            return true;
        }
        const server = this.servers.get(serverId);
        if (!server) {
            return false;
        }
        // Show permission dialog to user
        const message = this.formatPermissionMessage(server, scope, target);
        const choice = await vscode.window.showWarningMessage(message, { modal: true }, 'Allow', 'Deny');
        const granted = choice === 'Allow';
        if (granted) {
            const permission = {
                serverId,
                toolName: scope === 'tool' ? target : undefined,
                resourceUri: scope === 'resource' ? target : undefined,
                promptName: scope === 'prompt' ? target : undefined,
                granted: true,
                grantedAt: new Date(),
                scope
            };
            this.securityContext.permissions.push(permission);
            await this.saveSecurityContext();
        }
        return granted;
    }
    async checkToolPermission(serverId, toolName) {
        return this.checkPermission(serverId, 'tool', toolName);
    }
    async checkResourcePermission(serverId, uri) {
        return this.checkPermission(serverId, 'resource', uri);
    }
    async checkPromptPermission(serverId, promptName) {
        return this.checkPermission(serverId, 'prompt', promptName);
    }
    async checkPermission(serverId, scope, target) {
        // Check existing permissions
        const existingPermission = this.securityContext.permissions.find(p => p.serverId === serverId &&
            p.scope === scope &&
            (scope === 'server' ||
                (scope === 'tool' && p.toolName === target) ||
                (scope === 'resource' && p.resourceUri === target) ||
                (scope === 'prompt' && p.promptName === target)));
        if (existingPermission && existingPermission.granted) {
            // Check if permission has expired
            if (existingPermission.expiresAt && existingPermission.expiresAt < new Date()) {
                return false;
            }
            return true;
        }
        // Request new permission
        return await this.requestPermission(serverId, scope, target);
    }
    // Getters
    getServers() {
        return Array.from(this.servers.values());
    }
    getServer(serverId) {
        return this.servers.get(serverId);
    }
    getConnectedServers() {
        return this.getServers().filter(s => s.status === types_1.MCPConnectionStatus.Connected);
    }
    getAllTools() {
        const tools = [];
        for (const [serverId, server] of this.servers) {
            if (server.tools) {
                server.tools.forEach(tool => tools.push({ serverId, tool }));
            }
        }
        return tools;
    }
    getAllResources() {
        const resources = [];
        for (const [serverId, server] of this.servers) {
            if (server.resources) {
                server.resources.forEach(resource => resources.push({ serverId, resource }));
            }
        }
        return resources;
    }
    getAllPrompts() {
        const prompts = [];
        for (const [serverId, server] of this.servers) {
            if (server.prompts) {
                server.prompts.forEach(prompt => prompts.push({ serverId, prompt }));
            }
        }
        return prompts;
    }
    // Private helper methods
    getServerConfigurations() {
        const config = vscode.workspace.getConfiguration('aizen.mcp');
        return config.get('servers', []);
    }
    async refreshServerCapabilities(serverId) {
        await Promise.all([
            this.refreshServerTools(serverId),
            this.refreshServerResources(serverId),
            this.refreshServerPrompts(serverId)
        ]);
    }
    async refreshServerTools(serverId) {
        const client = this.clients.get(serverId);
        const server = this.servers.get(serverId);
        if (client && server && client.isConnected) {
            try {
                const result = await client.listTools();
                server.tools = result.tools;
                this.emit('toolsUpdated', serverId, result.tools);
            }
            catch (error) {
                console.error(`Failed to refresh tools for server ${serverId}:`, error);
            }
        }
    }
    async refreshServerResources(serverId) {
        const client = this.clients.get(serverId);
        const server = this.servers.get(serverId);
        if (client && server && client.isConnected) {
            try {
                const result = await client.listResources();
                server.resources = result.resources;
                this.emit('resourcesUpdated', serverId, result.resources);
            }
            catch (error) {
                console.error(`Failed to refresh resources for server ${serverId}:`, error);
            }
        }
    }
    async refreshServerPrompts(serverId) {
        const client = this.clients.get(serverId);
        const server = this.servers.get(serverId);
        if (client && server && client.isConnected) {
            try {
                const result = await client.listPrompts();
                server.prompts = result.prompts;
                this.emit('promptsUpdated', serverId, result.prompts);
            }
            catch (error) {
                console.error(`Failed to refresh prompts for server ${serverId}:`, error);
            }
        }
    }
    updateServerStatus(serverId, status) {
        const server = this.servers.get(serverId);
        if (server) {
            server.status = status;
            this.emit('serverStatusChanged', serverId, status);
        }
    }
    updateServerActivity(serverId) {
        const server = this.servers.get(serverId);
        if (server) {
            server.lastActivity = new Date();
        }
    }
    formatPermissionMessage(server, scope, target) {
        const serverName = server.config.name;
        switch (scope) {
            case 'tool':
                return `Allow "${serverName}" to execute tool "${target}"?`;
            case 'resource':
                return `Allow "${serverName}" to access resource "${target}"?`;
            case 'prompt':
                return `Allow "${serverName}" to use prompt "${target}"?`;
            case 'server':
                return `Allow connection to MCP server "${serverName}"?`;
            default:
                return `Allow "${serverName}" to perform this action?`;
        }
    }
    loadConfiguration() {
        // Load security context from extension storage
        const stored = this.context.globalState.get('mcpSecurityContext');
        if (stored) {
            this.securityContext = stored;
        }
    }
    async saveSecurityContext() {
        await this.context.globalState.update('mcpSecurityContext', this.securityContext);
    }
    async dispose() {
        console.log('🧹 Disposing MCP Manager...');
        // Disconnect all servers
        const disconnectPromises = Array.from(this.servers.keys()).map(serverId => this.disconnectServer(serverId).catch(error => console.error(`Error disconnecting server ${serverId}:`, error)));
        await Promise.all(disconnectPromises);
        this.servers.clear();
        this.clients.clear();
        console.log('✅ MCP Manager disposed');
    }
}
exports.MCPManager = MCPManager;
//# sourceMappingURL=manager.js.map