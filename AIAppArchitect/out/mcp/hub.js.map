{"version": 3, "file": "hub.js", "sourceRoot": "", "sources": ["../../src/mcp/hub.ts"], "names": [], "mappings": ";;;AAEA,mCAAsC;AACtC,uCAAuC;AAmCvC,MAAa,MAAO,SAAQ,qBAAY;IAMpC,YAAY,OAAgC;QACxC,KAAK,EAAE,CAAC;QALJ,qBAAgB,GAAuB,EAAE,CAAC;QAC1C,cAAS,GAA6B,IAAI,GAAG,EAAE,CAAC;QAKpD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,oBAAU,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAChC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAE3B,wCAAwC;QACxC,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEpC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7B,CAAC;IAED,oBAAoB;IACpB,KAAK,CAAC,SAAS,CAAC,MAAuB;QACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC3C,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAgB;QAC/B,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAgB;QAChC,MAAM,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACnC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED,wCAAwC;IACxC,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,QAAgB,EAAE,aAAkC,EAAE;QACtF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,SAAS,GAAqB;YAChC,QAAQ;YACR,QAAQ;YACR,SAAS,EAAE,UAAU;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;QAEF,IAAI,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,sBAAsB,QAAQ,eAAe,QAAQ,EAAE,CAAC,CAAC;YAErE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YAE3E,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC;YAC1B,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YAErC,OAAO,CAAC,GAAG,CAAC,iCAAiC,QAAQ,KAAK,SAAS,CAAC,QAAQ,KAAK,CAAC,CAAC;YACnF,OAAO,MAAM,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,SAAS,CAAC,KAAK,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzE,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;YAE5C,OAAO,CAAC,KAAK,CAAC,4BAA4B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,kBAAkB;IAClB,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,GAAW;QAC5C,OAAO,CAAC,GAAG,CAAC,wBAAwB,GAAG,iBAAiB,QAAQ,EAAE,CAAC,CAAC;QAEpE,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YAC9D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC;YACrD,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;YACvD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,oBAAoB;IACpB,KAAK,CAAC,SAAS,CAAC,QAAgB,EAAE,UAAkB,EAAE,aAAkC,EAAE;QACtF,OAAO,CAAC,GAAG,CAAC,sBAAsB,UAAU,iBAAiB,QAAQ,EAAE,CAAC,CAAC;QAEzE,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAC9E,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;YACtF,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;YACpE,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,sBAAsB;IACtB,KAAK,CAAC,cAAc,CAAC,QAA+C;QAChE,MAAM,EAAE,GAAG,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/E,MAAM,WAAW,GAAgB;YAC7B,GAAG,QAAQ;YACX,EAAE;YACF,SAAS,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;QACpC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;QAE1C,OAAO,EAAE,CAAC;IACd,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAAkB,EAAE,cAAmC,EAAE;QAC3E,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,YAAY,UAAU,YAAY,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,0BAA0B,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QAEvD,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAE5E,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAE3B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;YACpD,OAAO,MAAM,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,gCAAgC,QAAQ,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;YACtE,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,KAAwB,EAAE,IAAyB;QAClF,IAAI,WAAW,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAE9B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC;gBACD,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;oBAChB,KAAK,MAAM;wBACP,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;4BAChB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;gCAChE,GAAG,IAAI,CAAC,SAAS;gCACjB,GAAG,WAAW;6BACjB,CAAC,CAAC;4BACH,WAAW,GAAG,EAAE,GAAG,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;wBACpE,CAAC;wBACD,MAAM;oBAEV,KAAK,UAAU;wBACX,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;4BACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;4BACxE,WAAW,GAAG,EAAE,GAAG,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;wBACpE,CAAC;wBACD,MAAM;oBAEV,KAAK,QAAQ;wBACT,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;4BAClB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE;gCAChE,GAAG,IAAI,CAAC,SAAS;gCACjB,GAAG,WAAW;6BACjB,CAAC,CAAC;4BACH,WAAW,GAAG,EAAE,GAAG,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;wBACpE,CAAC;wBACD,MAAM;oBAEV,KAAK,WAAW;wBACZ,kDAAkD;wBAClD,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,CAAC;4BACzE,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gCACnB,qBAAqB;gCACrB,SAAS;4BACb,CAAC;iCAAM,CAAC;gCACJ,MAAM,CAAC,gBAAgB;4BAC3B,CAAC;wBACL,CAAC;wBACD,MAAM;gBACd,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,wCAAwC;oBACxC,WAAW,GAAG,EAAE,GAAG,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC;oBAC9D,SAAS;gBACb,CAAC;qBAAM,CAAC;oBACJ,MAAM,KAAK,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,iBAAiB,CAAC,SAAiB,EAAE,IAAyB;QAClE,kFAAkF;QAClF,IAAI,CAAC;YACD,uCAAuC;YACvC,IAAI,kBAAkB,GAAG,SAAS,CAAC;YACnC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9C,kBAAkB,GAAG,kBAAkB,CAAC,OAAO,CAC3C,IAAI,MAAM,CAAC,SAAS,GAAG,KAAK,EAAE,GAAG,CAAC,EAClC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CACxB,CAAC;YACN,CAAC;YAED,qDAAqD;YACrD,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAClE,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACpC,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED,qCAAqC;IACrC,KAAK,CAAC,wBAAwB;QAC1B,4EAA4E;QAC5E,OAAO;YACH;gBACI,IAAI,EAAE,YAAY;gBAClB,GAAG,EAAE,2BAA2B;gBAChC,SAAS,EAAE,iBAAiB;gBAC5B,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,KAAK;aACnB;YACD;gBACI,IAAI,EAAE,WAAW;gBACjB,GAAG,EAAE,2BAA2B;gBAChC,SAAS,EAAE,iBAAiB;gBAC5B,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,KAAK;aACnB;SACJ,CAAC;IACN,CAAC;IAED,2BAA2B;IAC3B,mBAAmB,CAAC,QAAgB,GAAG;QACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED,cAAc;QACV,MAAM,KAAK,GAAG,IAAI,GAAG,EAAsD,CAAC;QAE5E,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;YAClE,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,SAAS,CAAC;YAC3C,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1D,QAAQ;YACR,GAAG,IAAI;SACV,CAAC,CAAC,CAAC;IACR,CAAC;IAED,UAAU;IACV,UAAU;QACN,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;IACrC,CAAC;IAED,mBAAmB;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;IAC9C,CAAC;IAED,WAAW;QACP,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACtC,CAAC;IAED,eAAe;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;IAC1C,CAAC;IAED,aAAa;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;IACxC,CAAC;IAED,YAAY;QACR,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED,kBAAkB;IACV,yBAAyB;QAC7B,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;QAC7E,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QACjH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;QACjG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;QACjH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;IAC7G,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAChC,MAAM,cAAc,GAAsB;YACtC;gBACI,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,wBAAwB;gBAC7B,SAAS,EAAE,iBAAiB;gBAC5B,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,KAAK;gBAChB,GAAG,EAAE;oBACD,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;iBAC/C;aACJ;YACD;gBACI,IAAI,EAAE,WAAW;gBACjB,GAAG,EAAE,+BAA+B;gBACpC,SAAS,EAAE,iBAAiB;gBAC5B,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,KAAK;gBAChB,GAAG,EAAE;oBACD,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;iBAC3D;aACJ;SACJ,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;YAClC,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAC7B,OAAO,CAAC,GAAG,CAAC,iCAAiC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAChE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,wCAAwC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAChF,CAAC;QACL,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAgB,cAAc,EAAE,EAAE,CAAC,CAAC;QAC/E,KAAK,MAAM,QAAQ,IAAI,MAAM,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa;QACvB,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QACtD,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACrE,CAAC;IAED,KAAK,CAAC,OAAO;QACT,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IACtC,CAAC;CACJ;AA3WD,wBA2WC"}