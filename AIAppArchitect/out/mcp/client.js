"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPClient = void 0;
const events_1 = require("events");
const types_1 = require("./types");
class MCPClient extends events_1.EventEmitter {
    constructor(clientInfo, capabilities = {
        tools: { listChanged: true },
        resources: { subscribe: true, listChanged: true },
        prompts: { listChanged: true },
        sampling: {},
        elicitation: {}
    }) {
        super();
        this.clientInfo = clientInfo;
        this.capabilities = capabilities;
        this.transport = null;
        this.requestId = 0;
        this.pendingRequests = new Map();
        this.serverInfo = null;
        this.serverCapabilities = {};
        this.connectionStatus = types_1.MCPConnectionStatus.Disconnected;
        this.requestTimeout = 30000; // 30 seconds
    }
    async connect(transport) {
        if (this.transport) {
            await this.disconnect();
        }
        this.transport = transport;
        this.connectionStatus = types_1.MCPConnectionStatus.Connecting;
        this.emit('statusChanged', this.connectionStatus);
        // Set up transport event handlers
        this.transport.onMessage((message) => this.handleMessage(message));
        this.transport.onError((error) => this.handleError(error));
        this.transport.onClose(() => this.handleClose());
        try {
            // Send initialize request
            const initRequest = {
                jsonrpc: "2.0",
                id: this.getNextRequestId(),
                method: "initialize",
                params: {
                    protocolVersion: types_1.MCP_PROTOCOL_VERSION,
                    capabilities: this.capabilities,
                    clientInfo: this.clientInfo
                }
            };
            const response = await this.sendRequest(initRequest);
            this.serverInfo = response.serverInfo;
            this.serverCapabilities = response.capabilities;
            this.connectionStatus = types_1.MCPConnectionStatus.Connected;
            this.emit('statusChanged', this.connectionStatus);
            this.emit('connected', { serverInfo: this.serverInfo, capabilities: this.serverCapabilities });
        }
        catch (error) {
            this.connectionStatus = types_1.MCPConnectionStatus.Error;
            this.emit('statusChanged', this.connectionStatus);
            throw error;
        }
    }
    async disconnect() {
        if (this.transport) {
            await this.transport.close();
            this.transport = null;
        }
        this.connectionStatus = types_1.MCPConnectionStatus.Disconnected;
        this.emit('statusChanged', this.connectionStatus);
        // Reject all pending requests
        for (const [id, request] of this.pendingRequests) {
            clearTimeout(request.timeout);
            request.reject(new Error('Connection closed'));
        }
        this.pendingRequests.clear();
    }
    async listTools() {
        const request = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "tools/list",
            params: {}
        };
        return this.sendRequest(request);
    }
    async callTool(name, arguments_) {
        const request = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "tools/call",
            params: {
                name,
                arguments: arguments_
            }
        };
        return this.sendRequest(request);
    }
    async listResources() {
        const request = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "resources/list",
            params: {}
        };
        return this.sendRequest(request);
    }
    async readResource(uri) {
        const request = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "resources/read",
            params: { uri }
        };
        return this.sendRequest(request);
    }
    async listPrompts() {
        const request = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "prompts/list",
            params: {}
        };
        return this.sendRequest(request);
    }
    async getPrompt(name, arguments_) {
        const request = {
            jsonrpc: "2.0",
            id: this.getNextRequestId(),
            method: "prompts/get",
            params: {
                name,
                arguments: arguments_
            }
        };
        return this.sendRequest(request);
    }
    async sendRequest(request) {
        if (!this.transport) {
            throw new Error('Not connected to MCP server');
        }
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.pendingRequests.delete(request.id);
                reject(new Error(`Request timeout: ${request.method}`));
            }, this.requestTimeout);
            this.pendingRequests.set(request.id, {
                resolve,
                reject,
                timeout
            });
            this.transport.send(request).catch((error) => {
                this.pendingRequests.delete(request.id);
                clearTimeout(timeout);
                reject(error);
            });
        });
    }
    handleMessage(message) {
        if ('id' in message && message.id !== undefined) {
            // This is a response to a request
            const pending = this.pendingRequests.get(message.id);
            if (pending) {
                this.pendingRequests.delete(message.id);
                clearTimeout(pending.timeout);
                if (message.error) {
                    pending.reject(new Error(`MCP Error ${message.error.code}: ${message.error.message}`));
                }
                else {
                    pending.resolve(message.result);
                }
            }
        }
        else {
            // This is a notification
            this.handleNotification(message);
        }
    }
    handleNotification(notification) {
        switch (notification.method) {
            case 'notifications/tools/list_changed':
                this.emit('toolsChanged');
                break;
            case 'notifications/resources/list_changed':
                this.emit('resourcesChanged');
                break;
            case 'notifications/prompts/list_changed':
                this.emit('promptsChanged');
                break;
            default:
                this.emit('notification', notification);
                break;
        }
    }
    handleError(error) {
        this.connectionStatus = types_1.MCPConnectionStatus.Error;
        this.emit('statusChanged', this.connectionStatus);
        this.emit('error', error);
    }
    handleClose() {
        this.connectionStatus = types_1.MCPConnectionStatus.Disconnected;
        this.emit('statusChanged', this.connectionStatus);
        this.emit('disconnected');
    }
    getNextRequestId() {
        return ++this.requestId;
    }
    // Getters
    get status() {
        return this.connectionStatus;
    }
    get isConnected() {
        return this.connectionStatus === types_1.MCPConnectionStatus.Connected;
    }
    get server() {
        if (!this.serverInfo)
            return null;
        return {
            info: this.serverInfo,
            capabilities: this.serverCapabilities
        };
    }
}
exports.MCPClient = MCPClient;
//# sourceMappingURL=client.js.map