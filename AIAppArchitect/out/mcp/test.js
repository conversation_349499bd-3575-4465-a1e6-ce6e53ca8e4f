"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPTestSuite = void 0;
exports.runMCPTests = runMCPTests;
// MCP Integration Test Suite
const vscode = __importStar(require("vscode"));
const hub_1 = require("./hub");
const security_1 = require("./security");
const exa_1 = require("./servers/exa");
const firecrawl_1 = require("./servers/firecrawl");
class MCPTestSuite {
    constructor(context) {
        this.context = context;
        this.testResults = [];
        this.mcpHub = new hub_1.MCPHub(context);
        this.mcpSecurity = new security_1.MCPSecurityManager(context);
    }
    async runAllTests() {
        console.log('🧪 Starting MCP Integration Test Suite...');
        const tests = [
            { name: 'Hub Initialization', test: () => this.testHubInitialization() },
            { name: 'Security Manager', test: () => this.testSecurityManager() },
            { name: 'Server Configuration', test: () => this.testServerConfiguration() },
            { name: 'External Server Integration', test: () => this.testExternalServerIntegration() },
            { name: 'Tool Execution', test: () => this.testToolExecution() },
            { name: 'Permission System', test: () => this.testPermissionSystem() },
            { name: 'Error Handling', test: () => this.testErrorHandling() },
            { name: 'Performance', test: () => this.testPerformance() }
        ];
        for (const { name, test } of tests) {
            await this.runTest(name, test);
        }
        this.displayResults();
    }
    async runTest(name, testFn) {
        const startTime = Date.now();
        try {
            console.log(`🔍 Running test: ${name}`);
            await testFn();
            const duration = Date.now() - startTime;
            this.testResults.push({
                name,
                passed: true,
                duration,
                timestamp: new Date()
            });
            console.log(`✅ Test passed: ${name} (${duration}ms)`);
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.testResults.push({
                name,
                passed: false,
                error: error instanceof Error ? error.message : String(error),
                duration,
                timestamp: new Date()
            });
            console.error(`❌ Test failed: ${name} (${duration}ms)`, error);
        }
    }
    async testHubInitialization() {
        // Test MCP Hub initialization
        await this.mcpHub.initialize();
        // Verify hub is properly initialized
        const servers = this.mcpHub.getServers();
        if (!Array.isArray(servers)) {
            throw new Error('Hub getServers() should return an array');
        }
        console.log(`✓ Hub initialized with ${servers.length} servers`);
    }
    async testSecurityManager() {
        // Test security manager functionality
        const policy = this.mcpSecurity.getSecurityPolicy();
        if (!policy || typeof policy.requireExplicitConsent !== 'boolean') {
            throw new Error('Security policy not properly loaded');
        }
        // Test permission validation
        const permissions = this.mcpSecurity.getPermissions();
        if (!Array.isArray(permissions)) {
            throw new Error('Permissions should return an array');
        }
        console.log(`✓ Security manager loaded with ${permissions.length} permissions`);
    }
    async testServerConfiguration() {
        // Test adding and configuring servers
        const testConfig = {
            name: 'Test Server',
            url: 'https://test.example.com/mcp',
            transport: 'streamable-http',
            enabled: false,
            autoStart: false
        };
        const serverId = await this.mcpHub.addServer(testConfig);
        if (!serverId || typeof serverId !== 'string') {
            throw new Error('Server ID should be a non-empty string');
        }
        // Verify server was added
        const server = this.mcpHub.getServers().find(s => s.id === serverId);
        if (!server) {
            throw new Error('Server not found after adding');
        }
        if (server.config.name !== testConfig.name) {
            throw new Error('Server configuration not properly stored');
        }
        // Clean up
        await this.mcpHub.removeServer(serverId);
        console.log(`✓ Server configuration test passed`);
    }
    async testExternalServerIntegration() {
        // Test external server configurations
        const exaConfig = exa_1.ExaMCPServerConfig;
        const firecrawlConfig = firecrawl_1.FirecrawlMCPServerConfig;
        // Validate Exa configuration
        if (!exaConfig.name || !exaConfig.url || !exaConfig.transport) {
            throw new Error('Exa server configuration is incomplete');
        }
        // Validate Firecrawl configuration
        if (!firecrawlConfig.name || !firecrawlConfig.url || !firecrawlConfig.transport) {
            throw new Error('Firecrawl server configuration is incomplete');
        }
        // Test adding external servers (without connecting)
        const exaServerId = await this.mcpHub.addServer({
            ...exaConfig,
            enabled: false,
            autoStart: false
        });
        const firecrawlServerId = await this.mcpHub.addServer({
            ...firecrawlConfig,
            enabled: false,
            autoStart: false
        });
        // Verify servers were added
        const servers = this.mcpHub.getServers();
        const exaServer = servers.find(s => s.id === exaServerId);
        const firecrawlServer = servers.find(s => s.id === firecrawlServerId);
        if (!exaServer || !firecrawlServer) {
            throw new Error('External servers not properly added');
        }
        // Clean up
        await this.mcpHub.removeServer(exaServerId);
        await this.mcpHub.removeServer(firecrawlServerId);
        console.log(`✓ External server integration test passed`);
    }
    async testToolExecution() {
        // Test tool execution with mock server
        const mockServerId = await this.mcpHub.addServer({
            name: 'Mock Tool Server',
            url: 'https://mock.example.com/mcp',
            transport: 'streamable-http',
            enabled: false,
            autoStart: false
        });
        try {
            // This should fail gracefully since it's a mock server
            await this.mcpHub.executeTool(mockServerId, 'mock_tool', { test: true });
            throw new Error('Mock tool execution should have failed');
        }
        catch (error) {
            // Expected to fail - this is good
            if (error instanceof Error && error.message.includes('not connected')) {
                console.log(`✓ Tool execution properly handles disconnected servers`);
            }
            else {
                throw error;
            }
        }
        finally {
            await this.mcpHub.removeServer(mockServerId);
        }
    }
    async testPermissionSystem() {
        // Test permission request and validation
        const mockServerId = 'test-server-123';
        // Test permission request (this will show a dialog in real usage)
        // For testing, we'll just verify the method exists and can be called
        try {
            // This should work without throwing
            const permissions = this.mcpSecurity.getPermissions(mockServerId);
            if (!Array.isArray(permissions)) {
                throw new Error('getPermissions should return an array');
            }
            console.log(`✓ Permission system test passed`);
        }
        catch (error) {
            throw new Error(`Permission system test failed: ${error}`);
        }
    }
    async testErrorHandling() {
        // Test various error scenarios
        // Test invalid server ID
        try {
            await this.mcpHub.removeServer('invalid-server-id');
            throw new Error('Should have thrown error for invalid server ID');
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('not found')) {
                console.log(`✓ Error handling for invalid server ID works`);
            }
            else {
                throw error;
            }
        }
        // Test invalid tool execution
        try {
            await this.mcpHub.executeTool('invalid-server', 'invalid-tool', {});
            throw new Error('Should have thrown error for invalid server');
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('not found')) {
                console.log(`✓ Error handling for invalid tool execution works`);
            }
            else {
                throw error;
            }
        }
    }
    async testPerformance() {
        // Test performance of basic operations
        const startTime = Date.now();
        // Add multiple servers quickly
        const serverIds = [];
        for (let i = 0; i < 10; i++) {
            const serverId = await this.mcpHub.addServer({
                name: `Performance Test Server ${i}`,
                url: `https://test${i}.example.com/mcp`,
                transport: 'streamable-http',
                enabled: false,
                autoStart: false
            });
            serverIds.push(serverId);
        }
        // Get servers list
        const servers = this.mcpHub.getServers();
        if (servers.length < 10) {
            throw new Error('Not all servers were added');
        }
        // Remove all test servers
        for (const serverId of serverIds) {
            await this.mcpHub.removeServer(serverId);
        }
        const duration = Date.now() - startTime;
        if (duration > 5000) { // 5 seconds
            throw new Error(`Performance test took too long: ${duration}ms`);
        }
        console.log(`✓ Performance test passed (${duration}ms for 10 server operations)`);
    }
    displayResults() {
        const passed = this.testResults.filter(r => r.passed).length;
        const failed = this.testResults.filter(r => !r.passed).length;
        const totalDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0);
        console.log('\n🧪 MCP Test Suite Results:');
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`⏱️ Total Duration: ${totalDuration}ms`);
        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(r => {
                console.log(`  • ${r.name}: ${r.error}`);
            });
        }
        // Show results in VS Code
        const message = `🧪 MCP Tests: ${passed} passed, ${failed} failed (${totalDuration}ms)`;
        if (failed === 0) {
            vscode.window.showInformationMessage(message);
        }
        else {
            vscode.window.showWarningMessage(message);
        }
    }
    async dispose() {
        await this.mcpHub.dispose();
        await this.mcpSecurity.dispose();
    }
}
exports.MCPTestSuite = MCPTestSuite;
// Export test runner function
async function runMCPTests(context) {
    const testSuite = new MCPTestSuite(context);
    try {
        await testSuite.runAllTests();
    }
    finally {
        await testSuite.dispose();
    }
}
//# sourceMappingURL=test.js.map