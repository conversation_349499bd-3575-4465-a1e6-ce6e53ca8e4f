{"version": 3, "file": "stdio.js", "sourceRoot": "", "sources": ["../../../src/mcp/transports/stdio.ts"], "names": [], "mappings": ";;;AAAA,qCAAqC;AACrC,iDAAoD;AACpD,mCAAsC;AAYtC,MAAa,cAAe,SAAQ,qBAAY;IAK5C,YAAoB,MAA4B;QAC5C,KAAK,EAAE,CAAC;QADQ,WAAM,GAAN,MAAM,CAAsB;QAJxC,YAAO,GAAwB,IAAI,CAAC;QACpC,kBAAa,GAAG,EAAE,CAAC;QACnB,gBAAW,GAAG,KAAK,CAAC;IAI5B,CAAC;IAED,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAEnD,IAAI,CAAC,OAAO,GAAG,IAAA,qBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,EAAE;gBAC9D,GAAG;gBACH,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG;gBACpB,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1B,MAAM,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;gBACrC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACtB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC;YACP,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACtB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACpC,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC;YACP,CAAC;YAED,qCAAqC;YACrC,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;oBACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBACxB,OAAO,EAAE,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACJ,MAAM,CAAC,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAC;gBACjD,CAAC;YACL,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAuB;QAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;QAElD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,OAAQ,CAAC,KAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC7C,IAAI,KAAK,EAAE,CAAC;oBACR,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACJ,uDAAuD;oBACvD,kDAAkD;oBAClD,OAAO,CAAC,EAAqB,CAAC,CAAC;gBACnC,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,KAAK;QACP,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,CAAC;IACL,CAAC;IAED,SAAS,CAAC,OAA2C;QACjD,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,CAAC,OAA+B;QACnC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED,OAAO,CAAC,OAAmB;QACvB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC;IAEO,UAAU,CAAC,IAAY;QAC3B,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;QAE3B,oDAAoD;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,qCAAqC;QAE7E,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;gBACd,IAAI,CAAC;oBACD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAClC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;oBACpE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC,CAAC;gBACvE,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AApHD,wCAoHC"}