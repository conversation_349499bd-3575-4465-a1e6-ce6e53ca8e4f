"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPHub = void 0;
const events_1 = require("events");
const manager_1 = require("./manager");
class MCPHub extends events_1.EventEmitter {
    constructor(context) {
        super();
        this.executionHistory = [];
        this.workflows = new Map();
        this.context = context;
        this.manager = new manager_1.MCPManager(context);
        this.setupManagerEventHandlers();
    }
    async initialize() {
        console.log('🚀 Initializing MCP Hub...');
        await this.manager.initialize();
        await this.loadWorkflows();
        // Register default external MCP servers
        await this.registerDefaultServers();
        console.log('✅ MCP Hub initialized successfully');
        this.emit('initialized');
    }
    // Server Management
    async addServer(config) {
        const serverId = await this.manager.addServer(config);
        this.emit('serverAdded', serverId, config);
        return serverId;
    }
    async removeServer(serverId) {
        await this.manager.removeServer(serverId);
        this.emit('serverRemoved', serverId);
    }
    async connectServer(serverId) {
        await this.manager.connectServer(serverId);
    }
    async disconnectServer(serverId) {
        await this.manager.disconnectServer(serverId);
    }
    // Tool Execution with Enhanced Features
    async executeTool(serverId, toolName, arguments_ = {}) {
        const startTime = Date.now();
        const execution = {
            serverId,
            toolName,
            arguments: arguments_,
            timestamp: new Date()
        };
        try {
            console.log(`🔧 Executing tool: ${toolName} on server: ${serverId}`);
            const result = await this.manager.callTool(serverId, toolName, arguments_);
            execution.result = result;
            execution.duration = Date.now() - startTime;
            this.executionHistory.push(execution);
            this.emit('toolExecuted', execution);
            console.log(`✅ Tool executed successfully: ${toolName} (${execution.duration}ms)`);
            return result;
        }
        catch (error) {
            execution.error = error instanceof Error ? error.message : String(error);
            execution.duration = Date.now() - startTime;
            this.executionHistory.push(execution);
            this.emit('toolExecutionFailed', execution);
            console.error(`❌ Tool execution failed: ${toolName}`, error);
            throw error;
        }
    }
    // Resource Access
    async readResource(serverId, uri) {
        console.log(`📄 Reading resource: ${uri} from server: ${serverId}`);
        try {
            const result = await this.manager.readResource(serverId, uri);
            this.emit('resourceRead', { serverId, uri, result });
            return result;
        }
        catch (error) {
            console.error(`❌ Resource read failed: ${uri}`, error);
            this.emit('resourceReadFailed', { serverId, uri, error });
            throw error;
        }
    }
    // Prompt Management
    async getPrompt(serverId, promptName, arguments_ = {}) {
        console.log(`💬 Getting prompt: ${promptName} from server: ${serverId}`);
        try {
            const result = await this.manager.getPrompt(serverId, promptName, arguments_);
            this.emit('promptRetrieved', { serverId, promptName, arguments: arguments_, result });
            return result;
        }
        catch (error) {
            console.error(`❌ Prompt retrieval failed: ${promptName}`, error);
            this.emit('promptRetrievalFailed', { serverId, promptName, error });
            throw error;
        }
    }
    // Workflow Management
    async createWorkflow(workflow) {
        const id = `workflow-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const newWorkflow = {
            ...workflow,
            id,
            createdAt: new Date()
        };
        this.workflows.set(id, newWorkflow);
        await this.saveWorkflows();
        this.emit('workflowCreated', newWorkflow);
        return id;
    }
    async executeWorkflow(workflowId, initialData = {}) {
        const workflow = this.workflows.get(workflowId);
        if (!workflow) {
            throw new Error(`Workflow ${workflowId} not found`);
        }
        console.log(`🔄 Executing workflow: ${workflow.name}`);
        try {
            const result = await this.executeWorkflowSteps(workflow.steps, initialData);
            workflow.lastExecuted = new Date();
            await this.saveWorkflows();
            this.emit('workflowExecuted', { workflow, result });
            return result;
        }
        catch (error) {
            console.error(`❌ Workflow execution failed: ${workflow.name}`, error);
            this.emit('workflowExecutionFailed', { workflow, error });
            throw error;
        }
    }
    async executeWorkflowSteps(steps, data) {
        let currentData = { ...data };
        for (const step of steps) {
            try {
                switch (step.type) {
                    case 'tool':
                        if (step.toolName) {
                            const result = await this.executeTool(step.serverId, step.toolName, {
                                ...step.arguments,
                                ...currentData
                            });
                            currentData = { ...currentData, [`${step.id}_result`]: result };
                        }
                        break;
                    case 'resource':
                        if (step.resourceUri) {
                            const result = await this.readResource(step.serverId, step.resourceUri);
                            currentData = { ...currentData, [`${step.id}_result`]: result };
                        }
                        break;
                    case 'prompt':
                        if (step.promptName) {
                            const result = await this.getPrompt(step.serverId, step.promptName, {
                                ...step.arguments,
                                ...currentData
                            });
                            currentData = { ...currentData, [`${step.id}_result`]: result };
                        }
                        break;
                    case 'condition':
                        // Simple condition evaluation (could be enhanced)
                        if (step.condition && !this.evaluateCondition(step.condition, currentData)) {
                            if (step.errorStepId) {
                                // Jump to error step
                                continue;
                            }
                            else {
                                break; // Exit workflow
                            }
                        }
                        break;
                }
            }
            catch (error) {
                if (step.errorStepId) {
                    // Handle error by jumping to error step
                    currentData = { ...currentData, [`${step.id}_error`]: error };
                    continue;
                }
                else {
                    throw error;
                }
            }
        }
        return currentData;
    }
    evaluateCondition(condition, data) {
        // Simple condition evaluation - could be enhanced with a proper expression parser
        try {
            // Replace data references in condition
            let evaluatedCondition = condition;
            for (const [key, value] of Object.entries(data)) {
                evaluatedCondition = evaluatedCondition.replace(new RegExp(`\\$\\{${key}\\}`, 'g'), JSON.stringify(value));
            }
            // Basic safety check - only allow simple comparisons
            if (!/^[a-zA-Z0-9_\s"'<>=!&|().,\[\]{}]+$/.test(evaluatedCondition)) {
                return false;
            }
            return eval(evaluatedCondition);
        }
        catch {
            return false;
        }
    }
    // Discovery and Marketplace Features
    async discoverAvailableServers() {
        // This could be enhanced to discover servers from a marketplace or registry
        return [
            {
                name: 'Exa Search',
                url: 'http://localhost:3001/mcp',
                transport: 'streamable-http',
                enabled: false,
                autoStart: false
            },
            {
                name: 'Firecrawl',
                url: 'http://localhost:3002/mcp',
                transport: 'streamable-http',
                enabled: false,
                autoStart: false
            }
        ];
    }
    // Analytics and Monitoring
    getExecutionHistory(limit = 100) {
        return this.executionHistory.slice(-limit);
    }
    getServerStats() {
        const stats = new Map();
        for (const execution of this.executionHistory) {
            const current = stats.get(execution.serverId) || { toolCalls: 0 };
            current.toolCalls++;
            current.lastActivity = execution.timestamp;
            stats.set(execution.serverId, current);
        }
        return Array.from(stats.entries()).map(([serverId, data]) => ({
            serverId,
            ...data
        }));
    }
    // Getters
    getServers() {
        return this.manager.getServers();
    }
    getConnectedServers() {
        return this.manager.getConnectedServers();
    }
    getAllTools() {
        return this.manager.getAllTools();
    }
    getAllResources() {
        return this.manager.getAllResources();
    }
    getAllPrompts() {
        return this.manager.getAllPrompts();
    }
    getWorkflows() {
        return Array.from(this.workflows.values());
    }
    // Private Methods
    setupManagerEventHandlers() {
        this.manager.on('serverAdded', (server) => this.emit('serverAdded', server));
        this.manager.on('serverRemoved', (serverId) => this.emit('serverRemoved', serverId));
        this.manager.on('serverStatusChanged', (serverId, status) => this.emit('serverStatusChanged', serverId, status));
        this.manager.on('toolsUpdated', (serverId, tools) => this.emit('toolsUpdated', serverId, tools));
        this.manager.on('resourcesUpdated', (serverId, resources) => this.emit('resourcesUpdated', serverId, resources));
        this.manager.on('promptsUpdated', (serverId, prompts) => this.emit('promptsUpdated', serverId, prompts));
    }
    async registerDefaultServers() {
        const defaultServers = [
            {
                name: 'Exa AI',
                url: 'https://api.exa.ai/mcp',
                transport: 'streamable-http',
                enabled: false,
                autoStart: false,
                env: {
                    'EXA_API_KEY': process.env.EXA_API_KEY || ''
                }
            },
            {
                name: 'Firecrawl',
                url: 'https://api.firecrawl.dev/mcp',
                transport: 'streamable-http',
                enabled: false,
                autoStart: false,
                env: {
                    'FIRECRAWL_API_KEY': process.env.FIRECRAWL_API_KEY || ''
                }
            }
        ];
        for (const config of defaultServers) {
            try {
                await this.addServer(config);
                console.log(`📦 Registered default server: ${config.name}`);
            }
            catch (error) {
                console.warn(`⚠️ Failed to register default server ${config.name}:`, error);
            }
        }
    }
    async loadWorkflows() {
        const stored = this.context.globalState.get('mcpWorkflows', []);
        for (const workflow of stored) {
            this.workflows.set(workflow.id, workflow);
        }
    }
    async saveWorkflows() {
        const workflows = Array.from(this.workflows.values());
        await this.context.globalState.update('mcpWorkflows', workflows);
    }
    async dispose() {
        console.log('🧹 Disposing MCP Hub...');
        await this.manager.dispose();
        this.workflows.clear();
        this.executionHistory.length = 0;
        console.log('✅ MCP Hub disposed');
    }
}
exports.MCPHub = MCPHub;
//# sourceMappingURL=hub.js.map