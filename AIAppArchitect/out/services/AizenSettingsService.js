"use strict";
/**
 * Aizen AI Settings Service
 * Manages extension configuration using VS Code's native settings system
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AizenSettingsService = void 0;
const vscode = __importStar(require("vscode"));
class AizenSettingsService {
    constructor() {
        this._onDidChangeSettings = new vscode.EventEmitter();
        this.onDidChangeSettings = this._onDidChangeSettings.event;
        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration('aizen')) {
                this._onDidChangeSettings.fire(this.getSettings());
            }
        });
    }
    static getInstance() {
        if (!AizenSettingsService._instance) {
            AizenSettingsService._instance = new AizenSettingsService();
        }
        return AizenSettingsService._instance;
    }
    /**
     * Get all Aizen settings
     */
    getSettings() {
        const config = vscode.workspace.getConfiguration('aizen');
        return {
            model: {
                provider: config.get('model.provider', 'Anthropic'),
                name: config.get('model.name', 'claude-3-5-sonnet-20241022'),
                temperature: config.get('model.temperature', 0.7),
                maxTokens: config.get('model.maxTokens', 4096)
            },
            api: {
                anthropicKey: config.get('api.anthropicKey', ''),
                openaiKey: config.get('api.openaiKey', ''),
                googleKey: config.get('api.googleKey', '')
            },
            mcp: {
                exaEnabled: config.get('mcp.exaEnabled', true),
                firecrawlEnabled: config.get('mcp.firecrawlEnabled', true),
                exaApiKey: config.get('mcp.exaApiKey', 'f01e507f-cdd2-454d-adcf-545d24035692'),
                firecrawlApiKey: config.get('mcp.firecrawlApiKey', 'fc-73581888d5374a1a99893178925cc8bb')
            },
            ui: {
                theme: config.get('ui.theme', 'auto'),
                fontSize: config.get('ui.fontSize', 'medium')
            },
            features: {
                autoSave: config.get('features.autoSave', true),
                notifications: config.get('features.notifications', true)
            }
        };
    }
    /**
     * Update a specific setting
     */
    async updateSetting(key, value, target) {
        const config = vscode.workspace.getConfiguration('aizen');
        await config.update(key, value, target || vscode.ConfigurationTarget.Global);
        if (this.getSettings().features.notifications) {
            vscode.window.showInformationMessage(`Aizen AI: ${key} updated successfully`);
        }
    }
    /**
     * Update multiple settings at once
     */
    async updateSettings(updates, target) {
        const config = vscode.workspace.getConfiguration('aizen');
        // Update all settings
        const promises = Object.entries(updates).map(([key, value]) => config.update(key, value, target || vscode.ConfigurationTarget.Global));
        await Promise.all(promises);
        if (this.getSettings().features.notifications) {
            vscode.window.showInformationMessage(`Aizen AI: Settings updated successfully`);
        }
    }
    /**
     * Reset all settings to defaults
     */
    async resetSettings() {
        const config = vscode.workspace.getConfiguration('aizen');
        const keys = [
            'model.provider', 'model.name', 'model.temperature', 'model.maxTokens',
            'api.anthropicKey', 'api.openaiKey', 'api.googleKey',
            'mcp.exaEnabled', 'mcp.firecrawlEnabled', 'mcp.exaApiKey', 'mcp.firecrawlApiKey',
            'ui.theme', 'ui.fontSize',
            'features.autoSave', 'features.notifications'
        ];
        const promises = keys.map(key => config.update(key, undefined, vscode.ConfigurationTarget.Global));
        await Promise.all(promises);
        vscode.window.showInformationMessage('Aizen AI: All settings reset to defaults');
    }
    /**
     * Get model-specific settings for AI integration
     */
    getModelConfig() {
        const settings = this.getSettings();
        return {
            provider: settings.model.provider,
            model: settings.model.name,
            temperature: settings.model.temperature,
            maxTokens: settings.model.maxTokens,
            apiKey: this.getApiKeyForProvider(settings.model.provider, settings.api)
        };
    }
    /**
     * Get MCP server configuration
     */
    getMcpConfig() {
        const settings = this.getSettings();
        return {
            exa: {
                enabled: settings.mcp.exaEnabled,
                apiKey: settings.mcp.exaApiKey
            },
            firecrawl: {
                enabled: settings.mcp.firecrawlEnabled,
                apiKey: settings.mcp.firecrawlApiKey
            }
        };
    }
    getApiKeyForProvider(provider, apiKeys) {
        switch (provider) {
            case 'Anthropic': return apiKeys.anthropicKey;
            case 'OpenAI': return apiKeys.openaiKey;
            case 'Google': return apiKeys.googleKey;
            default: return '';
        }
    }
    /**
     * Validate settings and show warnings for missing required values
     */
    validateSettings() {
        const settings = this.getSettings();
        const warnings = [];
        // Check API key for selected provider
        const apiKey = this.getApiKeyForProvider(settings.model.provider, settings.api);
        if (!apiKey) {
            warnings.push(`Missing API key for ${settings.model.provider}`);
        }
        // Check MCP API keys if enabled
        if (settings.mcp.exaEnabled && !settings.mcp.exaApiKey) {
            warnings.push('Exa MCP server is enabled but API key is missing');
        }
        if (settings.mcp.firecrawlEnabled && !settings.mcp.firecrawlApiKey) {
            warnings.push('Firecrawl MCP server is enabled but API key is missing');
        }
        return {
            isValid: warnings.length === 0,
            warnings
        };
    }
    /**
     * Open VS Code settings to specific Aizen setting
     */
    async openVSCodeSettings(settingId) {
        const settingToOpen = settingId ? `aizen.${settingId}` : 'aizen';
        await vscode.commands.executeCommand('workbench.action.openSettings', settingToOpen);
    }
}
exports.AizenSettingsService = AizenSettingsService;
//# sourceMappingURL=AizenSettingsService.js.map