"use strict";
/**
 * Aizen Tree Data Provider
 * Manages the sidebar tree view for AI Agents and Performance metrics
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AizenMetricsTreeDataProvider = exports.AizenAgentsTreeDataProvider = exports.AizenTreeItem = void 0;
const vscode = __importStar(require("vscode"));
class AizenTreeItem extends vscode.TreeItem {
    constructor(label, collapsibleState, contextValue, command, iconPath, tooltip, description) {
        super(label, collapsibleState);
        this.contextValue = contextValue;
        this.command = command;
        this.iconPath = iconPath;
        this.tooltip = tooltip;
        this.description = description;
    }
}
exports.AizenTreeItem = AizenTreeItem;
class AizenAgentsTreeDataProvider {
    constructor(integrationService) {
        this.integrationService = integrationService;
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    getTreeItem(element) {
        return element;
    }
    async getChildren(element) {
        if (!element) {
            // Root level items
            return [
                new AizenTreeItem('Active Agents', vscode.TreeItemCollapsibleState.Expanded, 'agentsGroup', undefined, { id: 'robot', color: undefined }, 'Currently active AI agents'),
                new AizenTreeItem('Agent Types', vscode.TreeItemCollapsibleState.Collapsed, 'agentTypesGroup', undefined, { id: 'symbol-class', color: undefined }, 'Available agent types'),
                new AizenTreeItem('Quick Actions', vscode.TreeItemCollapsibleState.Collapsed, 'actionsGroup', undefined, { id: 'zap', color: undefined }, 'Quick agent actions')
            ];
        }
        if (element.contextValue === 'agentsGroup') {
            return this.getActiveAgents();
        }
        if (element.contextValue === 'agentTypesGroup') {
            return this.getAgentTypes();
        }
        if (element.contextValue === 'actionsGroup') {
            return this.getQuickActions();
        }
        return [];
    }
    async getActiveAgents() {
        try {
            const agents = await this.integrationService.getAllAgents();
            if (agents.length === 0) {
                return [
                    new AizenTreeItem('No active agents', vscode.TreeItemCollapsibleState.None, 'noAgents', undefined, { id: 'info', color: undefined }, 'Create an agent to get started')
                ];
            }
            return agents.map(agent => {
                const statusIcon = this.getStatusIcon(agent.status);
                const statusColor = this.getStatusColor(agent.status);
                return new AizenTreeItem(agent.name, vscode.TreeItemCollapsibleState.None, 'agent', {
                    command: 'aizen.showAgentDetails',
                    title: 'Show Agent Details',
                    arguments: [agent]
                }, statusIcon, `${agent.type} agent - Status: ${agent.status}${agent.currentTask ? `\nCurrent task: ${agent.currentTask}` : ''}`, `${agent.status} • ${agent.type}`);
            });
        }
        catch (error) {
            console.error('Failed to get agents:', error);
            return [
                new AizenTreeItem('Error loading agents', vscode.TreeItemCollapsibleState.None, 'error', undefined, { id: 'error', color: undefined }, 'Failed to load agents')
            ];
        }
    }
    getAgentTypes() {
        const agentTypes = [
            { name: 'Code Generation', type: 'code-generation', icon: 'code' },
            { name: 'Conversational', type: 'conversational', icon: 'comment-discussion' },
            { name: 'Debug Assistant', type: 'debug', icon: 'bug' },
            { name: 'Test Generator', type: 'test-generation', icon: 'beaker' },
            { name: 'Documentation', type: 'documentation', icon: 'book' },
            { name: 'Code Review', type: 'code-review', icon: 'eye' }
        ];
        return agentTypes.map(agentType => new AizenTreeItem(agentType.name, vscode.TreeItemCollapsibleState.None, 'agentType', {
            command: 'aizen.createAgentOfType',
            title: 'Create Agent',
            arguments: [agentType.type]
        }, { id: agentType.icon, color: undefined }, `Create a ${agentType.name} agent`, 'Click to create'));
    }
    getQuickActions() {
        return [
            new AizenTreeItem('Enable Swarm Intelligence', vscode.TreeItemCollapsibleState.None, 'action', {
                command: 'aizen.enableSwarmIntelligence',
                title: 'Enable Swarm Intelligence'
            }, { id: 'organization', color: undefined }, 'Enable collaborative agent swarm'),
            new AizenTreeItem('Enable Evolution', vscode.TreeItemCollapsibleState.None, 'action', {
                command: 'aizen.enableEvolution',
                title: 'Enable Evolution'
            }, { id: 'graph', color: undefined }, 'Enable agent self-improvement'),
            new AizenTreeItem('View Performance Metrics', vscode.TreeItemCollapsibleState.None, 'action', {
                command: 'aizen.showMetrics',
                title: 'Show Metrics'
            }, { id: 'dashboard', color: undefined }, 'View agent performance metrics')
        ];
    }
    getStatusIcon(status) {
        switch (status) {
            case 'idle':
                return { id: 'circle-outline', color: undefined };
            case 'busy':
                return { id: 'loading~spin', color: undefined };
            case 'error':
                return { id: 'error', color: undefined };
            default:
                return { id: 'circle', color: undefined };
        }
    }
    getStatusColor(status) {
        switch (status) {
            case 'idle':
                return 'charts.green';
            case 'busy':
                return 'charts.yellow';
            case 'error':
                return 'charts.red';
            default:
                return 'charts.blue';
        }
    }
}
exports.AizenAgentsTreeDataProvider = AizenAgentsTreeDataProvider;
class AizenMetricsTreeDataProvider {
    constructor(integrationService) {
        this.integrationService = integrationService;
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    getTreeItem(element) {
        return element;
    }
    async getChildren(element) {
        if (!element) {
            try {
                const metrics = await this.integrationService.getMetrics();
                return [
                    new AizenTreeItem(`Total Agents: ${metrics.totalAgents}`, vscode.TreeItemCollapsibleState.None, 'metric', undefined, { id: 'organization', color: undefined }, 'Total number of agents created'),
                    new AizenTreeItem(`Active: ${metrics.activeAgents}`, vscode.TreeItemCollapsibleState.None, 'metric', undefined, { id: 'pulse', color: undefined }, 'Currently active agents'),
                    new AizenTreeItem(`Completed Tasks: ${metrics.completedTasks}`, vscode.TreeItemCollapsibleState.None, 'metric', undefined, { id: 'check', color: undefined }, 'Successfully completed tasks'),
                    new AizenTreeItem(`Failed Tasks: ${metrics.failedTasks}`, vscode.TreeItemCollapsibleState.None, 'metric', undefined, { id: 'x', color: undefined }, 'Failed tasks'),
                    new AizenTreeItem(`Avg Execution: ${metrics.averageExecutionTime.toFixed(2)}s`, vscode.TreeItemCollapsibleState.None, 'metric', undefined, { id: 'clock', color: undefined }, 'Average task execution time'),
                    new AizenTreeItem(`Swarm Efficiency: ${(metrics.swarmEfficiency * 100).toFixed(1)}%`, vscode.TreeItemCollapsibleState.None, 'metric', undefined, { id: 'graph', color: undefined }, 'Swarm intelligence efficiency'),
                    new AizenTreeItem(`Evolution Score: ${(metrics.evolutionScore * 100).toFixed(1)}%`, vscode.TreeItemCollapsibleState.None, 'metric', undefined, { id: 'trending-up', color: undefined }, 'Agent evolution and improvement score')
                ];
            }
            catch (error) {
                return [
                    new AizenTreeItem('Error loading metrics', vscode.TreeItemCollapsibleState.None, 'error', undefined, { id: 'error', color: undefined }, 'Failed to load performance metrics')
                ];
            }
        }
        return [];
    }
}
exports.AizenMetricsTreeDataProvider = AizenMetricsTreeDataProvider;
//# sourceMappingURL=AizenTreeDataProvider.js.map