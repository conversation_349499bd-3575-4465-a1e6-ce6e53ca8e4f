const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Aizen AI Extension UI');
console.log('================================');

// Test 1: Check if all UI files exist
console.log('\n1. Checking UI file structure...');
const uiFiles = [
    'src/ui/index.html',
    'src/ui/styles.css', 
    'src/ui/chat.css',
    'src/ui/main.ts',
    'out/ui/main.js'
];

let allFilesExist = true;
uiFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file} exists`);
    } else {
        console.log(`❌ ${file} missing`);
        allFilesExist = false;
    }
});

// Test 2: Check HTML template placeholders
console.log('\n2. Checking HTML template...');
const htmlContent = fs.readFileSync('src/ui/index.html', 'utf8');
const placeholders = ['{{CSS_URI}}', '{{CHAT_CSS_URI}}', '{{SCRIPT_URI}}', '{{THEME_CLASS}}'];
placeholders.forEach(placeholder => {
    if (htmlContent.includes(placeholder)) {
        console.log(`✅ ${placeholder} placeholder found`);
    } else {
        console.log(`❌ ${placeholder} placeholder missing`);
    }
});

// Test 3: Check CSS variables
console.log('\n3. Checking CSS variables...');
const cssContent = fs.readFileSync('out/ui/styles.css', 'utf8');
const cssVariables = ['--glass-primary', '--gradient-primary', '--font-primary', '--space-4'];
cssVariables.forEach(variable => {
    if (cssContent.includes(variable)) {
        console.log(`✅ ${variable} CSS variable found`);
    } else {
        console.log(`❌ ${variable} CSS variable missing`);
    }
});

// Test 4: Check HugeIcons integration
console.log('\n4. Checking HugeIcons integration...');
const hugeIconClasses = ['hgi-stroke', 'hgi-user-circle', 'hgi-artificial-intelligence-02'];
hugeIconClasses.forEach(iconClass => {
    if (htmlContent.includes(iconClass)) {
        console.log(`✅ ${iconClass} icon class found`);
    } else {
        console.log(`❌ ${iconClass} icon class missing`);
    }
});

// Test 5: Check TypeScript compilation
console.log('\n5. Checking TypeScript compilation...');
if (fs.existsSync('out/ui/main.js')) {
    const jsContent = fs.readFileSync('out/ui/main.js', 'utf8');
    if (jsContent.includes('AizenUIController')) {
        console.log('✅ TypeScript compiled successfully');
        console.log('✅ AizenUIController class found in compiled JS');
    } else {
        console.log('❌ AizenUIController class not found in compiled JS');
    }
} else {
    console.log('❌ Compiled JavaScript file not found');
}

// Test 6: Check provider configuration
console.log('\n6. Checking provider configuration...');
const providerContent = fs.readFileSync('src/providers/AizenChatViewProvider.ts', 'utf8');
if (providerContent.includes('getHtmlForWebview') && 
    providerContent.includes('getFallbackHtml') &&
    providerContent.includes('handleWebviewMessage')) {
    console.log('✅ Provider methods found');
} else {
    console.log('❌ Provider methods missing');
}

// Test 7: Check package.json configuration
console.log('\n7. Checking package.json configuration...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
if (packageJson.contributes && 
    packageJson.contributes.views && 
    packageJson.contributes.views['aizen-ai'] &&
    packageJson.contributes.views['aizen-ai'].some(view => view.id === 'aizen.chatView')) {
    console.log('✅ Webview properly configured in package.json');
} else {
    console.log('❌ Webview configuration missing in package.json');
}

console.log('\n================================');
if (allFilesExist) {
    console.log('🎉 All UI tests passed! Extension ready for testing.');
    console.log('\n📋 To test the extension:');
    console.log('1. Press F5 in VS Code to run the extension');
    console.log('2. Look for the Aizen AI icon in the Activity Bar');
    console.log('3. Click on it to open the AI Chat webview');
    console.log('4. Test the modern UI with Liquid Glass effects');
} else {
    console.log('⚠️  Some files are missing. Please check the file structure.');
}
console.log('================================');
