const fs = require('fs');
const path = require('path');

console.log('🧪 COMPREHENSIVE AIZEN AI EXTENSION TEST');
console.log('==========================================');

let allTestsPassed = true;
const testResults = [];

function runTest(testName, testFunction) {
    try {
        const result = testFunction();
        if (result) {
            console.log(`✅ ${testName}`);
            testResults.push({ name: testName, status: 'PASS' });
        } else {
            console.log(`❌ ${testName}`);
            testResults.push({ name: testName, status: 'FAIL' });
            allTestsPassed = false;
        }
    } catch (error) {
        console.log(`❌ ${testName} - Error: ${error.message}`);
        testResults.push({ name: testName, status: 'ERROR', error: error.message });
        allTestsPassed = false;
    }
}

console.log('\n1. EXTENSION STRUCTURE TESTS');
console.log('-----------------------------');

runTest('Extension entry point exists', () => {
    return fs.existsSync('src/extension.ts');
});

runTest('Package.json is valid', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return pkg.name && pkg.main && pkg.contributes;
});

runTest('Extension compiles successfully', () => {
    return fs.existsSync('out/extension.js');
});

runTest('Webview provider exists', () => {
    return fs.existsSync('src/providers/AizenChatViewProvider.ts');
});

console.log('\n2. UI FILES TESTS');
console.log('------------------');

runTest('HTML template exists', () => {
    return fs.existsSync('out/ui/index.html');
});

runTest('Main CSS exists', () => {
    return fs.existsSync('out/ui/styles.css');
});

runTest('Chat CSS exists', () => {
    return fs.existsSync('out/ui/chat.css');
});

runTest('TypeScript UI compiled', () => {
    return fs.existsSync('out/ui/main.js');
});

console.log('\n3. CONTENT VALIDATION TESTS');
console.log('----------------------------');

runTest('HTML contains required placeholders', () => {
    const html = fs.readFileSync('out/ui/index.html', 'utf8');
    return html.includes('{{CSS_URI}}') && 
           html.includes('{{SCRIPT_URI}}') && 
           html.includes('{{THEME_CLASS}}');
});

runTest('CSS contains glass morphism variables', () => {
    const css = fs.readFileSync('out/ui/styles.css', 'utf8');
    return css.includes('--glass-primary') &&
           css.includes('--gradient-primary') &&
           css.includes('backdrop-filter');
});

runTest('HTML contains HugeIcons classes', () => {
    const html = fs.readFileSync('out/ui/index.html', 'utf8');
    return html.includes('hgi-stroke') && 
           html.includes('hgi-artificial-intelligence-02');
});

runTest('JavaScript contains UI controller', () => {
    const js = fs.readFileSync('out/ui/main.js', 'utf8');
    return js.includes('AizenUIController') || js.includes('AizenUI');
});

console.log('\n4. VS CODE INTEGRATION TESTS');
console.log('-----------------------------');

runTest('Package.json has correct activation events', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return pkg.activationEvents && pkg.activationEvents.includes('onView:aizen.chatView');
});

runTest('Webview is properly configured', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const views = pkg.contributes?.views?.['aizen-ai'];
    return views && views.some(view => view.id === 'aizen.chatView');
});

runTest('Provider handles webview messages', () => {
    const provider = fs.readFileSync('src/providers/AizenChatViewProvider.ts', 'utf8');
    return provider.includes('handleWebviewMessage') && 
           provider.includes('sendMessage') && 
           provider.includes('getHtmlForWebview');
});

runTest('CSP allows required resources', () => {
    const provider = fs.readFileSync('src/providers/AizenChatViewProvider.ts', 'utf8');
    return provider.includes('fonts.googleapis.com') && 
           provider.includes('cdn.jsdelivr.net');
});

console.log('\n5. FILE PATH TESTS');
console.log('------------------');

runTest('Provider uses correct file paths', () => {
    const provider = fs.readFileSync('src/providers/AizenChatViewProvider.ts', 'utf8');
    return provider.includes("'out', 'ui'") && 
           !provider.includes("'src', 'ui'");
});

runTest('All UI files are accessible', () => {
    const requiredFiles = [
        'out/ui/index.html',
        'out/ui/styles.css', 
        'out/ui/chat.css',
        'out/ui/main.js'
    ];
    return requiredFiles.every(file => fs.existsSync(file));
});

console.log('\n6. FUNCTIONALITY TESTS');
console.log('----------------------');

runTest('HTML has all required UI elements', () => {
    const html = fs.readFileSync('out/ui/index.html', 'utf8');
    return html.includes('top-nav') && 
           html.includes('chat-messages') && 
           html.includes('chat-input') && 
           html.includes('mode-selector');
});

runTest('CSS has responsive design', () => {
    const css = fs.readFileSync('out/ui/styles.css', 'utf8');
    return css.includes('@media') && css.includes('max-width');
});

runTest('JavaScript has event handlers', () => {
    const js = fs.readFileSync('out/ui/main.js', 'utf8');
    return js.includes('addEventListener') && 
           (js.includes('sendMessage') || js.includes('handleNavAction'));
});

console.log('\n==========================================');
console.log('TEST SUMMARY');
console.log('==========================================');

const passedTests = testResults.filter(t => t.status === 'PASS').length;
const failedTests = testResults.filter(t => t.status === 'FAIL').length;
const errorTests = testResults.filter(t => t.status === 'ERROR').length;

console.log(`✅ Passed: ${passedTests}`);
console.log(`❌ Failed: ${failedTests}`);
console.log(`⚠️  Errors: ${errorTests}`);
console.log(`📊 Total: ${testResults.length}`);

if (allTestsPassed) {
    console.log('\n🎉 ALL TESTS PASSED! Extension is ready for VS Code testing.');
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Press F5 in VS Code to launch Extension Development Host');
    console.log('2. Look for "Aizen AI" in the Activity Bar (left sidebar)');
    console.log('3. Click the icon to open the AI Chat webview');
    console.log('4. Test the modern UI with Liquid Glass effects');
    console.log('5. Verify all buttons, dropdowns, and input work correctly');
    console.log('6. Check browser dev tools for any console errors');
} else {
    console.log('\n⚠️  SOME TESTS FAILED. Please fix the issues above before testing in VS Code.');
    
    const failedTestNames = testResults
        .filter(t => t.status !== 'PASS')
        .map(t => `- ${t.name}${t.error ? ` (${t.error})` : ''}`)
        .join('\n');
    
    console.log('\nFailed tests:');
    console.log(failedTestNames);
}

console.log('\n==========================================');
