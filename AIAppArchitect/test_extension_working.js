#!/usr/bin/env node

/**
 * Test script to verify Aizen AI Extension is working properly
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Aizen AI Extension - Working Status');
console.log('='.repeat(50));

let passed = 0;
let failed = 0;

function runTest(name, testFn) {
    try {
        const result = testFn();
        if (result) {
            console.log(`✅ ${name}`);
            passed++;
        } else {
            console.log(`❌ ${name}`);
            failed++;
        }
    } catch (error) {
        console.log(`❌ ${name} - Error: ${error.message}`);
        failed++;
    }
}

console.log('\n1. EXTENSION PACKAGE TESTS');
console.log('---------------------------');

runTest('Extension VSIX exists', () => {
    return fs.existsSync('aizen-revolutionary-ai-2.0.0.vsix');
});

runTest('Package.json is valid', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return pkg.name === 'aizen-revolutionary-ai' && 
           pkg.main === './out/extension.js' &&
           pkg.contributes &&
           pkg.contributes.viewsContainers &&
           pkg.contributes.views;
});

runTest('Extension entry point compiled', () => {
    return fs.existsSync('out/extension.js');
});

console.log('\n2. UI COMPONENT TESTS');
console.log('---------------------');

runTest('HTML template exists', () => {
    return fs.existsSync('out/ui/index.html');
});

runTest('CSS styles exist', () => {
    return fs.existsSync('out/ui/styles.css') && 
           fs.existsSync('out/ui/chat.css');
});

runTest('JavaScript UI exists', () => {
    return fs.existsSync('out/ui/main.js');
});

runTest('HTML template has CSP', () => {
    const html = fs.readFileSync('out/ui/index.html', 'utf8');
    return html.includes('Content-Security-Policy') &&
           html.includes('{{CSP_SOURCE}}') &&
           html.includes('nonce="{{NONCE}}"');
});

console.log('\n3. PROVIDER TESTS');
console.log('-----------------');

runTest('Chat view provider compiled', () => {
    return fs.existsSync('out/providers/AizenChatViewProvider.js');
});

runTest('Integration service compiled', () => {
    return fs.existsSync('out/services/AizenIntegrationService.js');
});

console.log('\n4. MEDIA ASSETS TESTS');
console.log('---------------------');

runTest('Extension icon exists', () => {
    return fs.existsSync('media/icons/extension-icon-128.png');
});

runTest('Activity bar icon exists', () => {
    return fs.existsSync('media/icons/aizen-logo-white.svg');
});

runTest('All required icons exist', () => {
    const requiredIcons = [
        'media/icons/aizen-logo.svg',
        'media/icons/aizen-logo-white.svg',
        'media/icons/extension-icon-128.png'
    ];
    return requiredIcons.every(icon => fs.existsSync(icon));
});

console.log('\n5. CONFIGURATION TESTS');
console.log('----------------------');

runTest('Package.json has correct view configuration', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const views = pkg.contributes.views;
    return views && 
           views['aizen-ai'] && 
           views['aizen-ai'].length > 0 &&
           views['aizen-ai'][0].id === 'aizen.chatView';
});

runTest('Package.json has commands', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const commands = pkg.contributes.commands;
    return commands && 
           commands.length > 0 &&
           commands.some(cmd => cmd.command === 'aizen.showChatView');
});

runTest('Package.json has keybindings', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const keybindings = pkg.contributes.keybindings;
    return keybindings && 
           keybindings.length > 0 &&
           keybindings.some(kb => kb.key === 'ctrl+k');
});

console.log('\n6. DEPENDENCY TESTS');
console.log('-------------------');

runTest('Node modules installed', () => {
    return fs.existsSync('node_modules') && 
           fs.existsSync('node_modules/@types/vscode');
});

runTest('TypeScript compiled successfully', () => {
    const stats = fs.statSync('out/extension.js');
    const sourceStats = fs.statSync('src/extension.ts');
    return stats.mtime >= sourceStats.mtime;
});

console.log('\n' + '='.repeat(50));
console.log(`📊 TEST RESULTS: ${passed} passed, ${failed} failed`);

if (failed === 0) {
    console.log('🎉 All tests passed! Extension should be working properly.');
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Open VS Code');
    console.log('2. Look for "Aizen AI" in the activity bar (left sidebar)');
    console.log('3. Click on it to open the AI Chat view');
    console.log('4. Try using Ctrl+K to open the chat');
    console.log('5. Check the VS Code Developer Console (Help > Toggle Developer Tools) for any errors');
} else {
    console.log('⚠️  Some tests failed. Please check the issues above.');
    process.exit(1);
}
