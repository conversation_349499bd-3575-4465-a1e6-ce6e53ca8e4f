<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aizen AI Chat - Demo with Messages</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@hugeicons/icons@latest/css/hugeicons.css">
    <link rel="stylesheet" href="src/ui/styles.css">
    <link rel="stylesheet" href="src/ui/chat.css">
</head>
<body class="vscode-dark">
    <div id="root">
        <!-- Top Navigation Bar -->
        <header class="top-nav glass">
            <div class="nav-left">
                <div class="user-avatar glass">
                    <i class="hgi-stroke hgi-user-circle"></i>
                </div>
                <div class="nav-tabs">
                    <button class="tab-btn active" data-tab="actions">
                        <i class="hgi-stroke hgi-flash"></i>
                        <span>Actions</span>
                    </button>
                    <button class="tab-btn" data-tab="flows">
                        <i class="hgi-stroke hgi-workflow-square-02"></i>
                        <span>Flows</span>
                    </button>
                </div>
            </div>
            <div class="nav-right">
                <button class="nav-btn glass" title="New Chat" data-action="new-chat">
                    <i class="hgi-stroke hgi-add-01"></i>
                </button>
                <button class="nav-btn glass" title="Query History" data-action="history">
                    <i class="hgi-stroke hgi-clock-01"></i>
                </button>
                <button class="nav-btn glass" title="MCP Store" data-action="mcp-store">
                    <i class="hgi-stroke hgi-shopping-bag-01"></i>
                </button>
                <button class="nav-btn glass" title="Advanced Command Mode" data-action="advanced-mode">
                    <i class="hgi-stroke hgi-settings-02"></i>
                </button>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="main-content">
            <div class="chat-container custom-scroll">
                <div class="chat-messages" id="chatMessages">
                    <!-- Sample Chat Messages -->
                    <div class="chat-message user animate-in">
                        <div class="message-header">
                            <span class="message-sender">You</span>
                            <span class="message-time">2:30 PM</span>
                        </div>
                        <div class="message-content">Create a modern React component with TypeScript</div>
                    </div>

                    <div class="chat-message assistant animate-in">
                        <div class="message-header">
                            <span class="message-sender">Aizen AI</span>
                            <span class="message-time">2:30 PM</span>
                        </div>
                        <div class="message-content">
                            I'll create a modern React component with TypeScript for you. Here's a clean, functional component:

                            <pre><code>import React, { useState } from 'react';

interface ButtonProps {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}

export const ModernButton: React.FC&lt;ButtonProps&gt; = ({
  label,
  onClick,
  variant = 'primary',
  disabled = false
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    &lt;button
      className={`modern-btn ${variant}`}
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    &gt;
      {label}
    &lt;/button&gt;
  );
};</code></pre>

                            This component includes **TypeScript interfaces**, **state management**, and **modern React patterns**. Would you like me to add styling or additional features?
                        </div>
                        <div class="message-actions">
                            <button class="message-action">Copy</button>
                            <button class="message-action">Edit</button>
                            <button class="message-action">Regenerate</button>
                        </div>
                    </div>

                    <div class="chat-message user animate-in">
                        <div class="message-header">
                            <span class="message-sender">You</span>
                            <span class="message-time">2:32 PM</span>
                        </div>
                        <div class="message-content">Add some modern CSS with glassmorphism effects</div>
                    </div>

                    <div class="typing-indicator">
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Chat Input Area -->
        <footer class="chat-input-area glass">
            <div class="input-container">
                <div class="input-left">
                    <button class="input-btn glass" title="Upload Media">
                        <i class="hgi-stroke hgi-attachment-02"></i>
                    </button>
                    <div class="mode-selector">
                        <button class="mode-btn glass" id="modeButton">
                            <i class="hgi-stroke hgi-magic-wand-01"></i>
                            <span class="mode-text">Auto</span>
                            <i class="hgi-stroke hgi-arrow-down-01"></i>
                        </button>
                        <div class="mode-dropdown glass show" id="modeDropdown">
                            <div class="mode-option active" data-mode="auto">
                                <i class="hgi-stroke hgi-magic-wand-01"></i>
                                <span>Auto Mode</span>
                            </div>
                            <div class="mode-option" data-mode="research">
                                <i class="hgi-stroke hgi-search-01"></i>
                                <span>Research Mode</span>
                            </div>
                            <div class="mode-option" data-mode="debug">
                                <i class="hgi-stroke hgi-bug-01"></i>
                                <span>Debug Mode</span>
                            </div>
                            <div class="mode-option" data-mode="deep">
                                <i class="hgi-stroke hgi-brain"></i>
                                <span>Deep Mode</span>
                            </div>
                            <div class="mode-option" data-mode="mcp">
                                <i class="hgi-stroke hgi-hierarchy-square-02"></i>
                                <span>MCP Mode</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="input-center">
                    <input type="text" 
                           class="chat-input glass" 
                           placeholder="Ask anything with context @ or / for your custom agents"
                           id="chatInput"
                           value="Perfect! The glassmorphism effects look amazing 🚀">
                </div>
                <div class="input-right">
                    <div class="agent-mode-selector">
                        <button class="agent-mode-btn glass" id="agentModeButton">
                            <i class="hgi-stroke hgi-message-question"></i>
                            <span class="agent-mode-text">Ask</span>
                            <i class="hgi-stroke hgi-arrow-down-01"></i>
                        </button>
                        <div class="agent-mode-dropdown glass" id="agentModeDropdown">
                            <div class="agent-mode-option active" data-mode="ask">
                                <i class="hgi-stroke hgi-message-question"></i>
                                <span>Ask Mode</span>
                            </div>
                            <div class="agent-mode-option" data-mode="agent">
                                <i class="hgi-stroke hgi-artificial-intelligence-02"></i>
                                <span>Agent Mode</span>
                            </div>
                            <div class="agent-mode-option" data-mode="god">
                                <i class="hgi-stroke hgi-crown"></i>
                                <span>God Mode</span>
                            </div>
                        </div>
                    </div>
                    <div class="auto-toggle">
                        <label class="toggle-switch">
                            <input type="checkbox" id="autoToggle" checked>
                            <span class="toggle-slider glass"></span>
                        </label>
                        <span class="toggle-label">Auto</span>
                    </div>
                    <button class="send-btn glass" id="sendButton">
                        <i class="hgi-stroke hgi-sent"></i>
                    </button>
                </div>
            </div>
        </footer>
    </div>

    <!-- Success Notification -->
    <div class="notification success glass show">
        <i class="hgi-stroke hgi-check-circle"></i>
        <span>UI successfully loaded with 2025 design trends!</span>
    </div>

    <script>
        // Simple demo functionality
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎉 Aizen AI UI Demo Loaded!');
            
            // Add some interactive effects
            document.querySelectorAll('.glass').forEach(element => {
                element.addEventListener('mouseenter', () => {
                    element.style.transform = 'translateY(-2px)';
                });
                element.addEventListener('mouseleave', () => {
                    element.style.transform = 'translateY(0)';
                });
            });

            // Hide notification after 5 seconds
            setTimeout(() => {
                document.querySelector('.notification').classList.remove('show');
            }, 5000);
        });
    </script>
</body>
</html>
