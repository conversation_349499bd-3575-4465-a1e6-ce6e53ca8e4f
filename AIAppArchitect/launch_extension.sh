#!/bin/bash

echo "🚀 Launching Aizen AI Extension in VS Code Development Mode"
echo "=========================================================="

# Ensure we're in the correct directory
cd "$(dirname "$0")"

echo "📁 Current directory: $(pwd)"
echo "📦 Extension name: $(node -e "console.log(JSON.parse(require('fs').readFileSync('package.json', 'utf8')).name)")"

# Build the extension first
echo "🔨 Building extension..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "🎯 Launching VS Code Extension Development Host..."
    echo ""
    echo "📋 What to expect:"
    echo "1. A new VS Code window will open (Extension Development Host)"
    echo "2. Look for 'Aizen AI' icon in the Activity Bar (left sidebar)"
    echo "3. Click the icon to open the AI Chat webview"
    echo "4. You should see the modern Liquid Glass UI"
    echo ""
    
    # Launch VS Code with extension development
    code --extensionDevelopmentPath="$(pwd)" --new-window
    
    echo "🎉 VS Code launched! Check the new window for the Aizen AI extension."
else
    echo "❌ Build failed! Please check the errors above."
    exit 1
fi
