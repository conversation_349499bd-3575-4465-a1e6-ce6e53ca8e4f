<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Settings Button</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1e1e1e;
            color: #fff;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .nav-btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .nav-btn:hover {
            background: #005a9e;
        }
        .log {
            background: #2d2d2d;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Aizen AI Settings Button Test</h1>
        
        <p>This is a test page to verify the settings button functionality.</p>
        
        <button class="nav-btn" data-action="settings" id="settingsBtn">
            ⚙️ Settings
        </button>
        
        <button class="nav-btn" onclick="testVSCodeAPI()">
            🧪 Test VS Code API
        </button>
        
        <button class="nav-btn" onclick="clearLog()">
            🗑️ Clear Log
        </button>
        
        <div class="log" id="log">
Click the settings button to test functionality...
        </div>
    </div>

    <script>
        // Mock VS Code API for testing
        const vscode = {
            postMessage: function(message) {
                log('📤 Message sent to extension: ' + JSON.stringify(message, null, 2));
                
                // Simulate extension response
                setTimeout(() => {
                    log('📥 Simulated extension response: Command received');
                }, 100);
            }
        };

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = 'Log cleared...\n';
        }

        function testVSCodeAPI() {
            log('🧪 Testing VS Code API...');
            if (typeof vscode !== 'undefined') {
                log('✅ VS Code API is available');
            } else {
                log('❌ VS Code API is not available');
            }
        }

        // Settings button event listener
        document.getElementById('settingsBtn').addEventListener('click', function(e) {
            log('🔘 Settings button clicked');
            
            const button = e.target;
            const action = button.getAttribute('data-action');
            log('🔧 Button action: ' + action);
            
            if (action === 'settings') {
                log('⚙️ Settings action triggered, sending message to extension');
                
                if (typeof vscode !== 'undefined') {
                    vscode.postMessage({
                        command: 'navAction',
                        data: { action: 'settings' }
                    });
                } else {
                    log('❌ vscode API not available');
                }
            }
        });

        // Initialize
        log('🚀 Test page loaded');
        testVSCodeAPI();
    </script>
</body>
</html>
