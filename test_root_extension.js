#!/usr/bin/env node

/**
 * Test script to verify root extension structure
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Root Extension Structure');
console.log('='.repeat(40));

let passed = 0;
let failed = 0;

function runTest(name, testFn) {
    try {
        const result = testFn();
        if (result) {
            console.log(`✅ ${name}`);
            passed++;
        } else {
            console.log(`❌ ${name}`);
            failed++;
        }
    } catch (error) {
        console.log(`❌ ${name} - Error: ${error.message}`);
        failed++;
    }
}

console.log('\n1. PACKAGE.JSON TESTS');
console.log('---------------------');

runTest('Package.json exists', () => {
    return fs.existsSync('package.json');
});

runTest('Package.json has engines', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return pkg.engines && pkg.engines.vscode;
});

runTest('Package.json has main entry', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return pkg.main === './out/extension.js';
});

runTest('Package.json has contributes', () => {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return pkg.contributes && pkg.contributes.views;
});

console.log('\n2. COMPILED FILES TESTS');
console.log('-----------------------');

runTest('Extension.js exists', () => {
    return fs.existsSync('out/extension.js');
});

runTest('Providers directory exists', () => {
    return fs.existsSync('out/providers');
});

runTest('Services directory exists', () => {
    return fs.existsSync('out/services');
});

runTest('UI directory exists', () => {
    return fs.existsSync('out/ui');
});

console.log('\n3. MEDIA FILES TESTS');
console.log('--------------------');

runTest('Media directory exists', () => {
    return fs.existsSync('media');
});

runTest('Icons directory exists', () => {
    return fs.existsSync('media/icons');
});

runTest('Activity bar icon exists', () => {
    return fs.existsSync('media/icons/aizen-logo-white.svg');
});

runTest('Command icon exists', () => {
    return fs.existsSync('media/icons/aizen-logo.svg');
});

console.log('\n4. UI ASSETS TESTS');
console.log('------------------');

runTest('HTML template exists', () => {
    return fs.existsSync('out/ui/index.html');
});

runTest('CSS files exist', () => {
    return fs.existsSync('out/ui/styles.css') && 
           fs.existsSync('out/ui/chat.css');
});

runTest('JavaScript UI exists', () => {
    return fs.existsSync('out/ui/main.js');
});

console.log('\n' + '='.repeat(40));
console.log(`📊 TEST RESULTS: ${passed} passed, ${failed} failed`);

if (failed === 0) {
    console.log('🎉 All tests passed! Extension structure is correct.');
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Reload VS Code window (Ctrl+Shift+P → "Developer: Reload Window")');
    console.log('2. Look for "Aizen AI" in the activity bar');
    console.log('3. Click on it to open the AI Chat view');
    console.log('4. The beautiful UI should now load!');
} else {
    console.log('⚠️  Some tests failed. Please check the issues above.');
    process.exit(1);
}
