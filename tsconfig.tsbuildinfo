{"root": ["./src/extension.ts", "./src/providers/AizenChatViewProvider.ts", "./src/providers/AizenTreeDataProvider.ts", "./src/services/AizenIntegrationService.ts", "./src/services/RustBackendService.ts", "./src/ui/react/App.tsx", "./src/ui/react/index.tsx", "./src/ui/react/components/client-chat-page.tsx", "./src/ui/react/components/theme-provider.tsx", "./src/ui/react/components/theme-toggle.tsx", "./src/ui/react/components/chat/chat-header.tsx", "./src/ui/react/components/chat/chat-input.tsx", "./src/ui/react/components/chat/chat-messages.tsx", "./src/ui/react/components/chat/chat-sidebar.tsx", "./src/ui/react/components/chat/modern-chat-header.tsx", "./src/ui/react/components/chat/modern-chat-input.tsx", "./src/ui/react/components/chat/modern-chat-message.tsx", "./src/ui/react/components/chat/modern-chat-messages.tsx", "./src/ui/react/components/chat/modern-sidebar.tsx", "./src/ui/react/components/effects/glass-effect.tsx", "./src/ui/react/components/effects/glass-lens.tsx", "./src/ui/react/components/effects/liquid-glass.tsx", "./src/ui/react/components/effects/minimal-glass.tsx", "./src/ui/react/components/icons/icon-library.tsx", "./src/ui/react/components/panels/command-panel.tsx", "./src/ui/react/components/panels/history-panel.tsx", "./src/ui/react/components/panels/new-chat-panel.tsx", "./src/ui/react/components/panels/store-panel.tsx", "./src/ui/react/components/providers/theme-provider.tsx", "./src/ui/react/components/ui/avatar.tsx", "./src/ui/react/components/ui/badge.tsx", "./src/ui/react/components/ui/button.tsx", "./src/ui/react/components/ui/card.tsx", "./src/ui/react/components/ui/checkbox.tsx", "./src/ui/react/components/ui/dropdown-menu.tsx", "./src/ui/react/components/ui/input.tsx", "./src/ui/react/components/ui/keycap-button.tsx", "./src/ui/react/components/ui/scroll-area.tsx", "./src/ui/react/components/ui/select.tsx", "./src/ui/react/components/ui/tabs.tsx", "./src/ui/react/components/ui/textarea.tsx", "./src/ui/react/components/ui/toast.tsx", "./src/ui/react/components/ui/toaster.tsx", "./src/ui/react/components/ui/tooltip.tsx", "./src/ui/react/components/ui/use-toast.ts", "./src/ui/react/hooks/use-media-query.ts", "./src/ui/react/lib/api.ts", "./src/ui/react/lib/auth.ts", "./src/ui/react/lib/utils.ts"], "errors": true, "version": "5.8.3"}