/**
 * Aizen AI Extension - Webview Styles
 * VS Code theme-aware styles for the React UI
 */

/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    height: 100%;
    font-family: var(--vscode-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
    font-size: var(--vscode-font-size, 13px);
    color: var(--vscode-foreground);
    background-color: var(--vscode-editor-background);
    overflow: hidden;
}

#root {
    height: 100vh;
    overflow: auto;
}

/* VS Code theme variables fallbacks */
:root {
    --vscode-foreground: #cccccc;
    --vscode-editor-background: #1e1e1e;
    --vscode-editor-foreground: #d4d4d4;
    --vscode-panel-background: #252526;
    --vscode-panel-border: #3c3c3c;
    --vscode-button-background: #0e639c;
    --vscode-button-foreground: #ffffff;
    --vscode-button-hoverBackground: #1177bb;
    --vscode-input-background: #3c3c3c;
    --vscode-input-border: #3c3c3c;
    --vscode-input-foreground: #cccccc;
    --vscode-list-hoverBackground: #2a2d2e;
    --vscode-list-activeSelectionBackground: #094771;
    --vscode-scrollbarSlider-background: #79797966;
    --vscode-scrollbarSlider-hoverBackground: #646464b3;
    --vscode-scrollbarSlider-activeBackground: #bfbfbf66;
}

/* Light theme overrides */
.vscode-light {
    --vscode-foreground: #616161;
    --vscode-editor-background: #ffffff;
    --vscode-editor-foreground: #383a42;
    --vscode-panel-background: #f3f3f3;
    --vscode-panel-border: #e0e0e0;
    --vscode-button-background: #007acc;
    --vscode-button-foreground: #ffffff;
    --vscode-button-hoverBackground: #005a9e;
    --vscode-input-background: #ffffff;
    --vscode-input-border: #cecece;
    --vscode-input-foreground: #383a42;
    --vscode-list-hoverBackground: #f0f0f0;
    --vscode-list-activeSelectionBackground: #0066cc;
}

/* High contrast theme overrides */
.vscode-high-contrast {
    --vscode-foreground: #ffffff;
    --vscode-editor-background: #000000;
    --vscode-editor-foreground: #ffffff;
    --vscode-panel-background: #000000;
    --vscode-panel-border: #6fc3df;
    --vscode-button-background: #0e639c;
    --vscode-button-foreground: #ffffff;
    --vscode-button-hoverBackground: #1177bb;
    --vscode-input-background: #000000;
    --vscode-input-border: #6fc3df;
    --vscode-input-foreground: #ffffff;
    --vscode-list-hoverBackground: #2a2d2e;
    --vscode-list-activeSelectionBackground: #094771;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 14px;
    height: 14px;
}

::-webkit-scrollbar-thumb {
    background-color: var(--vscode-scrollbarSlider-background);
    border-radius: 7px;
    border: 3px solid transparent;
    background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--vscode-scrollbarSlider-hoverBackground);
}

::-webkit-scrollbar-thumb:active {
    background-color: var(--vscode-scrollbarSlider-activeBackground);
}

::-webkit-scrollbar-track {
    background-color: transparent;
}

::-webkit-scrollbar-corner {
    background-color: transparent;
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Utility classes */
.animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-slideInRight {
    animation: slideInRight 0.3s ease-out;
}

.animate-slideInLeft {
    animation: slideInLeft 0.3s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

/* Custom component styles */
.aizen-card {
    background-color: var(--vscode-panel-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    transition: all 0.2s ease;
}

.aizen-card:hover {
    border-color: var(--vscode-button-background);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.aizen-button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.aizen-button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.aizen-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.aizen-input {
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
    width: 100%;
}

.aizen-input:focus {
    outline: none;
    border-color: var(--vscode-button-background);
    box-shadow: 0 0 0 2px rgba(14, 99, 156, 0.2);
}

.aizen-chip {
    display: inline-block;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin: 2px;
}

.aizen-chip.secondary {
    background-color: var(--vscode-panel-border);
    color: var(--vscode-foreground);
}

.aizen-progress {
    width: 100%;
    height: 6px;
    background-color: var(--vscode-panel-border);
    border-radius: 3px;
    overflow: hidden;
}

.aizen-progress-bar {
    height: 100%;
    background-color: var(--vscode-button-background);
    transition: width 0.3s ease;
}

.aizen-status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.aizen-status-indicator.idle {
    background-color: #6c757d;
}

.aizen-status-indicator.busy {
    background-color: #28a745;
    animation: pulse 1.5s infinite;
}

.aizen-status-indicator.error {
    background-color: #dc3545;
}

/* Responsive design */
@media (max-width: 768px) {
    .aizen-card {
        padding: 12px;
        margin-bottom: 12px;
    }
    
    .aizen-button {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .aizen-input {
        padding: 6px 10px;
        font-size: 12px;
    }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
    .aizen-card {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }
    
    .aizen-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    }
}

/* Print styles */
@media print {
    .aizen-button,
    .aizen-input {
        display: none;
    }
    
    .aizen-card {
        border: 1px solid #000;
        box-shadow: none;
    }
}
