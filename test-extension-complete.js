#!/usr/bin/env node

/**
 * Complete Extension Test - Verify all components are working
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Complete Aizen AI Extension Test...\n');

// Test results
const results = {
    compilation: false,
    commandRegistration: false,
    uiFiles: false,
    mcpComponents: false,
    packageJson: false
};

console.log('📋 Running comprehensive tests...\n');

// Test 1: TypeScript Compilation
console.log('1️⃣ Testing TypeScript Compilation...');
try {
    const { execSync } = require('child_process');
    execSync('npx tsc --noEmit', { stdio: 'pipe' });
    console.log('✅ TypeScript compilation successful');
    results.compilation = true;
} catch (error) {
    console.log('❌ TypeScript compilation failed');
    console.log('   Error:', error.message);
}

// Test 2: Command Registration
console.log('\n2️⃣ Testing Command Registration...');
try {
    const extensionContent = fs.readFileSync('src/extension.ts', 'utf8');
    const packageContent = fs.readFileSync('package.json', 'utf8');
    const packageJson = JSON.parse(packageContent);
    
    const requiredCommands = [
        'aizen.mcp.configEditor',
        'aizen.mcp.openSettings',
        'aizen.mcp.status',
        'aizen.mcp.addExternalServer'
    ];
    
    let commandsValid = true;
    
    for (const command of requiredCommands) {
        const inExtension = extensionContent.includes(command);
        const inPackage = packageJson.contributes?.commands?.some(cmd => cmd.command === command);
        
        if (inExtension && inPackage) {
            console.log(`✅ Command ${command} properly registered`);
        } else {
            console.log(`❌ Command ${command} missing (ext: ${inExtension}, pkg: ${inPackage})`);
            commandsValid = false;
        }
    }
    
    results.commandRegistration = commandsValid;
} catch (error) {
    console.log('❌ Command registration test failed:', error.message);
}

// Test 3: UI Files
console.log('\n3️⃣ Testing UI Files...');
try {
    const uiFiles = [
        'out/ui/settings.html',
        'out/ui/settings.css',
        'out/ui/settings.js'
    ];
    
    let uiValid = true;
    
    for (const file of uiFiles) {
        if (fs.existsSync(file)) {
            console.log(`✅ UI file ${file} exists`);
        } else {
            console.log(`❌ UI file ${file} missing`);
            uiValid = false;
        }
    }
    
    results.uiFiles = uiValid;
} catch (error) {
    console.log('❌ UI files test failed:', error.message);
}

// Test 4: MCP Components
console.log('\n4️⃣ Testing MCP Components...');
try {
    const mcpFiles = [
        'src/mcp/MCPClient.ts',
        'src/mcp/MCPConfigEditorProvider.ts',
        'src/mcp/StandardMCPManager.ts',
        'src/mcp/MCPSettingsProvider.ts',
        'src/mcp/types.ts'
    ];
    
    let mcpValid = true;
    
    for (const file of mcpFiles) {
        if (fs.existsSync(file)) {
            console.log(`✅ MCP component ${file} exists`);
        } else {
            console.log(`❌ MCP component ${file} missing`);
            mcpValid = false;
        }
    }
    
    // Check if compiled files exist
    const compiledFiles = [
        'out/mcp/MCPClient.js',
        'out/mcp/MCPConfigEditorProvider.js',
        'out/mcp/StandardMCPManager.js'
    ];
    
    for (const file of compiledFiles) {
        if (fs.existsSync(file)) {
            console.log(`✅ Compiled MCP file ${file} exists`);
        } else {
            console.log(`❌ Compiled MCP file ${file} missing`);
            mcpValid = false;
        }
    }
    
    results.mcpComponents = mcpValid;
} catch (error) {
    console.log('❌ MCP components test failed:', error.message);
}

// Test 5: Package.json Configuration
console.log('\n5️⃣ Testing Package.json Configuration...');
try {
    const packageContent = fs.readFileSync('package.json', 'utf8');
    const packageJson = JSON.parse(packageContent);
    
    const requiredFields = [
        'name',
        'displayName',
        'description',
        'version',
        'engines',
        'categories',
        'activationEvents',
        'main',
        'contributes'
    ];
    
    let packageValid = true;
    
    for (const field of requiredFields) {
        if (packageJson[field]) {
            console.log(`✅ Package.json has ${field}`);
        } else {
            console.log(`❌ Package.json missing ${field}`);
            packageValid = false;
        }
    }
    
    // Check specific configurations
    if (packageJson.contributes?.commands?.length > 0) {
        console.log(`✅ Package.json has ${packageJson.contributes.commands.length} commands`);
    } else {
        console.log('❌ Package.json has no commands');
        packageValid = false;
    }
    
    if (packageJson.activationEvents?.length > 0) {
        console.log(`✅ Package.json has ${packageJson.activationEvents.length} activation events`);
    } else {
        console.log('❌ Package.json missing activation events');
        packageValid = false;
    }
    
    results.packageJson = packageValid;
} catch (error) {
    console.log('❌ Package.json test failed:', error.message);
}

// Final Results
console.log('\n📊 Test Results Summary');
console.log('========================');

const allPassed = Object.values(results).every(result => result);

for (const [test, passed] of Object.entries(results)) {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test}`);
}

console.log('\n🎯 Overall Status:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');

if (allPassed) {
    console.log('\n🚀 Extension is ready for testing!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('   1. Press F5 to launch Extension Development Host');
    console.log('   2. Open Command Palette (Ctrl+Shift+P)');
    console.log('   3. Type "Aizen MCP: Configure MCP Servers"');
    console.log('   4. Verify the command appears and executes');
    console.log('');
    console.log('🎉 The command should now work without errors!');
} else {
    console.log('\n🔧 Issues found. Please check the failed tests above.');
    console.log('');
    console.log('💡 Common fixes:');
    console.log('   • Run "npm run build" to recompile');
    console.log('   • Check TypeScript errors with "npx tsc --noEmit"');
    console.log('   • Verify all files are in correct locations');
    console.log('   • Restart VS Code Extension Development Host');
}

process.exit(allPassed ? 0 : 1);
