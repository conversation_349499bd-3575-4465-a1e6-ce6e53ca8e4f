#!/usr/bin/env node

/**
 * Debug script to check UI file paths and content
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging UI file paths...\n');

// Check if all UI files exist
const uiFiles = [
    'out/ui/index.html',
    'out/ui/main.js',
    'out/ui/styles.css',
    'out/ui/chat.css',
    'out/ui/settings.html',
    'out/ui/settings.js'
];

console.log('1. 📁 Checking UI files existence:');
for (const file of uiFiles) {
    const exists = fs.existsSync(file);
    const size = exists ? fs.statSync(file).size : 0;
    console.log(`   ${exists ? '✅' : '❌'} ${file} ${exists ? `(${size} bytes)` : '- MISSING'}`);
}

// Check HTML template content
console.log('\n2. 📄 Checking HTML template content:');
try {
    const htmlPath = 'out/ui/index.html';
    if (fs.existsSync(htmlPath)) {
        const content = fs.readFileSync(htmlPath, 'utf8');
        console.log(`   ✅ HTML file size: ${content.length} characters`);
        
        // Check for placeholders
        const placeholders = ['{{CSS_URI}}', '{{CHAT_CSS_URI}}', '{{SCRIPT_URI}}', '{{THEME_CLASS}}', '{{NONCE}}', '{{CSP_SOURCE}}'];
        console.log('   📝 Placeholders found:');
        for (const placeholder of placeholders) {
            const found = content.includes(placeholder);
            console.log(`      ${found ? '✅' : '❌'} ${placeholder}`);
        }
        
        // Check for basic HTML structure
        const hasDoctype = content.includes('<!DOCTYPE html>');
        const hasHead = content.includes('<head>');
        const hasBody = content.includes('<body>');
        const hasRoot = content.includes('id="root"');
        
        console.log('   🏗️ HTML structure:');
        console.log(`      ${hasDoctype ? '✅' : '❌'} DOCTYPE`);
        console.log(`      ${hasHead ? '✅' : '❌'} HEAD tag`);
        console.log(`      ${hasBody ? '✅' : '❌'} BODY tag`);
        console.log(`      ${hasRoot ? '✅' : '❌'} Root div`);
        
    } else {
        console.log('   ❌ HTML file not found');
    }
} catch (error) {
    console.log(`   ❌ Error reading HTML: ${error.message}`);
}

// Check main.js content
console.log('\n3. 📜 Checking main.js content:');
try {
    const jsPath = 'out/ui/main.js';
    if (fs.existsSync(jsPath)) {
        const content = fs.readFileSync(jsPath, 'utf8');
        console.log(`   ✅ JS file size: ${content.length} characters`);
        
        // Check for key classes/functions
        const hasController = content.includes('AizenUIController');
        const hasConstructor = content.includes('constructor()');
        const hasEventListeners = content.includes('setupEventListeners');
        
        console.log('   🔧 JS structure:');
        console.log(`      ${hasController ? '✅' : '❌'} AizenUIController class`);
        console.log(`      ${hasConstructor ? '✅' : '❌'} Constructor`);
        console.log(`      ${hasEventListeners ? '✅' : '❌'} Event listeners`);
        
    } else {
        console.log('   ❌ JS file not found');
    }
} catch (error) {
    console.log(`   ❌ Error reading JS: ${error.message}`);
}

// Check CSS files
console.log('\n4. 🎨 Checking CSS files:');
const cssFiles = ['out/ui/styles.css', 'out/ui/chat.css'];
for (const cssFile of cssFiles) {
    try {
        if (fs.existsSync(cssFile)) {
            const content = fs.readFileSync(cssFile, 'utf8');
            console.log(`   ✅ ${cssFile}: ${content.length} characters`);
        } else {
            console.log(`   ❌ ${cssFile}: Not found`);
        }
    } catch (error) {
        console.log(`   ❌ ${cssFile}: Error reading - ${error.message}`);
    }
}

// Check extension structure
console.log('\n5. 🏗️ Checking extension structure:');
const extensionFiles = [
    'out/extension.js',
    'out/providers/AizenChatViewProvider.js',
    'out/services/AizenExtensionManager.js',
    'package.json'
];

for (const file of extensionFiles) {
    const exists = fs.existsSync(file);
    console.log(`   ${exists ? '✅' : '❌'} ${file}`);
}

console.log('\n📊 Summary:');
const allUIFilesExist = uiFiles.every(file => fs.existsSync(file));
const allExtensionFilesExist = extensionFiles.every(file => fs.existsSync(file));

if (allUIFilesExist && allExtensionFilesExist) {
    console.log('✅ All files are present');
    console.log('✅ Extension should be able to load UI');
    console.log('\n🚀 If the extension still shows "no data provider", check:');
    console.log('   1. VS Code Developer Console for errors');
    console.log('   2. Extension Host output panel');
    console.log('   3. Make sure the extension is actually activating');
    console.log('   4. Try the command: "Aizen AI: Basic Test"');
} else {
    console.log('❌ Some files are missing');
    console.log('💡 Try running: npm run compile');
}
