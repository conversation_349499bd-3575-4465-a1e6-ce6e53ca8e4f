{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020", "DOM", "DOM.Iterable"], "sourceMap": true, "rootDir": "src", "strict": false, "jsx": "react-jsx", "moduleResolution": "node", "baseUrl": "./", "paths": {"*": ["node_modules/*"]}, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "declarationMap": false, "inlineSourceMap": false, "inlineSources": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noImplicitReturns": false, "noImplicitThis": false, "noImplicitAny": false, "strictBindCallApply": false, "strictFunctionTypes": false, "strictNullChecks": false, "strictPropertyInitialization": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false}, "include": ["src/**/*"], "exclude": ["node_modules", ".vscode-test", "out", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true}}