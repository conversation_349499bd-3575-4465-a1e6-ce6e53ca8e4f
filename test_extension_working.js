#!/usr/bin/env node

/**
 * Test script to verify the Aizen AI Extension is working properly
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Aizen AI Extension...\n');

// Test 1: Check if extension files are compiled and present
console.log('1. 📁 Checking compiled extension files...');
const requiredFiles = [
    'out/extension.js',
    'out/providers/AizenChatViewProvider.js',
    'out/services/AizenExtensionManager.js',
    'out/mcp/hub.js',
    'out/mcp/security.js'
];

let allFilesPresent = true;
for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
        console.log(`   ✅ ${file}`);
    } else {
        console.log(`   ❌ ${file} - MISSING`);
        allFilesPresent = false;
    }
}

// Test 2: Check UI files
console.log('\n2. 🎨 Checking UI files...');
const uiFiles = [
    'out/ui/index.html',
    'out/ui/main.js',
    'out/ui/styles.css',
    'out/ui/settings.html'
];

let allUIFilesPresent = true;
for (const file of uiFiles) {
    if (fs.existsSync(file)) {
        console.log(`   ✅ ${file}`);
    } else {
        console.log(`   ❌ ${file} - MISSING`);
        allUIFilesPresent = false;
    }
}

// Test 3: Check package.json configuration
console.log('\n3. 📦 Checking package.json configuration...');
try {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // Check main entry point
    if (pkg.main === './out/extension.js') {
        console.log('   ✅ Main entry point: ./out/extension.js');
    } else {
        console.log(`   ❌ Main entry point: ${pkg.main} (should be ./out/extension.js)`);
    }
    
    // Check activation events
    if (pkg.activationEvents && pkg.activationEvents.includes('onStartupFinished')) {
        console.log('   ✅ Activation event: onStartupFinished');
    } else {
        console.log('   ❌ Missing activation event: onStartupFinished');
    }
    
    // Check views container
    if (pkg.contributes && pkg.contributes.viewsContainers && pkg.contributes.viewsContainers.activitybar) {
        console.log('   ✅ Activity bar container configured');
    } else {
        console.log('   ❌ Activity bar container not configured');
    }
    
    // Check views
    if (pkg.contributes && pkg.contributes.views && pkg.contributes.views['aizen-ai']) {
        console.log('   ✅ Chat view configured');
    } else {
        console.log('   ❌ Chat view not configured');
    }
    
} catch (error) {
    console.log(`   ❌ Error reading package.json: ${error.message}`);
}

// Test 4: Check icon files
console.log('\n4. 🎯 Checking icon files...');
const iconFiles = [
    'media/icons/aizen-logo-white.svg',
    'media/icons/aizen-logo.svg'
];

for (const file of iconFiles) {
    if (fs.existsSync(file)) {
        console.log(`   ✅ ${file}`);
    } else {
        console.log(`   ❌ ${file} - MISSING`);
    }
}

// Test 5: Check if extension.js has proper exports
console.log('\n5. 🔧 Checking extension.js exports...');
try {
    const extensionJs = fs.readFileSync('out/extension.js', 'utf8');
    
    if (extensionJs.includes('exports.activate')) {
        console.log('   ✅ activate function exported');
    } else {
        console.log('   ❌ activate function not exported');
    }
    
    if (extensionJs.includes('exports.deactivate')) {
        console.log('   ✅ deactivate function exported');
    } else {
        console.log('   ❌ deactivate function not exported');
    }
    
} catch (error) {
    console.log(`   ❌ Error reading extension.js: ${error.message}`);
}

// Summary
console.log('\n📊 Test Summary:');
if (allFilesPresent && allUIFilesPresent) {
    console.log('✅ All required files are present');
    console.log('✅ Extension should be working properly');
    console.log('\n🚀 To test the extension:');
    console.log('   1. Open VS Code');
    console.log('   2. Press F5 to open Extension Development Host');
    console.log('   3. Look for "Aizen AI" in the activity bar (left sidebar)');
    console.log('   4. Click on it to open the chat view');
    console.log('   5. Try the command palette: Ctrl+Shift+P -> "Aizen"');
} else {
    console.log('❌ Some files are missing - extension may not work properly');
    console.log('💡 Try running: npm run compile');
}

console.log('\n🎯 Quick test commands to try in VS Code:');
console.log('   - Ctrl+Shift+P -> "Aizen AI: Basic Test"');
console.log('   - Ctrl+Shift+P -> "Aizen AI: Open AI Chat"');
console.log('   - Ctrl+Shift+P -> "Aizen MCP: Show MCP Server Status"');
