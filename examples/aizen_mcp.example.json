{"$schema": "../schemas/aizen_mcp.schema.json", "mcpServers": {"playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"}}, "filesystem": {"command": "node", "args": ["/path/to/server.js", "/allowed/directory"], "env": {"NODE_ENV": "production"}}, "sqlite": {"command": "npx", "args": ["mcp-server-sqlite", "--db-path", "/path/to/database.db"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "postgresql://user:password@localhost:5432/database"}}, "remote-api": {"url": "https://api.example.com/mcp", "headers": {"Authorization": "Bearer your_api_token_here", "Content-Type": "application/json"}}, "deepwiki-sse": {"url": "https://mcp.deepwiki.com/sse"}, "windsurf-server": {"serverUrl": "https://mcp.windsurf.com/sse"}, "cline-server": {"url": "https://mcp.cline.com/sse", "disabled": false, "autoApprove": ["safe_tool_1", "safe_tool_2"]}, "python-server": {"command": "python", "args": ["-m", "my_mcp_server"], "cwd": "/path/to/server/directory", "env": {"PYTHONPATH": "/path/to/server", "API_KEY": "your_api_key"}}, "docker-server": {"command": "docker", "args": ["run", "-it", "--rm", "my-mcp-server:latest"]}, "bun-server": {"command": "bun", "args": ["run", "server.ts"], "cwd": "/path/to/bun/project"}}, "globalSettings": {"autoStart": true, "maxConcurrentServers": 10, "defaultTimeout": 30000, "logLevel": "info"}, "security": {"requireConfirmation": true, "allowedCommands": ["node", "python", "npx", "bun", "docker"], "blockedCommands": ["rm", "del", "sudo", "format"], "allowedDomains": ["api.github.com", "mcp.deepwiki.com"], "maxExecutionTime": 60000}, "metadata": {"name": "Aizen AI MCP Configuration", "description": "Standard MCP server configuration following industry formats", "author": "Aizen AI", "created": "2024-01-15T10:30:00Z", "lastModified": "2024-01-15T10:30:00Z"}}