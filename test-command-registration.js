#!/usr/bin/env node

/**
 * Test script to verify MCP command registration
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Aizen AI Command Registration...\n');

// Check if extension.ts has the command registered
const extensionPath = 'src/extension.ts';
const packageJsonPath = 'package.json';

console.log('📁 Checking command registration files...');

try {
    // Check extension.ts
    const extensionContent = fs.readFileSync(extensionPath, 'utf8');
    console.log('✅ src/extension.ts exists');
    
    // Check package.json
    const packageContent = fs.readFileSync(packageJsonPath, 'utf8');
    const packageJson = JSON.parse(packageContent);
    console.log('✅ package.json exists');
    
    console.log('\n🔍 Checking command registration...');
    
    // Check if command is registered in extension.ts
    const commandRegistrations = [
        'aizen.mcp.configEditor',
        'aizen.mcp.openSettings',
        'aizen.mcp.status',
        'aizen.mcp.addExternalServer'
    ];
    
    let registrationValid = true;
    
    for (const command of commandRegistrations) {
        if (extensionContent.includes(command)) {
            console.log(`✅ Command ${command} is registered in extension.ts`);
        } else {
            console.log(`❌ Command ${command} is missing from extension.ts`);
            registrationValid = false;
        }
    }
    
    console.log('\n🔍 Checking package.json commands...');
    
    // Check if commands are declared in package.json
    const commands = packageJson.contributes?.commands || [];
    const commandIds = commands.map(cmd => cmd.command);
    
    for (const command of commandRegistrations) {
        if (commandIds.includes(command)) {
            console.log(`✅ Command ${command} is declared in package.json`);
        } else {
            console.log(`❌ Command ${command} is missing from package.json`);
            registrationValid = false;
        }
    }
    
    console.log('\n🔍 Checking variable declarations...');
    
    // Check if required variables are declared
    const requiredVars = [
        'mcpConfigEditorProvider',
        'standardMCPManager',
        'MCPConfigEditorProvider',
        'StandardMCPManager'
    ];
    
    for (const varName of requiredVars) {
        if (extensionContent.includes(varName)) {
            console.log(`✅ Variable ${varName} is declared`);
        } else {
            console.log(`❌ Variable ${varName} is missing`);
            registrationValid = false;
        }
    }
    
    console.log('\n🔍 Checking initialization order...');
    
    // Check initialization order
    const initChecks = [
        { name: 'StandardMCPManager initialization', pattern: 'standardMCPManager = new StandardMCPManager' },
        { name: 'MCPConfigEditorProvider initialization', pattern: 'mcpConfigEditorProvider = new MCPConfigEditorProvider' },
        { name: 'Command registration', pattern: 'aizen.mcp.configEditor' }
    ];
    
    for (const check of initChecks) {
        if (extensionContent.includes(check.pattern)) {
            console.log(`✅ ${check.name} found`);
        } else {
            console.log(`❌ ${check.name} missing`);
            registrationValid = false;
        }
    }
    
    console.log('\n📊 Command Registration Test Summary');
    console.log('=====================================');
    
    if (registrationValid) {
        console.log('✅ All command registrations are valid!');
        console.log('');
        console.log('🚀 Commands available:');
        console.log('   • aizen.mcp.configEditor - Open MCP Configuration Editor');
        console.log('   • aizen.mcp.openSettings - Open MCP Settings Panel');
        console.log('   • aizen.mcp.status - Show MCP Server Status');
        console.log('   • aizen.mcp.addExternalServer - Add External MCP Server');
        console.log('');
        console.log('🎯 How to test:');
        console.log('   1. Press F5 to launch Extension Development Host');
        console.log('   2. Open Command Palette (Ctrl+Shift+P)');
        console.log('   3. Type "Aizen MCP: Configure MCP Servers"');
        console.log('   4. Command should appear and execute successfully');
        console.log('');
        console.log('✨ Ready for testing!');
    } else {
        console.log('❌ Command registration has issues!');
        console.log('');
        console.log('🔧 Please check:');
        console.log('   • All commands are registered in extension.ts');
        console.log('   • All commands are declared in package.json');
        console.log('   • All required variables are declared');
        console.log('   • Initialization order is correct');
    }
    
} catch (error) {
    console.error('❌ Error during command registration test:', error);
    process.exit(1);
}
