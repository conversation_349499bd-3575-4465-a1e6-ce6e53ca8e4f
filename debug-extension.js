#!/usr/bin/env node

/**
 * Debug Extension Loading - Check if extension is loading properly
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Debug: Checking Extension Loading...\n');

// Check if the compiled extension exists
const extensionPath = 'out/extension.js';

if (!fs.existsSync(extensionPath)) {
    console.log('❌ Compiled extension not found at:', extensionPath);
    console.log('   Run "npm run build" first');
    process.exit(1);
}

console.log('✅ Compiled extension found');

// Read the compiled extension
const extensionContent = fs.readFileSync(extensionPath, 'utf8');

// Check for critical components
const checks = [
    {
        name: 'Extension exports activate function',
        pattern: 'exports.activate = activate',
        required: true
    },
    {
        name: 'MCP command registration',
        pattern: 'aizen.mcp.configEditor',
        required: true
    },
    {
        name: 'VS Code imports',
        pattern: 'require("vscode")',
        required: true
    },
    {
        name: 'MCP Client initialization',
        pattern: 'AizenMCPClient',
        required: true
    },
    {
        name: 'Error handling in activate',
        pattern: 'catch.*error',
        required: false
    }
];

console.log('\n🔍 Checking compiled extension...');

let allPassed = true;

for (const check of checks) {
    const regex = new RegExp(check.pattern, 'i');
    const found = regex.test(extensionContent);
    
    if (found) {
        console.log(`✅ ${check.name}`);
    } else {
        const status = check.required ? '❌' : '⚠️';
        console.log(`${status} ${check.name}`);
        if (check.required) {
            allPassed = false;
        }
    }
}

// Check package.json activation events
console.log('\n🔍 Checking package.json...');

const packageContent = fs.readFileSync('package.json', 'utf8');
const packageJson = JSON.parse(packageContent);

if (packageJson.activationEvents && packageJson.activationEvents.length > 0) {
    console.log(`✅ Activation events: ${packageJson.activationEvents.join(', ')}`);
} else {
    console.log('❌ No activation events found');
    allPassed = false;
}

if (packageJson.main === './out/extension.js') {
    console.log('✅ Main entry point correct');
} else {
    console.log(`❌ Main entry point incorrect: ${packageJson.main}`);
    allPassed = false;
}

// Check for command declarations
const commands = packageJson.contributes?.commands || [];
const mcpCommands = commands.filter(cmd => cmd.command.startsWith('aizen.mcp'));

if (mcpCommands.length > 0) {
    console.log(`✅ ${mcpCommands.length} MCP commands declared in package.json`);
    for (const cmd of mcpCommands) {
        console.log(`   • ${cmd.command}: ${cmd.title}`);
    }
} else {
    console.log('❌ No MCP commands declared in package.json');
    allPassed = false;
}

console.log('\n📊 Debug Results');
console.log('================');

if (allPassed) {
    console.log('✅ Extension should load correctly');
    console.log('');
    console.log('🔧 If commands still not working, try:');
    console.log('   1. Close VS Code completely');
    console.log('   2. Reopen the project');
    console.log('   3. Press F5 to launch Extension Development Host');
    console.log('   4. Check Developer Console for errors:');
    console.log('      Help > Toggle Developer Tools > Console');
    console.log('   5. Look for activation errors or MCP initialization failures');
    console.log('');
    console.log('🎯 Test commands:');
    console.log('   • Ctrl+Shift+P → "Aizen MCP: Configure MCP Servers"');
    console.log('   • Ctrl+Shift+P → "Aizen: Test" (basic test command)');
} else {
    console.log('❌ Extension has issues that prevent proper loading');
    console.log('');
    console.log('🔧 Fix the issues above and rebuild:');
    console.log('   npm run build');
}

// Create a simple test command file
console.log('\n🧪 Creating simple test extension...');

const simpleExtension = `
const vscode = require('vscode');

function activate(context) {
    console.log('🚀 SIMPLE TEST: Extension activating...');
    
    // Register a simple test command
    const testCommand = vscode.commands.registerCommand('aizen.test.simple', () => {
        vscode.window.showInformationMessage('🎉 SIMPLE TEST WORKS! Extension is loading correctly.');
        console.log('🎉 SIMPLE TEST COMMAND EXECUTED');
    });
    
    context.subscriptions.push(testCommand);
    
    // Register MCP config command
    const mcpCommand = vscode.commands.registerCommand('aizen.mcp.configEditor', () => {
        vscode.window.showInformationMessage('🚀 MCP Config command works!');
        console.log('🚀 MCP CONFIG COMMAND EXECUTED');
    });
    
    context.subscriptions.push(mcpCommand);
    
    console.log('✅ SIMPLE TEST: Commands registered successfully');
    vscode.window.showInformationMessage('🚀 Aizen AI Test Extension loaded! Try "Aizen: Simple Test" command.');
}

function deactivate() {
    console.log('🛑 SIMPLE TEST: Extension deactivating...');
}

module.exports = { activate, deactivate };
`;

fs.writeFileSync('out/extension-simple.js', simpleExtension);
console.log('✅ Created simple test extension: out/extension-simple.js');

console.log('\n🔧 To test with simple extension:');
console.log('   1. Temporarily rename out/extension.js to out/extension-backup.js');
console.log('   2. Rename out/extension-simple.js to out/extension.js');
console.log('   3. Press F5 to test');
console.log('   4. Try command: "Aizen: Simple Test"');
console.log('   5. If it works, the issue is in the main extension initialization');

process.exit(allPassed ? 0 : 1);
