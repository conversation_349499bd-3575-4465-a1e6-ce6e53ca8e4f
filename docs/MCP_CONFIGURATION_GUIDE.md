# Aizen AI MCP Configuration Guide

## Overview

Aizen AI provides a powerful and secure system for configuring external MCP (Model Context Protocol) servers through the `aizen_mcp.json` configuration file. This guide will help you understand how to add, configure, and manage external MCP servers safely and effectively.

## Quick Start

1. **Open MCP Settings**: Use `Ctrl+Shift+P` → "Aizen AI: Open MCP Settings"
2. **Add External Server**: Click the "+" button in the MCP settings interface
3. **Edit Configuration**: This opens the `aizen_mcp.json` file with IntelliSense support
4. **Save and Reload**: Changes are automatically detected and applied

## Configuration File Location

The `aizen_mcp.json` file is created in:
- **Workspace**: `{workspace-root}/aizen_mcp.json` (if you have a workspace open)
- **Global**: `{extension-storage}/aizen_mcp.json` (fallback for global configuration)

## Configuration File Structure

```json
{
  "$schema": "./schemas/aizen_mcp.schema.json",
  "version": "1.0.0",
  "metadata": {
    "name": "My MCP Servers",
    "description": "External MCP servers configuration",
    "author": "Your Name"
  },
  "globalSettings": {
    "autoStart": true,
    "maxConcurrentServers": 10,
    "defaultTimeout": 30000,
    "retryAttempts": 3,
    "logLevel": "info"
  },
  "security": {
    "requireConfirmation": true,
    "allowedCommands": ["node", "python", "npx"],
    "blockedCommands": ["rm", "del", "sudo"],
    "maxExecutionTime": 60000
  },
  "servers": [
    // Your server configurations go here
  ]
}
```

## Adding External Servers

### Example 1: GitHub MCP Server

```json
{
  "id": "github-mcp",
  "name": "GitHub MCP Server",
  "description": "Provides GitHub API access for repository management",
  "icon": "🐙",
  "category": "development",
  "transport": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-github"],
    "env": {
      "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"
    }
  },
  "apiKey": {
    "required": true,
    "envVar": "GITHUB_TOKEN",
    "description": "GitHub Personal Access Token",
    "url": "https://github.com/settings/tokens"
  },
  "settings": {
    "enabled": true,
    "autoStart": true,
    "timeout": 30000,
    "priority": 10
  },
  "security": {
    "riskLevel": "medium",
    "requireConfirmation": true,
    "allowedTools": ["create_repository", "search_repositories"],
    "maxExecutionTime": 60000
  }
}
```

### Example 2: Custom HTTP Server

```json
{
  "id": "custom-api",
  "name": "Custom API Server",
  "description": "Custom MCP server for internal APIs",
  "icon": "🔧",
  "category": "api",
  "transport": {
    "type": "http",
    "url": "https://api.example.com/mcp",
    "headers": {
      "Authorization": "Bearer ${API_TOKEN}",
      "Content-Type": "application/json"
    }
  },
  "apiKey": {
    "required": true,
    "envVar": "API_TOKEN",
    "description": "API token for custom service"
  },
  "settings": {
    "enabled": true,
    "autoStart": false,
    "timeout": 30000
  },
  "security": {
    "riskLevel": "low",
    "requireConfirmation": false
  }
}
```

## Transport Types

### STDIO Transport
For servers that run as separate processes:

```json
"transport": {
  "type": "stdio",
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-example"],
  "cwd": "/path/to/working/directory",
  "env": {
    "API_KEY": "${MY_API_KEY}"
  }
}
```

### HTTP Transport
For servers accessible via HTTP/HTTPS:

```json
"transport": {
  "type": "http",
  "url": "https://api.example.com/mcp",
  "headers": {
    "Authorization": "Bearer ${TOKEN}"
  }
}
```

### SSE Transport
For Server-Sent Events:

```json
"transport": {
  "type": "sse",
  "url": "https://api.example.com/mcp/sse"
}
```

## Security Configuration

### Global Security Settings

```json
"security": {
  "requireConfirmation": true,
  "allowedCommands": ["node", "python", "npx", "pip"],
  "blockedCommands": ["rm", "del", "sudo", "format"],
  "allowedDomains": ["api.github.com", "api.openai.com"],
  "blockedDomains": ["localhost", "127.0.0.1"],
  "maxExecutionTime": 120000,
  "sandboxMode": false
}
```

### Server-Specific Security

```json
"security": {
  "riskLevel": "medium",
  "requireConfirmation": true,
  "allowedTools": ["safe_tool_1", "safe_tool_2"],
  "blockedTools": ["dangerous_tool"],
  "maxExecutionTime": 30000
}
```

## API Key Management

### Environment Variables
Always use environment variables for API keys:

```json
"apiKey": {
  "required": true,
  "envVar": "MY_API_KEY",
  "description": "API key for the service",
  "url": "https://example.com/api-keys"
}
```

### Setting Environment Variables

**Windows:**
```cmd
set MY_API_KEY=your_api_key_here
```

**macOS/Linux:**
```bash
export MY_API_KEY=your_api_key_here
```

**VS Code Settings:**
Add to your VS Code settings.json:
```json
{
  "terminal.integrated.env.windows": {
    "MY_API_KEY": "your_api_key_here"
  },
  "terminal.integrated.env.osx": {
    "MY_API_KEY": "your_api_key_here"
  },
  "terminal.integrated.env.linux": {
    "MY_API_KEY": "your_api_key_here"
  }
}
```

## Categories and Icons

### Available Categories
- `search` - Search and discovery tools
- `scraping` - Web scraping and content extraction
- `database` - Database access and queries
- `api` - API integrations
- `file-system` - File system operations
- `development` - Development tools
- `productivity` - Productivity applications
- `communication` - Communication tools
- `analytics` - Analytics and reporting
- `security` - Security tools
- `other` - Other categories

### Icon Examples
Use Unicode emojis for server icons:
- 🐙 GitHub
- 🔍 Search
- 📊 Analytics
- 🗄️ Database
- 🌐 Web API
- 📁 File System
- 🔧 Tools
- 🤖 AI/ML

## Best Practices

### 1. Security First
- Always use environment variables for API keys
- Set appropriate risk levels
- Use allowlists instead of blocklists when possible
- Enable confirmation for high-risk operations

### 2. Naming and Organization
- Use descriptive server IDs (alphanumeric, underscore, hyphen only)
- Provide clear names and descriptions
- Use appropriate categories and icons
- Set meaningful priorities

### 3. Configuration Management
- Keep configurations in version control (without secrets)
- Use separate configurations for different environments
- Document your server configurations
- Regular security reviews

### 4. Performance
- Set reasonable timeouts
- Limit concurrent servers
- Use appropriate retry settings
- Monitor server performance

## Troubleshooting

### Common Issues

**Server Won't Start**
- Check command path and arguments
- Verify environment variables are set
- Check working directory permissions
- Review security validation errors

**API Key Issues**
- Ensure environment variable is set correctly
- Check API key permissions and scope
- Verify API key format and validity

**Security Validation Errors**
- Review blocked commands/domains lists
- Check for dangerous patterns in commands
- Verify URL protocols and domains
- Review risk level settings

**Connection Timeouts**
- Increase timeout values
- Check network connectivity
- Verify server URL and ports
- Review firewall settings

### Validation Commands

Use these VS Code commands for troubleshooting:
- `Aizen AI: Validate MCP Configuration` - Check configuration validity
- `Aizen AI: Reload MCP Configuration` - Reload after changes
- `Aizen AI: Show MCP Status` - View server status
- `Aizen AI: Test MCP Connection` - Test individual server connections

## Advanced Configuration

### Custom Working Directories
```json
"transport": {
  "type": "stdio",
  "command": "python",
  "args": ["-m", "my_mcp_server"],
  "cwd": "/path/to/server/directory"
}
```

### Complex Environment Setup
```json
"transport": {
  "env": {
    "NODE_ENV": "production",
    "LOG_LEVEL": "info",
    "API_BASE_URL": "https://api.example.com",
    "TIMEOUT": "30000"
  }
}
```

### Priority-Based Loading
```json
"settings": {
  "priority": 1,  // Highest priority (1-100)
  "autoStart": true,
  "enabled": true
}
```

## Support and Resources

- **Documentation**: [https://docs.aizen.ai/mcp](https://docs.aizen.ai/mcp)
- **Examples**: Check the `examples/` directory in the extension
- **Schema**: Full JSON schema available in `schemas/aizen_mcp.schema.json`
- **Community**: Join our Discord for help and discussions

## Contributing

Found a bug or want to suggest improvements? Please open an issue on our GitHub repository or submit a pull request with your enhancements.
