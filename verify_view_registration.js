/**
 * Verify View Registration
 * Checks if the package.json properly defines the webview view
 */

const fs = require('fs');

console.log('🔍 Verifying View Registration...');
console.log('==================================\n');

try {
    const pkg = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    
    console.log('1. Checking viewsContainers...');
    if (pkg.contributes?.viewsContainers?.activitybar) {
        const containers = pkg.contributes.viewsContainers.activitybar;
        const aizenContainer = containers.find(c => c.id === 'aizen-ai');
        
        if (aizenContainer) {
            console.log('✅ Activity bar container found:');
            console.log(`   ID: ${aizenContainer.id}`);
            console.log(`   Title: ${aizenContainer.title}`);
            console.log(`   Icon: ${aizenContainer.icon}`);
            
            // Check if icon file exists
            if (fs.existsSync(aizenContainer.icon)) {
                console.log('✅ Icon file exists');
            } else {
                console.log('❌ Icon file missing');
            }
        } else {
            console.log('❌ Activity bar container "aizen-ai" not found');
        }
    } else {
        console.log('❌ No viewsContainers.activitybar defined');
    }
    
    console.log('\n2. Checking views...');
    if (pkg.contributes?.views?.['aizen-ai']) {
        const views = pkg.contributes.views['aizen-ai'];
        const chatView = views.find(v => v.id === 'aizen.chatView');
        
        if (chatView) {
            console.log('✅ Chat view found:');
            console.log(`   ID: ${chatView.id}`);
            console.log(`   Name: ${chatView.name}`);
            console.log(`   Icon: ${chatView.icon}`);
            console.log(`   When: ${chatView.when}`);
        } else {
            console.log('❌ Chat view "aizen.chatView" not found');
        }
    } else {
        console.log('❌ No views for "aizen-ai" container defined');
    }
    
    console.log('\n3. Checking commands...');
    if (pkg.contributes?.commands) {
        const commands = pkg.contributes.commands;
        const relevantCommands = commands.filter(c => 
            c.command.startsWith('aizen.') && 
            (c.command.includes('chat') || c.command.includes('test'))
        );
        
        console.log(`✅ Found ${relevantCommands.length} relevant commands:`);
        relevantCommands.forEach(cmd => {
            console.log(`   • ${cmd.command} - ${cmd.title}`);
        });
    } else {
        console.log('❌ No commands defined');
    }
    
    console.log('\n4. Checking activation events...');
    if (pkg.activationEvents) {
        console.log('✅ Activation events:');
        pkg.activationEvents.forEach(event => {
            console.log(`   • ${event}`);
        });
    } else {
        console.log('❌ No activation events defined');
    }
    
    console.log('\n5. Summary...');
    const hasContainer = pkg.contributes?.viewsContainers?.activitybar?.some(c => c.id === 'aizen-ai');
    const hasView = pkg.contributes?.views?.['aizen-ai']?.some(v => v.id === 'aizen.chatView');
    const hasCommands = pkg.contributes?.commands?.some(c => c.command.startsWith('aizen.'));
    
    if (hasContainer && hasView && hasCommands) {
        console.log('✅ Package.json configuration looks correct!');
        console.log('\nIf you\'re still getting "no data provider" error:');
        console.log('1. The extension might not be loading');
        console.log('2. The provider registration code might be failing');
        console.log('3. Check VS Code Developer Console for errors');
    } else {
        console.log('❌ Package.json configuration has issues:');
        if (!hasContainer) console.log('   • Missing activity bar container');
        if (!hasView) console.log('   • Missing chat view definition');
        if (!hasCommands) console.log('   • Missing commands');
    }
    
} catch (error) {
    console.log(`❌ Error reading package.json: ${error.message}`);
}

console.log('\n📋 Next Steps:');
console.log('1. Reload VS Code: Ctrl+Shift+P → "Developer: Reload Window"');
console.log('2. Check console: Help → Toggle Developer Tools → Console');
console.log('3. Test basic command: Ctrl+Shift+P → "Aizen AI: Basic Test"');
console.log('4. Look for Aizen AI icon in activity bar');
console.log('5. If icon present, click it to test view loading');
