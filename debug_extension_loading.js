/**
 * Debug Extension Loading Issues
 * This script helps identify why the extension is failing to load
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging Extension Loading Issues...');
console.log('==========================================\n');

// 1. Check package.json validity
console.log('1. Checking package.json...');
try {
    const packagePath = './package.json';
    const packageContent = fs.readFileSync(packagePath, 'utf8');
    const pkg = JSON.parse(packageContent);
    
    console.log('✅ package.json is valid JSON');
    console.log(`   Name: ${pkg.name}`);
    console.log(`   Version: ${pkg.version}`);
    console.log(`   Publisher: ${pkg.publisher}`);
    console.log(`   Main: ${pkg.main}`);
    console.log(`   Engines: ${JSON.stringify(pkg.engines)}`);
    
    // Check required fields
    const requiredFields = ['name', 'version', 'publisher', 'engines', 'main', 'contributes'];
    const missingFields = requiredFields.filter(field => !pkg[field]);
    
    if (missingFields.length > 0) {
        console.log(`❌ Missing required fields: ${missingFields.join(', ')}`);
    } else {
        console.log('✅ All required fields present');
    }
    
    // Check engines format
    if (pkg.engines && typeof pkg.engines === 'object' && pkg.engines.vscode) {
        console.log('✅ Engines field is properly formatted');
    } else {
        console.log('❌ Engines field is invalid or missing vscode version');
    }
    
} catch (error) {
    console.log(`❌ package.json error: ${error.message}`);
}

// 2. Check main entry point
console.log('\n2. Checking main entry point...');
const mainFile = './out/extension.js';
if (fs.existsSync(mainFile)) {
    console.log('✅ Main entry point exists');
    
    // Check if it's valid JavaScript
    try {
        require.resolve(path.resolve(mainFile));
        console.log('✅ Main entry point is valid JavaScript');
    } catch (error) {
        console.log(`❌ Main entry point has syntax errors: ${error.message}`);
    }
} else {
    console.log('❌ Main entry point missing');
}

// 3. Check critical files
console.log('\n3. Checking critical files...');
const criticalFiles = [
    'out/extension.js',
    'out/extension.js.map',
    'out/services/AizenExtensionManager.js',
    'out/providers/AizenChatViewProvider.js',
    'out/ui/index.html',
    'out/ui/main.js',
    'media/icons/aizen-logo-white.svg'
];

criticalFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} missing`);
    }
});

// 4. Check for common issues
console.log('\n4. Checking for common issues...');

// Check for circular dependencies
console.log('   Checking for potential circular dependencies...');
try {
    const extensionContent = fs.readFileSync('./out/extension.js', 'utf8');
    if (extensionContent.includes('Cannot resolve module')) {
        console.log('❌ Module resolution issues detected');
    } else {
        console.log('✅ No obvious module resolution issues');
    }
} catch (error) {
    console.log(`⚠️ Could not check extension content: ${error.message}`);
}

// Check for path issues
console.log('   Checking for path issues...');
const currentDir = process.cwd();
console.log(`   Current directory: ${currentDir}`);
console.log(`   Expected extension path: ${path.resolve('.')}`);

// 5. Try to load the extension module
console.log('\n5. Testing extension module loading...');
try {
    // Clear require cache
    delete require.cache[path.resolve('./out/extension.js')];
    
    const extension = require('./out/extension.js');
    console.log('✅ Extension module loads successfully');
    
    if (typeof extension.activate === 'function') {
        console.log('✅ activate function is present');
    } else {
        console.log('❌ activate function is missing');
    }
    
    if (typeof extension.deactivate === 'function') {
        console.log('✅ deactivate function is present');
    } else {
        console.log('⚠️ deactivate function is missing (optional)');
    }
    
} catch (error) {
    console.log(`❌ Extension module loading failed: ${error.message}`);
    console.log(`   Stack trace: ${error.stack}`);
}

// 6. Check VS Code compatibility
console.log('\n6. Checking VS Code compatibility...');
try {
    const pkg = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    const vscodeVersion = pkg.engines?.vscode;
    
    if (vscodeVersion) {
        console.log(`✅ VS Code version requirement: ${vscodeVersion}`);
        
        // Check if version format is valid
        if (vscodeVersion.match(/^\^?\d+\.\d+\.\d+$/)) {
            console.log('✅ Version format is valid');
        } else {
            console.log('⚠️ Version format might be invalid');
        }
    } else {
        console.log('❌ No VS Code version requirement specified');
    }
} catch (error) {
    console.log(`❌ Could not check VS Code compatibility: ${error.message}`);
}

console.log('\n🏁 Debug Summary:');
console.log('================');
console.log('If all checks pass but the extension still fails to load:');
console.log('1. Try restarting VS Code completely');
console.log('2. Clear VS Code extension cache');
console.log('3. Run: code --extensionDevelopmentPath="$(pwd)" --disable-extensions');
console.log('4. Check VS Code Developer Console (Help > Toggle Developer Tools)');
console.log('5. Look for more detailed error messages in the console');
