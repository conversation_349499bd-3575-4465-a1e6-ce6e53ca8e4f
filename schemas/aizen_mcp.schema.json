{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://aizen.ai/schemas/mcp-config.json", "title": "Aizen AI MCP Configuration", "description": "Standard MCP server configuration following Claude <PERSON>, VS Code, and industry formats", "type": "object", "properties": {"$schema": {"type": "string", "description": "JSON Schema reference for validation and IntelliSense"}, "mcpServers": {"type": "object", "description": "MCP servers configuration following industry standard format", "patternProperties": {"^[a-zA-Z0-9_-]+$": {"type": "object", "description": "MCP server configuration", "oneOf": [{"title": "STDIO Transport", "properties": {"command": {"type": "string", "description": "Command to execute for STDIO transport (e.g., 'npx', 'node', 'python')"}, "args": {"type": "array", "items": {"type": "string"}, "description": "Command line arguments"}, "env": {"type": "object", "patternProperties": {"^[A-Z_][A-Z0-9_]*$": {"type": "string"}}, "description": "Environment variables for the command"}, "cwd": {"type": "string", "description": "Working directory for the command"}, "disabled": {"type": "boolean", "description": "Whether this server is disabled", "default": false}, "autoApprove": {"type": "array", "items": {"type": "string"}, "description": "List of tools to auto-approve (Cline format)"}}, "required": ["command"], "additionalProperties": false}, {"title": "HTTP Transport", "properties": {"url": {"type": "string", "format": "uri", "pattern": "^https?://", "description": "HTTP/HTTPS URL for the server"}, "headers": {"type": "object", "patternProperties": {"^[A-Za-z0-9-]+$": {"type": "string"}}, "description": "HTTP headers to send with requests"}, "disabled": {"type": "boolean", "description": "Whether this server is disabled", "default": false}, "autoApprove": {"type": "array", "items": {"type": "string"}, "description": "List of tools to auto-approve"}}, "required": ["url"], "additionalProperties": false}, {"title": "SSE Transport (Windsurf format)", "properties": {"serverUrl": {"type": "string", "format": "uri", "pattern": "^https?://", "description": "Server-Sent Events URL (Windsurf format)"}, "disabled": {"type": "boolean", "description": "Whether this server is disabled", "default": false}}, "required": ["serverUrl"], "additionalProperties": false}]}}, "additionalProperties": false}, "globalSettings": {"type": "object", "description": "Global settings for MCP server management", "properties": {"autoStart": {"type": "boolean", "description": "Whether to automatically start servers on extension activation", "default": true}, "maxConcurrentServers": {"type": "integer", "minimum": 1, "maximum": 50, "description": "Maximum number of servers to run concurrently", "default": 10}, "defaultTimeout": {"type": "integer", "minimum": 5000, "maximum": 300000, "description": "Default timeout for server operations (milliseconds)", "default": 30000}, "logLevel": {"type": "string", "enum": ["debug", "info", "warn", "error"], "description": "Logging level for MCP operations", "default": "info"}}, "additionalProperties": false}, "security": {"type": "object", "description": "Security settings for MCP servers", "properties": {"requireConfirmation": {"type": "boolean", "description": "Require user confirmation before executing tools", "default": true}, "allowedCommands": {"type": "array", "items": {"type": "string"}, "description": "List of allowed commands for STDIO transport (whitelist)"}, "blockedCommands": {"type": "array", "items": {"type": "string"}, "description": "List of blocked commands for STDIO transport (blacklist)"}, "allowedDomains": {"type": "array", "items": {"type": "string", "format": "hostname"}, "description": "List of allowed domains for HTTP/SSE transport"}, "maxExecutionTime": {"type": "integer", "minimum": 1000, "maximum": 600000, "description": "Maximum execution time for tools (milliseconds)", "default": 60000}}, "additionalProperties": false}, "metadata": {"type": "object", "description": "Configuration file metadata (optional)", "properties": {"name": {"type": "string", "description": "Human-readable name for this configuration"}, "description": {"type": "string", "description": "Description of this configuration file"}, "author": {"type": "string", "description": "Author of this configuration"}, "created": {"type": "string", "format": "date-time", "description": "Creation timestamp (ISO 8601)"}, "lastModified": {"type": "string", "format": "date-time", "description": "Last modification timestamp (ISO 8601)"}}, "additionalProperties": false}}, "required": ["mcpServers"], "additionalProperties": false}