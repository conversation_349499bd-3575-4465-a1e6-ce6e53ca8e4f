#!/usr/bin/env node

/**
 * Validate Extension Package
 * Checks if the extension is properly configured for VS Code
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Extension Package...\n');

// Check package.json
console.log('📋 Checking package.json...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const checks = [
    {
        name: 'Has name',
        check: () => !!packageJson.name,
        fix: 'Add "name" field to package.json'
    },
    {
        name: 'Has version',
        check: () => !!packageJson.version,
        fix: 'Add "version" field to package.json'
    },
    {
        name: 'Has engines.vscode',
        check: () => !!packageJson.engines?.vscode,
        fix: 'Add "engines.vscode" field to package.json'
    },
    {
        name: 'Has main entry point',
        check: () => !!packageJson.main,
        fix: 'Add "main" field pointing to compiled extension'
    },
    {
        name: 'Main file exists',
        check: () => fs.existsSync(packageJson.main || ''),
        fix: 'Compile the extension or fix main path'
    },
    {
        name: 'Has activation events',
        check: () => Array.isArray(packageJson.activationEvents) && packageJson.activationEvents.length > 0,
        fix: 'Add activation events to package.json'
    },
    {
        name: 'Has contributes section',
        check: () => !!packageJson.contributes,
        fix: 'Add "contributes" section to package.json'
    },
    {
        name: 'Has commands',
        check: () => Array.isArray(packageJson.contributes?.commands) && packageJson.contributes.commands.length > 0,
        fix: 'Add commands to contributes.commands'
    }
];

let allPassed = true;

for (const check of checks) {
    try {
        if (check.check()) {
            console.log(`✅ ${check.name}`);
        } else {
            console.log(`❌ ${check.name} - ${check.fix}`);
            allPassed = false;
        }
    } catch (error) {
        console.log(`❌ ${check.name} - Error: ${error.message}`);
        allPassed = false;
    }
}

// Check extension file syntax
console.log('\n📋 Checking extension file...');
try {
    const extensionPath = packageJson.main;
    if (fs.existsSync(extensionPath)) {
        const extensionContent = fs.readFileSync(extensionPath, 'utf8');
        
        // Basic syntax checks
        if (extensionContent.includes('activate')) {
            console.log('✅ Has activate function');
        } else {
            console.log('❌ Missing activate function');
            allPassed = false;
        }
        
        if (extensionContent.includes('deactivate')) {
            console.log('✅ Has deactivate function');
        } else {
            console.log('❌ Missing deactivate function');
            allPassed = false;
        }
        
        if (extensionContent.includes('module.exports')) {
            console.log('✅ Has module.exports');
        } else {
            console.log('❌ Missing module.exports');
            allPassed = false;
        }
        
        // Try to require it
        try {
            delete require.cache[path.resolve(extensionPath)];
            const ext = require(path.resolve(extensionPath));
            if (typeof ext.activate === 'function') {
                console.log('✅ Extension can be loaded and has activate function');
            } else {
                console.log('❌ Extension loaded but activate is not a function');
                allPassed = false;
            }
        } catch (error) {
            console.log(`❌ Extension cannot be loaded: ${error.message}`);
            allPassed = false;
        }
        
    } else {
        console.log(`❌ Extension file not found: ${extensionPath}`);
        allPassed = false;
    }
} catch (error) {
    console.log(`❌ Error checking extension file: ${error.message}`);
    allPassed = false;
}

console.log('\n📊 Validation Results');
console.log('====================');

if (allPassed) {
    console.log('✅ Extension package is valid!');
    console.log('\n🚀 Next steps:');
    console.log('   1. Press F5 in VS Code');
    console.log('   2. Check Developer Console for any runtime errors');
    console.log('   3. Look for the debug message in console');
    console.log('   4. Try Ctrl+Shift+P → "🔥 DEBUG TEST"');
} else {
    console.log('❌ Extension package has issues');
    console.log('\n🔧 Fix the issues above and try again');
}

// Show package.json summary
console.log('\n📋 Package Summary:');
console.log(`   Name: ${packageJson.name}`);
console.log(`   Version: ${packageJson.version}`);
console.log(`   Main: ${packageJson.main}`);
console.log(`   VS Code: ${packageJson.engines?.vscode}`);
console.log(`   Activation: ${packageJson.activationEvents?.join(', ')}`);
console.log(`   Commands: ${packageJson.contributes?.commands?.length || 0}`);

process.exit(allPassed ? 0 : 1);
