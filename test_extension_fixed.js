#!/usr/bin/env node

/**
 * Quick test to verify the Aizen AI Extension is fixed and working
 */

const fs = require('fs');

console.log('🔧 Testing Aizen AI Extension Fix...\n');

// Test 1: Check if the duplicate registration issue is fixed
console.log('1. 🔍 Checking for duplicate registration fix...');
try {
    const extensionJs = fs.readFileSync('out/extension.js', 'utf8');
    
    // Count how many times 'registerWebviewViewProvider' appears
    const matches = extensionJs.match(/registerWebviewViewProvider/g);
    const count = matches ? matches.length : 0;
    
    console.log(`   Found ${count} registerWebviewViewProvider calls in extension.js`);
    
    if (count <= 1) {
        console.log('   ✅ No duplicate registration in main extension file');
    } else {
        console.log('   ⚠️ Multiple registrations found - may cause conflicts');
    }
    
} catch (error) {
    console.log(`   ❌ Error reading extension.js: ${error.message}`);
}

// Test 2: Check if AizenExtensionManager has the registration guard
console.log('\n2. 🛡️ Checking registration guard in AizenExtensionManager...');
try {
    const managerJs = fs.readFileSync('out/services/AizenExtensionManager.js', 'utf8');
    
    if (managerJs.includes('_chatProviderRegistered')) {
        console.log('   ✅ Registration guard flag found');
    } else {
        console.log('   ❌ Registration guard flag not found');
    }
    
    if (managerJs.includes('already registered')) {
        console.log('   ✅ Duplicate registration check found');
    } else {
        console.log('   ❌ Duplicate registration check not found');
    }
    
} catch (error) {
    console.log(`   ❌ Error reading AizenExtensionManager.js: ${error.message}`);
}

// Test 3: Verify all required files are still present
console.log('\n3. 📁 Verifying all required files...');
const requiredFiles = [
    'out/extension.js',
    'out/services/AizenExtensionManager.js',
    'out/providers/AizenChatViewProvider.js',
    'out/ui/index.html',
    'out/ui/main.js',
    'package.json'
];

let allPresent = true;
for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
        console.log(`   ✅ ${file}`);
    } else {
        console.log(`   ❌ ${file} - MISSING`);
        allPresent = false;
    }
}

// Test 4: Check package.json configuration
console.log('\n4. 📦 Checking package.json configuration...');
try {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    if (pkg.contributes && pkg.contributes.views && pkg.contributes.views['aizen-ai']) {
        console.log('   ✅ Chat view properly configured in package.json');
    } else {
        console.log('   ❌ Chat view not properly configured');
    }
    
} catch (error) {
    console.log(`   ❌ Error reading package.json: ${error.message}`);
}

// Summary
console.log('\n📊 Fix Summary:');
if (allPresent) {
    console.log('✅ Extension files are present');
    console.log('✅ Duplicate registration issue should be fixed');
    console.log('✅ Extension should now load without errors');
    
    console.log('\n🚀 To test the fix:');
    console.log('   1. Close any open VS Code Extension Development Host windows');
    console.log('   2. Press F5 in VS Code to open a new Extension Development Host');
    console.log('   3. Check the Developer Console (F12) for any errors');
    console.log('   4. Look for "Aizen AI" in the activity bar');
    console.log('   5. Try the command: Ctrl+Shift+P -> "Aizen AI: Basic Test"');
    
    console.log('\n✨ Expected behavior:');
    console.log('   - No "already registered" errors in console');
    console.log('   - Aizen AI icon appears in activity bar');
    console.log('   - Chat view opens when clicked');
    console.log('   - Commands work in command palette');
    
} else {
    console.log('❌ Some files are missing');
    console.log('💡 Try running: npm run compile');
}

console.log('\n🎯 If you still see errors, check:');
console.log('   - VS Code Developer Console (Help -> Toggle Developer Tools)');
console.log('   - Extension Host output panel');
console.log('   - Make sure no other instances of the extension are running');
