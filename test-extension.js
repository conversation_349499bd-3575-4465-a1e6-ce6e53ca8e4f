#!/usr/bin/env node

/**
 * Test Extension Loading - Diagnose why extension isn't working
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSING EXTENSION LOADING ISSUES\n');

// Check critical files
const criticalFiles = [
    'out/extension.js',
    'package.json',
    'out/ui/settings.html',
    'src/extension.ts',
    'node_modules/vscode/package.json'
];

console.log('📁 Checking critical files...');
for (const file of criticalFiles) {
    if (fs.existsSync(file)) {
        const stats = fs.statSync(file);
        console.log(`✅ ${file} (${stats.size} bytes)`);
    } else {
        console.log(`❌ MISSING: ${file}`);
    }
}

// Check package.json structure
console.log('\n📦 Checking package.json...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const requiredFields = ['main', 'activationEvents', 'contributes'];
for (const field of requiredFields) {
    if (packageJson[field]) {
        console.log(`✅ ${field}: ${typeof packageJson[field] === 'object' ? 'present' : packageJson[field]}`);
    } else {
        console.log(`❌ MISSING: ${field}`);
    }
}

// Check commands
if (packageJson.contributes?.commands) {
    console.log(`✅ Commands: ${packageJson.contributes.commands.length} declared`);
    const mcpCommands = packageJson.contributes.commands.filter(cmd => cmd.command.includes('mcp'));
    console.log(`   • MCP commands: ${mcpCommands.length}`);
} else {
    console.log('❌ No commands declared');
}

// Test extension loading
console.log('\n🧪 Testing extension loading...');

try {
    // Try to require the extension
    const extensionPath = path.resolve('out/extension.js');
    console.log(`📍 Extension path: ${extensionPath}`);
    
    // Mock VS Code API
    const mockVscode = {
        commands: {
            registerCommand: (command, callback) => {
                console.log(`   📝 Command registered: ${command}`);
                return { dispose: () => {} };
            }
        },
        window: {
            showInformationMessage: (msg) => console.log(`   ℹ️  ${msg}`),
            showErrorMessage: (msg) => console.log(`   ❌ ${msg}`),
            createWebviewPanel: () => ({
                webview: { html: '', onDidReceiveMessage: () => {} },
                iconPath: null
            })
        },
        ViewColumn: { Two: 2 },
        Uri: {
            file: (path) => ({ fsPath: path })
        },
        workspace: {
            getConfiguration: () => ({
                get: () => undefined,
                update: () => Promise.resolve()
            })
        }
    };
    
    // Mock require for vscode
    const originalRequire = require;
    require = function(id) {
        if (id === 'vscode') {
            return mockVscode;
        }
        return originalRequire.apply(this, arguments);
    };
    
    // Load the extension
    const extension = originalRequire(extensionPath);
    
    if (extension.activate) {
        console.log('✅ Extension exports activate function');
        
        // Mock context
        const mockContext = {
            subscriptions: [],
            extensionPath: process.cwd()
        };
        
        console.log('🚀 Testing activate function...');
        
        // Test activation
        extension.activate(mockContext)
            .then(() => {
                console.log('✅ Extension activated successfully!');
                console.log(`📊 Registered ${mockContext.subscriptions.length} subscriptions`);
            })
            .catch(error => {
                console.log(`❌ Activation failed: ${error.message}`);
                console.log(`   Stack: ${error.stack}`);
            });
            
    } else {
        console.log('❌ Extension does not export activate function');
    }
    
} catch (error) {
    console.log(`❌ Failed to load extension: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
}

// Check VS Code version compatibility
console.log('\n🔧 VS Code Compatibility...');
const engines = packageJson.engines?.vscode;
if (engines) {
    console.log(`✅ Requires VS Code: ${engines}`);
} else {
    console.log('❌ No VS Code engine requirement specified');
}

// Provide troubleshooting steps
console.log('\n🔧 TROUBLESHOOTING STEPS:');
console.log('1. Close VS Code completely');
console.log('2. Delete node_modules and package-lock.json');
console.log('3. Run: npm install');
console.log('4. Run: npm run build');
console.log('5. Open VS Code and press F5');
console.log('6. Check Developer Console: Help > Toggle Developer Tools');
console.log('7. Look for activation errors in console');
console.log('');
console.log('🎯 Test commands to try:');
console.log('   • Ctrl+Shift+P → "Aizen: Test Extension"');
console.log('   • Ctrl+Shift+P → "Aizen: Show Settings"');
console.log('   • Ctrl+Shift+P → "Aizen MCP: Configure MCP Servers"');

console.log('\n💡 If still not working:');
console.log('   • Check if extension is enabled in Extensions panel');
console.log('   • Try reloading window: Ctrl+Shift+P → "Developer: Reload Window"');
console.log('   • Check if there are conflicting extensions');
