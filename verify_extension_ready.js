/**
 * Extension Readiness Verification
 * Checks if the extension is ready for deployment
 */

const fs = require('fs');
const path = require('path');

class ExtensionReadinessChecker {
    constructor() {
        this.issues = [];
        this.warnings = [];
        this.passed = [];
    }

    log(message, type = 'info') {
        const prefix = {
            'info': '📝',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️'
        }[type] || '📝';
        
        console.log(`${prefix} ${message}`);
    }

    checkFile(filePath, description) {
        if (fs.existsSync(filePath)) {
            this.passed.push(`${description}: ${filePath}`);
            return true;
        } else {
            this.issues.push(`Missing ${description}: ${filePath}`);
            return false;
        }
    }

    checkPackageJson() {
        this.log('Checking package.json...');
        
        if (!this.checkFile('package.json', 'Package manifest')) {
            return false;
        }

        try {
            const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            
            // Check required fields
            const requiredFields = ['name', 'version', 'publisher', 'engines', 'main', 'contributes'];
            for (const field of requiredFields) {
                if (!pkg[field]) {
                    this.issues.push(`Missing package.json field: ${field}`);
                } else {
                    this.passed.push(`Package field present: ${field}`);
                }
            }

            // Check VS Code engine version
            if (pkg.engines && pkg.engines.vscode) {
                this.passed.push(`VS Code engine: ${pkg.engines.vscode}`);
            } else {
                this.issues.push('Missing VS Code engine specification');
            }

            // Check main entry point
            if (pkg.main && fs.existsSync(pkg.main)) {
                this.passed.push(`Main entry point exists: ${pkg.main}`);
            } else {
                this.issues.push(`Main entry point missing: ${pkg.main}`);
            }

            return true;
        } catch (error) {
            this.issues.push(`Invalid package.json: ${error.message}`);
            return false;
        }
    }

    checkCompiledFiles() {
        this.log('Checking compiled files...');
        
        const requiredFiles = [
            'out/extension.js',
            'out/extension.js.map'
        ];

        let allPresent = true;
        for (const file of requiredFiles) {
            if (!this.checkFile(file, 'Compiled file')) {
                allPresent = false;
            }
        }

        return allPresent;
    }

    checkUIFiles() {
        this.log('Checking UI files...');
        
        const requiredUIFiles = [
            'out/ui/index.html',
            'out/ui/main.js',
            'out/ui/styles.css',
            'out/ui/chat.css',
            'out/ui/settings.html',
            'out/ui/settings.js'
        ];

        let allPresent = true;
        for (const file of requiredUIFiles) {
            if (!this.checkFile(file, 'UI file')) {
                allPresent = false;
            }
        }

        return allPresent;
    }

    checkIconFiles() {
        this.log('Checking icon files...');
        
        const requiredIcons = [
            'media/icons/aizen-logo-white.svg',
            'media/icons/aizen-logo.svg'
        ];

        let allPresent = true;
        for (const icon of requiredIcons) {
            if (!this.checkFile(icon, 'Icon file')) {
                allPresent = false;
            }
        }

        return allPresent;
    }

    checkProviderFiles() {
        this.log('Checking provider files...');
        
        const requiredProviders = [
            'out/providers/AizenChatViewProvider.js',
            'out/providers/AizenSettingsProvider.js',
            'out/services/AizenExtensionManager.js',
            'out/services/AizenSettingsService.js',
            'out/services/AizenIntegrationService.js'
        ];

        let allPresent = true;
        for (const provider of requiredProviders) {
            if (!this.checkFile(provider, 'Provider file')) {
                allPresent = false;
            }
        }

        return allPresent;
    }

    checkMCPFiles() {
        this.log('Checking MCP files...');
        
        const requiredMCPFiles = [
            'out/mcp/hub.js',
            'out/mcp/manager.js',
            'out/mcp/security.js'
        ];

        let allPresent = true;
        for (const file of requiredMCPFiles) {
            if (!this.checkFile(file, 'MCP file')) {
                allPresent = false;
            }
        }

        return allPresent;
    }

    checkDependencies() {
        this.log('Checking dependencies...');
        
        if (!this.checkFile('node_modules', 'Node modules directory')) {
            this.warnings.push('Dependencies not installed - run npm install');
            return false;
        }

        // Check critical dependencies
        const criticalDeps = [
            'node_modules/@modelcontextprotocol',
            'node_modules/@types/vscode',
            'node_modules/typescript'
        ];

        let allPresent = true;
        for (const dep of criticalDeps) {
            if (!this.checkFile(dep, 'Critical dependency')) {
                allPresent = false;
            }
        }

        return allPresent;
    }

    async runAllChecks() {
        this.log('🔍 Starting Extension Readiness Check...');
        
        const checks = [
            () => this.checkPackageJson(),
            () => this.checkCompiledFiles(),
            () => this.checkUIFiles(),
            () => this.checkIconFiles(),
            () => this.checkProviderFiles(),
            () => this.checkMCPFiles(),
            () => this.checkDependencies()
        ];

        let allPassed = true;
        for (const check of checks) {
            if (!check()) {
                allPassed = false;
            }
        }

        this.printResults(allPassed);
        return allPassed;
    }

    printResults(allPassed) {
        this.log('\n📊 Readiness Check Results:');
        
        this.log(`✅ Passed checks: ${this.passed.length}`, 'success');
        this.log(`❌ Failed checks: ${this.issues.length}`, this.issues.length > 0 ? 'error' : 'success');
        this.log(`⚠️ Warnings: ${this.warnings.length}`, this.warnings.length > 0 ? 'warning' : 'success');

        if (this.issues.length > 0) {
            this.log('\n❌ Issues that need to be fixed:', 'error');
            this.issues.forEach(issue => this.log(`  • ${issue}`, 'error'));
        }

        if (this.warnings.length > 0) {
            this.log('\n⚠️ Warnings:', 'warning');
            this.warnings.forEach(warning => this.log(`  • ${warning}`, 'warning'));
        }

        const status = allPassed ? 'READY FOR DEPLOYMENT' : 'NOT READY - ISSUES FOUND';
        const statusType = allPassed ? 'success' : 'error';
        this.log(`\n🏁 Extension Status: ${status}`, statusType);

        if (allPassed) {
            this.log('\n🎉 The extension is ready to be used in VS Code!', 'success');
            this.log('💡 Next steps:', 'info');
            this.log('  1. Reload VS Code window (Ctrl+Shift+P -> "Developer: Reload Window")', 'info');
            this.log('  2. Check that Aizen AI appears in the activity bar', 'info');
            this.log('  3. Configure API keys via Command Palette -> "Aizen MCP: Add Exa Server"', 'info');
        }
    }
}

// Run the readiness check
const checker = new ExtensionReadinessChecker();
checker.runAllChecks().catch(error => {
    console.error('❌ Readiness check failed:', error);
});
