{
  "$schema": "../schemas/aizen_mcp.schema.json",
  "mcpServers": {
    "// POPULAR MCP SERVERS - Remove // to enable": "",

    "// playwright": {
    "//   command": "npx",
    "//   args": ["@playwright/mcp@latest"]
    "// },

    "// github": {
    "//   command": "npx",
    "//   args": ["-y", "@modelcontextprotocol/server-github"],
    "//   env": {
    "//     GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"
    "//   }
    "// },

    "// filesystem": {
    "//   command": "npx",
    "//   args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/directory"]
    "// },

    "// sqlite": {
    "//   command": "npx",
    "//   args": ["mcp-server-sqlite", "--db-path", "/path/to/database.db"]
    "// },

    "// postgres": {
    "//   command": "npx",
    "//   args": ["-y", "@modelcontextprotocol/server-postgres"],
    "//   env": {
    "//     POSTGRES_CONNECTION_STRING": "postgresql://user:password@localhost:5432/database"
    "//   }
    "// },

    "// brave-search": {
    "//   command": "npx",
    "//   args": ["-y", "@modelcontextprotocol/server-brave-search"],
    "//   env": {
    "//     BRAVE_API_KEY": "your_brave_api_key"
    "//   }
    "// },

    "// puppeteer": {
    "//   command": "npx",
    "//   args": ["-y", "@modelcontextprotocol/server-puppeteer"]
    "// },

    "// memory": {
    "//   command": "npx",
    "//   args": ["-y", "@modelcontextprotocol/server-memory"]
    "// },

    "// HTTP/SSE SERVERS": "",

    "// deepwiki": {
    "//   url": "https://mcp.deepwiki.com/sse"
    "// },

    "// windsurf-server": {
    "//   serverUrl": "https://mcp.windsurf.com/sse"
    "// },

    "// custom-api": {
    "//   url": "https://api.example.com/mcp",
    "//   headers": {
    "//     Authorization": "Bearer your_api_token",
    "//     Content-Type": "application/json"
    "//   }
    "// },

    "// PLATFORM-SPECIFIC EXAMPLES": "",

    "// python-server": {
    "//   command": "python",
    "//   args": ["-m", "my_mcp_server"],
    "//   cwd": "/path/to/server/directory",
    "//   env": {
    "//     PYTHONPATH": "/path/to/server",
    "//     API_KEY": "your_api_key"
    "//   }
    "// },

    "// bun-server": {
    "//   command": "bun",
    "//   args": ["run", "server.ts"],
    "//   cwd": "/path/to/bun/project"
    "// },

    "// docker-server": {
    "//   command": "docker",
    "//   args": ["run", "-it", "--rm", "my-mcp-server:latest"]
    "// },

    "// DISABLED EXAMPLES - Remove disabled: true to enable": "",

    "example-stdio": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-everything"],
      "disabled": true
    },

    "example-http": {
      "url": "https://api.example.com/mcp",
      "headers": {
        "Content-Type": "application/json"
      },
      "disabled": true
    }
  },

  "globalSettings": {
    "autoStart": true,
    "maxConcurrentServers": 10,
    "defaultTimeout": 30000,
    "logLevel": "info"
  },

  "security": {
    "requireConfirmation": true,
    "allowedCommands": ["node", "python", "python3", "npx", "bun"],
    "blockedCommands": ["rm", "del", "sudo", "format", "shutdown"],
    "allowedDomains": ["api.github.com", "api.openai.com"],
    "maxExecutionTime": 60000
  },

  "metadata": {
    "name": "Aizen AI MCP Configuration Template",
    "description": "Template for configuring MCP servers following industry standards",
    "author": "Aizen AI",
    "created": "2024-01-15T10:00:00Z"
  },

  "// INSTRUCTIONS": {
    "// 1": "Remove '//' comments from servers you want to enable",
    "// 2": "Replace placeholder values with your actual configuration",
    "// 3": "Set up environment variables for API keys",
    "// 4": "Remove 'disabled: true' to enable servers",
    "// 5": "Save the file - changes are automatically detected",
    "// ": "",
    "// Documentation": "See docs/MCP_CONFIGURATION_GUIDE.md for detailed help",
    "// ": "",
    "// Transport Types": {
      "// stdio": "Run as separate process (most common)",
      "// http": "HTTP/HTTPS endpoint",
      "// sse": "Server-Sent Events (serverUrl format)"
    },
    "// ": "",
    "// Popular Servers": {
      "// @playwright/mcp": "Browser automation and testing",
      "// @modelcontextprotocol/server-github": "GitHub API access",
      "// @modelcontextprotocol/server-filesystem": "File system operations",
      "// @modelcontextprotocol/server-postgres": "PostgreSQL database access",
      "// @modelcontextprotocol/server-brave-search": "Web search via Brave",
      "// @modelcontextprotocol/server-puppeteer": "Web scraping and automation",
      "// @modelcontextprotocol/server-memory": "Persistent memory storage"
    }
  }
}
