#!/usr/bin/env node

/**
 * Test MCP Command Registration
 * This script tests if the MCP commands are properly registered
 */

console.log('🧪 Testing MCP Command Registration...\n');

// Mock VS Code API for testing
const mockVSCode = {
    commands: {
        registerCommand: (command, callback) => {
            console.log(`✅ Command registered: ${command}`);
            return { dispose: () => {} };
        }
    },
    window: {
        showInformationMessage: (message) => {
            console.log(`📢 Info: ${message}`);
        },
        showErrorMessage: (message) => {
            console.log(`❌ Error: ${message}`);
        }
    },
    ExtensionContext: class {
        constructor() {
            this.subscriptions = [];
            this.extensionPath = '/test/path';
        }
    }
};

// Test the command registration
try {
    console.log('🔍 Testing command registration...');
    
    // Simulate the MCP command registration
    const commands = [
        'aizen.mcp.configEditor',
        'aizen.mcp.openSettings', 
        'aizen.mcp.status',
        'aizen.mcp.addExternalServer',
        'aizen.mcp.testExaSearch',
        'aizen.mcp.testFirecrawlScrape',
        'aizen.mcp.listTools',
        'aizen.mcp.runTests'
    ];
    
    console.log('\n📋 Expected MCP Commands:');
    commands.forEach(cmd => {
        mockVSCode.commands.registerCommand(cmd, () => {
            console.log(`🎯 ${cmd} executed!`);
        });
    });
    
    console.log('\n✅ All MCP commands registered successfully!');
    console.log('\n🔧 Next steps:');
    console.log('   1. Press F5 in VS Code to launch Extension Development Host');
    console.log('   2. In the new window, press Ctrl+Shift+P');
    console.log('   3. Type "Aizen MCP" to see available commands');
    console.log('   4. Try "Aizen MCP: Configure MCP Servers"');
    
    console.log('\n🐛 If commands still not found:');
    console.log('   1. Check VS Code Developer Console (Help > Toggle Developer Tools)');
    console.log('   2. Look for extension activation errors');
    console.log('   3. Verify package.json activation events');
    console.log('   4. Check if extension is actually loading');
    
} catch (error) {
    console.error('❌ Error during command registration test:', error);
    process.exit(1);
}

// Test package.json validation
console.log('\n🔍 Validating package.json...');

const fs = require('fs');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

// Check activation events
if (!packageJson.activationEvents || packageJson.activationEvents.length === 0) {
    console.log('❌ No activation events found in package.json');
    process.exit(1);
}

console.log(`✅ Activation events: ${packageJson.activationEvents.join(', ')}`);

// Check command contributions
const commands = packageJson.contributes?.commands || [];
const mcpCommands = commands.filter(cmd => cmd.command.startsWith('aizen.mcp'));

if (mcpCommands.length === 0) {
    console.log('❌ No MCP commands found in package.json contributions');
    process.exit(1);
}

console.log(`✅ Found ${mcpCommands.length} MCP commands in package.json`);

// Check main entry point
if (packageJson.main !== './out/extension.js') {
    console.log(`❌ Incorrect main entry point: ${packageJson.main}`);
    process.exit(1);
}

console.log('✅ Main entry point is correct');

// Check if compiled extension exists
if (!fs.existsSync('out/extension.js')) {
    console.log('❌ Compiled extension not found at out/extension.js');
    console.log('   Run: npx tsc');
    process.exit(1);
}

console.log('✅ Compiled extension exists');

console.log('\n🎉 All validation checks passed!');
console.log('🚀 Extension should work correctly now.');

console.log('\n📝 Debug checklist if still not working:');
console.log('   □ Extension loads without errors in Developer Console');
console.log('   □ Commands appear in Command Palette (Ctrl+Shift+P)');
console.log('   □ No TypeScript compilation errors');
console.log('   □ All MCP provider files exist in out/mcp/');
console.log('   □ Extension activates on startup (onStartupFinished)');

process.exit(0);
