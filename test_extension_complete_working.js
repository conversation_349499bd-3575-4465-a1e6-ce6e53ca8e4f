/**
 * Complete Extension Test Suite
 * Tests all major functionality of the Aizen AI extension
 */

const vscode = require('vscode');

class ExtensionTester {
    constructor() {
        this.results = {
            passed: 0,
            failed: 0,
            warnings: 0,
            tests: []
        };
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            'info': '📝',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️'
        }[type] || '📝';
        
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    async runTest(name, testFn) {
        this.log(`Running test: ${name}`);
        try {
            const result = await testFn();
            if (result === true) {
                this.results.passed++;
                this.log(`Test passed: ${name}`, 'success');
                this.results.tests.push({ name, status: 'passed' });
            } else if (result === 'warning') {
                this.results.warnings++;
                this.log(`Test warning: ${name}`, 'warning');
                this.results.tests.push({ name, status: 'warning' });
            } else {
                this.results.failed++;
                this.log(`Test failed: ${name}`, 'error');
                this.results.tests.push({ name, status: 'failed', error: result });
            }
        } catch (error) {
            this.results.failed++;
            this.log(`Test failed with exception: ${name} - ${error.message}`, 'error');
            this.results.tests.push({ name, status: 'failed', error: error.message });
        }
    }

    async testExtensionLoaded() {
        const extension = vscode.extensions.getExtension('aizen-ai.aizen-revolutionary-ai');
        if (!extension) {
            return 'Extension not found in VS Code';
        }
        
        if (!extension.isActive) {
            await extension.activate();
        }
        
        return extension.isActive;
    }

    async testCommandsRegistered() {
        const commands = await vscode.commands.getCommands();
        const aizenCommands = commands.filter(cmd => cmd.startsWith('aizen.'));
        
        const expectedCommands = [
            'aizen.showChatView',
            'aizen.showSettings',
            'aizen.mcp.status',
            'aizen.mcp.addExaServer',
            'aizen.mcp.addFirecrawlServer'
        ];
        
        const missingCommands = expectedCommands.filter(cmd => !aizenCommands.includes(cmd));
        
        if (missingCommands.length > 0) {
            return `Missing commands: ${missingCommands.join(', ')}`;
        }
        
        this.log(`Found ${aizenCommands.length} Aizen commands`);
        return true;
    }

    async testConfiguration() {
        const config = vscode.workspace.getConfiguration('aizen');
        
        // Test that configuration properties exist
        const requiredProps = [
            'model.provider',
            'model.name',
            'mcp.exaEnabled',
            'mcp.firecrawlEnabled',
            'ui.theme'
        ];
        
        for (const prop of requiredProps) {
            const value = config.get(prop);
            if (value === undefined) {
                return `Configuration property missing: ${prop}`;
            }
        }
        
        this.log('Configuration properties verified');
        return true;
    }

    async testMCPStatus() {
        try {
            await vscode.commands.executeCommand('aizen.mcp.status');
            return true;
        } catch (error) {
            return `MCP status command failed: ${error.message}`;
        }
    }

    async testUIFiles() {
        const fs = require('fs');
        const path = require('path');
        
        const requiredFiles = [
            'out/ui/index.html',
            'out/ui/main.js',
            'out/ui/styles.css',
            'out/ui/settings.html',
            'out/ui/settings.js'
        ];
        
        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                return `Missing UI file: ${file}`;
            }
        }
        
        this.log('All UI files present');
        return true;
    }

    async testIconFiles() {
        const fs = require('fs');
        
        const requiredIcons = [
            'media/icons/aizen-logo-white.svg',
            'media/icons/aizen-logo.svg'
        ];
        
        for (const icon of requiredIcons) {
            if (!fs.existsSync(icon)) {
                return `Missing icon file: ${icon}`;
            }
        }
        
        this.log('All icon files present');
        return true;
    }

    async testSettingsCommand() {
        try {
            await vscode.commands.executeCommand('aizen.showSettings');
            return true;
        } catch (error) {
            return `Settings command failed: ${error.message}`;
        }
    }

    async runAllTests() {
        this.log('🚀 Starting Aizen AI Extension Test Suite');
        
        await this.runTest('Extension Loaded', () => this.testExtensionLoaded());
        await this.runTest('Commands Registered', () => this.testCommandsRegistered());
        await this.runTest('Configuration Valid', () => this.testConfiguration());
        await this.runTest('MCP Status Working', () => this.testMCPStatus());
        await this.runTest('UI Files Present', () => this.testUIFiles());
        await this.runTest('Icon Files Present', () => this.testIconFiles());
        await this.runTest('Settings Command Working', () => this.testSettingsCommand());
        
        this.printResults();
    }

    printResults() {
        this.log('🎯 Test Results Summary');
        this.log(`✅ Passed: ${this.results.passed}`);
        this.log(`❌ Failed: ${this.results.failed}`);
        this.log(`⚠️ Warnings: ${this.results.warnings}`);
        
        if (this.results.failed > 0) {
            this.log('❌ Failed Tests:', 'error');
            this.results.tests
                .filter(t => t.status === 'failed')
                .forEach(t => this.log(`  • ${t.name}: ${t.error}`, 'error'));
        }
        
        if (this.results.warnings > 0) {
            this.log('⚠️ Warning Tests:', 'warning');
            this.results.tests
                .filter(t => t.status === 'warning')
                .forEach(t => this.log(`  • ${t.name}`, 'warning'));
        }
        
        const overall = this.results.failed === 0 ? 'PASSED' : 'FAILED';
        this.log(`🏁 Overall Result: ${overall}`, this.results.failed === 0 ? 'success' : 'error');
    }
}

// Run the tests
const tester = new ExtensionTester();
tester.runAllTests().catch(error => {
    console.error('❌ Test suite failed:', error);
});
